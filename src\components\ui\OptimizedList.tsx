import React, { useCallback, useMemo, useState } from 'react';
import {
  FlatList,
  FlatListProps,
  View,
  Text,
  StyleSheet,
  RefreshControl,
  ActivityIndicator,
} from 'react-native';
import { debounce, throttle } from '../../utils/performanceUtils';

interface OptimizedListProps<T> extends Omit<FlatListProps<T>, 'data' | 'renderItem'> {
  data: T[];
  renderItem: (item: T, index: number) => React.ReactElement;
  itemHeight?: number;
  estimatedItemSize?: number;
  onRefresh?: () => Promise<void>;
  onLoadMore?: () => Promise<void>;
  hasMore?: boolean;
  loading?: boolean;
  error?: string;
  emptyComponent?: React.ReactNode;
  loadingComponent?: React.ReactNode;
  errorComponent?: React.ReactNode;
  searchQuery?: string;
  filterFn?: (item: T, query: string) => boolean;
  sortFn?: (a: T, b: T) => number;
  enableVirtualization?: boolean;
  windowSize?: number;
  maxToRenderPerBatch?: number;
  updateCellsBatchingPeriod?: number;
}

export function OptimizedList<T>({
  data,
  renderItem,
  itemHeight,
  estimatedItemSize = 60,
  onRefresh,
  onLoadMore,
  hasMore = false,
  loading = false,
  error,
  emptyComponent,
  loadingComponent,
  errorComponent,
  searchQuery = '',
  filterFn,
  sortFn,
  enableVirtualization = true,
  windowSize = 10,
  maxToRenderPerBatch = 10,
  updateCellsBatchingPeriod = 50,
  ...props
}: OptimizedListProps<T>) {
  const [refreshing, setRefreshing] = useState(false);
  const [loadingMore, setLoadingMore] = useState(false);

  // Memoized filtered and sorted data
  const processedData = useMemo(() => {
    let result = [...data];

    // Apply search filter
    if (searchQuery && filterFn) {
      result = result.filter(item => filterFn(item, searchQuery));
    }

    // Apply sorting
    if (sortFn) {
      result.sort(sortFn);
    }

    return result;
  }, [data, searchQuery, filterFn, sortFn]);

  // Optimized render item with memoization
  const memoizedRenderItem = useCallback(
    ({ item, index }: { item: T; index: number }) => {
      return renderItem(item, index);
    },
    [renderItem]
  );

  // Optimized key extractor
  const keyExtractor = useCallback(
    (item: T, index: number) => {
      // Try to use item id if available, otherwise use index
      if (item && typeof item === 'object' && 'id' in item) {
        return String((item as any).id);
      }
      return String(index);
    },
    []
  );

  // Handle refresh
  const handleRefresh = useCallback(async () => {
    if (!onRefresh || refreshing) return;

    setRefreshing(true);
    try {
      await onRefresh();
    } catch (error) {
      console.error('Refresh error:', error);
    } finally {
      setRefreshing(false);
    }
  }, [onRefresh, refreshing]);

  // Handle load more with throttling
  const handleLoadMore = useCallback(
    throttle(async () => {
      if (!onLoadMore || loadingMore || !hasMore) return;

      setLoadingMore(true);
      try {
        await onLoadMore();
      } catch (error) {
        console.error('Load more error:', error);
      } finally {
        setLoadingMore(false);
      }
    }, 1000),
    [onLoadMore, loadingMore, hasMore]
  );

  // Footer component for load more
  const renderFooter = useCallback(() => {
    if (!hasMore && !loadingMore) return null;

    return (
      <View style={styles.footer}>
        {loadingMore && <ActivityIndicator size="small" color="#666" />}
      </View>
    );
  }, [hasMore, loadingMore]);

  // Empty component
  const renderEmpty = useCallback(() => {
    if (loading) {
      return (
        loadingComponent || (
          <View style={styles.center}>
            <ActivityIndicator size="large" color="#666" />
            <Text style={styles.loadingText}>Loading...</Text>
          </View>
        )
      );
    }

    if (error) {
      return (
        errorComponent || (
          <View style={styles.center}>
            <Text style={styles.errorText}>{error}</Text>
          </View>
        )
      );
    }

    return (
      emptyComponent || (
        <View style={styles.center}>
          <Text style={styles.emptyText}>No items found</Text>
        </View>
      )
    );
  }, [loading, error, emptyComponent, loadingComponent, errorComponent]);

  // Get item layout for better performance
  const getItemLayout = useCallback(
    (data: any, index: number) => {
      if (!itemHeight) return undefined;
      
      return {
        length: itemHeight,
        offset: itemHeight * index,
        index,
      };
    },
    [itemHeight]
  );

  return (
    <FlatList
      data={processedData}
      renderItem={memoizedRenderItem}
      keyExtractor={keyExtractor}
      getItemLayout={getItemLayout}
      estimatedItemSize={estimatedItemSize}
      ListEmptyComponent={renderEmpty}
      ListFooterComponent={renderFooter}
      refreshControl={
        onRefresh ? (
          <RefreshControl refreshing={refreshing} onRefresh={handleRefresh} />
        ) : undefined
      }
      onEndReached={handleLoadMore}
      onEndReachedThreshold={0.1}
      // Performance optimizations
      removeClippedSubviews={enableVirtualization}
      windowSize={windowSize}
      maxToRenderPerBatch={maxToRenderPerBatch}
      updateCellsBatchingPeriod={updateCellsBatchingPeriod}
      initialNumToRender={10}
      // Memory optimizations
      disableVirtualization={!enableVirtualization}
      legacyImplementation={false}
      {...props}
    />
  );
}

// Specialized list for orders
export interface OrderItem {
  id: string;
  orderNumber: string;
  restaurant: string;
  status: string;
  earnings: number;
  date: string;
}

export const OptimizedOrderList: React.FC<{
  orders: OrderItem[];
  onOrderPress: (order: OrderItem) => void;
  onRefresh?: () => Promise<void>;
  onLoadMore?: () => Promise<void>;
  hasMore?: boolean;
  loading?: boolean;
}> = ({ orders, onOrderPress, ...props }) => {
  const renderOrderItem = useCallback(
    (order: OrderItem, index: number) => (
      <View style={styles.orderItem}>
        <Text style={styles.orderNumber}>#{order.orderNumber}</Text>
        <Text style={styles.restaurant}>{order.restaurant}</Text>
        <Text style={styles.status}>{order.status}</Text>
        <Text style={styles.earnings}>PKR {order.earnings}</Text>
      </View>
    ),
    []
  );

  const filterOrders = useCallback(
    (order: OrderItem, query: string) => {
      return (
        order.orderNumber.toLowerCase().includes(query.toLowerCase()) ||
        order.restaurant.toLowerCase().includes(query.toLowerCase())
      );
    },
    []
  );

  return (
    <OptimizedList
      data={orders}
      renderItem={renderOrderItem}
      itemHeight={80}
      filterFn={filterOrders}
      {...props}
    />
  );
};

const styles = StyleSheet.create({
  footer: {
    padding: 20,
    alignItems: 'center',
  },
  center: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  loadingText: {
    marginTop: 10,
    color: '#666',
    fontSize: 16,
  },
  errorText: {
    color: '#e74c3c',
    fontSize: 16,
    textAlign: 'center',
  },
  emptyText: {
    color: '#666',
    fontSize: 16,
    textAlign: 'center',
  },
  orderItem: {
    padding: 15,
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
    backgroundColor: '#fff',
  },
  orderNumber: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
  },
  restaurant: {
    fontSize: 14,
    color: '#666',
    marginTop: 4,
  },
  status: {
    fontSize: 12,
    color: '#2ecc71',
    marginTop: 4,
  },
  earnings: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#27ae60',
    marginTop: 4,
  },
});

export default OptimizedList;
