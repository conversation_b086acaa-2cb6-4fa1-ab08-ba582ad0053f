import { Platform, AppState, Dimensions } from 'react-native';
import { performanceMonitor } from './performanceMonitoring';
import { optimizedStorage } from './optimizedStorage';
import { optimizedAnimations } from './optimizedAnimations';
import { optimizedLocationService } from './location/OptimizedLocationService';
import { networkService } from './networkOptimization';
import { PERFORMANCE_CONFIG, FEATURE_FLAGS, getOptimizationStrategy } from '../config/performanceConfig';

/**
 * Centralized optimization initialization service
 * Configures all performance optimizations based on device capabilities
 */

interface DeviceInfo {
  platform: string;
  version: string | number;
  screenWidth: number;
  screenHeight: number;
  totalMemory?: number;
  cpuCount?: number;
  isLowEndDevice?: boolean;
}

class OptimizationInitializer {
  private isInitialized = false;
  private deviceInfo: DeviceInfo;
  private optimizationStrategy: any;

  constructor() {
    this.deviceInfo = this.getDeviceInfo();
    this.optimizationStrategy = getOptimizationStrategy({
      totalMemory: this.deviceInfo.totalMemory,
      cpuCount: this.deviceInfo.cpuCount,
      isLowEndDevice: this.deviceInfo.isLowEndDevice,
    });
  }

  // Get device information
  private getDeviceInfo(): DeviceInfo {
    const { width, height } = Dimensions.get('window');
    
    return {
      platform: Platform.OS,
      version: Platform.Version,
      screenWidth: width,
      screenHeight: height,
      totalMemory: this.estimateDeviceMemory(),
      cpuCount: this.estimateCpuCount(),
      isLowEndDevice: this.detectLowEndDevice(),
    };
  }

  // Estimate device memory (simplified)
  private estimateDeviceMemory(): number {
    // This is a simplified estimation
    // In a real app, you might use a native module to get actual memory info
    const { width, height } = Dimensions.get('window');
    const screenSize = width * height;
    
    if (screenSize < 800 * 600) return 1024; // 1GB
    if (screenSize < 1200 * 800) return 2048; // 2GB
    if (screenSize < 1600 * 1200) return 4096; // 4GB
    return 6144; // 6GB+
  }

  // Estimate CPU count (simplified)
  private estimateCpuCount(): number {
    // Simplified estimation based on platform and screen size
    if (Platform.OS === 'ios') {
      return this.deviceInfo?.screenWidth > 400 ? 6 : 4;
    }
    return 4; // Default for Android
  }

  // Detect low-end device
  private detectLowEndDevice(): boolean {
    const { width, height } = Dimensions.get('window');
    const screenSize = width * height;
    
    // Consider devices with small screens or old versions as low-end
    if (Platform.OS === 'android' && Platform.Version < 28) return true;
    if (screenSize < 800 * 600) return true;
    
    return false;
  }

  // Initialize all optimizations
  async initialize(): Promise<void> {
    if (this.isInitialized) {
      console.warn('[Optimization] Already initialized');
      return;
    }

    const startTime = Date.now();

    try {
      console.log('[Optimization] Initializing performance optimizations...');
      
      // Initialize performance monitoring
      await this.initializePerformanceMonitoring();
      
      // Initialize storage optimizations
      await this.initializeStorageOptimizations();
      
      // Initialize network optimizations
      await this.initializeNetworkOptimizations();
      
      // Initialize location optimizations
      await this.initializeLocationOptimizations();
      
      // Initialize animation optimizations
      await this.initializeAnimationOptimizations();
      
      // Initialize memory management
      await this.initializeMemoryManagement();
      
      // Initialize app state monitoring
      await this.initializeAppStateMonitoring();
      
      // Setup cleanup handlers
      this.setupCleanupHandlers();
      
      this.isInitialized = true;
      
      const initTime = Date.now() - startTime;
      console.log(`[Optimization] Initialization completed in ${initTime}ms`);
      
      if (FEATURE_FLAGS.PERFORMANCE_MONITORING) {
        performanceMonitor.trackMetric('optimization_init_time', initTime, {
          deviceInfo: this.deviceInfo,
          strategy: 'optimized',
        });
      }
      
    } catch (error) {
      console.error('[Optimization] Initialization failed:', error);
      throw error;
    }
  }

  // Initialize performance monitoring
  private async initializePerformanceMonitoring(): Promise<void> {
    if (!FEATURE_FLAGS.PERFORMANCE_MONITORING) return;
    
    performanceMonitor.setEnabled(true);
    performanceMonitor.trackAppStartup();
    
    console.log('[Optimization] Performance monitoring initialized');
  }

  // Initialize storage optimizations
  private async initializeStorageOptimizations(): Promise<void> {
    try {
      // Get current storage stats
      const stats = await optimizedStorage.getStorageStats();
      
      // Clear cache if it's too large
      if (stats.estimatedSize > PERFORMANCE_CONFIG.STORAGE.MAX_CACHE_SIZE) {
        console.log('[Optimization] Storage cache too large, clearing...');
        await optimizedStorage.clear();
      }
      
      console.log('[Optimization] Storage optimizations initialized');
    } catch (error) {
      console.error('[Optimization] Storage initialization error:', error);
    }
  }

  // Initialize network optimizations
  private async initializeNetworkOptimizations(): Promise<void> {
    if (!FEATURE_FLAGS.NETWORK_CACHING) return;
    
    try {
      // Clear old network cache
      await networkService.clearCache();
      
      // Preload critical data if needed
      const criticalEndpoints = [
        { url: '/api/rider/profile', cacheKey: 'rider_profile', ttl: 30 * 60 * 1000 },
        { url: '/api/app/config', cacheKey: 'app_config', ttl: 60 * 60 * 1000 },
      ];
      
      await networkService.preloadData(criticalEndpoints);
      
      console.log('[Optimization] Network optimizations initialized');
    } catch (error) {
      console.error('[Optimization] Network initialization error:', error);
    }
  }

  // Initialize location optimizations
  private async initializeLocationOptimizations(): Promise<void> {
    if (!FEATURE_FLAGS.LOCATION_OPTIMIZATION) return;
    
    try {
      // Request permissions early
      await optimizedLocationService.requestPermissions();
      
      console.log('[Optimization] Location optimizations initialized');
    } catch (error) {
      console.error('[Optimization] Location initialization error:', error);
    }
  }

  // Initialize animation optimizations
  private async initializeAnimationOptimizations(): Promise<void> {
    // Set up animation cleanup
    optimizedAnimations.cleanup();
    
    console.log('[Optimization] Animation optimizations initialized');
  }

  // Initialize memory management
  private async initializeMemoryManagement(): Promise<void> {
    if (!FEATURE_FLAGS.MEMORY_MANAGEMENT) return;
    
    // Set up memory monitoring
    const checkMemory = () => {
      // In a real app, you'd use native modules to check actual memory usage
      if (FEATURE_FLAGS.PERFORMANCE_MONITORING) {
        performanceMonitor.trackMetric('memory_check', Date.now());
      }
    };
    
    // Check memory every 5 minutes
    setInterval(checkMemory, 5 * 60 * 1000);
    
    console.log('[Optimization] Memory management initialized');
  }

  // Initialize app state monitoring
  private async initializeAppStateMonitoring(): Promise<void> {
    AppState.addEventListener('change', this.handleAppStateChange);
    console.log('[Optimization] App state monitoring initialized');
  }

  // Handle app state changes
  private handleAppStateChange = (nextAppState: string) => {
    if (FEATURE_FLAGS.PERFORMANCE_MONITORING) {
      performanceMonitor.trackMetric('app_state_change', 1, { 
        state: nextAppState,
        timestamp: Date.now(),
      });
    }

    // Optimize based on app state
    switch (nextAppState) {
      case 'background':
        this.optimizeForBackground();
        break;
      case 'active':
        this.optimizeForForeground();
        break;
      case 'inactive':
        this.optimizeForInactive();
        break;
    }
  };

  // Optimize for background state
  private optimizeForBackground(): void {
    // Stop unnecessary animations
    optimizedAnimations.stopAllAnimations();
    
    // Reduce location accuracy
    // This would be handled by the location service automatically
    
    console.log('[Optimization] Optimized for background state');
  }

  // Optimize for foreground state
  private optimizeForForeground(): void {
    // Resume normal operations
    console.log('[Optimization] Optimized for foreground state');
  }

  // Optimize for inactive state
  private optimizeForInactive(): void {
    // Pause non-critical operations
    console.log('[Optimization] Optimized for inactive state');
  }

  // Setup cleanup handlers
  private setupCleanupHandlers(): void {
    // Cleanup on app termination
    const cleanup = () => {
      this.cleanup();
    };

    // Add event listeners for cleanup
    if (Platform.OS === 'web') {
      window.addEventListener('beforeunload', cleanup);
    }
  }

  // Get optimization status
  getOptimizationStatus(): {
    isInitialized: boolean;
    deviceInfo: DeviceInfo;
    enabledFeatures: string[];
    performanceMetrics?: any;
  } {
    const enabledFeatures = Object.entries(FEATURE_FLAGS)
      .filter(([_, enabled]) => enabled)
      .map(([feature, _]) => feature);

    return {
      isInitialized: this.isInitialized,
      deviceInfo: this.deviceInfo,
      enabledFeatures,
      performanceMetrics: FEATURE_FLAGS.PERFORMANCE_MONITORING 
        ? performanceMonitor.getPerformanceSummary()
        : undefined,
    };
  }

  // Force cleanup
  cleanup(): void {
    try {
      // Cleanup all services
      optimizedAnimations.cleanup();
      optimizedLocationService.cleanup();
      
      // Remove event listeners
      AppState.removeEventListener('change', this.handleAppStateChange);
      
      console.log('[Optimization] Cleanup completed');
    } catch (error) {
      console.error('[Optimization] Cleanup error:', error);
    }
  }

  // Get device-specific recommendations
  getOptimizationRecommendations(): string[] {
    const recommendations: string[] = [];
    
    if (this.deviceInfo.isLowEndDevice) {
      recommendations.push('Consider reducing animation complexity');
      recommendations.push('Enable aggressive memory management');
      recommendations.push('Reduce image quality settings');
    }
    
    if (this.deviceInfo.totalMemory && this.deviceInfo.totalMemory < 2048) {
      recommendations.push('Enable memory-efficient list rendering');
      recommendations.push('Reduce cache sizes');
    }
    
    if (Platform.OS === 'android' && Platform.Version < 28) {
      recommendations.push('Enable legacy compatibility mode');
      recommendations.push('Reduce background processing');
    }
    
    return recommendations;
  }
}

// Create singleton instance
export const optimizationInitializer = new OptimizationInitializer();

export default optimizationInitializer;
