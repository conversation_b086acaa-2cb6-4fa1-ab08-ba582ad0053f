# Backend API Requirements - FoodWay Rider App

## 📋 Complete List of Components Requiring Backend API Integration

### 🔐 **AUTHENTICATION & USER MANAGEMENT**

#### 1. **AuthContext** (`src/context/AuthContext.tsx`)
**Status**: ✅ IMPLEMENTED
- `POST /auth/register` - User registration
- `POST /auth/login` - User authentication
- `GET /auth/me` - Get current user profile
- `POST /auth/refresh` - Refresh access token
- `POST /auth/logout` - Logout user
- `PUT /auth/profile` - Update user profile
- `PUT /auth/change-password` - Change password
- `DELETE /auth/account` - Delete account

#### 2. **LoginScreen** (`src/screens/auth/LoginScreen.tsx`)
**Status**: ✅ IMPLEMENTED
- Uses AuthContext login method

#### 3. **RegistrationScreen** (`src/screens/auth/RegistrationScreen.tsx`)
**Status**: ✅ IMPLEMENTED
- Uses AuthContext register method

#### 4. **OTPVerificationScreen** (`src/screens/auth/OTPVerificationScreen.tsx`)
**Status**: ⚠️ NEEDS API INTEGRATION
**Required APIs**:
- `POST /auth/verify-otp` - Verify OTP code
- `POST /auth/resend-otp` - Resend OTP

#### 5. **ForgotPasswordScreen** (if exists)
**Status**: ⚠️ NEEDS IMPLEMENTATION
**Required APIs**:
- `POST /auth/forgot-password` - Send reset email
- `POST /auth/reset-password` - Reset password with token

---

### 📄 **DOCUMENT VERIFICATION**

#### 6. **DocumentStatusScreen** (`src/screens/profile/DocumentStatusScreen.tsx`)
**Status**: ✅ IMPLEMENTED
- `GET /documents/verification-status` - Get verification status
- `GET /documents/status` - Get all documents status
- `POST /documents/upload` - Upload document
- `PUT /documents/resubmit/{type}` - Resubmit rejected document

#### 7. **VehicleInfoScreen** (`src/screens/profile/VehicleInfoScreen.tsx`)
**Status**: ⚠️ NEEDS API INTEGRATION
**Required APIs**:
- `GET /vehicles/info` - Get vehicle information
- `PUT /vehicles/info` - Update vehicle information
- `POST /documents/upload` - Upload vehicle documents
- `GET /documents/requirements/{vehicleType}` - Get document requirements

---

### 👤 **PROFILE MANAGEMENT**

#### 8. **ProfileScreen** (`src/screens/profile/ProfileScreen.tsx`)
**Status**: ⚠️ NEEDS API INTEGRATION
**Required APIs**:
- `GET /profile` - Get rider profile
- `PUT /profile` - Update profile information
- `POST /profile/photo` - Upload profile photo
- `GET /profile/stats` - Get profile statistics

#### 9. **ProfileMainScreen** (`src/screens/profile/ProfileMainScreen.tsx`)
**Status**: ⚠️ NEEDS API INTEGRATION
**Required APIs**:
- `GET /profile/summary` - Get profile summary
- `GET /profile/referral-stats` - Get referral statistics
- `POST /profile/referral/share` - Share referral code

#### 10. **PaymentMethodsScreen** (`src/screens/profile/PaymentMethodsScreen.tsx`)
**Status**: ⚠️ NEEDS API INTEGRATION
**Required APIs**:
- `GET /payment-methods` - Get payment methods
- `POST /payment-methods` - Add payment method
- `PUT /payment-methods/{id}` - Update payment method
- `DELETE /payment-methods/{id}` - Delete payment method
- `PUT /payment-methods/{id}/primary` - Set primary payment method

---

### 🏠 **DASHBOARD & ORDERS**

#### 11. **DashboardScreen** (`src/screens/dashboard/DashboardScreen.tsx`)
**Status**: ⚠️ NEEDS API INTEGRATION
**Required APIs**:
- `GET /dashboard/stats` - Get dashboard statistics
- `GET /dashboard/earnings/today` - Get today's earnings
- `GET /dashboard/orders/active` - Get active orders
- `POST /rider/status` - Update online/offline status
- `GET /orders/available` - Get available orders
- `GET /notifications/unread` - Get unread notifications

#### 12. **OrderRequestsScreen** (`src/screens/dashboard/OrderRequestsScreen.tsx`)
**Status**: ⚠️ NEEDS API INTEGRATION
**Required APIs**:
- `GET /orders/requests` - Get order requests
- `POST /orders/{id}/accept` - Accept order
- `POST /orders/{id}/decline` - Decline order
- `GET /orders/heatmap` - Get demand heatmap

#### 13. **OrderDetailsScreen** (`src/screens/orders/OrderDetailsScreen.tsx`)
**Status**: ⚠️ NEEDS API INTEGRATION
**Required APIs**:
- `GET /orders/{id}` - Get order details
- `POST /orders/{id}/start` - Start order
- `POST /orders/{id}/pickup` - Mark as picked up
- `POST /orders/{id}/deliver` - Mark as delivered
- `POST /orders/{id}/cancel` - Cancel order
- `GET /orders/{id}/route` - Get navigation route

---

### 💰 **EARNINGS & WALLET**

#### 14. **EarningsScreen** (`src/screens/wallet/EarningsScreen.tsx`)
**Status**: ⚠️ NEEDS API INTEGRATION
**Required APIs**:
- `GET /earnings/summary` - Get earnings summary
- `GET /earnings/daily` - Get daily earnings
- `GET /earnings/weekly` - Get weekly earnings
- `GET /earnings/monthly` - Get monthly earnings
- `GET /earnings/history` - Get earnings history

#### 15. **WalletScreen** (`src/screens/wallet/WalletScreen.tsx`)
**Status**: ⚠️ NEEDS API INTEGRATION
**Required APIs**:
- `GET /wallet/balance` - Get wallet balance
- `GET /wallet/transactions` - Get transaction history
- `POST /wallet/withdraw` - Request withdrawal
- `GET /wallet/withdraw/history` - Get withdrawal history

#### 16. **PayoutsScreen** (`src/screens/wallet/PayoutsScreen.tsx`)
**Status**: ⚠️ NEEDS API INTEGRATION
**Required APIs**:
- `GET /payouts/schedule` - Get payout schedule
- `GET /payouts/history` - Get payout history
- `GET /payouts/pending` - Get pending payouts
- `POST /payouts/request` - Request immediate payout

---

### 📊 **ANALYTICS & PERFORMANCE**

#### 17. **OrderHistoryAnalyticsScreen** (`src/screens/analytics/OrderHistoryAnalyticsScreen.tsx`)
**Status**: ⚠️ NEEDS API INTEGRATION
**Required APIs**:
- `GET /analytics/orders/history` - Get order history
- `GET /analytics/performance/summary` - Get performance summary
- `GET /analytics/ratings/trends` - Get rating trends
- `GET /analytics/delivery-time/average` - Get delivery time analytics

#### 18. **PerformanceAnalyticsScreen** (`src/screens/delivery/PerformanceAnalyticsScreen.tsx`)
**Status**: ⚠️ NEEDS API INTEGRATION
**Required APIs**:
- `GET /analytics/performance/overview` - Performance overview
- `GET /analytics/performance/trends` - Performance trends
- `GET /analytics/performance/time-analysis` - Time analysis
- `GET /analytics/performance/location` - Location performance

---

### 🚨 **SAFETY & SUPPORT**

#### 19. **EmergencySOSScreen** (`src/screens/safety/EmergencySOSScreen.tsx`)
**Status**: ⚠️ NEEDS API INTEGRATION
**Required APIs**:
- `POST /emergency/sos` - Trigger emergency SOS
- `POST /emergency/location` - Share live location
- `GET /emergency/contacts` - Get emergency contacts
- `POST /emergency/cancel` - Cancel emergency

#### 20. **IncidentReportScreen** (`src/screens/safety/IncidentReportScreen.tsx`)
**Status**: ⚠️ NEEDS API INTEGRATION
**Required APIs**:
- `POST /incidents/report` - Report incident
- `GET /incidents/history` - Get incident history
- `POST /incidents/{id}/update` - Update incident status

#### 21. **HelpCenterMainScreen** (`src/screens/support/HelpCenterMainScreen.tsx`)
**Status**: ⚠️ NEEDS API INTEGRATION
**Required APIs**:
- `GET /support/faq` - Get FAQ items
- `POST /support/ticket` - Create support ticket
- `GET /support/tickets` - Get support tickets
- `POST /support/chat` - Send chat message

---

### 🛠 **ADVANCED FEATURES**

#### 22. **HeatmapScreen** (`src/screens/tools/HeatmapScreen.tsx`)
**Status**: ⚠️ NEEDS API INTEGRATION
**Required APIs**:
- `GET /heatmap/demand` - Get demand heatmap
- `GET /heatmap/surge` - Get surge pricing areas
- `GET /heatmap/earnings-forecast` - Get earnings forecast

#### 23. **TrainingScreen** (`src/screens/tools/TrainingScreen.tsx`)
**Status**: ⚠️ NEEDS API INTEGRATION
**Required APIs**:
- `GET /training/courses` - Get training courses
- `GET /training/progress` - Get training progress
- `POST /training/complete` - Mark course complete
- `GET /training/certificates` - Get certificates

#### 24. **NotificationsScreen** (`src/screens/notifications/NotificationsScreen.tsx`)
**Status**: ⚠️ NEEDS API INTEGRATION
**Required APIs**:
- `GET /notifications` - Get notifications
- `PUT /notifications/{id}/read` - Mark as read
- `DELETE /notifications/{id}` - Delete notification
- `PUT /notifications/read-all` - Mark all as read

---

### 🔧 **SETTINGS & PREFERENCES**

#### 25. **SettingsMainScreen** (`src/screens/settings/SettingsMainScreen.tsx`)
**Status**: ⚠️ NEEDS API INTEGRATION
**Required APIs**:
- `GET /settings/preferences` - Get user preferences
- `PUT /settings/preferences` - Update preferences
- `PUT /settings/notifications` - Update notification settings
- `PUT /settings/privacy` - Update privacy settings

---

### 📍 **LOCATION & TRACKING**

#### 26. **LocationHistoryScreen** (`src/screens/analytics/LocationHistoryScreen.tsx`)
**Status**: ⚠️ NEEDS API INTEGRATION
**Required APIs**:
- `GET /location/history` - Get location history
- `POST /location/update` - Update current location
- `GET /location/tracking/{orderId}` - Get order tracking

---

### 🎯 **CONTEXT PROVIDERS**

#### 27. **OrderContext** (`src/context/OrderContext.tsx`)
**Status**: ⚠️ NEEDS API INTEGRATION
**Required APIs**:
- `GET /orders/active` - Get active orders
- `POST /orders/{id}/update-status` - Update order status
- `GET /orders/history` - Get order history

#### 28. **EarningsContext** (`src/context/EarningsContext.tsx`)
**Status**: ⚠️ NEEDS API INTEGRATION
**Required APIs**:
- `GET /earnings/current` - Get current earnings
- `GET /earnings/goals` - Get earnings goals
- `PUT /earnings/goals` - Update earnings goals

#### 29. **LocationContext** (`src/context/LocationContext.tsx`)
**Status**: ⚠️ NEEDS API INTEGRATION
**Required APIs**:
- `POST /location/track` - Send location updates
- `GET /location/zones` - Get delivery zones

---

### 📱 **COMPONENTS**

#### 30. **OrderRequestModal** (`src/components/orders/OrderRequestModal.tsx`)
**Status**: ⚠️ NEEDS API INTEGRATION
**Required APIs**:
- `POST /orders/{id}/accept` - Accept order
- `POST /orders/{id}/decline` - Decline order

#### 31. **DeliveryProofModal** (`src/components/orders/DeliveryProofModal.tsx`)
**Status**: ⚠️ NEEDS API INTEGRATION
**Required APIs**:
- `POST /orders/{id}/proof` - Upload delivery proof
- `POST /orders/{id}/complete` - Complete delivery

---

## 🎯 **PRIORITY IMPLEMENTATION ORDER**

### **Phase 1: Core Functionality** (CRITICAL)
1. ✅ AuthContext (DONE)
2. ✅ DocumentStatusScreen (DONE)
3. ⚠️ DashboardScreen - Online/offline status
4. ⚠️ OrderContext - Order management
5. ⚠️ OrderDetailsScreen - Order workflow

### **Phase 2: Essential Features** (HIGH)
6. ⚠️ EarningsScreen - Earnings tracking
7. ⚠️ WalletScreen - Wallet management
8. ⚠️ ProfileScreen - Profile management
9. ⚠️ VehicleInfoScreen - Vehicle info
10. ⚠️ PaymentMethodsScreen - Payment methods

### **Phase 3: Analytics & History** (MEDIUM)
11. ⚠️ OrderHistoryAnalyticsScreen
12. ⚠️ PerformanceAnalyticsScreen
13. ⚠️ LocationHistoryScreen
14. ⚠️ PayoutsScreen

### **Phase 4: Safety & Support** (MEDIUM)
15. ⚠️ EmergencySOSScreen
16. ⚠️ IncidentReportScreen
17. ⚠️ HelpCenterMainScreen
18. ⚠️ NotificationsScreen

### **Phase 5: Advanced Features** (LOW)
19. ⚠️ HeatmapScreen
20. ⚠️ TrainingScreen
21. ⚠️ SettingsMainScreen
22. ⚠️ OTPVerificationScreen

---

## 📝 **IMPLEMENTATION NOTES**

- ✅ **DONE**: Already implemented with API integration
- ⚠️ **NEEDS API**: Currently using mock data, needs backend integration
- ❌ **NOT IMPLEMENTED**: Component doesn't exist yet

Each component will need corresponding API service methods and proper error handling following the patterns established in the AuthService and DocumentService.
