import React, { useRef, useEffect } from 'react';
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  Animated,
  Dimensions,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import { LinearGradient } from 'expo-linear-gradient';
import { Card } from '../../components/ui';

const { width } = Dimensions.get('window');

interface RegistrationInfoScreenProps {
  navigation?: any;
  onBack?: () => void;
}

const REGISTRATION_STEPS = [
  {
    id: 1,
    title: 'Personal Information',
    icon: 'person-outline',
    description: 'Basic details about yourself',
    requirements: [
      'First name (required)',
      'Last name (optional)',
      'Email address with @ symbol',
      'Phone number',
      'CNIC number (13 digits)',
      'Strong password (8+ chars, uppercase, lowercase, number, special char)',
    ],
  },
  {
    id: 2,
    title: 'Vehicle Information',
    icon: 'car-outline',
    description: 'Details about your delivery vehicle',
    requirements: [
      'Vehicle type (bicycle, motorcycle, scooter, car)',
      'Vehicle make and model',
      'Manufacturing year',
      'Vehicle color',
      'License plate number',
    ],
  },
  {
    id: 3,
    title: 'Document Upload',
    icon: 'document-outline',
    description: 'Required documents for verification',
    requirements: [
      'CNIC front and back photos',
      'Driving license',
      'Vehicle registration documents',
      'Profile photo',
    ],
  },
  {
    id: 4,
    title: 'Payment Setup',
    icon: 'card-outline',
    description: 'Bank account for earnings',
    requirements: [
      'Account type (Bank/JazzCash/EasyPaisa)',
      'Account number',
      'Account holder name',
      'Bank name and IBAN (for bank accounts)',
    ],
  },
];

const BENEFITS = [
  {
    icon: 'cash-outline',
    title: 'Flexible Earnings',
    description: 'Earn money on your own schedule',
  },
  {
    icon: 'time-outline',
    title: 'Work Anytime',
    description: 'Choose your working hours',
  },
  {
    icon: 'location-outline',
    title: 'Local Deliveries',
    description: 'Deliver in your neighborhood',
  },
  {
    icon: 'shield-checkmark-outline',
    title: 'Secure Platform',
    description: 'Safe and reliable payment system',
  },
];

const RegistrationInfoScreen: React.FC<RegistrationInfoScreenProps> = ({ navigation, onBack }) => {
  // Animation refs
  const fadeAnim = useRef(new Animated.Value(0)).current;
  const slideAnim = useRef(new Animated.Value(50)).current;
  const scaleAnim = useRef(new Animated.Value(0.9)).current;

  useEffect(() => {
    // Start animations
    Animated.parallel([
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 800,
        useNativeDriver: true,
      }),
      Animated.timing(slideAnim, {
        toValue: 0,
        duration: 600,
        useNativeDriver: true,
      }),
      Animated.spring(scaleAnim, {
        toValue: 1,
        tension: 50,
        friction: 7,
        useNativeDriver: true,
      }),
    ]).start();
  }, []);

  const handleGetStarted = () => {
    navigation?.navigate('Registration');
  };

  const handleSignIn = () => {
    navigation?.navigate('Login');
  };

  const handleBack = () => {
    navigation?.goBack() || onBack?.();
  };

  const renderHeader = () => (
    <Animated.View
      style={{
        opacity: fadeAnim,
        transform: [{ translateY: slideAnim }],
      }}
    >
      <View style={{
        alignItems: 'center',
        marginBottom: 32,
      }}>
        {/* Logo */}
        <Animated.View
          style={{
            width: 80,
            height: 80,
            backgroundColor: 'white',
            borderRadius: 40,
            justifyContent: 'center',
            alignItems: 'center',
            marginBottom: 16,
            shadowColor: '#000',
            shadowOffset: { width: 0, height: 8 },
            shadowOpacity: 0.15,
            shadowRadius: 16,
            elevation: 8,
            transform: [{ scale: scaleAnim }],
          }}
        >
          <Ionicons name="bicycle" size={40} color="#F97316" />
        </Animated.View>

        <Text style={{
          fontSize: 28,
          fontWeight: 'bold',
          color: 'white',
          textAlign: 'center',
          marginBottom: 8,
        }}>
          Join FoodWay Riders
        </Text>
        
        <Text style={{
          fontSize: 16,
          color: 'rgba(255, 255, 255, 0.9)',
          textAlign: 'center',
          lineHeight: 24,
        }}>
          Start earning by delivering food in your area
        </Text>
      </View>
    </Animated.View>
  );

  const renderBenefits = () => (
    <Animated.View
      style={{
        opacity: fadeAnim,
        transform: [{ translateY: slideAnim }],
      }}
    >
      <Card variant="elevated" padding="lg" className="mx-4 mb-6">
        <Text style={{
          fontSize: 20,
          fontWeight: 'bold',
          color: '#1F2937',
          marginBottom: 16,
          textAlign: 'center',
        }}>
          Why Join FoodWay?
        </Text>

        <View style={{
          flexDirection: 'row',
          flexWrap: 'wrap',
          justifyContent: 'space-between',
        }}>
          {BENEFITS.map((benefit, index) => (
            <View
              key={benefit.icon}
              style={{
                width: '48%',
                alignItems: 'center',
                marginBottom: 20,
              }}
            >
              <View style={{
                width: 50,
                height: 50,
                backgroundColor: '#FFF7ED',
                borderRadius: 25,
                justifyContent: 'center',
                alignItems: 'center',
                marginBottom: 8,
              }}>
                <Ionicons
                  name={benefit.icon as keyof typeof Ionicons.glyphMap}
                  size={24}
                  color="#F97316"
                />
              </View>
              <Text style={{
                fontSize: 14,
                fontWeight: '600',
                color: '#1F2937',
                textAlign: 'center',
                marginBottom: 4,
              }}>
                {benefit.title}
              </Text>
              <Text style={{
                fontSize: 12,
                color: '#6B7280',
                textAlign: 'center',
                lineHeight: 16,
              }}>
                {benefit.description}
              </Text>
            </View>
          ))}
        </View>
      </Card>
    </Animated.View>
  );

  const renderRegistrationSteps = () => (
    <Animated.View
      style={{
        opacity: fadeAnim,
        transform: [{ translateY: slideAnim }],
      }}
    >
      <Card variant="elevated" padding="lg" className="mx-4 mb-6">
        <Text style={{
          fontSize: 20,
          fontWeight: 'bold',
          color: '#1F2937',
          marginBottom: 16,
          textAlign: 'center',
        }}>
          Registration Process
        </Text>

        {REGISTRATION_STEPS.map((step, index) => (
          <View
            key={step.id}
            style={{
              flexDirection: 'row',
              marginBottom: index === REGISTRATION_STEPS.length - 1 ? 0 : 20,
            }}
          >
            {/* Step Number */}
            <View style={{
              width: 40,
              height: 40,
              backgroundColor: '#F97316',
              borderRadius: 20,
              justifyContent: 'center',
              alignItems: 'center',
              marginRight: 16,
            }}>
              <Text style={{
                color: 'white',
                fontSize: 16,
                fontWeight: 'bold',
              }}>
                {step.id}
              </Text>
            </View>

            {/* Step Content */}
            <View style={{ flex: 1 }}>
              <View style={{
                flexDirection: 'row',
                alignItems: 'center',
                marginBottom: 8,
              }}>
                <Ionicons
                  name={step.icon as keyof typeof Ionicons.glyphMap}
                  size={20}
                  color="#F97316"
                  style={{ marginRight: 8 }}
                />
                <Text style={{
                  fontSize: 16,
                  fontWeight: '600',
                  color: '#1F2937',
                  flex: 1,
                }}>
                  {step.title}
                </Text>
              </View>

              <Text style={{
                fontSize: 14,
                color: '#6B7280',
                marginBottom: 8,
                lineHeight: 20,
              }}>
                {step.description}
              </Text>

              {/* Requirements */}
              {step.requirements.map((requirement, reqIndex) => (
                <View
                  key={reqIndex}
                  style={{
                    flexDirection: 'row',
                    alignItems: 'center',
                    marginBottom: 4,
                  }}
                >
                  <View style={{
                    width: 4,
                    height: 4,
                    backgroundColor: '#F97316',
                    borderRadius: 2,
                    marginRight: 8,
                  }} />
                  <Text style={{
                    fontSize: 13,
                    color: '#4B5563',
                    flex: 1,
                  }}>
                    {requirement}
                  </Text>
                </View>
              ))}
            </View>
          </View>
        ))}
      </Card>
    </Animated.View>
  );

  const renderActionButtons = () => (
    <Animated.View
      style={{
        opacity: fadeAnim,
        transform: [{ translateY: slideAnim }],
      }}
    >
      <View style={{
        paddingHorizontal: 16,
        paddingBottom: 16,
      }}>
        {/* Get Started Button */}
        <TouchableOpacity
          onPress={handleGetStarted}
          style={{
            backgroundColor: 'white',
            paddingVertical: 16,
            paddingHorizontal: 24,
            borderRadius: 12,
            flexDirection: 'row',
            alignItems: 'center',
            justifyContent: 'center',
            marginBottom: 12,
            shadowColor: '#000',
            shadowOffset: { width: 0, height: 4 },
            shadowOpacity: 0.1,
            shadowRadius: 8,
            elevation: 4,
          }}
        >
          <Text style={{
            color: '#F97316',
            fontSize: 16,
            fontWeight: 'bold',
            marginRight: 8,
          }}>
            Get Started
          </Text>
          <Ionicons name="arrow-forward" size={20} color="#F97316" />
        </TouchableOpacity>

        {/* Sign In Link */}
        <View style={{
          flexDirection: 'row',
          alignItems: 'center',
          justifyContent: 'center',
        }}>
          <Text style={{
            color: 'rgba(255, 255, 255, 0.9)',
            fontSize: 14,
          }}>
            Already have an account?{' '}
          </Text>
          <TouchableOpacity onPress={handleSignIn}>
            <Text style={{
              color: 'white',
              fontSize: 14,
              fontWeight: '600',
              textDecorationLine: 'underline',
            }}>
              Sign In
            </Text>
          </TouchableOpacity>
        </View>
      </View>
    </Animated.View>
  );

  return (
    <SafeAreaView style={{ flex: 1 }}>
      <LinearGradient
        colors={['#EF4444', '#DC2626', '#B91C1C']}
        style={{ flex: 1 }}
      >
        {/* Header */}
        <View style={{
          flexDirection: 'row',
          alignItems: 'center',
          padding: 16,
          paddingTop: 8,
        }}>
          <TouchableOpacity onPress={handleBack} style={{ marginRight: 16 }}>
            <Ionicons name="arrow-back" size={24} color="white" />
          </TouchableOpacity>
          <Text style={{
            fontSize: 18,
            fontWeight: '600',
            color: 'white',
          }}>
            Registration Guide
          </Text>
        </View>

        <ScrollView
          style={{ flex: 1 }}
          contentContainerStyle={{
            paddingHorizontal: 0,
            paddingBottom: 32,
          }}
          showsVerticalScrollIndicator={false}
        >
          {renderHeader()}
          {renderBenefits()}
          {renderRegistrationSteps()}
          {renderActionButtons()}
        </ScrollView>
      </LinearGradient>
    </SafeAreaView>
  );
};

export default RegistrationInfoScreen;
