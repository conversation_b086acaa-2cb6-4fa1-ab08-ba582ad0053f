import {
  DeliveryOrder,
  OrderHistoryFilter,
  PerformanceMetrics,
  DailyPerformance,
  WeeklyPerformance,
  MonthlyPerformance,
  RatingTrend,
  DeliveryTimeAnalysis,
  LocationAnalysis,
  OrderStatus,
  OrderType,
} from '../types/deliveryHistory';

// Mock data for comprehensive testing
export const mockDeliveryOrders: DeliveryOrder[] = [
  {
    id: '1',
    orderNumber: 'ORD-2024-001',
    status: OrderStatus.DELIVERED,
    type: OrderType.FOOD,
    customer: {
      id: 'c1',
      name: '<PERSON>',
      phone: '+92 300 1234567',
      rating: 4.8,
    },
    restaurant: {
      id: 'r1',
      name: 'KFC DHA',
      address: 'DHA Phase 2, Lahore',
      phone: '+92 42 1234567',
      category: 'Fast Food',
    },
    pickupLocation: {
      latitude: 31.4697,
      longitude: 74.4142,
      address: 'DHA Phase 2, Lahore',
      area: 'DHA',
      city: 'Lahore',
    },
    deliveryLocation: {
      latitude: 31.4504,
      longitude: 74.3588,
      address: 'Gulberg III, Lahore',
      area: 'Gulberg',
      city: 'Lahore',
    },
    items: [
      { id: '1', name: 'Zinger Burger', quantity: 2, price: 800, specialInstructions: 'Extra spicy' },
      { id: '2', name: 'Fries', quantity: 1, price: 300 },
      { id: '3', name: 'Pepsi', quantity: 2, price: 150 },
    ],
    totalAmount: 1250,
    deliveryFee: 150,
    tip: 100,
    timeline: {
      orderPlaced: '2024-01-20T12:00:00Z',
      orderAccepted: '2024-01-20T12:02:00Z',
      arrivedAtRestaurant: '2024-01-20T12:15:00Z',
      orderPickedUp: '2024-01-20T12:25:00Z',
      deliveryStarted: '2024-01-20T12:26:00Z',
      orderDelivered: '2024-01-20T12:45:00Z',
    },
    metrics: {
      totalDistance: 5.2,
      deliveryTime: 19,
      pickupTime: 10,
      waitingTime: 8,
      averageSpeed: 16.4,
    },
    rating: {
      overall: 5,
      delivery: 5,
      communication: 4,
      timeliness: 5,
      comment: 'Great service, very fast delivery!',
      ratedAt: '2024-01-20T13:00:00Z',
    },
    paymentMethod: 'Cash',
    proofOfDelivery: 'https://example.com/proof1.jpg',
    specialInstructions: 'Ring the bell twice, apartment 3A',
    createdAt: '2024-01-20T12:00:00Z',
    updatedAt: '2024-01-20T12:45:00Z',
  },
  {
    id: '2',
    orderNumber: 'ORD-2024-002',
    status: OrderStatus.DELIVERED,
    type: OrderType.GROCERY,
    customer: {
      id: 'c2',
      name: 'Fatima Ali',
      phone: '+92 301 2345678',
      rating: 4.5,
    },
    restaurant: {
      id: 'r2',
      name: 'Metro Cash & Carry',
      address: 'Johar Town, Lahore',
      phone: '+92 42 2345678',
      category: 'Grocery',
    },
    pickupLocation: {
      latitude: 31.4504,
      longitude: 74.3588,
      address: 'Johar Town, Lahore',
      area: 'Johar Town',
      city: 'Lahore',
    },
    deliveryLocation: {
      latitude: 31.4697,
      longitude: 74.4142,
      address: 'Model Town, Lahore',
      area: 'Model Town',
      city: 'Lahore',
    },
    items: [
      { id: '4', name: 'Rice 5kg', quantity: 1, price: 1200 },
      { id: '5', name: 'Cooking Oil 1L', quantity: 2, price: 450 },
      { id: '6', name: 'Sugar 1kg', quantity: 1, price: 120 },
    ],
    totalAmount: 2220,
    deliveryFee: 200,
    tip: 50,
    timeline: {
      orderPlaced: '2024-01-19T14:30:00Z',
      orderAccepted: '2024-01-19T14:32:00Z',
      arrivedAtRestaurant: '2024-01-19T14:50:00Z',
      orderPickedUp: '2024-01-19T15:05:00Z',
      deliveryStarted: '2024-01-19T15:06:00Z',
      orderDelivered: '2024-01-19T15:35:00Z',
    },
    metrics: {
      totalDistance: 7.8,
      deliveryTime: 29,
      pickupTime: 15,
      waitingTime: 12,
      averageSpeed: 16.1,
    },
    rating: {
      overall: 4,
      delivery: 4,
      communication: 4,
      timeliness: 4,
      comment: 'Good service, items were fresh',
      ratedAt: '2024-01-19T16:00:00Z',
    },
    paymentMethod: 'Card',
    proofOfDelivery: 'https://example.com/proof2.jpg',
    specialInstructions: 'Handle with care, fragile items',
    createdAt: '2024-01-19T14:30:00Z',
    updatedAt: '2024-01-19T15:35:00Z',
  },
  {
    id: '3',
    orderNumber: 'ORD-2024-003',
    status: OrderStatus.CANCELLED,
    type: OrderType.FOOD,
    customer: {
      id: 'c3',
      name: 'Hassan Sheikh',
      phone: '+92 302 3456789',
      rating: 3.8,
    },
    restaurant: {
      id: 'r3',
      name: 'Pizza Hut Gulberg',
      address: 'Gulberg III, Lahore',
      phone: '+92 42 3456789',
      category: 'Fast Food',
    },
    pickupLocation: {
      latitude: 31.4504,
      longitude: 74.3588,
      address: 'Gulberg III, Lahore',
      area: 'Gulberg',
      city: 'Lahore',
    },
    deliveryLocation: {
      latitude: 31.5204,
      longitude: 74.3587,
      address: 'Cantt, Lahore',
      area: 'Cantt',
      city: 'Lahore',
    },
    items: [
      { id: '7', name: 'Large Pizza', quantity: 1, price: 1800 },
      { id: '8', name: 'Garlic Bread', quantity: 1, price: 400 },
    ],
    totalAmount: 2200,
    deliveryFee: 180,
    tip: 0,
    timeline: {
      orderPlaced: '2024-01-18T19:15:00Z',
      orderAccepted: '2024-01-18T19:17:00Z',
      arrivedAtRestaurant: '2024-01-18T19:35:00Z',
      orderCancelled: '2024-01-18T19:45:00Z',
    },
    metrics: {
      totalDistance: 0,
      deliveryTime: 0,
      pickupTime: 0,
      waitingTime: 10,
      averageSpeed: 0,
    },
    paymentMethod: 'Cash',
    specialInstructions: 'Customer cancelled due to long wait time',
    createdAt: '2024-01-18T19:15:00Z',
    updatedAt: '2024-01-18T19:45:00Z',
  },
  {
    id: '4',
    orderNumber: 'ORD-2024-004',
    status: OrderStatus.DELIVERED,
    type: OrderType.PHARMACY,
    customer: {
      id: 'c4',
      name: 'Aisha Malik',
      phone: '+92 303 4567890',
      rating: 4.9,
    },
    restaurant: {
      id: 'r4',
      name: 'Dawakhana Pharmacy',
      address: 'DHA Phase 5, Lahore',
      phone: '+92 42 4567890',
      category: 'Pharmacy',
    },
    pickupLocation: {
      latitude: 31.4697,
      longitude: 74.4142,
      address: 'DHA Phase 5, Lahore',
      area: 'DHA',
      city: 'Lahore',
    },
    deliveryLocation: {
      latitude: 31.4504,
      longitude: 74.3588,
      address: 'EME Society, Lahore',
      area: 'EME',
      city: 'Lahore',
    },
    items: [
      { id: '9', name: 'Panadol 20 tablets', quantity: 2, price: 80 },
      { id: '10', name: 'Vitamin D3', quantity: 1, price: 450 },
      { id: '11', name: 'Blood Pressure Monitor', quantity: 1, price: 3500 },
    ],
    totalAmount: 4110,
    deliveryFee: 120,
    tip: 200,
    timeline: {
      orderPlaced: '2024-01-17T10:20:00Z',
      orderAccepted: '2024-01-17T10:22:00Z',
      arrivedAtRestaurant: '2024-01-17T10:35:00Z',
      orderPickedUp: '2024-01-17T10:40:00Z',
      deliveryStarted: '2024-01-17T10:41:00Z',
      orderDelivered: '2024-01-17T11:05:00Z',
    },
    metrics: {
      totalDistance: 4.2,
      deliveryTime: 24,
      pickupTime: 5,
      waitingTime: 3,
      averageSpeed: 10.5,
    },
    rating: {
      overall: 5,
      delivery: 5,
      communication: 5,
      timeliness: 5,
      comment: 'Excellent service, very careful with medicines',
      ratedAt: '2024-01-17T11:30:00Z',
    },
    paymentMethod: 'Card',
    proofOfDelivery: 'https://example.com/proof4.jpg',
    specialInstructions: 'Handle medicines carefully, urgent delivery',
    createdAt: '2024-01-17T10:20:00Z',
    updatedAt: '2024-01-17T11:05:00Z',
  },
  {
    id: '5',
    orderNumber: 'ORD-2024-005',
    status: OrderStatus.DELIVERED,
    type: OrderType.FOOD,
    customer: {
      id: 'c5',
      name: 'Omar Farooq',
      phone: '+92 304 5678901',
      rating: 4.2,
    },
    restaurant: {
      id: 'r5',
      name: 'Subway MM Alam',
      address: 'MM Alam Road, Lahore',
      phone: '+92 42 5678901',
      category: 'Fast Food',
    },
    pickupLocation: {
      latitude: 31.5204,
      longitude: 74.3587,
      address: 'MM Alam Road, Lahore',
      area: 'Gulberg',
      city: 'Lahore',
    },
    deliveryLocation: {
      latitude: 31.4697,
      longitude: 74.4142,
      address: 'LUMS University, Lahore',
      area: 'DHA',
      city: 'Lahore',
    },
    items: [
      { id: '12', name: 'Chicken Teriyaki Sub', quantity: 1, price: 650 },
      { id: '13', name: 'Cookies', quantity: 2, price: 200 },
      { id: '14', name: 'Soft Drink', quantity: 1, price: 150 },
    ],
    totalAmount: 1000,
    deliveryFee: 130,
    tip: 80,
    timeline: {
      orderPlaced: '2024-01-16T13:45:00Z',
      orderAccepted: '2024-01-16T13:47:00Z',
      arrivedAtRestaurant: '2024-01-16T14:00:00Z',
      orderPickedUp: '2024-01-16T14:08:00Z',
      deliveryStarted: '2024-01-16T14:09:00Z',
      orderDelivered: '2024-01-16T14:30:00Z',
    },
    metrics: {
      totalDistance: 6.5,
      deliveryTime: 21,
      pickupTime: 8,
      waitingTime: 5,
      averageSpeed: 18.6,
    },
    rating: {
      overall: 4,
      delivery: 4,
      communication: 4,
      timeliness: 4,
      comment: 'Good delivery, food was still warm',
      ratedAt: '2024-01-16T15:00:00Z',
    },
    paymentMethod: 'Cash',
    proofOfDelivery: 'https://example.com/proof5.jpg',
    specialInstructions: 'University gate 2, call on arrival',
    createdAt: '2024-01-16T13:45:00Z',
    updatedAt: '2024-01-16T14:30:00Z',
  },
];

export const mockPerformanceMetrics: PerformanceMetrics = {
  averageDeliveryTime: 22.5,
  averagePickupTime: 8.3,
  averageWaitingTime: 5.2,
  averageDistance: 4.8,
  averageSpeed: 18.5,
  totalOrders: 156,
  completedOrders: 142,
  cancelledOrders: 8,
  rejectedOrders: 6,
  acceptanceRate: 96.2,
  completionRate: 91.0,
  cancellationRate: 5.1,
  onTimeDeliveryRate: 88.7,
  averageRating: 4.6,
  totalRatings: 128,
  ratingDistribution: {
    1: 2,
    2: 3,
    3: 8,
    4: 35,
    5: 80,
  },
  totalEarnings: 137250,
  averageEarningsPerOrder: 880,
  totalTips: 25260,
  averageTipPerOrder: 162,
  period: 'month',
  startDate: '2024-01-01',
  endDate: '2024-01-31',
};

// Service functions
export class DeliveryHistoryService {
  static async fetchOrderHistory(filter?: OrderHistoryFilter): Promise<DeliveryOrder[]> {
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    let orders = [...mockDeliveryOrders];
    
    if (filter) {
      orders = this.applyFilter(orders, filter);
    }
    
    return orders;
  }

  static async fetchOrderDetails(orderId: string): Promise<DeliveryOrder | null> {
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 500));
    
    const order = mockDeliveryOrders.find(o => o.id === orderId);
    return order || null;
  }

  static async fetchPerformanceMetrics(period: string): Promise<PerformanceMetrics> {
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    return {
      ...mockPerformanceMetrics,
      period: period as any,
    };
  }

  static async fetchDailyPerformance(startDate: string, endDate: string): Promise<DailyPerformance[]> {
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 800));
    
    // Generate mock daily performance data
    const dailyData: DailyPerformance[] = [];
    const start = new Date(startDate);
    const end = new Date(endDate);
    
    for (let d = new Date(start); d <= end; d.setDate(d.getDate() + 1)) {
      const dateStr = d.toISOString().split('T')[0];
      dailyData.push({
        date: dateStr,
        ordersCompleted: Math.floor(Math.random() * 15) + 5,
        totalEarnings: Math.floor(Math.random() * 3000) + 1500,
        averageRating: Math.round((Math.random() * 1.5 + 3.5) * 10) / 10,
        averageDeliveryTime: Math.floor(Math.random() * 15) + 15,
        totalDistance: Math.round((Math.random() * 40 + 20) * 10) / 10,
        hoursWorked: Math.round((Math.random() * 4 + 4) * 10) / 10,
      });
    }
    
    return dailyData;
  }

  static applyFilter(orders: DeliveryOrder[], filter: OrderHistoryFilter): DeliveryOrder[] {
    let filtered = [...orders];

    // Apply status filter
    if (filter.status && filter.status.length > 0) {
      filtered = filtered.filter(order => filter.status!.includes(order.status));
    }

    // Apply type filter
    if (filter.type && filter.type.length > 0) {
      filtered = filtered.filter(order => filter.type!.includes(order.type));
    }

    // Apply date range filter
    if (filter.startDate) {
      filtered = filtered.filter(order => new Date(order.createdAt) >= new Date(filter.startDate!));
    }

    if (filter.endDate) {
      filtered = filtered.filter(order => new Date(order.createdAt) <= new Date(filter.endDate!));
    }

    // Apply area filter
    if (filter.area) {
      filtered = filtered.filter(order => 
        order.pickupLocation.area.toLowerCase().includes(filter.area!.toLowerCase()) ||
        order.deliveryLocation.area.toLowerCase().includes(filter.area!.toLowerCase())
      );
    }

    // Apply amount filters
    if (filter.minAmount) {
      filtered = filtered.filter(order => order.totalAmount >= filter.minAmount!);
    }

    if (filter.maxAmount) {
      filtered = filtered.filter(order => order.totalAmount <= filter.maxAmount!);
    }

    // Apply rating filter
    if (filter.rating) {
      filtered = filtered.filter(order => order.rating && order.rating.overall >= filter.rating!);
    }

    // Apply sorting
    filtered.sort((a, b) => {
      let aValue: any, bValue: any;
      
      switch (filter.sortBy) {
        case 'date':
          aValue = new Date(a.createdAt);
          bValue = new Date(b.createdAt);
          break;
        case 'amount':
          aValue = a.totalAmount;
          bValue = b.totalAmount;
          break;
        case 'rating':
          aValue = a.rating?.overall || 0;
          bValue = b.rating?.overall || 0;
          break;
        case 'distance':
          aValue = a.metrics?.totalDistance || 0;
          bValue = b.metrics?.totalDistance || 0;
          break;
        default:
          aValue = new Date(a.createdAt);
          bValue = new Date(b.createdAt);
      }

      if (filter.sortOrder === 'asc') {
        return aValue > bValue ? 1 : -1;
      } else {
        return aValue < bValue ? 1 : -1;
      }
    });

    return filtered;
  }

  static calculateAnalytics(orders: DeliveryOrder[]): {
    ratingTrends: RatingTrend[];
    deliveryTimeAnalysis: DeliveryTimeAnalysis[];
    locationAnalysis: LocationAnalysis[];
  } {
    // Calculate rating trends
    const ratingTrends: RatingTrend[] = [];
    const dateGroups: { [key: string]: { ratings: number[], count: number } } = {};
    
    orders.forEach(order => {
      if (order.rating) {
        const date = order.createdAt.split('T')[0];
        if (!dateGroups[date]) {
          dateGroups[date] = { ratings: [], count: 0 };
        }
        dateGroups[date].ratings.push(order.rating.overall);
        dateGroups[date].count++;
      }
    });

    Object.entries(dateGroups).forEach(([date, data]) => {
      const avgRating = data.ratings.reduce((sum, r) => sum + r, 0) / data.ratings.length;
      ratingTrends.push({
        date,
        rating: Math.round(avgRating * 10) / 10,
        ordersCount: data.count,
      });
    });

    // Calculate delivery time analysis by time slots
    const timeSlots = [
      { slot: '09:00-12:00', start: 9, end: 12 },
      { slot: '12:00-15:00', start: 12, end: 15 },
      { slot: '15:00-18:00', start: 15, end: 18 },
      { slot: '18:00-21:00', start: 18, end: 21 },
      { slot: '21:00-24:00', start: 21, end: 24 },
    ];

    const deliveryTimeAnalysis: DeliveryTimeAnalysis[] = timeSlots.map(({ slot, start, end }) => {
      const slotOrders = orders.filter(order => {
        const hour = new Date(order.createdAt).getHours();
        return hour >= start && hour < end && order.metrics;
      });

      const avgTime = slotOrders.length > 0 
        ? slotOrders.reduce((sum, order) => sum + order.metrics!.deliveryTime, 0) / slotOrders.length
        : 0;

      const efficiency = avgTime > 0 ? Math.max(0, 100 - (avgTime - 15) * 2) : 0;

      return {
        timeSlot: slot,
        averageTime: Math.round(avgTime * 10) / 10,
        ordersCount: slotOrders.length,
        efficiency: Math.round(efficiency * 10) / 10,
      };
    });

    // Calculate location analysis
    const locationGroups: { [key: string]: DeliveryOrder[] } = {};
    
    orders.forEach(order => {
      const area = order.deliveryLocation.area;
      if (!locationGroups[area]) {
        locationGroups[area] = [];
      }
      locationGroups[area].push(order);
    });

    const locationAnalysis: LocationAnalysis[] = Object.entries(locationGroups).map(([area, areaOrders]) => {
      const completedOrders = areaOrders.filter(o => o.status === OrderStatus.DELIVERED);
      
      const avgDeliveryTime = completedOrders.length > 0
        ? completedOrders.reduce((sum, order) => sum + (order.metrics?.deliveryTime || 0), 0) / completedOrders.length
        : 0;

      const avgDistance = completedOrders.length > 0
        ? completedOrders.reduce((sum, order) => sum + (order.metrics?.totalDistance || 0), 0) / completedOrders.length
        : 0;

      const avgEarnings = completedOrders.length > 0
        ? completedOrders.reduce((sum, order) => sum + order.totalAmount + order.deliveryFee + order.tip, 0) / completedOrders.length
        : 0;

      const avgRating = completedOrders.filter(o => o.rating).length > 0
        ? completedOrders.filter(o => o.rating).reduce((sum, order) => sum + order.rating!.overall, 0) / completedOrders.filter(o => o.rating).length
        : 0;

      return {
        area,
        city: areaOrders[0].deliveryLocation.city,
        ordersCount: areaOrders.length,
        averageDeliveryTime: Math.round(avgDeliveryTime * 10) / 10,
        averageDistance: Math.round(avgDistance * 10) / 10,
        averageEarnings: Math.round(avgEarnings),
        averageRating: Math.round(avgRating * 10) / 10,
      };
    });

    return {
      ratingTrends: ratingTrends.sort((a, b) => new Date(a.date).getTime() - new Date(b.date).getTime()),
      deliveryTimeAnalysis,
      locationAnalysis: locationAnalysis.sort((a, b) => b.averageEarnings - a.averageEarnings),
    };
  }
}
