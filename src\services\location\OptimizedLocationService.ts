import * as Location from 'expo-location';
import { AppState, AppStateStatus } from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { LocationData, LocationPermissionStatus } from '../../types/location';
import { LOCATION_CONFIG } from '../../utils/constants';
import { throttle, debounce } from '../../utils/performanceUtils';

interface LocationConfig {
  accuracy: Location.Accuracy;
  timeInterval: number;
  distanceInterval: number;
  enableHighAccuracy: boolean;
  maximumAge: number;
  timeout: number;
}

class OptimizedLocationService {
  private watchId: Location.LocationSubscription | null = null;
  private currentLocation: LocationData | null = null;
  private isTracking: boolean = false;
  private appState: AppStateStatus = 'active';
  private locationHistory: LocationData[] = [];
  private maxHistorySize: number = 100;
  private lastLocationUpdate: number = 0;
  private minUpdateInterval: number = 3000; // 3 seconds minimum between updates
  private distanceCache = new Map<string, number>();

  // Adaptive location configuration based on app state and battery
  private configs: Record<string, LocationConfig> = {
    active: {
      accuracy: Location.Accuracy.High,
      timeInterval: 5000,
      distanceInterval: 10,
      enableHighAccuracy: true,
      maximumAge: 10000,
      timeout: 15000,
    },
    background: {
      accuracy: Location.Accuracy.Balanced,
      timeInterval: 30000,
      distanceInterval: 50,
      enableHighAccuracy: false,
      maximumAge: 60000,
      timeout: 30000,
    },
    inactive: {
      accuracy: Location.Accuracy.Low,
      timeInterval: 60000,
      distanceInterval: 100,
      enableHighAccuracy: false,
      maximumAge: 120000,
      timeout: 45000,
    },
  };

  constructor() {
    this.setupAppStateListener();
    this.loadLocationHistory();
  }

  private setupAppStateListener(): void {
    AppState.addEventListener('change', this.handleAppStateChange);
  }

  private handleAppStateChange = (nextAppState: AppStateStatus): void => {
    this.appState = nextAppState;
    
    if (this.isTracking) {
      // Restart tracking with appropriate config for new app state
      this.stopLocationTracking();
      setTimeout(() => this.startOptimizedTracking(), 1000);
    }
  };

  private async loadLocationHistory(): Promise<void> {
    try {
      const history = await AsyncStorage.getItem('location_history');
      if (history) {
        this.locationHistory = JSON.parse(history);
      }
    } catch (error) {
      console.error('Error loading location history:', error);
    }
  }

  private async saveLocationHistory(): Promise<void> {
    try {
      await AsyncStorage.setItem('location_history', JSON.stringify(this.locationHistory));
    } catch (error) {
      console.error('Error saving location history:', error);
    }
  }

  async requestPermissions(): Promise<LocationPermissionStatus> {
    try {
      const { status } = await Location.requestForegroundPermissionsAsync();
      
      if (status === 'granted') {
        // Also request background permissions for delivery tracking
        const backgroundStatus = await Location.requestBackgroundPermissionsAsync();
        return backgroundStatus.status === Location.PermissionStatus.GRANTED 
          ? Location.PermissionStatus.GRANTED 
          : Location.PermissionStatus.DENIED;
      }
      
      return status as LocationPermissionStatus;
    } catch (error) {
      console.error('Error requesting location permissions:', error);
      return Location.PermissionStatus.DENIED;
    }
  }

  async getCurrentLocation(forceUpdate: boolean = false): Promise<LocationData | null> {
    try {
      // Return cached location if recent enough and not forcing update
      if (!forceUpdate && this.currentLocation) {
        const timeSinceLastUpdate = Date.now() - this.currentLocation.timestamp;
        if (timeSinceLastUpdate < this.minUpdateInterval) {
          return this.currentLocation;
        }
      }

      const permissionStatus = await this.requestPermissions();
      if (permissionStatus === 'denied') {
        throw new Error('Location permission denied');
      }

      const config = this.configs[this.appState] || this.configs.active;
      const location = await Location.getCurrentPositionAsync({
        accuracy: config.accuracy,
        maximumAge: config.maximumAge,
        timeout: config.timeout,
      });

      const locationData: LocationData = {
        coords: {
          latitude: location.coords.latitude,
          longitude: location.coords.longitude,
        },
        accuracy: location.coords.accuracy || 0,
        altitude: location.coords.altitude || 0,
        heading: location.coords.heading || 0,
        speed: location.coords.speed || 0,
        timestamp: location.timestamp,
      };

      this.updateLocation(locationData);
      return locationData;
    } catch (error) {
      console.error('Error getting current location:', error);
      return this.currentLocation; // Return last known location
    }
  }

  private updateLocation = throttle((locationData: LocationData) => {
    this.currentLocation = locationData;
    this.addToHistory(locationData);
    this.lastLocationUpdate = Date.now();
  }, 2000);

  private addToHistory(location: LocationData): void {
    this.locationHistory.push(location);
    
    // Keep only recent locations
    if (this.locationHistory.length > this.maxHistorySize) {
      this.locationHistory = this.locationHistory.slice(-this.maxHistorySize);
    }
    
    // Save to storage periodically
    this.debouncedSaveHistory();
  }

  private debouncedSaveHistory = debounce(() => {
    this.saveLocationHistory();
  }, 10000);

  async startOptimizedTracking(
    onLocationUpdate?: (location: LocationData) => void,
    onError?: (error: Error) => void
  ): Promise<void> {
    try {
      if (this.isTracking) {
        return; // Already tracking
      }

      const permissionStatus = await this.requestPermissions();
      if (permissionStatus === 'denied') {
        throw new Error('Location permission denied');
      }

      const config = this.configs[this.appState] || this.configs.active;
      
      this.watchId = await Location.watchPositionAsync(
        {
          accuracy: config.accuracy,
          timeInterval: config.timeInterval,
          distanceInterval: config.distanceInterval,
        },
        (location) => {
          const locationData: LocationData = {
            coords: {
              latitude: location.coords.latitude,
              longitude: location.coords.longitude,
            },
            accuracy: location.coords.accuracy || 0,
            altitude: location.coords.altitude || 0,
            heading: location.coords.heading || 0,
            speed: location.coords.speed || 0,
            timestamp: location.timestamp,
          };

          this.updateLocation(locationData);
          onLocationUpdate?.(locationData);
        }
      );

      this.isTracking = true;
    } catch (error: any) {
      console.error('Error starting optimized location tracking:', error);
      onError?.(new Error(`Failed to start location tracking: ${error.message}`));
    }
  }

  async stopLocationTracking(): Promise<void> {
    try {
      if (this.watchId) {
        this.watchId.remove();
        this.watchId = null;
      }
      this.isTracking = false;
    } catch (error) {
      console.error('Error stopping location tracking:', error);
    }
  }

  getCachedLocation(): LocationData | null {
    return this.currentLocation;
  }

  getLocationHistory(): LocationData[] {
    return [...this.locationHistory];
  }

  // Optimized distance calculation with caching
  calculateDistance(
    lat1: number,
    lon1: number,
    lat2: number,
    lon2: number
  ): number {
    const cacheKey = `${lat1.toFixed(6)},${lon1.toFixed(6)},${lat2.toFixed(6)},${lon2.toFixed(6)}`;
    
    if (this.distanceCache.has(cacheKey)) {
      return this.distanceCache.get(cacheKey)!;
    }

    const R = 6371; // Radius of the Earth in kilometers
    const dLat = this.deg2rad(lat2 - lat1);
    const dLon = this.deg2rad(lon2 - lon1);
    const a =
      Math.sin(dLat / 2) * Math.sin(dLat / 2) +
      Math.cos(this.deg2rad(lat1)) *
        Math.cos(this.deg2rad(lat2)) *
        Math.sin(dLon / 2) *
        Math.sin(dLon / 2);
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
    const distance = R * c; // Distance in kilometers

    // Cache the result
    this.distanceCache.set(cacheKey, distance);
    
    // Limit cache size
    if (this.distanceCache.size > 1000) {
      const firstKey = this.distanceCache.keys().next().value;
      this.distanceCache.delete(firstKey);
    }

    return distance;
  }

  private deg2rad(deg: number): number {
    return deg * (Math.PI / 180);
  }

  // Get estimated time of arrival
  getETA(destinationLat: number, destinationLon: number, averageSpeed: number = 30): number {
    if (!this.currentLocation) return 0;
    
    const distance = this.calculateDistance(
      this.currentLocation.coords.latitude,
      this.currentLocation.coords.longitude,
      destinationLat,
      destinationLon
    );
    
    return (distance / averageSpeed) * 60; // Return in minutes
  }

  // Check if rider is moving
  isMoving(threshold: number = 0.5): boolean {
    if (this.locationHistory.length < 2) return false;
    
    const recent = this.locationHistory.slice(-2);
    const distance = this.calculateDistance(
      recent[0].coords.latitude,
      recent[0].coords.longitude,
      recent[1].coords.latitude,
      recent[1].coords.longitude
    );
    
    return distance > threshold / 1000; // Convert meters to kilometers
  }

  // Get current speed in km/h
  getCurrentSpeed(): number {
    return this.currentLocation?.speed ? this.currentLocation.speed * 3.6 : 0;
  }

  // Clean up resources
  cleanup(): void {
    this.stopLocationTracking();
    AppState.removeEventListener('change', this.handleAppStateChange);
    this.saveLocationHistory();
  }
}

export const optimizedLocationService = new OptimizedLocationService();
