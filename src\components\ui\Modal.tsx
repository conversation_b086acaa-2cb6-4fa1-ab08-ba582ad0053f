import React from 'react';
import {
  Modal as RNModal,
  View,
  Text,
  TouchableOpacity,
  TouchableWithoutFeedback,
  ViewStyle,
  ModalProps as RNModalProps,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';

interface ModalProps extends Omit<RNModalProps, 'children'> {
  isVisible: boolean;
  onClose: () => void;
  title?: string;
  children: React.ReactNode;
  variant?: 'default' | 'fullscreen' | 'bottom-sheet' | 'center';
  showCloseButton?: boolean;
  closeOnBackdrop?: boolean;
  size?: 'sm' | 'md' | 'lg' | 'xl';
}

const Modal: React.FC<ModalProps> = ({
  isVisible,
  onClose,
  title,
  children,
  variant = 'default',
  showCloseButton = true,
  closeOnBackdrop = true,
  size = 'md',
  ...props
}) => {
  const getBackdropStyle = (): ViewStyle => {
    return {
      flex: 1,
      backgroundColor: 'rgba(0, 0, 0, 0.5)',
      justifyContent: variant === 'bottom-sheet' ? 'flex-end' : 'center',
      alignItems: 'center',
      padding: variant === 'fullscreen' ? 0 : 20,
    };
  };

  const getModalStyle = (): ViewStyle => {
    const baseStyle: ViewStyle = {
      backgroundColor: 'white',
      shadowColor: '#000',
      shadowOffset: { width: 0, height: 10 },
      shadowOpacity: 0.25,
      shadowRadius: 20,
      elevation: 20,
    };

    switch (variant) {
      case 'fullscreen':
        return {
          ...baseStyle,
          flex: 1,
          width: '100%',
          borderRadius: 0,
        };

      case 'bottom-sheet':
        return {
          ...baseStyle,
          width: '100%',
          borderTopLeftRadius: 24,
          borderTopRightRadius: 24,
          maxHeight: '80%',
        };

      case 'center':
        return {
          ...baseStyle,
          borderRadius: 24,
          width: getSizeWidth(),
          maxHeight: '80%',
        };

      default:
        return {
          ...baseStyle,
          borderRadius: 20,
          width: getSizeWidth(),
          maxHeight: '80%',
        };
    }
  };

  const getSizeWidth = (): string => {
    switch (size) {
      case 'sm': return '80%';
      case 'md': return '90%';
      case 'lg': return '95%';
      case 'xl': return '100%';
      default: return '90%';
    }
  };

  const handleBackdropPress = () => {
    if (closeOnBackdrop) {
      onClose();
    }
  };

  return (
    <RNModal
      visible={isVisible}
      transparent
      animationType="fade"
      onRequestClose={onClose}
      {...props}
    >
      <TouchableWithoutFeedback onPress={handleBackdropPress}>
        <View style={getBackdropStyle()}>
          <TouchableWithoutFeedback>
            <View style={getModalStyle()}>
              {/* Header */}
              {(title || showCloseButton) && (
                <View style={{
                  flexDirection: 'row',
                  alignItems: 'center',
                  justifyContent: 'space-between',
                  padding: 20,
                  borderBottomWidth: 1,
                  borderBottomColor: '#f1f5f9',
                }}>
                  {title && (
                    <Text style={{
                      fontSize: 20,
                      fontWeight: 'bold',
                      color: '#111827',
                      flex: 1,
                    }}>
                      {title}
                    </Text>
                  )}
                  
                  {showCloseButton && (
                    <TouchableOpacity
                      onPress={onClose}
                      style={{
                        width: 40,
                        height: 40,
                        borderRadius: 20,
                        backgroundColor: '#f8fafc',
                        alignItems: 'center',
                        justifyContent: 'center',
                        borderWidth: 2,
                        borderColor: '#e2e8f0',
                      }}
                    >
                      <Ionicons name="close" size={20} color="#6b7280" />
                    </TouchableOpacity>
                  )}
                </View>
              )}

              {/* Content */}
              <View style={{ flex: 1 }}>
                {children}
              </View>
            </View>
          </TouchableWithoutFeedback>
        </View>
      </TouchableWithoutFeedback>
    </RNModal>
  );
};

export default Modal;
