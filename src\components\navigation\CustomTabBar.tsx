import React from 'react';
import { View, TouchableOpacity, Text, Dimensions } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { BottomTabBarProps } from '@react-navigation/bottom-tabs';

const { width: screenWidth } = Dimensions.get('window');

interface TabConfig {
  name: string;
  label: string;
  activeIcon: keyof typeof Ionicons.glyphMap;
  inactiveIcon: keyof typeof Ionicons.glyphMap;
}

const tabConfigs: TabConfig[] = [
  {
    name: 'Dashboard',
    label: 'Home',
    activeIcon: 'home',
    inactiveIcon: 'home-outline',
  },
  {
    name: 'Orders',
    label: 'Orders',
    activeIcon: 'receipt',
    inactiveIcon: 'receipt-outline',
  },
  {
    name: 'Earnings',
    label: 'Wallet',
    activeIcon: 'wallet',
    inactiveIcon: 'wallet-outline',
  },
  {
    name: 'Profile',
    label: 'Profile',
    activeIcon: 'person',
    inactiveIcon: 'person-outline',
  },
  {
    name: 'Safety',
    label: 'Safety',
    activeIcon: 'shield-checkmark',
    inactiveIcon: 'shield-outline',
  },
];

const CustomTabBar: React.FC<BottomTabBarProps> = ({ state, descriptors, navigation }) => {
  const visibleRoutes = state.routes.filter(route => {
    const { options } = descriptors[route.key];
    return options.tabBarButton !== null;
  });

  const tabWidth = screenWidth / visibleRoutes.length;

  return (
    <View style={{
      flexDirection: 'row',
      backgroundColor: '#ffffff',
      borderTopLeftRadius: 24,
      borderTopRightRadius: 24,
      paddingBottom: 12,
      paddingTop: 16,
      paddingHorizontal: 8,
      height: 85,
      shadowColor: '#dc2626',
      shadowOffset: { width: 0, height: -8 },
      shadowOpacity: 0.15,
      shadowRadius: 20,
      elevation: 16,
      justifyContent: 'space-around',
      alignItems: 'center',
      borderWidth: 1,
      borderColor: 'rgba(220, 38, 38, 0.05)',
      borderBottomWidth: 0,
    }}>
      {visibleRoutes.map((route, index) => {
        const { options } = descriptors[route.key];
        const isFocused = state.index === state.routes.findIndex(r => r.key === route.key);
        
        const tabConfig = tabConfigs.find(config => config.name === route.name);
        if (!tabConfig) return null;

        const onPress = () => {
          const event = navigation.emit({
            type: 'tabPress',
            target: route.key,
            canPreventDefault: true,
          });

          if (!isFocused && !event.defaultPrevented) {
            navigation.navigate(route.name, route.params);
          }
        };

        const onLongPress = () => {
          navigation.emit({
            type: 'tabLongPress',
            target: route.key,
          });
        };

        return (
          <TouchableOpacity
            key={route.key}
            accessibilityRole="button"
            accessibilityState={isFocused ? { selected: true } : {}}
            accessibilityLabel={options.tabBarAccessibilityLabel}
            testID={options.tabBarTestID}
            onPress={onPress}
            onLongPress={onLongPress}
            style={{
              flex: 1,
              alignItems: 'center',
              justifyContent: 'center',
              paddingVertical: 8,
              paddingHorizontal: 4,
              minWidth: tabWidth * 0.85,
              maxWidth: tabWidth * 1.15,
            }}
          >
            {/* Enhanced Icon Container */}
            <View style={{
              alignItems: 'center',
              justifyContent: 'center',
              width: isFocused ? 48 : 40,
              height: isFocused ? 48 : 40,
              borderRadius: isFocused ? 24 : 20,
              backgroundColor: isFocused ? '#dc2626' : 'transparent',
              marginBottom: 4,
              transform: [{ scale: isFocused ? 1.1 : 1 }],
              shadowColor: isFocused ? '#dc2626' : 'transparent',
              shadowOffset: { width: 0, height: isFocused ? 6 : 0 },
              shadowOpacity: isFocused ? 0.3 : 0,
              shadowRadius: isFocused ? 12 : 0,
              elevation: isFocused ? 8 : 0,
              borderWidth: isFocused ? 0 : 2,
              borderColor: isFocused ? 'transparent' : '#f1f5f9',
            }}>
              <Ionicons
                name={isFocused ? tabConfig.activeIcon : tabConfig.inactiveIcon}
                size={isFocused ? 26 : 24}
                color={isFocused ? '#ffffff' : '#9ca3af'}
              />
            </View>

            {/* Enhanced Label */}
            <Text style={{
              fontSize: isFocused ? 12 : 11,
              fontWeight: isFocused ? 'bold' : '600',
              color: isFocused ? '#dc2626' : '#9ca3af',
              textAlign: 'center',
              marginTop: 2,
              letterSpacing: 0.3,
            }}>
              {tabConfig.label}
            </Text>

            {/* Active Indicator Dot */}
            {isFocused && (
              <View style={{
                width: 5,
                height: 5,
                borderRadius: 2.5,
                backgroundColor: '#ef4444',
                marginTop: 3,
                opacity: 0.8,
              }} />
            )}
          </TouchableOpacity>
        );
      })}
    </View>
  );
};

export default CustomTabBar;
