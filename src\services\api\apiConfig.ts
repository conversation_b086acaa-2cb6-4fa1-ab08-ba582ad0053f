import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse, AxiosError } from 'axios';
import AsyncStorage from '@react-native-async-storage/async-storage';

// API Configuration
export const API_CONFIG = {
  BASE_URL: __DEV__ ? 'http://localhost:3000/api' : 'https://your-production-api.com/api',
  TIMEOUT: 30000,
  RETRY_ATTEMPTS: 3,
  RETRY_DELAY: 1000,
};

// Storage keys
export const STORAGE_KEYS = {
  ACCESS_TOKEN: 'access_token',
  REFRESH_TOKEN: 'refresh_token',
  USER_DATA: 'user_data',
  DEVICE_ID: 'device_id',
};

// API Response types
export interface ApiResponse<T = any> {
  success: boolean;
  data: T;
  message: string;
  errors?: string[];
  meta?: {
    pagination?: {
      page: number;
      limit: number;
      total: number;
      totalPages: number;
    };
  };
}

export interface ApiError {
  success: false;
  message: string;
  errors?: string[];
  statusCode?: number;
}

// Create axios instance
const createApiInstance = (): AxiosInstance => {
  const instance = axios.create({
    baseURL: API_CONFIG.BASE_URL,
    timeout: API_CONFIG.TIMEOUT,
    headers: {
      'Content-Type': 'application/json',
      'Accept': 'application/json',
    },
  });

  // Request interceptor
  instance.interceptors.request.use(
    async (config: AxiosRequestConfig) => {
      try {
        // Add auth token if available
        const token = await AsyncStorage.getItem(STORAGE_KEYS.ACCESS_TOKEN);
        if (token && config.headers) {
          config.headers.Authorization = `Bearer ${token}`;
        }

        // Add device ID for tracking
        const deviceId = await AsyncStorage.getItem(STORAGE_KEYS.DEVICE_ID);
        if (deviceId && config.headers) {
          config.headers['X-Device-ID'] = deviceId;
        }

        // Log request in development
        if (__DEV__) {
          console.log('🚀 API Request:', {
            method: config.method?.toUpperCase(),
            url: config.url,
            data: config.data,
            headers: config.headers,
          });
        }

        return config;
      } catch (error) {
        console.error('Request interceptor error:', error);
        return config;
      }
    },
    (error: AxiosError) => {
      console.error('Request interceptor error:', error);
      return Promise.reject(error);
    }
  );

  // Response interceptor
  instance.interceptors.response.use(
    (response: AxiosResponse<ApiResponse>) => {
      // Log response in development
      if (__DEV__) {
        console.log('✅ API Response:', {
          status: response.status,
          url: response.config.url,
          data: response.data,
        });
      }

      return response;
    },
    async (error: AxiosError<ApiError>) => {
      // Log error in development
      if (__DEV__) {
        console.error('❌ API Error:', {
          status: error.response?.status,
          url: error.config?.url,
          message: error.response?.data?.message || error.message,
          errors: error.response?.data?.errors,
        });
      }

      // Handle token expiration
      if (error.response?.status === 401) {
        try {
          const refreshToken = await AsyncStorage.getItem(STORAGE_KEYS.REFRESH_TOKEN);
          if (refreshToken) {
            // Try to refresh token
            const refreshResponse = await axios.post(`${API_CONFIG.BASE_URL}/auth/refresh`, {
              refreshToken,
            });

            if (refreshResponse.data.success) {
              const { accessToken, refreshToken: newRefreshToken } = refreshResponse.data.data;
              
              // Store new tokens
              await AsyncStorage.setItem(STORAGE_KEYS.ACCESS_TOKEN, accessToken);
              if (newRefreshToken) {
                await AsyncStorage.setItem(STORAGE_KEYS.REFRESH_TOKEN, newRefreshToken);
              }

              // Retry original request
              if (error.config && error.config.headers) {
                error.config.headers.Authorization = `Bearer ${accessToken}`;
                return instance.request(error.config);
              }
            }
          }
        } catch (refreshError) {
          console.error('Token refresh failed:', refreshError);
          // Clear tokens and redirect to login
          await AsyncStorage.multiRemove([
            STORAGE_KEYS.ACCESS_TOKEN,
            STORAGE_KEYS.REFRESH_TOKEN,
            STORAGE_KEYS.USER_DATA,
          ]);
        }
      }

      // Handle network errors
      if (!error.response) {
        return Promise.reject({
          success: false,
          message: 'Network error. Please check your internet connection.',
          statusCode: 0,
        } as ApiError);
      }

      // Return formatted error
      return Promise.reject({
        success: false,
        message: error.response.data?.message || 'An unexpected error occurred',
        errors: error.response.data?.errors,
        statusCode: error.response.status,
      } as ApiError);
    }
  );

  return instance;
};

// Create and export API instance
export const apiClient = createApiInstance();

// Utility functions
export const setAuthToken = async (token: string) => {
  await AsyncStorage.setItem(STORAGE_KEYS.ACCESS_TOKEN, token);
};

export const clearAuthToken = async () => {
  await AsyncStorage.removeItem(STORAGE_KEYS.ACCESS_TOKEN);
};

export const getAuthToken = async (): Promise<string | null> => {
  return await AsyncStorage.getItem(STORAGE_KEYS.ACCESS_TOKEN);
};

// Retry mechanism for failed requests
export const retryRequest = async <T>(
  requestFn: () => Promise<T>,
  maxAttempts: number = API_CONFIG.RETRY_ATTEMPTS,
  delay: number = API_CONFIG.RETRY_DELAY
): Promise<T> => {
  let lastError: any;

  for (let attempt = 1; attempt <= maxAttempts; attempt++) {
    try {
      return await requestFn();
    } catch (error) {
      lastError = error;
      
      if (attempt === maxAttempts) {
        break;
      }

      // Wait before retrying
      await new Promise(resolve => setTimeout(resolve, delay * attempt));
    }
  }

  throw lastError;
};

// File upload helper
export const createFormData = (data: Record<string, any>): FormData => {
  const formData = new FormData();

  Object.keys(data).forEach(key => {
    const value = data[key];
    
    if (value !== null && value !== undefined) {
      if (typeof value === 'object' && value.uri) {
        // Handle file upload
        formData.append(key, {
          uri: value.uri,
          type: value.type || 'image/jpeg',
          name: value.name || `${key}.jpg`,
        } as any);
      } else {
        formData.append(key, String(value));
      }
    }
  });

  return formData;
};

export default apiClient;
