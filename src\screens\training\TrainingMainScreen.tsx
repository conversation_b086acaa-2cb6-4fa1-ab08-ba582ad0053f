import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  StatusBar,
  Animated,
  Alert,
  Modal,
  Image,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import { useNavigation } from '@react-navigation/native';
import { LinearGradient } from 'expo-linear-gradient';

interface TrainingVideo {
  id: string;
  title: string;
  description: string;
  category: 'safety' | 'order_handling' | 'app_usage';
  duration: number; // in seconds
  thumbnailUrl?: string;
  isRequired: boolean;
  points: number;
  difficulty: 'beginner' | 'intermediate' | 'advanced';
  instructor: string;
}

interface VideoProgress {
  videoId: string;
  watchedDuration: number;
  totalDuration: number;
  isCompleted: boolean;
  completedAt?: string;
  rating?: number;
}

interface TrainingCourse {
  id: string;
  title: string;
  description: string;
  category: 'safety' | 'order_handling' | 'app_usage';
  videos: TrainingVideo[];
  totalDuration: number;
  completedVideos: number;
  totalVideos: number;
  progressPercentage: number;
  badge?: string;
}

const TrainingMainScreen: React.FC = () => {
  const navigation = useNavigation();
  
  const [selectedCategory, setSelectedCategory] = useState<'all' | 'safety' | 'order_handling' | 'app_usage'>('all');
  const [videoProgress, setVideoProgress] = useState<VideoProgress[]>([]);
  const [selectedVideo, setSelectedVideo] = useState<TrainingVideo | null>(null);
  const [showVideoPlayer, setShowVideoPlayer] = useState(false);
  const [currentVideoTime, setCurrentVideoTime] = useState(0);
  const [isPlaying, setIsPlaying] = useState(false);

  // Animation refs
  const fadeAnimation = useRef(new Animated.Value(0)).current;
  const slideAnimation = useRef(new Animated.Value(50)).current;
  const badgeAnimation = useRef(new Animated.Value(0)).current;

  // Mock training data
  const trainingVideos: TrainingVideo[] = [
    {
      id: 'safety-1',
      title: 'Traffic Safety Rules',
      description: 'Essential traffic rules and safety protocols for delivery riders',
      category: 'safety',
      duration: 480, // 8 minutes
      isRequired: true,
      points: 100,
      difficulty: 'beginner',
      instructor: 'Safety Expert Ahmad Khan',
    },
    {
      id: 'safety-2',
      title: 'Personal Protection Equipment',
      description: 'Proper use of helmets, protective gear, and safety equipment',
      category: 'safety',
      duration: 360, // 6 minutes
      isRequired: true,
      points: 80,
      difficulty: 'beginner',
      instructor: 'Safety Expert Ahmad Khan',
    },
    {
      id: 'safety-3',
      title: 'Emergency Procedures',
      description: 'What to do in case of accidents, emergencies, and dangerous situations',
      category: 'safety',
      duration: 600, // 10 minutes
      isRequired: true,
      points: 120,
      difficulty: 'intermediate',
      instructor: 'Emergency Response Team',
    },
    {
      id: 'order-1',
      title: 'Order Acceptance Best Practices',
      description: 'How to efficiently accept and manage incoming orders',
      category: 'order_handling',
      duration: 420, // 7 minutes
      isRequired: true,
      points: 90,
      difficulty: 'beginner',
      instructor: 'Operations Manager Sarah Ahmed',
    },
    {
      id: 'order-2',
      title: 'Customer Communication',
      description: 'Professional communication with customers and restaurants',
      category: 'order_handling',
      duration: 540, // 9 minutes
      isRequired: true,
      points: 110,
      difficulty: 'intermediate',
      instructor: 'Customer Service Lead',
    },
    {
      id: 'order-3',
      title: 'Delivery Optimization',
      description: 'Tips for faster deliveries and route optimization',
      category: 'order_handling',
      duration: 720, // 12 minutes
      isRequired: false,
      points: 150,
      difficulty: 'advanced',
      instructor: 'Senior Delivery Expert',
    },
    {
      id: 'app-1',
      title: 'FoodWay App Basics',
      description: 'Complete guide to navigating the FoodWay rider app',
      category: 'app_usage',
      duration: 600, // 10 minutes
      isRequired: true,
      points: 120,
      difficulty: 'beginner',
      instructor: 'Tech Trainer Fatima Khan',
    },
    {
      id: 'app-2',
      title: 'Advanced App Features',
      description: 'Using analytics, earnings tracking, and advanced features',
      category: 'app_usage',
      duration: 480, // 8 minutes
      isRequired: false,
      points: 100,
      difficulty: 'intermediate',
      instructor: 'Tech Trainer Fatima Khan',
    },
    {
      id: 'app-3',
      title: 'Troubleshooting Common Issues',
      description: 'Solving common app problems and technical issues',
      category: 'app_usage',
      duration: 360, // 6 minutes
      isRequired: false,
      points: 80,
      difficulty: 'intermediate',
      instructor: 'Technical Support Team',
    },
  ];

  // Mock progress data
  const mockProgress: VideoProgress[] = [
    {
      videoId: 'safety-1',
      watchedDuration: 480,
      totalDuration: 480,
      isCompleted: true,
      completedAt: '2024-01-10',
      rating: 5,
    },
    {
      videoId: 'safety-2',
      watchedDuration: 360,
      totalDuration: 360,
      isCompleted: true,
      completedAt: '2024-01-11',
      rating: 4,
    },
    {
      videoId: 'order-1',
      watchedDuration: 210,
      totalDuration: 420,
      isCompleted: false,
    },
    {
      videoId: 'app-1',
      watchedDuration: 600,
      totalDuration: 600,
      isCompleted: true,
      completedAt: '2024-01-12',
      rating: 5,
    },
  ];

  useEffect(() => {
    // Start entrance animations
    Animated.parallel([
      Animated.timing(fadeAnimation, {
        toValue: 1,
        duration: 800,
        useNativeDriver: true,
      }),
      Animated.timing(slideAnimation, {
        toValue: 0,
        duration: 800,
        useNativeDriver: true,
      }),
    ]).start();

    // Load progress data
    setVideoProgress(mockProgress);

    // Start badge animation
    Animated.loop(
      Animated.sequence([
        Animated.timing(badgeAnimation, {
          toValue: 1,
          duration: 2000,
          useNativeDriver: true,
        }),
        Animated.timing(badgeAnimation, {
          toValue: 0,
          duration: 2000,
          useNativeDriver: true,
        }),
      ])
    ).start();
  }, []);

  const getVideoProgress = (videoId: string): VideoProgress | undefined => {
    return videoProgress.find(p => p.videoId === videoId);
  };

  const getProgressPercentage = (videoId: string): number => {
    const progress = getVideoProgress(videoId);
    if (!progress) return 0;
    return Math.round((progress.watchedDuration / progress.totalDuration) * 100);
  };

  const isVideoCompleted = (videoId: string): boolean => {
    const progress = getVideoProgress(videoId);
    return progress?.isCompleted || false;
  };

  const getCategoryIcon = (category: string) => {
    switch (category) {
      case 'safety':
        return 'shield-checkmark-outline';
      case 'order_handling':
        return 'receipt-outline';
      case 'app_usage':
        return 'phone-portrait-outline';
      default:
        return 'play-circle-outline';
    }
  };

  const getCategoryColor = (category: string) => {
    switch (category) {
      case 'safety':
        return '#dc2626';
      case 'order_handling':
        return '#059669';
      case 'app_usage':
        return '#2563eb';
      default:
        return '#6b7280';
    }
  };

  const getCategoryTitle = (category: string) => {
    switch (category) {
      case 'safety':
        return 'Safety Training';
      case 'order_handling':
        return 'Order Handling';
      case 'app_usage':
        return 'App Usage';
      default:
        return 'All Courses';
    }
  };

  const formatDuration = (seconds: number): string => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
  };

  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case 'beginner':
        return '#10b981';
      case 'intermediate':
        return '#f59e0b';
      case 'advanced':
        return '#ef4444';
      default:
        return '#6b7280';
    }
  };

  const startVideo = (video: TrainingVideo) => {
    setSelectedVideo(video);
    const progress = getVideoProgress(video.id);
    setCurrentVideoTime(progress?.watchedDuration || 0);
    setShowVideoPlayer(true);
  };

  const completeVideo = (videoId: string) => {
    const video = trainingVideos.find(v => v.id === videoId);
    if (!video) return;

    // Update progress
    const updatedProgress = videoProgress.map(p =>
      p.videoId === videoId
        ? { ...p, isCompleted: true, completedAt: new Date().toISOString().split('T')[0] }
        : p
    );

    // Add new progress if doesn't exist
    if (!videoProgress.find(p => p.videoId === videoId)) {
      updatedProgress.push({
        videoId,
        watchedDuration: video.duration,
        totalDuration: video.duration,
        isCompleted: true,
        completedAt: new Date().toISOString().split('T')[0],
      });
    }

    setVideoProgress(updatedProgress);
    setShowVideoPlayer(false);

    // Show completion alert
    Alert.alert(
      'Video Completed! 🎉',
      `Congratulations! You've completed "${video.title}" and earned ${video.points} points!`,
      [{ text: 'Continue Learning', style: 'default' }]
    );
  };

  const getFilteredVideos = () => {
    if (selectedCategory === 'all') {
      return trainingVideos;
    }
    return trainingVideos.filter(video => video.category === selectedCategory);
  };

  const getCoursesData = (): TrainingCourse[] => {
    const categories = ['safety', 'order_handling', 'app_usage'] as const;
    
    return categories.map(category => {
      const categoryVideos = trainingVideos.filter(v => v.category === category);
      const completedVideos = categoryVideos.filter(v => isVideoCompleted(v.id)).length;
      const totalDuration = categoryVideos.reduce((sum, v) => sum + v.duration, 0);
      const progressPercentage = categoryVideos.length > 0 ? Math.round((completedVideos / categoryVideos.length) * 100) : 0;
      
      return {
        id: category,
        title: getCategoryTitle(category),
        description: `${categoryVideos.length} training videos`,
        category,
        videos: categoryVideos,
        totalDuration,
        completedVideos,
        totalVideos: categoryVideos.length,
        progressPercentage,
        badge: progressPercentage === 100 ? '🏆' : undefined,
      };
    });
  };

  const renderHeader = () => (
    <LinearGradient
      colors={['#f97316', '#ea580c']}
      style={{
        paddingTop: StatusBar.currentHeight || 0,
        paddingBottom: 20,
      }}
    >
      <SafeAreaView>
        <View style={{
          flexDirection: 'row',
          alignItems: 'center',
          justifyContent: 'space-between',
          paddingHorizontal: 20,
          paddingTop: 16,
        }}>
          <TouchableOpacity
            onPress={() => navigation.goBack()}
            style={{
              width: 40,
              height: 40,
              borderRadius: 20,
              backgroundColor: 'rgba(255,255,255,0.2)',
              alignItems: 'center',
              justifyContent: 'center',
            }}
          >
            <Ionicons name="arrow-back" size={24} color="white" />
          </TouchableOpacity>

          <Text style={{
            fontSize: 24,
            fontWeight: 'bold',
            color: 'white',
          }}>
            Training Center
          </Text>

          <View style={{ width: 40 }} />
        </View>
      </SafeAreaView>
    </LinearGradient>
  );

  return (
    <View style={{ flex: 1, backgroundColor: '#f8fafc' }}>
      <StatusBar barStyle="light-content" backgroundColor="#f97316" />
      
      {renderHeader()}

      <Animated.View
        style={{
          flex: 1,
          opacity: fadeAnimation,
          transform: [{ translateY: slideAnimation }],
        }}
      >
        {/* Progress Overview */}
        <View style={{
          backgroundColor: 'white',
          marginHorizontal: 20,
          marginTop: 20,
          borderRadius: 16,
          padding: 20,
          shadowColor: '#000',
          shadowOffset: { width: 0, height: 2 },
          shadowOpacity: 0.1,
          shadowRadius: 8,
          elevation: 3,
        }}>
          <View style={{
            flexDirection: 'row',
            alignItems: 'center',
            justifyContent: 'space-between',
            marginBottom: 16,
          }}>
            <Text style={{
              fontSize: 18,
              fontWeight: '600',
              color: '#111827',
            }}>
              Your Progress
            </Text>

            <Animated.View
              style={{
                opacity: badgeAnimation,
                transform: [{
                  scale: badgeAnimation.interpolate({
                    inputRange: [0, 1],
                    outputRange: [1, 1.2],
                  }),
                }],
              }}
            >
              <Text style={{ fontSize: 24 }}>🎓</Text>
            </Animated.View>
          </View>

          <View style={{
            flexDirection: 'row',
            justifyContent: 'space-between',
          }}>
            <View style={{ alignItems: 'center' }}>
              <Text style={{
                fontSize: 24,
                fontWeight: 'bold',
                color: '#f97316',
              }}>
                {videoProgress.filter(p => p.isCompleted).length}
              </Text>
              <Text style={{
                fontSize: 12,
                color: '#6b7280',
              }}>
                Completed
              </Text>
            </View>

            <View style={{ alignItems: 'center' }}>
              <Text style={{
                fontSize: 24,
                fontWeight: 'bold',
                color: '#059669',
              }}>
                {videoProgress.filter(p => p.isCompleted).reduce((sum, p) => {
                  const video = trainingVideos.find(v => v.id === p.videoId);
                  return sum + (video?.points || 0);
                }, 0)}
              </Text>
              <Text style={{
                fontSize: 12,
                color: '#6b7280',
              }}>
                Points Earned
              </Text>
            </View>

            <View style={{ alignItems: 'center' }}>
              <Text style={{
                fontSize: 24,
                fontWeight: 'bold',
                color: '#2563eb',
              }}>
                {Math.round((videoProgress.filter(p => p.isCompleted).length / trainingVideos.length) * 100)}%
              </Text>
              <Text style={{
                fontSize: 12,
                color: '#6b7280',
              }}>
                Overall Progress
              </Text>
            </View>
          </View>
        </View>

        {/* Category Filter */}
        <ScrollView
          horizontal
          showsHorizontalScrollIndicator={false}
          style={{
            marginTop: 20,
            marginBottom: 10,
          }}
          contentContainerStyle={{
            paddingHorizontal: 20,
          }}
        >
          {(['all', 'safety', 'order_handling', 'app_usage'] as const).map((category) => (
            <TouchableOpacity
              key={category}
              onPress={() => setSelectedCategory(category)}
              style={{
                backgroundColor: selectedCategory === category ? '#f97316' : 'white',
                borderRadius: 20,
                paddingHorizontal: 16,
                paddingVertical: 8,
                marginRight: 12,
                borderWidth: 1,
                borderColor: selectedCategory === category ? '#f97316' : '#e5e7eb',
              }}
            >
              <Text style={{
                fontSize: 14,
                fontWeight: '600',
                color: selectedCategory === category ? 'white' : '#6b7280',
              }}>
                {getCategoryTitle(category)}
              </Text>
            </TouchableOpacity>
          ))}
        </ScrollView>

        {/* Course Sections */}
        <ScrollView
          style={{ flex: 1 }}
          showsVerticalScrollIndicator={false}
          contentContainerStyle={{ paddingBottom: 100 }}
        >
          {selectedCategory === 'all' ? (
            // Show course sections when "all" is selected
            getCoursesData().map((course) => (
              <View key={course.id} style={{
                backgroundColor: 'white',
                marginHorizontal: 20,
                marginBottom: 20,
                borderRadius: 16,
                padding: 20,
                shadowColor: '#000',
                shadowOffset: { width: 0, height: 2 },
                shadowOpacity: 0.1,
                shadowRadius: 8,
                elevation: 3,
              }}>
                {/* Course Header */}
                <View style={{
                  flexDirection: 'row',
                  alignItems: 'center',
                  marginBottom: 16,
                }}>
                  <View style={{
                    width: 48,
                    height: 48,
                    borderRadius: 24,
                    backgroundColor: `${getCategoryColor(course.category)}20`,
                    alignItems: 'center',
                    justifyContent: 'center',
                    marginRight: 12,
                  }}>
                    <Ionicons
                      name={getCategoryIcon(course.category) as any}
                      size={24}
                      color={getCategoryColor(course.category)}
                    />
                  </View>

                  <View style={{ flex: 1 }}>
                    <View style={{
                      flexDirection: 'row',
                      alignItems: 'center',
                    }}>
                      <Text style={{
                        fontSize: 18,
                        fontWeight: '600',
                        color: '#111827',
                        marginRight: 8,
                      }}>
                        {course.title}
                      </Text>

                      {course.badge && (
                        <Text style={{ fontSize: 20 }}>{course.badge}</Text>
                      )}
                    </View>

                    <Text style={{
                      fontSize: 14,
                      color: '#6b7280',
                    }}>
                      {course.description} • {formatDuration(course.totalDuration)}
                    </Text>
                  </View>
                </View>

                {/* Progress Bar */}
                <View style={{
                  backgroundColor: '#f3f4f6',
                  height: 8,
                  borderRadius: 4,
                  marginBottom: 16,
                }}>
                  <View style={{
                    backgroundColor: getCategoryColor(course.category),
                    height: 8,
                    borderRadius: 4,
                    width: `${course.progressPercentage}%`,
                  }} />
                </View>

                <View style={{
                  flexDirection: 'row',
                  justifyContent: 'space-between',
                  alignItems: 'center',
                  marginBottom: 16,
                }}>
                  <Text style={{
                    fontSize: 14,
                    color: '#6b7280',
                  }}>
                    {course.completedVideos} of {course.totalVideos} videos completed
                  </Text>

                  <Text style={{
                    fontSize: 14,
                    fontWeight: '600',
                    color: getCategoryColor(course.category),
                  }}>
                    {course.progressPercentage}%
                  </Text>
                </View>

                {/* Video List */}
                {course.videos.slice(0, 3).map((video) => {
                  const progress = getProgressPercentage(video.id);
                  const completed = isVideoCompleted(video.id);

                  return (
                    <TouchableOpacity
                      key={video.id}
                      onPress={() => startVideo(video)}
                      style={{
                        flexDirection: 'row',
                        alignItems: 'center',
                        paddingVertical: 12,
                        borderBottomWidth: 1,
                        borderBottomColor: '#f3f4f6',
                      }}
                    >
                      {/* Play/Check Icon */}
                      <View style={{
                        width: 40,
                        height: 40,
                        borderRadius: 20,
                        backgroundColor: completed ? '#dcfdf7' : '#f3f4f6',
                        alignItems: 'center',
                        justifyContent: 'center',
                        marginRight: 12,
                      }}>
                        <Ionicons
                          name={completed ? 'checkmark' : 'play'}
                          size={20}
                          color={completed ? '#059669' : '#6b7280'}
                        />
                      </View>

                      {/* Video Info */}
                      <View style={{ flex: 1 }}>
                        <Text style={{
                          fontSize: 16,
                          fontWeight: '600',
                          color: '#111827',
                          marginBottom: 4,
                        }}>
                          {video.title}
                        </Text>

                        <View style={{
                          flexDirection: 'row',
                          alignItems: 'center',
                        }}>
                          <Text style={{
                            fontSize: 12,
                            color: '#6b7280',
                            marginRight: 12,
                          }}>
                            {formatDuration(video.duration)}
                          </Text>

                          <View style={{
                            backgroundColor: getDifficultyColor(video.difficulty) + '20',
                            paddingHorizontal: 6,
                            paddingVertical: 2,
                            borderRadius: 8,
                            marginRight: 8,
                          }}>
                            <Text style={{
                              fontSize: 10,
                              color: getDifficultyColor(video.difficulty),
                              fontWeight: '600',
                            }}>
                              {video.difficulty.toUpperCase()}
                            </Text>
                          </View>

                          {video.isRequired && (
                            <View style={{
                              backgroundColor: '#fef2f2',
                              paddingHorizontal: 6,
                              paddingVertical: 2,
                              borderRadius: 8,
                            }}>
                              <Text style={{
                                fontSize: 10,
                                color: '#dc2626',
                                fontWeight: '600',
                              }}>
                                REQUIRED
                              </Text>
                            </View>
                          )}
                        </View>

                        {/* Progress Bar for Individual Video */}
                        {progress > 0 && !completed && (
                          <View style={{
                            backgroundColor: '#f3f4f6',
                            height: 4,
                            borderRadius: 2,
                            marginTop: 8,
                          }}>
                            <View style={{
                              backgroundColor: getCategoryColor(video.category),
                              height: 4,
                              borderRadius: 2,
                              width: `${progress}%`,
                            }} />
                          </View>
                        )}
                      </View>

                      {/* Points */}
                      <View style={{
                        alignItems: 'center',
                        marginLeft: 12,
                      }}>
                        <Text style={{
                          fontSize: 12,
                          fontWeight: '600',
                          color: '#f97316',
                        }}>
                          {video.points}
                        </Text>
                        <Text style={{
                          fontSize: 10,
                          color: '#6b7280',
                        }}>
                          pts
                        </Text>
                      </View>
                    </TouchableOpacity>
                  );
                })}

                {course.videos.length > 3 && (
                  <TouchableOpacity
                    onPress={() => setSelectedCategory(course.category)}
                    style={{
                      paddingVertical: 12,
                      alignItems: 'center',
                    }}
                  >
                    <Text style={{
                      fontSize: 14,
                      color: getCategoryColor(course.category),
                      fontWeight: '600',
                    }}>
                      View All {course.videos.length} Videos
                    </Text>
                  </TouchableOpacity>
                )}
              </View>
            ))
          ) : (
            // Show individual videos when a specific category is selected
            <View style={{
              marginHorizontal: 20,
            }}>
              {getFilteredVideos().map((video) => {
                const progress = getProgressPercentage(video.id);
                const completed = isVideoCompleted(video.id);

                return (
                  <TouchableOpacity
                    key={video.id}
                    onPress={() => startVideo(video)}
                    style={{
                      backgroundColor: 'white',
                      borderRadius: 16,
                      marginBottom: 16,
                      overflow: 'hidden',
                      shadowColor: '#000',
                      shadowOffset: { width: 0, height: 2 },
                      shadowOpacity: 0.1,
                      shadowRadius: 8,
                      elevation: 3,
                    }}
                  >
                    {/* Video Thumbnail */}
                    <View style={{
                      height: 120,
                      backgroundColor: getCategoryColor(video.category) + '20',
                      alignItems: 'center',
                      justifyContent: 'center',
                      position: 'relative',
                    }}>
                      <Ionicons
                        name={getCategoryIcon(video.category) as any}
                        size={40}
                        color={getCategoryColor(video.category)}
                      />

                      {/* Play/Check Button */}
                      <View style={{
                        position: 'absolute',
                        bottom: 12,
                        right: 12,
                        width: 40,
                        height: 40,
                        borderRadius: 20,
                        backgroundColor: completed ? '#059669' : 'rgba(0,0,0,0.7)',
                        alignItems: 'center',
                        justifyContent: 'center',
                      }}>
                        <Ionicons
                          name={completed ? 'checkmark' : 'play'}
                          size={20}
                          color="white"
                        />
                      </View>

                      {/* Duration Badge */}
                      <View style={{
                        position: 'absolute',
                        top: 12,
                        right: 12,
                        backgroundColor: 'rgba(0,0,0,0.7)',
                        paddingHorizontal: 8,
                        paddingVertical: 4,
                        borderRadius: 8,
                      }}>
                        <Text style={{
                          fontSize: 12,
                          color: 'white',
                          fontWeight: '600',
                        }}>
                          {formatDuration(video.duration)}
                        </Text>
                      </View>
                    </View>

                    {/* Video Info */}
                    <View style={{ padding: 16 }}>
                      <View style={{
                        flexDirection: 'row',
                        alignItems: 'center',
                        justifyContent: 'space-between',
                        marginBottom: 8,
                      }}>
                        <Text style={{
                          fontSize: 18,
                          fontWeight: '600',
                          color: '#111827',
                          flex: 1,
                        }}>
                          {video.title}
                        </Text>

                        {completed && (
                          <View style={{
                            backgroundColor: '#dcfdf7',
                            paddingHorizontal: 8,
                            paddingVertical: 4,
                            borderRadius: 12,
                            marginLeft: 8,
                          }}>
                            <Text style={{
                              fontSize: 12,
                              color: '#059669',
                              fontWeight: '600',
                            }}>
                              COMPLETED
                            </Text>
                          </View>
                        )}
                      </View>

                      <Text style={{
                        fontSize: 14,
                        color: '#6b7280',
                        marginBottom: 12,
                        lineHeight: 20,
                      }}>
                        {video.description}
                      </Text>

                      <View style={{
                        flexDirection: 'row',
                        alignItems: 'center',
                        justifyContent: 'space-between',
                        marginBottom: 12,
                      }}>
                        <View style={{
                          flexDirection: 'row',
                          alignItems: 'center',
                        }}>
                          <View style={{
                            backgroundColor: getDifficultyColor(video.difficulty) + '20',
                            paddingHorizontal: 8,
                            paddingVertical: 4,
                            borderRadius: 8,
                            marginRight: 8,
                          }}>
                            <Text style={{
                              fontSize: 12,
                              color: getDifficultyColor(video.difficulty),
                              fontWeight: '600',
                            }}>
                              {video.difficulty.toUpperCase()}
                            </Text>
                          </View>

                          {video.isRequired && (
                            <View style={{
                              backgroundColor: '#fef2f2',
                              paddingHorizontal: 8,
                              paddingVertical: 4,
                              borderRadius: 8,
                            }}>
                              <Text style={{
                                fontSize: 12,
                                color: '#dc2626',
                                fontWeight: '600',
                              }}>
                                REQUIRED
                              </Text>
                            </View>
                          )}
                        </View>

                        <View style={{
                          flexDirection: 'row',
                          alignItems: 'center',
                        }}>
                          <Ionicons name="trophy" size={16} color="#f97316" />
                          <Text style={{
                            fontSize: 14,
                            fontWeight: '600',
                            color: '#f97316',
                            marginLeft: 4,
                          }}>
                            {video.points} pts
                          </Text>
                        </View>
                      </View>

                      <Text style={{
                        fontSize: 12,
                        color: '#6b7280',
                        marginBottom: 8,
                      }}>
                        Instructor: {video.instructor}
                      </Text>

                      {/* Progress Bar */}
                      {progress > 0 && (
                        <View style={{
                          backgroundColor: '#f3f4f6',
                          height: 6,
                          borderRadius: 3,
                          marginTop: 8,
                        }}>
                          <View style={{
                            backgroundColor: getCategoryColor(video.category),
                            height: 6,
                            borderRadius: 3,
                            width: `${progress}%`,
                          }} />
                        </View>
                      )}
                    </View>
                  </TouchableOpacity>
                );
              })}
            </View>
          )}
        </ScrollView>
      </Animated.View>

      {/* Video Player Modal */}
      <Modal
        visible={showVideoPlayer}
        animationType="slide"
        presentationStyle="fullScreen"
      >
        {selectedVideo && (
          <SafeAreaView style={{ flex: 1, backgroundColor: '#000' }}>
            {/* Video Player Header */}
            <View style={{
              flexDirection: 'row',
              alignItems: 'center',
              paddingHorizontal: 16,
              paddingVertical: 12,
              backgroundColor: 'rgba(0,0,0,0.8)',
            }}>
              <TouchableOpacity
                onPress={() => setShowVideoPlayer(false)}
                style={{
                  width: 40,
                  height: 40,
                  alignItems: 'center',
                  justifyContent: 'center',
                  marginRight: 8,
                }}
              >
                <Ionicons name="close" size={24} color="white" />
              </TouchableOpacity>

              <Text style={{
                fontSize: 16,
                fontWeight: '600',
                color: 'white',
                flex: 1,
              }}>
                {selectedVideo.title}
              </Text>

              <View style={{
                backgroundColor: 'rgba(255,255,255,0.2)',
                paddingHorizontal: 8,
                paddingVertical: 4,
                borderRadius: 8,
              }}>
                <Text style={{
                  fontSize: 12,
                  color: 'white',
                  fontWeight: '600',
                }}>
                  {formatDuration(selectedVideo.duration)}
                </Text>
              </View>
            </View>

            {/* Video Player Area */}
            <View style={{
              flex: 1,
              alignItems: 'center',
              justifyContent: 'center',
              backgroundColor: '#000',
            }}>
              {/* Mock Video Player */}
              <View style={{
                width: '100%',
                aspectRatio: 16/9,
                backgroundColor: '#1f2937',
                alignItems: 'center',
                justifyContent: 'center',
                position: 'relative',
              }}>
                <Ionicons
                  name={getCategoryIcon(selectedVideo.category) as any}
                  size={80}
                  color={getCategoryColor(selectedVideo.category)}
                />

                {/* Play/Pause Button */}
                <TouchableOpacity
                  onPress={() => setIsPlaying(!isPlaying)}
                  style={{
                    position: 'absolute',
                    width: 80,
                    height: 80,
                    borderRadius: 40,
                    backgroundColor: 'rgba(249, 115, 22, 0.9)',
                    alignItems: 'center',
                    justifyContent: 'center',
                  }}
                >
                  <Ionicons
                    name={isPlaying ? 'pause' : 'play'}
                    size={40}
                    color="white"
                  />
                </TouchableOpacity>

                {/* Progress Indicator */}
                <View style={{
                  position: 'absolute',
                  bottom: 0,
                  left: 0,
                  right: 0,
                  height: 4,
                  backgroundColor: 'rgba(255,255,255,0.3)',
                }}>
                  <View style={{
                    height: 4,
                    backgroundColor: '#f97316',
                    width: `${(currentVideoTime / selectedVideo.duration) * 100}%`,
                  }} />
                </View>
              </View>

              {/* Video Controls */}
              <View style={{
                width: '100%',
                paddingHorizontal: 20,
                paddingVertical: 20,
              }}>
                {/* Progress Bar */}
                <View style={{
                  backgroundColor: 'rgba(255,255,255,0.3)',
                  height: 6,
                  borderRadius: 3,
                  marginBottom: 16,
                }}>
                  <View style={{
                    backgroundColor: '#f97316',
                    height: 6,
                    borderRadius: 3,
                    width: `${(currentVideoTime / selectedVideo.duration) * 100}%`,
                  }} />
                </View>

                {/* Time Display */}
                <View style={{
                  flexDirection: 'row',
                  justifyContent: 'space-between',
                  marginBottom: 20,
                }}>
                  <Text style={{
                    fontSize: 14,
                    color: 'white',
                  }}>
                    {formatDuration(currentVideoTime)}
                  </Text>
                  <Text style={{
                    fontSize: 14,
                    color: 'white',
                  }}>
                    {formatDuration(selectedVideo.duration)}
                  </Text>
                </View>

                {/* Control Buttons */}
                <View style={{
                  flexDirection: 'row',
                  alignItems: 'center',
                  justifyContent: 'center',
                  marginBottom: 20,
                }}>
                  <TouchableOpacity
                    onPress={() => setCurrentVideoTime(Math.max(0, currentVideoTime - 10))}
                    style={{
                      width: 48,
                      height: 48,
                      borderRadius: 24,
                      backgroundColor: 'rgba(255,255,255,0.2)',
                      alignItems: 'center',
                      justifyContent: 'center',
                      marginHorizontal: 8,
                    }}
                  >
                    <Ionicons name="play-back" size={24} color="white" />
                  </TouchableOpacity>

                  <TouchableOpacity
                    onPress={() => setIsPlaying(!isPlaying)}
                    style={{
                      width: 64,
                      height: 64,
                      borderRadius: 32,
                      backgroundColor: '#f97316',
                      alignItems: 'center',
                      justifyContent: 'center',
                      marginHorizontal: 16,
                    }}
                  >
                    <Ionicons
                      name={isPlaying ? 'pause' : 'play'}
                      size={32}
                      color="white"
                    />
                  </TouchableOpacity>

                  <TouchableOpacity
                    onPress={() => setCurrentVideoTime(Math.min(selectedVideo.duration, currentVideoTime + 10))}
                    style={{
                      width: 48,
                      height: 48,
                      borderRadius: 24,
                      backgroundColor: 'rgba(255,255,255,0.2)',
                      alignItems: 'center',
                      justifyContent: 'center',
                      marginHorizontal: 8,
                    }}
                  >
                    <Ionicons name="play-forward" size={24} color="white" />
                  </TouchableOpacity>
                </View>

                {/* Complete Video Button */}
                <TouchableOpacity
                  onPress={() => completeVideo(selectedVideo.id)}
                  style={{
                    backgroundColor: '#059669',
                    borderRadius: 12,
                    paddingVertical: 16,
                    alignItems: 'center',
                    marginTop: 20,
                  }}
                >
                  <View style={{
                    flexDirection: 'row',
                    alignItems: 'center',
                  }}>
                    <Ionicons name="checkmark-circle" size={20} color="white" />
                    <Text style={{
                      fontSize: 16,
                      fontWeight: '600',
                      color: 'white',
                      marginLeft: 8,
                    }}>
                      Mark as Completed (+{selectedVideo.points} pts)
                    </Text>
                  </View>
                </TouchableOpacity>
              </View>
            </View>

            {/* Video Info */}
            <View style={{
              backgroundColor: 'rgba(0,0,0,0.9)',
              paddingHorizontal: 20,
              paddingBottom: 20,
            }}>
              <Text style={{
                fontSize: 18,
                fontWeight: '600',
                color: 'white',
                marginBottom: 8,
              }}>
                {selectedVideo.title}
              </Text>

              <Text style={{
                fontSize: 14,
                color: '#d1d5db',
                marginBottom: 12,
                lineHeight: 20,
              }}>
                {selectedVideo.description}
              </Text>

              <View style={{
                flexDirection: 'row',
                alignItems: 'center',
                justifyContent: 'space-between',
              }}>
                <View style={{
                  flexDirection: 'row',
                  alignItems: 'center',
                }}>
                  <View style={{
                    backgroundColor: getDifficultyColor(selectedVideo.difficulty) + '40',
                    paddingHorizontal: 8,
                    paddingVertical: 4,
                    borderRadius: 8,
                    marginRight: 8,
                  }}>
                    <Text style={{
                      fontSize: 12,
                      color: getDifficultyColor(selectedVideo.difficulty),
                      fontWeight: '600',
                    }}>
                      {selectedVideo.difficulty.toUpperCase()}
                    </Text>
                  </View>

                  {selectedVideo.isRequired && (
                    <View style={{
                      backgroundColor: 'rgba(220, 38, 38, 0.3)',
                      paddingHorizontal: 8,
                      paddingVertical: 4,
                      borderRadius: 8,
                    }}>
                      <Text style={{
                        fontSize: 12,
                        color: '#fca5a5',
                        fontWeight: '600',
                      }}>
                        REQUIRED
                      </Text>
                    </View>
                  )}
                </View>

                <Text style={{
                  fontSize: 12,
                  color: '#d1d5db',
                }}>
                  Instructor: {selectedVideo.instructor}
                </Text>
              </View>
            </View>
          </SafeAreaView>
        )}
      </Modal>
    </View>
  );
};

export default TrainingMainScreen;
