import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  Alert,
  Vibration,
  Linking,
  Animated,
  Modal,
  Image,
  StatusBar,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import { LinearGradient } from 'expo-linear-gradient';
import { BlurView } from 'expo-blur';
import * as ImagePicker from 'expo-image-picker';
// import QRCode from 'react-native-qrcode-svg';
import { useNavigation, useRoute } from '@react-navigation/native';
import { Card, Button, Badge } from '../../components/ui';
import { useOrders } from '../../context/OrderContext';
import { Order, OrderStatus } from '../../types/orders';
import { formatCurrency, formatDistance, formatDuration } from '../../utils/helpers';

interface RouteParams {
  orderId: string;
}

const PickupFlowScreen: React.FC = () => {
  const navigation = useNavigation();
  const route = useRoute();
  const { orderId } = route.params as RouteParams;
  const { state, updateOrderStatus, startPickupTimer, markAsPickedUp } = useOrders();

  const [order, setOrder] = useState<Order | null>(null);
  const [pickupTimer, setPickupTimer] = useState(0);
  const [isTimerRunning, setIsTimerRunning] = useState(false);
  const [hasArrivedAtRestaurant, setHasArrivedAtRestaurant] = useState(false);
  const [showQRCode, setShowQRCode] = useState(false);
  const [showPhotoUpload, setShowPhotoUpload] = useState(false);
  const [foodPhoto, setFoodPhoto] = useState<string | null>(null);

  // Animation refs
  const fadeAnimation = useRef(new Animated.Value(0)).current;
  const slideAnimation = useRef(new Animated.Value(50)).current;
  const timerPulseAnimation = useRef(new Animated.Value(1)).current;
  const qrPulseAnimation = useRef(new Animated.Value(1)).current;

  useEffect(() => {
    const foundOrder = state.acceptedOrders.find(o => o.id === orderId);
    setOrder(foundOrder || null);

    if (foundOrder?.status === OrderStatus.ARRIVED_AT_RESTAURANT) {
      setHasArrivedAtRestaurant(true);
    }

    // Start entrance animations
    Animated.parallel([
      Animated.timing(fadeAnimation, {
        toValue: 1,
        duration: 800,
        useNativeDriver: true,
      }),
      Animated.timing(slideAnimation, {
        toValue: 0,
        duration: 800,
        useNativeDriver: true,
      }),
    ]).start();
  }, [orderId, state]);

  useEffect(() => {
    let interval: NodeJS.Timeout;
    if (isTimerRunning) {
      interval = setInterval(() => {
        setPickupTimer(prev => prev + 1);
      }, 1000);

      // Start timer pulse animation
      const pulseAnimation = Animated.loop(
        Animated.sequence([
          Animated.timing(timerPulseAnimation, {
            toValue: 1.1,
            duration: 1000,
            useNativeDriver: true,
          }),
          Animated.timing(timerPulseAnimation, {
            toValue: 1,
            duration: 1000,
            useNativeDriver: true,
          }),
        ])
      );
      pulseAnimation.start();

      return () => {
        pulseAnimation.stop();
        if (interval) clearInterval(interval);
      };
    }
    return () => {
      if (interval) clearInterval(interval);
    };
  }, [isTimerRunning]);

  const handleArrivedAtRestaurant = async () => {
    if (!order) return;
    
    try {
      await updateOrderStatus(order.id, OrderStatus.ARRIVED_AT_RESTAURANT);
      setOrder(prev => prev ? { ...prev, status: OrderStatus.ARRIVED_AT_RESTAURANT } : null);
      setHasArrivedAtRestaurant(true);
      Vibration.vibrate(200);
      Alert.alert('Status Updated', 'Marked as arrived at restaurant');
    } catch (error: any) {
      Alert.alert('Error', error.message);
    }
  };

  const handleStartPickupTimer = () => {
    if (!order) return;
    
    setIsTimerRunning(true);
    startPickupTimer(order.id);
    Alert.alert('Timer Started', 'Pickup wait time tracking started');
  };

  const handlePickedUp = async () => {
    if (!order) return;

    try {
      setIsTimerRunning(false);
      await markAsPickedUp(order.id, pickupTimer);
      setOrder(prev => prev ? { ...prev, status: OrderStatus.PICKED_UP } : null);

      Alert.alert(
        'Order Picked Up!',
        'Ready for delivery. Navigate to delivery flow?',
        [
          { text: 'Stay Here', style: 'cancel' },
          {
            text: 'Start Delivery',
            onPress: () => navigation.navigate('DeliveryFlow', { orderId: order.id })
          }
        ]
      );
    } catch (error: any) {
      Alert.alert('Error', error.message);
    }
  };

  const handleTakePhoto = async () => {
    try {
      const { status } = await ImagePicker.requestCameraPermissionsAsync();
      if (status !== 'granted') {
        Alert.alert('Permission Required', 'Camera permission is needed to take food photos');
        return;
      }

      const result = await ImagePicker.launchCameraAsync({
        mediaTypes: ImagePicker.MediaTypeOptions.Images,
        allowsEditing: true,
        aspect: [4, 3],
        quality: 0.8,
      });

      if (!result.canceled && result.assets[0]) {
        setFoodPhoto(result.assets[0].uri);
        setShowPhotoUpload(false);
        Vibration.vibrate(50);
        Alert.alert('Photo Captured', 'Food photo has been saved successfully!');
      }
    } catch (error) {
      console.error('Error taking photo:', error);
      Alert.alert('Error', 'Could not take photo');
    }
  };

  const handleShowQRCode = () => {
    setShowQRCode(true);
    // Start QR pulse animation
    const qrPulse = Animated.loop(
      Animated.sequence([
        Animated.timing(qrPulseAnimation, {
          toValue: 1.05,
          duration: 2000,
          useNativeDriver: true,
        }),
        Animated.timing(qrPulseAnimation, {
          toValue: 1,
          duration: 2000,
          useNativeDriver: true,
        }),
      ])
    );
    qrPulse.start();
  };

  const formatTimer = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  };

  const renderHeader = () => (
    <View style={{
      backgroundColor: '#dc2626',
      paddingHorizontal: 20,
      paddingTop: (StatusBar.currentHeight || 0) + 20,
      paddingBottom: 28,
      borderBottomLeftRadius: 28,
      borderBottomRightRadius: 28,
      shadowColor: '#dc2626',
      shadowOffset: { width: 0, height: 12 },
      shadowOpacity: 0.3,
      shadowRadius: 20,
      elevation: 16,
    }}>
      <View style={{
        flexDirection: 'row',
        alignItems: 'center',
        marginBottom: 20,
      }}>
        <TouchableOpacity
          onPress={() => navigation.goBack()}
          style={{
            width: 48,
            height: 48,
            borderRadius: 24,
            backgroundColor: 'rgba(255,255,255,0.2)',
            justifyContent: 'center',
            alignItems: 'center',
            marginRight: 16,
            borderWidth: 2,
            borderColor: 'rgba(255,255,255,0.3)',
          }}
        >
          <Ionicons name="arrow-back" size={24} color="white" />
        </TouchableOpacity>

        <View style={{ flex: 1 }}>
          <Text style={{
            fontSize: 24,
            fontWeight: 'bold',
            color: 'white',
            marginBottom: 4,
          }}>
            🏪 Pickup Order
          </Text>
          {order && (
            <Text style={{
              fontSize: 16,
              color: 'rgba(255,255,255,0.9)',
              fontWeight: '600',
            }}>
              #{order.orderNumber}
            </Text>
          )}
        </View>

        <View style={{
          backgroundColor: hasArrivedAtRestaurant ? '#10b981' : '#f59e0b',
          paddingHorizontal: 16,
          paddingVertical: 8,
          borderRadius: 16,
          borderWidth: 2,
          borderColor: 'white',
          shadowColor: hasArrivedAtRestaurant ? '#10b981' : '#f59e0b',
          shadowOffset: { width: 0, height: 4 },
          shadowOpacity: 0.3,
          shadowRadius: 8,
          elevation: 6,
        }}>
          <Text style={{
            fontSize: 14,
            fontWeight: 'bold',
            color: 'white',
          }}>
            {hasArrivedAtRestaurant ? "At Restaurant" : "En Route"}
          </Text>
        </View>
      </View>
    </View>
  );

  const renderRestaurantInfo = () => (
    <Animated.View style={{
      opacity: fadeAnimation,
      transform: [{ translateY: slideAnimation }],
    }}>
      <Card variant="elevated" margin="md" padding="none">
        <LinearGradient
          colors={['#10b981', '#059669']}
          style={{
            padding: 20,
            borderTopLeftRadius: 16,
            borderTopRightRadius: 16,
          }}
        >
          <View style={{
            flexDirection: 'row',
            alignItems: 'center',
            marginBottom: 16,
          }}>
            <View style={{
              width: 72,
              height: 72,
              backgroundColor: 'rgba(255,255,255,0.2)',
              borderRadius: 36,
              alignItems: 'center',
              justifyContent: 'center',
              marginRight: 16,
              borderWidth: 3,
              borderColor: 'rgba(255,255,255,0.3)',
              shadowColor: 'white',
              shadowOffset: { width: 0, height: 4 },
              shadowOpacity: 0.3,
              shadowRadius: 8,
              elevation: 6,
            }}>
              <Ionicons name="restaurant" size={32} color="white" />
            </View>

            <View style={{ flex: 1 }}>
              <Text style={{
                fontSize: 22,
                fontWeight: 'bold',
                color: 'white',
                marginBottom: 6,
              }}>
                {order?.restaurant.name}
              </Text>
              <Text style={{
                fontSize: 16,
                color: 'rgba(255,255,255,0.9)',
                fontWeight: '600',
              }}>
                📍 {order?.restaurant.address.street}
              </Text>
            </View>

            <TouchableOpacity
              onPress={handleShowQRCode}
              style={{
                backgroundColor: 'rgba(255,255,255,0.2)',
                borderRadius: 16,
                padding: 16,
                borderWidth: 2,
                borderColor: 'rgba(255,255,255,0.3)',
                shadowColor: 'white',
                shadowOffset: { width: 0, height: 4 },
                shadowOpacity: 0.3,
                shadowRadius: 8,
                elevation: 6,
              }}
            >
              <Ionicons name="qr-code" size={28} color="white" />
            </TouchableOpacity>
          </View>

          {/* Enhanced Contact Buttons */}
          <View style={{ flexDirection: 'row', gap: 16 }}>
            <TouchableOpacity
              onPress={() => {
                Vibration.vibrate(50);
                if (order) {
                  const phoneUrl = `tel:${order.restaurant.phone}`;
                  Linking.openURL(phoneUrl);
                }
              }}
              style={{
                flex: 1,
                backgroundColor: 'rgba(255,255,255,0.2)',
                borderRadius: 16,
                padding: 16,
                flexDirection: 'row',
                alignItems: 'center',
                justifyContent: 'center',
                borderWidth: 2,
                borderColor: 'rgba(255,255,255,0.3)',
                shadowColor: 'white',
                shadowOffset: { width: 0, height: 4 },
                shadowOpacity: 0.3,
                shadowRadius: 8,
                elevation: 6,
              }}
            >
              <Ionicons name="call" size={22} color="white" style={{ marginRight: 10 }} />
              <Text style={{ color: 'white', fontWeight: 'bold', fontSize: 16 }}>Call</Text>
            </TouchableOpacity>

            <TouchableOpacity
              onPress={() => {
                Vibration.vibrate(50);
                Alert.alert('Chat', 'Opening chat with restaurant...');
              }}
              style={{
                flex: 1,
                backgroundColor: 'rgba(255,255,255,0.2)',
                borderRadius: 16,
                padding: 16,
                flexDirection: 'row',
                alignItems: 'center',
                justifyContent: 'center',
                borderWidth: 2,
                borderColor: 'rgba(255,255,255,0.3)',
                shadowColor: 'white',
                shadowOffset: { width: 0, height: 4 },
                shadowOpacity: 0.3,
                shadowRadius: 8,
                elevation: 6,
              }}
            >
              <Ionicons name="chatbubble" size={22} color="white" style={{ marginRight: 10 }} />
              <Text style={{ color: 'white', fontWeight: 'bold', fontSize: 16 }}>Chat</Text>
            </TouchableOpacity>
          </View>
        </LinearGradient>

        <View style={{ padding: 20 }}>
          <View style={{
            flexDirection: 'row',
            justifyContent: 'space-between',
            marginBottom: 8,
          }}>
            <Text style={{ fontSize: 14, color: '#6b7280' }}>Distance to Restaurant</Text>
            <Text style={{ fontSize: 14, fontWeight: '500', color: '#111827' }}>
              {formatDistance(order?.estimatedDistance || 0)}
            </Text>
          </View>

          <View style={{
            flexDirection: 'row',
            justifyContent: 'space-between',
            marginBottom: 8,
          }}>
            <Text style={{ fontSize: 14, color: '#6b7280' }}>Estimated Pickup Time</Text>
            <Text style={{ fontSize: 14, fontWeight: '500', color: '#111827' }}>
              {formatDuration(order?.estimatedDuration || 0)}
            </Text>
          </View>

          <View style={{
            flexDirection: 'row',
            justifyContent: 'space-between',
          }}>
            <Text style={{ fontSize: 14, color: '#6b7280' }}>Pickup Earnings</Text>
            <Text style={{ fontSize: 14, fontWeight: '500', color: '#059669' }}>
              {formatCurrency(order?.estimatedEarnings || 0)}
            </Text>
          </View>
        </View>
      </Card>
    </Animated.View>
  );

  const renderPickupTimer = () => {
    if (!hasArrivedAtRestaurant) return null;

    return (
      <Animated.View style={{
        opacity: fadeAnimation,
        transform: [{ translateY: slideAnimation }],
      }}>
        <Card variant="elevated" margin="md" padding="lg">
          <View style={{
            flexDirection: 'row',
            alignItems: 'center',
            marginBottom: 16,
          }}>
            <View style={{
              width: 40,
              height: 40,
              borderRadius: 20,
              backgroundColor: '#f59e0b',
              alignItems: 'center',
              justifyContent: 'center',
              marginRight: 12,
            }}>
              <Ionicons name="timer" size={20} color="white" />
            </View>
            <View>
              <Text style={{
                fontSize: 18,
                fontWeight: 'bold',
                color: '#111827',
              }}>
                Pickup Wait Time
              </Text>
              <Text style={{
                fontSize: 12,
                color: '#6b7280',
              }}>
                Track your waiting time
              </Text>
            </View>
          </View>

          <View style={{ alignItems: 'center', marginBottom: 16 }}>
            {isTimerRunning ? (
              <Animated.View style={{
                transform: [{ scale: timerPulseAnimation }],
              }}>
                <LinearGradient
                  colors={['#f59e0b', '#d97706']}
                  style={{
                    paddingHorizontal: 32,
                    paddingVertical: 16,
                    borderRadius: 30,
                    alignItems: 'center',
                  }}
                >
                  <Text style={{
                    fontSize: 36,
                    fontWeight: 'bold',
                    color: 'white',
                  }}>
                    {formatTimer(pickupTimer)}
                  </Text>
                </LinearGradient>
              </Animated.View>
            ) : (
              <View style={{
                backgroundColor: '#f3f4f6',
                paddingHorizontal: 32,
                paddingVertical: 16,
                borderRadius: 30,
                alignItems: 'center',
              }}>
                <Text style={{
                  fontSize: 28,
                  fontWeight: 'bold',
                  color: '#6b7280',
                }}>
                  00:00
                </Text>
              </View>
            )}
          </View>

          <View style={{
            backgroundColor: isTimerRunning ? '#fef3c7' : '#f8fafc',
            padding: 16,
            borderRadius: 12,
            borderWidth: isTimerRunning ? 1 : 0,
            borderColor: isTimerRunning ? '#f59e0b' : 'transparent',
          }}>
            <Text style={{
              fontSize: 14,
              color: isTimerRunning ? '#92400e' : '#6b7280',
              textAlign: 'center',
              lineHeight: 20,
            }}>
              {isTimerRunning
                ? '⏱️ Timer is running. Mark as picked up when ready.'
                : '🏪 Start timer when you arrive at the restaurant.'
              }
            </Text>
          </View>
        </Card>
      </Animated.View>
    );
  };

  const renderActionButtons = () => {
    if (!order) return null;

    if (!hasArrivedAtRestaurant) {
      return (
        <View style={{ padding: 20 }}>
          <Button
            title="Mark as Arrived at Restaurant"
            leftIcon="location"
            onPress={handleArrivedAtRestaurant}
            fullWidth
            size="lg"
          />
        </View>
      );
    }

    return (
      <View style={{ padding: 20 }}>
        {/* Primary Action Button */}
        {!isTimerRunning ? (
          <TouchableOpacity
            onPress={handleStartPickupTimer}
            style={{
              marginBottom: 12,
            }}
          >
            <LinearGradient
              colors={['#f59e0b', '#d97706']}
              style={{
                borderRadius: 16,
                padding: 16,
                flexDirection: 'row',
                alignItems: 'center',
                justifyContent: 'center',
              }}
            >
              <Ionicons name="timer" size={20} color="white" style={{ marginRight: 8 }} />
              <Text style={{ color: 'white', fontSize: 16, fontWeight: '600' }}>
                Start Pickup Timer
              </Text>
            </LinearGradient>
          </TouchableOpacity>
        ) : (
          <TouchableOpacity
            onPress={handlePickedUp}
            style={{
              marginBottom: 12,
            }}
          >
            <LinearGradient
              colors={['#10b981', '#059669']}
              style={{
                borderRadius: 16,
                padding: 16,
                flexDirection: 'row',
                alignItems: 'center',
                justifyContent: 'center',
              }}
            >
              <Ionicons name="checkmark-circle" size={20} color="white" style={{ marginRight: 8 }} />
              <Text style={{ color: 'white', fontSize: 16, fontWeight: '600' }}>
                Mark as Picked Up
              </Text>
            </LinearGradient>
          </TouchableOpacity>
        )}

        {/* Photo Upload Button */}
        <TouchableOpacity
          onPress={() => setShowPhotoUpload(true)}
          style={{
            backgroundColor: 'white',
            borderRadius: 16,
            padding: 16,
            flexDirection: 'row',
            alignItems: 'center',
            justifyContent: 'center',
            marginBottom: 12,
            borderWidth: 2,
            borderColor: foodPhoto ? '#10b981' : '#e5e7eb',
          }}
        >
          <Ionicons
            name={foodPhoto ? "checkmark-circle" : "camera"}
            size={20}
            color={foodPhoto ? '#10b981' : '#6b7280'}
            style={{ marginRight: 8 }}
          />
          <Text style={{
            color: foodPhoto ? '#10b981' : '#6b7280',
            fontSize: 16,
            fontWeight: '600'
          }}>
            {foodPhoto ? 'Photo Captured ✓' : 'Upload Food Photo'}
          </Text>
        </TouchableOpacity>
      </View>
    );
  };

  if (!order) {
    return (
      <SafeAreaView style={{ flex: 1, backgroundColor: '#f8fafc' }}>
        {renderHeader()}
        <View style={{
          flex: 1,
          justifyContent: 'center',
          alignItems: 'center',
        }}>
          <Text style={{
            fontSize: 18,
            color: '#6b7280',
          }}>
            Order not found
          </Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={{ flex: 1, backgroundColor: '#f8fafc' }}>
      {renderHeader()}
      
      <ScrollView
        style={{ flex: 1 }}
        showsVerticalScrollIndicator={false}
      >
        {renderRestaurantInfo()}
        {renderPickupTimer()}

        {/* Bottom spacing */}
        <View style={{ height: 100 }} />
      </ScrollView>

      {renderActionButtons()}

      {/* QR Code Modal */}
      <Modal
        visible={showQRCode}
        animationType="slide"
        presentationStyle="pageSheet"
        onRequestClose={() => setShowQRCode(false)}
      >
        <SafeAreaView style={{ flex: 1, backgroundColor: '#f8fafc' }}>
          <LinearGradient
            colors={['#10b981', '#059669']}
            style={{ paddingVertical: 16, paddingHorizontal: 20 }}
          >
            <View style={{ flexDirection: 'row', alignItems: 'center', justifyContent: 'space-between' }}>
              <View style={{ flexDirection: 'row', alignItems: 'center' }}>
                <Ionicons name="qr-code" size={24} color="white" />
                <Text style={{ fontSize: 20, fontWeight: 'bold', color: 'white', marginLeft: 12 }}>
                  Restaurant QR Code
                </Text>
              </View>
              <TouchableOpacity
                onPress={() => setShowQRCode(false)}
                style={{
                  backgroundColor: 'rgba(255,255,255,0.2)',
                  borderRadius: 20,
                  padding: 8,
                }}
              >
                <Ionicons name="close" size={20} color="white" />
              </TouchableOpacity>
            </View>
          </LinearGradient>

          <View style={{ flex: 1, alignItems: 'center', justifyContent: 'center', padding: 40 }}>
            <Animated.View style={{
              transform: [{ scale: qrPulseAnimation }],
              backgroundColor: 'white',
              padding: 20,
              borderRadius: 20,
              shadowColor: '#000',
              shadowOffset: { width: 0, height: 4 },
              shadowOpacity: 0.1,
              shadowRadius: 8,
              elevation: 5,
            }}>
              <QRCode
                value={`restaurant:${order?.restaurant.id}:order:${order?.id}`}
                size={200}
                color="#111827"
                backgroundColor="white"
              />
            </Animated.View>

            <Text style={{
              fontSize: 16,
              color: '#6b7280',
              textAlign: 'center',
              marginTop: 24,
              lineHeight: 24,
            }}>
              Show this QR code to the restaurant staff for quick order identification
            </Text>
          </View>
        </SafeAreaView>
      </Modal>

      {/* Photo Upload Modal */}
      <Modal
        visible={showPhotoUpload}
        animationType="slide"
        presentationStyle="pageSheet"
        onRequestClose={() => setShowPhotoUpload(false)}
      >
        <SafeAreaView style={{ flex: 1, backgroundColor: '#f8fafc' }}>
          <LinearGradient
            colors={['#3b82f6', '#2563eb']}
            style={{ paddingVertical: 16, paddingHorizontal: 20 }}
          >
            <View style={{ flexDirection: 'row', alignItems: 'center', justifyContent: 'space-between' }}>
              <View style={{ flexDirection: 'row', alignItems: 'center' }}>
                <Ionicons name="camera" size={24} color="white" />
                <Text style={{ fontSize: 20, fontWeight: 'bold', color: 'white', marginLeft: 12 }}>
                  Upload Food Photo
                </Text>
              </View>
              <TouchableOpacity
                onPress={() => setShowPhotoUpload(false)}
                style={{
                  backgroundColor: 'rgba(255,255,255,0.2)',
                  borderRadius: 20,
                  padding: 8,
                }}
              >
                <Ionicons name="close" size={20} color="white" />
              </TouchableOpacity>
            </View>
          </LinearGradient>

          <View style={{ flex: 1, padding: 20 }}>
            {foodPhoto ? (
              <View style={{ flex: 1, alignItems: 'center', justifyContent: 'center' }}>
                <Image
                  source={{ uri: foodPhoto }}
                  style={{
                    width: 300,
                    height: 225,
                    borderRadius: 16,
                    marginBottom: 20,
                  }}
                  resizeMode="cover"
                />
                <Text style={{ fontSize: 16, color: '#10b981', fontWeight: '600', marginBottom: 20 }}>
                  ✓ Photo captured successfully!
                </Text>
                <TouchableOpacity
                  onPress={handleTakePhoto}
                  style={{
                    backgroundColor: '#3b82f6',
                    borderRadius: 12,
                    padding: 16,
                    flexDirection: 'row',
                    alignItems: 'center',
                  }}
                >
                  <Ionicons name="camera" size={20} color="white" style={{ marginRight: 8 }} />
                  <Text style={{ color: 'white', fontSize: 16, fontWeight: '600' }}>
                    Take Another Photo
                  </Text>
                </TouchableOpacity>
              </View>
            ) : (
              <View style={{ flex: 1, alignItems: 'center', justifyContent: 'center' }}>
                <View style={{
                  width: 120,
                  height: 120,
                  borderRadius: 60,
                  backgroundColor: '#e5e7eb',
                  alignItems: 'center',
                  justifyContent: 'center',
                  marginBottom: 24,
                }}>
                  <Ionicons name="camera" size={48} color="#6b7280" />
                </View>

                <Text style={{ fontSize: 18, fontWeight: '600', color: '#111827', marginBottom: 8 }}>
                  Take Food Photo
                </Text>
                <Text style={{ fontSize: 14, color: '#6b7280', textAlign: 'center', marginBottom: 32 }}>
                  Take a photo of the food for quality assurance and delivery proof
                </Text>

                <TouchableOpacity
                  onPress={handleTakePhoto}
                  style={{
                    backgroundColor: '#3b82f6',
                    borderRadius: 16,
                    padding: 16,
                    flexDirection: 'row',
                    alignItems: 'center',
                    minWidth: 200,
                    justifyContent: 'center',
                  }}
                >
                  <Ionicons name="camera" size={20} color="white" style={{ marginRight: 8 }} />
                  <Text style={{ color: 'white', fontSize: 16, fontWeight: '600' }}>
                    Take Photo
                  </Text>
                </TouchableOpacity>
              </View>
            )}
          </View>
        </SafeAreaView>
      </Modal>
    </SafeAreaView>
  );
};

export default PickupFlowScreen;
