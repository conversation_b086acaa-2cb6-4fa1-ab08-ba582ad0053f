import React from 'react';
import { createStackNavigator } from '@react-navigation/stack';
import { OrderStackParamList } from '../types';

// Order workflow screens
import OrderListScreen from '../screens/orders/OrderListScreen';
import OrderDetailsScreen from '../screens/orders/OrderDetailsScreen';
import OrderHistoryScreen from '../screens/orders/OrderHistoryScreen';
import PickupFlowScreen from '../screens/orders/PickupFlowScreen';
import DeliveryFlowScreen from '../screens/orders/DeliveryFlowScreen';
import TripSummaryScreen from '../screens/orders/TripSummaryScreen';

const Stack = createStackNavigator<OrderStackParamList>();

const OrderNavigator: React.FC = () => {
  return (
    <Stack.Navigator
      id={"OrderStack" as any}
      screenOptions={{
        headerShown: false,
      }}
    >
      <Stack.Screen name="OrderList" component={OrderListScreen} />
      <Stack.Screen name="OrderDetails" component={OrderDetailsScreen} />
      <Stack.Screen name="OrderHistory" component={OrderHistoryScreen} />
      <Stack.Screen name="PickupFlow" component={PickupFlowScreen} />
      <Stack.Screen name="DeliveryFlow" component={DeliveryFlowScreen} />
      <Stack.Screen name="TripSummary" component={TripSummaryScreen} />
    </Stack.Navigator>
  );
};

export default OrderNavigator;
