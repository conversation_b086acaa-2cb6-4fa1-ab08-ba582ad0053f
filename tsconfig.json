{"extends": "expo/tsconfig.base", "compilerOptions": {"strict": false, "skipLibCheck": true, "noImplicitAny": false, "types": ["nativewind/types"], "moduleResolution": "bundler", "allowSyntheticDefaultImports": true, "esModuleInterop": true, "jsx": "react-jsx", "noImplicitReturns": false, "noUnusedLocals": false, "noUnusedParameters": false}, "include": ["**/*.ts", "**/*.tsx", "nativewind-env.d.ts"], "exclude": ["node_modules"]}