import React from 'react';
import { createStackNavigator } from '@react-navigation/stack';
import { SupportStackParamList } from '../types';

// Support Screens
import SupportMainScreen from '../screens/support/SupportMainScreen';
import LiveChatScreen from '../screens/support/LiveChatScreen';
import HelpCenterScreen from '../screens/support/HelpCenterScreen';
import HelpCenterMainScreen from '../screens/help/HelpCenterMainScreen';
import CallSupportScreen from '../screens/support/CallSupportScreen';
import ReportIssueScreen from '../screens/support/ReportIssueScreen';
import AppFeedbackScreen from '../screens/support/AppFeedbackScreen';

const Stack = createStackNavigator<SupportStackParamList>();

const SupportNavigator: React.FC = () => {
  return (
    <Stack.Navigator
      screenOptions={{
        headerShown: false,
        cardStyle: { backgroundColor: '#f9fafb' },
      }}
    >
      <Stack.Screen
        name="SupportMain"
        component={SupportMainScreen}
        options={{
          title: 'Support & Help',
        }}
      />
      
      <Stack.Screen
        name="LiveChat"
        component={LiveChatScreen}
        options={{
          title: 'Live Chat',
        }}
      />
      
      <Stack.Screen
        name="HelpCenter"
        component={HelpCenterMainScreen}
        options={{
          title: 'Help Center',
        }}
      />

      <Stack.Screen
        name="HelpCenterLegacy"
        component={HelpCenterScreen}
        options={{
          title: 'Help Center (Legacy)',
        }}
      />
      
      <Stack.Screen
        name="CallSupport"
        component={CallSupportScreen}
        options={{
          title: 'Call Support',
        }}
      />
      
      <Stack.Screen
        name="ReportIssue"
        component={ReportIssueScreen}
        options={{
          title: 'Report Issue',
        }}
      />
      
      <Stack.Screen
        name="AppFeedback"
        component={AppFeedbackScreen}
        options={{
          title: 'App Feedback',
        }}
      />
    </Stack.Navigator>
  );
};

export default SupportNavigator;
