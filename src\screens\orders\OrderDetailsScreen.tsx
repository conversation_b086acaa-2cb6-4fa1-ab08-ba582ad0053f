import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  Alert,
  Linking,
  Dimensions,
  Modal,
  Animated,
  ImageBackground,
  StatusBar,
  Vibration,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import { LinearGradient } from 'expo-linear-gradient';
import { BlurView } from 'expo-blur';
import { useNavigation, useRoute } from '@react-navigation/native';
import { Card, Button, Badge } from '../../components/ui';
import { useOrders } from '../../context/OrderContext';
import { Order, OrderStatus } from '../../types/orders';
import { formatCurrency, formatDistance, formatDuration, formatPhoneNumber } from '../../utils/helpers';
import OrderTrackingMap from '../../components/maps/OrderTrackingMap';
import DeliveryProofModal from '../../components/orders/DeliveryProofModal';

const { width, height } = Dimensions.get('window');

const OrderDetailsScreen: React.FC = () => {
  const navigation = useNavigation();
  const route = useRoute();
  const { state, updateOrderStatus, startPickupTimer, markAsPickedUp, markAsDelivered } = useOrders();
  const [order, setOrder] = useState<Order | null>(null);
  const [pickupTimer, setPickupTimer] = useState(0);
  const [isTimerRunning, setIsTimerRunning] = useState(false);
  const [showMap, setShowMap] = useState(false);
  const [showDeliveryProof, setShowDeliveryProof] = useState(false);
  const [cannotDeliverReason, setCannotDeliverReason] = useState('');
  const [showReportIssue, setShowReportIssue] = useState(false);

  // Animation refs
  const fadeAnimation = useRef(new Animated.Value(0)).current;
  const slideAnimation = useRef(new Animated.Value(50)).current;
  const timerPulseAnimation = useRef(new Animated.Value(1)).current;

  const orderId = (route.params as any)?.orderId;

  useEffect(() => {
    if (orderId) {
      // Find order in available or accepted orders
      const foundOrder =
        state.availableOrders.find(o => o.id === orderId) ||
        state.acceptedOrders.find(o => o.id === orderId) ||
        state.orderHistory.find(o => o.id === orderId);

      setOrder(foundOrder || null);

      // Start entrance animations
      if (foundOrder) {
        Animated.parallel([
          Animated.timing(fadeAnimation, {
            toValue: 1,
            duration: 800,
            useNativeDriver: true,
          }),
          Animated.timing(slideAnimation, {
            toValue: 0,
            duration: 600,
            useNativeDriver: true,
          }),
        ]).start();
      }
    }
  }, [orderId, state]);

  // Pickup timer effect
  useEffect(() => {
    let interval: NodeJS.Timeout;
    if (isTimerRunning) {
      interval = setInterval(() => {
        setPickupTimer(prev => prev + 1);
      }, 1000);

      // Start timer pulse animation
      Animated.loop(
        Animated.sequence([
          Animated.timing(timerPulseAnimation, {
            toValue: 1.1,
            duration: 1000,
            useNativeDriver: true,
          }),
          Animated.timing(timerPulseAnimation, {
            toValue: 1,
            duration: 1000,
            useNativeDriver: true,
          }),
        ])
      ).start();
    } else {
      timerPulseAnimation.setValue(1);
    }
    return () => {
      if (interval) clearInterval(interval);
    };
  }, [isTimerRunning]);

  const handleStatusUpdate = async (newStatus: OrderStatus) => {
    if (!order) return;

    try {
      await updateOrderStatus(order.id, newStatus);
      setOrder(prev => prev ? { ...prev, status: newStatus } : null);

      if (newStatus === OrderStatus.DELIVERED) {
        Alert.alert('Success', 'Order marked as delivered!', [
          { text: 'OK', onPress: () => navigation.goBack() }
        ]);
      }
    } catch (error: any) {
      Alert.alert('Error', error.message);
    }
  };

  const handleArrivedAtRestaurant = async () => {
    if (!order) return;

    try {
      await updateOrderStatus(order.id, OrderStatus.ARRIVED_AT_RESTAURANT);
      setOrder(prev => prev ? { ...prev, status: OrderStatus.ARRIVED_AT_RESTAURANT } : null);
      Alert.alert('Status Updated', 'Marked as arrived at restaurant');
    } catch (error: any) {
      Alert.alert('Error', error.message);
    }
  };

  const handleStartPickupTimer = () => {
    if (!order) return;

    setIsTimerRunning(true);
    Alert.alert('Timer Started', 'Pickup wait time tracking started');
  };

  const handlePickedUp = async () => {
    if (!order) return;

    try {
      setIsTimerRunning(false);
      await markAsPickedUp(order.id, pickupTimer);
      setOrder(prev => prev ? { ...prev, status: OrderStatus.PICKED_UP } : null);
      Alert.alert('Order Picked Up', 'Ready for delivery!');
    } catch (error: any) {
      Alert.alert('Error', error.message);
    }
  };

  const handleStartNavigation = () => {
    if (!order) return;

    const { lat, lng } = order.deliveryAddress.coordinates;
    const url = `https://www.google.com/maps/dir/?api=1&destination=${lat},${lng}&travelmode=driving`;

    Linking.openURL(url).catch(() => {
      Alert.alert('Error', 'Could not open navigation app');
    });
  };

  const handleMarkAsDelivered = () => {
    setShowDeliveryProof(true);
  };

  const handleDeliveryConfirmed = async (proofData: any) => {
    if (!order) return;

    try {
      await markAsDelivered(order.id, proofData);
      setOrder(prev => prev ? { ...prev, status: OrderStatus.DELIVERED } : null);
      setShowDeliveryProof(false);
      Alert.alert('Success', 'Order marked as delivered!', [
        { text: 'OK', onPress: () => navigation.goBack() }
      ]);
    } catch (error: any) {
      Alert.alert('Error', error.message);
    }
  };

  const handleCannotDeliver = () => {
    Alert.alert(
      'Cannot Deliver',
      'Please select a reason:',
      [
        { text: 'Customer not available', onPress: () => reportCannotDeliver('Customer not available') },
        { text: 'Wrong address', onPress: () => reportCannotDeliver('Wrong address') },
        { text: 'Customer refused order', onPress: () => reportCannotDeliver('Customer refused order') },
        { text: 'Other', onPress: () => reportCannotDeliver('Other') },
        { text: 'Cancel', style: 'cancel' },
      ]
    );
  };

  const reportCannotDeliver = async (reason: string) => {
    if (!order) return;

    try {
      await updateOrderStatus(order.id, OrderStatus.CANCELLED);
      setOrder(prev => prev ? { ...prev, status: OrderStatus.CANCELLED } : null);
      Alert.alert('Order Cancelled', `Reason: ${reason}`, [
        { text: 'OK', onPress: () => navigation.goBack() }
      ]);
    } catch (error: any) {
      Alert.alert('Error', error.message);
    }
  };

  const handleCall = (phoneNumber: string, type: 'customer' | 'restaurant') => {
    Alert.alert(
      `Call ${type === 'customer' ? 'Customer' : 'Restaurant'}`,
      `Do you want to call ${formatPhoneNumber(phoneNumber)}?`,
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Call',
          onPress: () => Linking.openURL(`tel:${phoneNumber}`)
        }
      ]
    );
  };

  const handleNavigate = (address: any) => {
    const url = `https://maps.google.com/?q=${address.latitude},${address.longitude}`;
    Linking.openURL(url);
  };

  const handleCallRestaurant = () => {
    if (!order) return;
    handleCall(order.restaurant.phone, 'restaurant');
  };

  const handleCallCustomer = () => {
    if (!order) return;
    handleCall(order.customer.phone, 'customer');
  };

  const formatTimer = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  };

  // Get food icon based on item name
  const getFoodIcon = (itemName: string): string => {
    const name = itemName.toLowerCase();
    if (name.includes('pizza')) return '🍕';
    if (name.includes('burger') || name.includes('sandwich')) return '🍔';
    if (name.includes('chicken') || name.includes('wings')) return '🍗';
    if (name.includes('fries') || name.includes('chips')) return '🍟';
    if (name.includes('drink') || name.includes('soda') || name.includes('pepsi') || name.includes('coke')) return '🥤';
    if (name.includes('coffee')) return '☕';
    if (name.includes('tea')) return '🍵';
    if (name.includes('ice cream') || name.includes('dessert')) return '🍦';
    if (name.includes('salad')) return '🥗';
    if (name.includes('pasta') || name.includes('spaghetti')) return '🍝';
    if (name.includes('rice') || name.includes('biryani')) return '🍚';
    if (name.includes('noodles')) return '🍜';
    if (name.includes('taco')) return '🌮';
    if (name.includes('wrap')) return '🌯';
    if (name.includes('donut')) return '🍩';
    if (name.includes('cake')) return '🍰';
    return '🍽️'; // Default food icon
  };

  // Enhanced map preview component
  const renderMapPreview = () => {
    if (!order) return null;

    return (
      <Animated.View style={{
        opacity: fadeAnimation,
        transform: [{ translateY: slideAnimation }],
      }}>
        <Card variant="elevated" margin="md" padding="none">
          <View style={{ height: 200, borderRadius: 16, overflow: 'hidden' }}>
            <ImageBackground
              source={{
                uri: 'https://images.unsplash.com/photo-1524661135-423995f22d0b?w=800&h=400&fit=crop'
              }}
              style={{ flex: 1 }}
              blurRadius={1}
            >
              <BlurView intensity={15} style={{ flex: 1, padding: 16 }}>
                {/* Route Line Simulation */}
                <View style={{
                  position: 'absolute',
                  top: 80,
                  left: 60,
                  right: 60,
                  height: 3,
                  backgroundColor: '#3b82f6',
                  borderRadius: 2,
                  opacity: 0.8,
                }} />

                {/* Pickup Pin (Restaurant) */}
                <View style={{
                  position: 'absolute',
                  top: 60,
                  left: 40,
                  alignItems: 'center',
                }}>
                  <View style={{
                    width: 40,
                    height: 40,
                    borderRadius: 20,
                    backgroundColor: '#10b981',
                    alignItems: 'center',
                    justifyContent: 'center',
                    shadowColor: '#000',
                    shadowOffset: { width: 0, height: 2 },
                    shadowOpacity: 0.3,
                    shadowRadius: 4,
                    elevation: 5,
                  }}>
                    <Ionicons name="restaurant" size={20} color="white" />
                  </View>
                  <Text style={{
                    fontSize: 10,
                    fontWeight: 'bold',
                    color: '#111827',
                    marginTop: 4,
                    textAlign: 'center',
                    backgroundColor: 'rgba(255,255,255,0.9)',
                    paddingHorizontal: 6,
                    paddingVertical: 2,
                    borderRadius: 4,
                  }}>
                    Pickup
                  </Text>
                </View>

                {/* Delivery Pin (Customer) */}
                <View style={{
                  position: 'absolute',
                  top: 60,
                  right: 40,
                  alignItems: 'center',
                }}>
                  <View style={{
                    width: 40,
                    height: 40,
                    borderRadius: 20,
                    backgroundColor: '#dc2626',
                    alignItems: 'center',
                    justifyContent: 'center',
                    shadowColor: '#000',
                    shadowOffset: { width: 0, height: 2 },
                    shadowOpacity: 0.3,
                    shadowRadius: 4,
                    elevation: 5,
                  }}>
                    <Ionicons name="home" size={20} color="white" />
                  </View>
                  <Text style={{
                    fontSize: 10,
                    fontWeight: 'bold',
                    color: '#111827',
                    marginTop: 4,
                    textAlign: 'center',
                    backgroundColor: 'rgba(255,255,255,0.9)',
                    paddingHorizontal: 6,
                    paddingVertical: 2,
                    borderRadius: 4,
                  }}>
                    Drop-off
                  </Text>
                </View>

                {/* Distance and ETA Info */}
                <View style={{
                  position: 'absolute',
                  bottom: 16,
                  left: 16,
                  right: 16,
                  backgroundColor: 'rgba(0,0,0,0.8)',
                  borderRadius: 12,
                  padding: 12,
                  flexDirection: 'row',
                  justifyContent: 'space-between',
                }}>
                  <View style={{ alignItems: 'center' }}>
                    <Text style={{ color: 'white', fontSize: 16, fontWeight: 'bold' }}>
                      {formatDistance(order.estimatedDistance)}
                    </Text>
                    <Text style={{ color: 'rgba(255,255,255,0.8)', fontSize: 12 }}>
                      Distance
                    </Text>
                  </View>
                  <View style={{ alignItems: 'center' }}>
                    <Text style={{ color: 'white', fontSize: 16, fontWeight: 'bold' }}>
                      {formatDuration(order.estimatedDuration)}
                    </Text>
                    <Text style={{ color: 'rgba(255,255,255,0.8)', fontSize: 12 }}>
                      ETA
                    </Text>
                  </View>
                </View>

                {/* Full Map Button */}
                <TouchableOpacity
                  onPress={() => setShowMap(true)}
                  style={{
                    position: 'absolute',
                    top: 16,
                    right: 16,
                    backgroundColor: 'rgba(255,255,255,0.9)',
                    borderRadius: 20,
                    padding: 8,
                  }}
                >
                  <Ionicons name="expand" size={16} color="#374151" />
                </TouchableOpacity>
              </BlurView>
            </ImageBackground>
          </View>
        </Card>
      </Animated.View>
    );
  };

  const getStatusColor = (status: OrderStatus) => {
    switch (status) {
      case OrderStatus.AVAILABLE:
        return '#3b82f6';
      case OrderStatus.ACCEPTED:
        return '#ef4444';
      case OrderStatus.PICKED_UP:
        return '#8b5cf6';
      case OrderStatus.DELIVERED:
        return '#10b981';
      default:
        return '#6b7280';
    }
  };

  const getNextStatusAction = () => {
    if (!order) return null;

    switch (order.status) {
      case OrderStatus.ACCEPTED:
        return {
          title: 'Mark as Picked Up',
          status: OrderStatus.PICKED_UP,
          icon: 'checkmark-circle-outline'
        };
      case OrderStatus.PICKED_UP:
        return {
          title: 'Mark as Delivered',
          status: OrderStatus.DELIVERED,
          icon: 'checkmark-done-outline'
        };
      default:
        return null;
    }
  };

  const renderWorkflowActions = () => {
    if (!order) return null;

    switch (order.status) {
      case OrderStatus.ACCEPTED:
        return (
          <View style={{ padding: 20 }}>
            <Button
              title="Arrived at Restaurant"
              leftIcon="location"
              onPress={handleArrivedAtRestaurant}
              fullWidth
              size="lg"
              style={{ marginBottom: 12 }}
            />
            <Button
              title="View on Map"
              variant="outline"
              leftIcon="map"
              onPress={() => setShowMap(true)}
              fullWidth
              size="lg"
            />
          </View>
        );

      case OrderStatus.ARRIVED_AT_RESTAURANT:
        return (
          <View style={{ padding: 20 }}>
            {isTimerRunning && (
              <Card variant="elevated" padding="md" style={{ marginBottom: 16 }}>
                <View style={{
                  flexDirection: 'row',
                  alignItems: 'center',
                  justifyContent: 'center',
                }}>
                  <Ionicons name="timer" size={20} color="#f59e0b" />
                  <Text style={{
                    fontSize: 18,
                    fontWeight: 'bold',
                    color: '#f59e0b',
                    marginLeft: 8,
                  }}>
                    Wait Time: {formatTimer(pickupTimer)}
                  </Text>
                </View>
              </Card>
            )}

            {!isTimerRunning ? (
              <Button
                title="Start Pickup Timer"
                leftIcon="timer"
                onPress={handleStartPickupTimer}
                fullWidth
                size="lg"
                style={{ marginBottom: 12 }}
              />
            ) : (
              <Button
                title="Mark as Picked Up"
                leftIcon="checkmark-circle"
                onPress={handlePickedUp}
                fullWidth
                size="lg"
                style={{ marginBottom: 12 }}
              />
            )}

            <Button
              title="Call Restaurant"
              variant="outline"
              leftIcon="call"
              onPress={handleCallRestaurant}
              fullWidth
              size="lg"
            />
          </View>
        );

      case OrderStatus.PICKED_UP:
        return (
          <View style={{ padding: 20 }}>
            <Button
              title="Start Navigation"
              leftIcon="navigate"
              onPress={handleStartNavigation}
              fullWidth
              size="lg"
              style={{ marginBottom: 12 }}
            />
            <Button
              title="Mark as Delivered"
              leftIcon="checkmark-circle"
              onPress={handleMarkAsDelivered}
              fullWidth
              size="lg"
              style={{ marginBottom: 12 }}
            />
            <View style={{
              flexDirection: 'row',
              justifyContent: 'space-between',
            }}>
              <Button
                title="Call Customer"
                variant="outline"
                leftIcon="call"
                onPress={handleCallCustomer}
                style={{ flex: 1, marginRight: 8 }}
                size="lg"
              />
              <Button
                title="Cannot Deliver"
                variant="outline"
                leftIcon="close-circle"
                onPress={handleCannotDeliver}
                style={{ flex: 1, marginLeft: 8 }}
                size="lg"
              />
            </View>
          </View>
        );

      case OrderStatus.DELIVERED:
        return (
          <View style={{ padding: 20 }}>
            <Card variant="elevated" padding="lg">
              <View style={{
                alignItems: 'center',
              }}>
                <Ionicons name="checkmark-circle" size={48} color="#10b981" />
                <Text style={{
                  fontSize: 18,
                  fontWeight: 'bold',
                  color: '#10b981',
                  marginTop: 8,
                }}>
                  Order Delivered Successfully!
                </Text>
                <Text style={{
                  fontSize: 14,
                  color: '#6b7280',
                  textAlign: 'center',
                  marginTop: 4,
                }}>
                  Great job! You've completed this delivery.
                </Text>
              </View>
            </Card>
          </View>
        );

      default:
        return null;
    }
  };

  const renderHeader = () => (
    <View style={{
      backgroundColor: '#dc2626',
      paddingHorizontal: 20,
      paddingTop: (StatusBar.currentHeight || 0) + 20,
      paddingBottom: 28,
      borderBottomLeftRadius: 28,
      borderBottomRightRadius: 28,
      shadowColor: '#dc2626',
      shadowOffset: { width: 0, height: 12 },
      shadowOpacity: 0.3,
      shadowRadius: 20,
      elevation: 16,
    }}>
      <View style={{
        flexDirection: 'row',
        alignItems: 'center',
        marginBottom: 20,
      }}>
        <TouchableOpacity
          onPress={() => navigation.goBack()}
          style={{
            width: 48,
            height: 48,
            borderRadius: 24,
            backgroundColor: 'rgba(255,255,255,0.2)',
            justifyContent: 'center',
            alignItems: 'center',
            marginRight: 16,
            borderWidth: 2,
            borderColor: 'rgba(255,255,255,0.3)',
          }}
        >
          <Ionicons name="arrow-back" size={24} color="white" />
        </TouchableOpacity>

        <View style={{ flex: 1 }}>
          <Text style={{ fontSize: 24, fontWeight: 'bold', color: 'white', marginBottom: 4 }}>
            Order Details
          </Text>
          {order && (
            <View style={{ flexDirection: 'row', alignItems: 'center' }}>
              <Text style={{ fontSize: 16, color: 'rgba(255,255,255,0.9)', marginRight: 16, fontWeight: '600' }}>
                #{order.orderNumber}
              </Text>
              {isTimerRunning && (
                <Animated.View style={{
                  transform: [{ scale: timerPulseAnimation }],
                  flexDirection: 'row',
                  alignItems: 'center',
                  backgroundColor: 'rgba(255,255,255,0.2)',
                  paddingHorizontal: 12,
                  paddingVertical: 6,
                  borderRadius: 16,
                  borderWidth: 2,
                  borderColor: 'rgba(255,255,255,0.3)',
                }}>
                  <Ionicons name="timer" size={16} color="white" />
                  <Text style={{
                    fontSize: 14,
                    fontWeight: 'bold',
                    color: 'white',
                    marginLeft: 6,
                  }}>
                    {formatTimer(pickupTimer)}
                  </Text>
                </Animated.View>
              )}
            </View>
          )}
        </View>

        {order && (
          <View style={{
            backgroundColor: order.status === OrderStatus.DELIVERED ? '#10b981' : '#fbbf24',
            paddingHorizontal: 12,
            paddingVertical: 6,
            borderRadius: 16,
          }}>
            <Text style={{
              fontSize: 12,
              fontWeight: 'bold',
              color: 'white',
              textTransform: 'capitalize',
            }}>
              {order.status.replace('_', ' ')}
            </Text>
          </View>
        )}
      </View>
    </View>
  );

  if (!order) {
    return (
      <SafeAreaView style={{ flex: 1, backgroundColor: '#f8fafc' }}>
        {renderHeader()}
        <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center' }}>
          <Text style={{ fontSize: 16, color: '#6b7280' }}>Order not found</Text>
        </View>
      </SafeAreaView>
    );
  }

  const nextAction = getNextStatusAction();

  return (
    <SafeAreaView style={{ flex: 1, backgroundColor: '#f8fafc' }}>
      {renderHeader()}

      <ScrollView style={{ flex: 1 }} showsVerticalScrollIndicator={false}>
        {/* Enhanced Order Summary */}
        <Animated.View style={{
          opacity: fadeAnimation,
          transform: [{ translateY: slideAnimation }],
          marginTop: -12,
        }}>
          <Card variant="elevated" margin="md" padding="lg" style={{
            backgroundColor: 'white',
            borderRadius: 20,
            shadowColor: '#dc2626',
            shadowOffset: { width: 0, height: 8 },
            shadowOpacity: 0.1,
            shadowRadius: 16,
            elevation: 8,
            borderWidth: 1,
            borderColor: 'rgba(220, 38, 38, 0.05)',
          }}>
            <View style={{ flexDirection: 'row', alignItems: 'center', marginBottom: 20 }}>
              <View style={{
                width: 56,
                height: 56,
                borderRadius: 28,
                backgroundColor: '#dc2626',
                alignItems: 'center',
                justifyContent: 'center',
                marginRight: 16,
                shadowColor: '#dc2626',
                shadowOffset: { width: 0, height: 6 },
                shadowOpacity: 0.3,
                shadowRadius: 12,
                elevation: 8,
              }}>
                <Ionicons name="analytics" size={24} color="white" />
              </View>
              <View>
                <Text style={{ fontSize: 20, fontWeight: 'bold', color: '#111827' }}>
                  Order Summary
                </Text>
                <Text style={{ fontSize: 14, color: '#6b7280', fontWeight: '500' }}>
                  Earnings & Details
                </Text>
              </View>
            </View>

            <View style={{
              backgroundColor: '#10b981',
              flexDirection: 'row',
              justifyContent: 'space-between',
              alignItems: 'center',
              marginBottom: 20,
              paddingVertical: 20,
              paddingHorizontal: 24,
              borderRadius: 20,
              shadowColor: '#10b981',
              shadowOffset: { width: 0, height: 8 },
              shadowOpacity: 0.3,
              shadowRadius: 16,
              elevation: 12,
              borderWidth: 2,
              borderColor: '#d1fae5',
            }}>
              <View>
                <Text style={{ fontSize: 16, color: 'rgba(255,255,255,0.9)', fontWeight: 'bold', marginBottom: 4 }}>
                  💰 Your Earnings
                </Text>
                <Text style={{ fontSize: 32, fontWeight: 'bold', color: 'white' }}>
                  {formatCurrency(order.estimatedEarnings)}
                </Text>
              </View>
              <View style={{
                width: 64,
                height: 64,
                borderRadius: 32,
                backgroundColor: 'rgba(255,255,255,0.2)',
                alignItems: 'center',
                justifyContent: 'center',
                borderWidth: 2,
                borderColor: 'rgba(255,255,255,0.3)',
              }}>
                <Ionicons name="wallet" size={32} color="white" />
              </View>
            </View>

            <View style={{ flexDirection: 'row', justifyContent: 'space-between' }}>
              <View style={{ alignItems: 'center', flex: 1 }}>
                <Text style={{ fontSize: 16, fontWeight: 'bold', color: '#111827' }}>
                  {formatDistance(order.estimatedDistance)}
                </Text>
                <Text style={{ fontSize: 12, color: '#6b7280' }}>Distance</Text>
              </View>

              <View style={{ alignItems: 'center', flex: 1 }}>
                <Text style={{ fontSize: 16, fontWeight: 'bold', color: '#111827' }}>
                  {formatDuration(order.estimatedDuration)}
                </Text>
                <Text style={{ fontSize: 12, color: '#6b7280' }}>Duration</Text>
              </View>

              <View style={{ alignItems: 'center', flex: 1 }}>
                <Text style={{ fontSize: 16, fontWeight: 'bold', color: '#111827' }}>
                  {formatCurrency(order.total)}
                </Text>
                <Text style={{ fontSize: 12, color: '#6b7280' }}>Order Total</Text>
              </View>
            </View>
          </Card>
        </Animated.View>

        {/* Map Preview */}
        {renderMapPreview()}

        {/* Restaurant Info */}
        <Animated.View style={{
          opacity: fadeAnimation,
          transform: [{ translateY: slideAnimation }],
        }}>
          <Card variant="elevated" margin="md" padding="lg">
            <View style={{ flexDirection: 'row', justifyContent: 'space-between', alignItems: 'center', marginBottom: 16 }}>
              <View style={{ flexDirection: 'row', alignItems: 'center' }}>
                <View style={{
                  width: 40,
                  height: 40,
                  borderRadius: 20,
                  backgroundColor: '#10b981',
                  alignItems: 'center',
                  justifyContent: 'center',
                  marginRight: 12,
                }}>
                  <Ionicons name="restaurant" size={20} color="white" />
                </View>
                <View>
                  <Text style={{ fontSize: 18, fontWeight: 'bold', color: '#111827' }}>
                    Pickup Location
                  </Text>
                  <Text style={{ fontSize: 12, color: '#6b7280' }}>
                    Restaurant Details
                  </Text>
                </View>
              </View>
            </View>

            <Text style={{ fontSize: 16, fontWeight: '600', color: '#111827', marginBottom: 8 }}>
              {order.restaurant.name}
            </Text>

            <View style={{ flexDirection: 'row', alignItems: 'center', marginBottom: 16 }}>
              <Ionicons name="location-outline" size={16} color="#6b7280" />
              <Text style={{ fontSize: 14, color: '#6b7280', marginLeft: 8, flex: 1 }}>
                {order.pickupAddress.street}, {order.pickupAddress.city}, {order.pickupAddress.state} {order.pickupAddress.zipCode}
              </Text>
            </View>

            {/* Contact Buttons Row */}
            <View style={{ flexDirection: 'row', marginBottom: 16 }}>
              <TouchableOpacity
                onPress={() => handleCall(order.restaurant.phone, 'restaurant')}
                style={{
                  flex: 1,
                  flexDirection: 'row',
                  alignItems: 'center',
                  justifyContent: 'center',
                  backgroundColor: '#10b981',
                  paddingVertical: 12,
                  borderRadius: 12,
                  marginRight: 8,
                }}
              >
                <Animated.View style={{
                  transform: [{ scale: 1 }],
                }}>
                  <Ionicons name="call" size={18} color="white" />
                </Animated.View>
                <Text style={{ color: 'white', fontSize: 14, fontWeight: '600', marginLeft: 8 }}>
                  Call
                </Text>
              </TouchableOpacity>

              <TouchableOpacity
                onPress={() => {
                  Vibration.vibrate(50);
                  Alert.alert('Chat', 'Opening chat with restaurant...');
                }}
                style={{
                  flex: 1,
                  flexDirection: 'row',
                  alignItems: 'center',
                  justifyContent: 'center',
                  backgroundColor: '#3b82f6',
                  paddingVertical: 12,
                  borderRadius: 12,
                  marginLeft: 8,
                }}
              >
                <Animated.View style={{
                  transform: [{ scale: 1 }],
                }}>
                  <Ionicons name="chatbubble" size={18} color="white" />
                </Animated.View>
                <Text style={{ color: 'white', fontSize: 14, fontWeight: '600', marginLeft: 8 }}>
                  Chat
                </Text>
              </TouchableOpacity>
            </View>

            <Button
              title="Start Navigation"
              variant="outline"
              leftIcon="navigate-outline"
              onPress={() => handleNavigate(order.pickupAddress)}
              fullWidth
            />
          </Card>
        </Animated.View>

        {/* Enhanced Customer Info */}
        <Card variant="elevated" margin="md" padding="lg" style={{
          backgroundColor: 'white',
          borderRadius: 20,
          shadowColor: '#3b82f6',
          shadowOffset: { width: 0, height: 8 },
          shadowOpacity: 0.1,
          shadowRadius: 16,
          elevation: 8,
          borderWidth: 1,
          borderColor: 'rgba(59, 130, 246, 0.1)',
        }}>
          <View style={{ flexDirection: 'row', alignItems: 'center', marginBottom: 16 }}>
            <View style={{
              width: 56,
              height: 56,
              borderRadius: 28,
              backgroundColor: '#3b82f6',
              alignItems: 'center',
              justifyContent: 'center',
              marginRight: 16,
              shadowColor: '#3b82f6',
              shadowOffset: { width: 0, height: 6 },
              shadowOpacity: 0.3,
              shadowRadius: 12,
              elevation: 8,
            }}>
              <Ionicons name="person" size={24} color="white" />
            </View>
            <View style={{ flex: 1 }}>
              <Text style={{ fontSize: 20, fontWeight: 'bold', color: '#111827', marginBottom: 4 }}>
                Delivery Location
              </Text>
              <Text style={{ fontSize: 14, color: '#6b7280', fontWeight: '500' }}>
                Customer details
              </Text>
            </View>
            <TouchableOpacity
              onPress={() => handleCall(order.customer.phoneNumber, 'customer')}
              style={{
                backgroundColor: '#10b981',
                paddingHorizontal: 16,
                paddingVertical: 10,
                borderRadius: 16,
                flexDirection: 'row',
                alignItems: 'center',
                shadowColor: '#10b981',
                shadowOffset: { width: 0, height: 4 },
                shadowOpacity: 0.3,
                shadowRadius: 8,
                elevation: 6,
              }}
            >
              <Ionicons name="call" size={18} color="white" />
              <Text style={{ color: 'white', fontSize: 14, fontWeight: 'bold', marginLeft: 6 }}>
                Call
              </Text>
            </TouchableOpacity>
          </View>

          <Text style={{ fontSize: 18, fontWeight: 'bold', color: '#111827', marginBottom: 8 }}>
            {order.customer.firstName} {order.customer.lastName}
          </Text>

          <View style={{ flexDirection: 'row', alignItems: 'flex-start', marginBottom: 16 }}>
            <View style={{
              width: 32,
              height: 32,
              borderRadius: 16,
              backgroundColor: '#fef3c7',
              alignItems: 'center',
              justifyContent: 'center',
              marginRight: 12,
              marginTop: 2,
            }}>
              <Ionicons name="location" size={16} color="#f59e0b" />
            </View>
            <Text style={{ fontSize: 16, color: '#374151', flex: 1, lineHeight: 24 }}>
              {order.deliveryAddress.street}, {order.deliveryAddress.city}, {order.deliveryAddress.state} {order.deliveryAddress.zipCode}
            </Text>
          </View>

          <Button
            title="Navigate to Customer"
            variant="outline"
            leftIcon="navigate-outline"
            onPress={() => handleNavigate(order.deliveryAddress)}
            fullWidth
          />
        </Card>

        {/* Order Items */}
        <Animated.View style={{
          opacity: fadeAnimation,
          transform: [{ translateY: slideAnimation }],
        }}>
          <Card variant="elevated" margin="md" padding="lg">
            <View style={{ flexDirection: 'row', alignItems: 'center', marginBottom: 16 }}>
              <View style={{
                width: 40,
                height: 40,
                borderRadius: 20,
                backgroundColor: '#f97316',
                alignItems: 'center',
                justifyContent: 'center',
                marginRight: 12,
              }}>
                <Ionicons name="restaurant-outline" size={20} color="white" />
              </View>
              <View>
                <Text style={{ fontSize: 18, fontWeight: 'bold', color: '#111827' }}>
                  Order Items
                </Text>
                <Text style={{ fontSize: 12, color: '#6b7280' }}>
                  {order.items.length} item{order.items.length !== 1 ? 's' : ''}
                </Text>
              </View>
            </View>

            {order.items.map((item, index) => (
              <View key={item.id} style={{
                flexDirection: 'row',
                alignItems: 'center',
                paddingVertical: 12,
                paddingHorizontal: 16,
                marginBottom: index < order.items.length - 1 ? 12 : 0,
                backgroundColor: '#f8fafc',
                borderRadius: 12,
                borderLeftWidth: 4,
                borderLeftColor: '#f97316',
              }}>
                {/* Food Icon */}
                <View style={{
                  width: 48,
                  height: 48,
                  borderRadius: 24,
                  backgroundColor: 'white',
                  alignItems: 'center',
                  justifyContent: 'center',
                  marginRight: 12,
                  shadowColor: '#000',
                  shadowOffset: { width: 0, height: 1 },
                  shadowOpacity: 0.1,
                  shadowRadius: 2,
                  elevation: 2,
                }}>
                  <Text style={{ fontSize: 24 }}>
                    {getFoodIcon(item.name)}
                  </Text>
                </View>

                <View style={{ flex: 1 }}>
                  <Text style={{ fontSize: 16, fontWeight: '600', color: '#111827', marginBottom: 4 }}>
                    {item.name}
                  </Text>
                  <View style={{ flexDirection: 'row', alignItems: 'center' }}>
                    <View style={{
                      backgroundColor: '#f97316',
                      paddingHorizontal: 8,
                      paddingVertical: 2,
                      borderRadius: 8,
                      marginRight: 8,
                    }}>
                      <Text style={{ fontSize: 12, fontWeight: 'bold', color: 'white' }}>
                        Qty: {item.quantity}
                      </Text>
                    </View>
                    <Text style={{ fontSize: 12, color: '#6b7280' }}>
                      {formatCurrency(item.price)} each
                    </Text>
                  </View>
                </View>

                <View style={{ alignItems: 'flex-end' }}>
                  <Text style={{ fontSize: 18, fontWeight: 'bold', color: '#111827' }}>
                    {formatCurrency(item.price * item.quantity)}
                  </Text>
                </View>
              </View>
            ))}

            {/* Order Total */}
            <View style={{
              marginTop: 16,
              paddingTop: 16,
              borderTopWidth: 2,
              borderTopColor: '#e5e7eb',
              flexDirection: 'row',
              justifyContent: 'space-between',
              alignItems: 'center',
            }}>
              <Text style={{ fontSize: 18, fontWeight: 'bold', color: '#111827' }}>
                Total Amount
              </Text>
              <Text style={{ fontSize: 20, fontWeight: 'bold', color: '#f97316' }}>
                {formatCurrency(order.total)}
              </Text>
            </View>
          </Card>
        </Animated.View>

        {/* Action Buttons */}
        <Animated.View style={{
          opacity: fadeAnimation,
          transform: [{ translateY: slideAnimation }],
        }}>
          <Card variant="elevated" margin="md" padding="lg">
            <View style={{ flexDirection: 'row', alignItems: 'center', marginBottom: 16 }}>
              <View style={{
                width: 40,
                height: 40,
                borderRadius: 20,
                backgroundColor: '#8b5cf6',
                alignItems: 'center',
                justifyContent: 'center',
                marginRight: 12,
              }}>
                <Ionicons name="flash" size={20} color="white" />
              </View>
              <View>
                <Text style={{ fontSize: 18, fontWeight: 'bold', color: '#111827' }}>
                  Quick Actions
                </Text>
                <Text style={{ fontSize: 12, color: '#6b7280' }}>
                  Navigation & Support
                </Text>
              </View>
            </View>

            <View style={{ flexDirection: 'row', marginBottom: 16 }}>
              <TouchableOpacity
                onPress={handleStartNavigation}
                style={{
                  flex: 1,
                  marginRight: 8,
                }}
              >
                <LinearGradient
                  colors={['#3b82f6', '#1d4ed8']}
                  style={{
                    flexDirection: 'row',
                    alignItems: 'center',
                    justifyContent: 'center',
                    paddingVertical: 16,
                    borderRadius: 16,
                  }}
                >
                  <Animated.View style={{
                    transform: [{ scale: 1 }],
                  }}>
                    <Ionicons name="navigate" size={20} color="white" />
                  </Animated.View>
                  <Text style={{
                    color: 'white',
                    fontSize: 16,
                    fontWeight: 'bold',
                    marginLeft: 8,
                  }}>
                    Start Navigation
                  </Text>
                </LinearGradient>
              </TouchableOpacity>

              <TouchableOpacity
                onPress={() => setShowReportIssue(true)}
                style={{
                  flex: 1,
                  marginLeft: 8,
                }}
              >
                <LinearGradient
                  colors={['#dc2626', '#b91c1c']}
                  style={{
                    flexDirection: 'row',
                    alignItems: 'center',
                    justifyContent: 'center',
                    paddingVertical: 16,
                    borderRadius: 16,
                  }}
                >
                  <Animated.View style={{
                    transform: [{ scale: 1 }],
                  }}>
                    <Ionicons name="warning" size={20} color="white" />
                  </Animated.View>
                  <Text style={{
                    color: 'white',
                    fontSize: 16,
                    fontWeight: 'bold',
                    marginLeft: 8,
                  }}>
                    Report Issue
                  </Text>
                </LinearGradient>
              </TouchableOpacity>
            </View>

            {/* Additional Quick Actions */}
            <View style={{ flexDirection: 'row' }}>
              <TouchableOpacity
                onPress={() => setShowMap(true)}
                style={{
                  flex: 1,
                  flexDirection: 'row',
                  alignItems: 'center',
                  justifyContent: 'center',
                  backgroundColor: '#f3f4f6',
                  paddingVertical: 12,
                  borderRadius: 12,
                  marginRight: 8,
                }}
              >
                <Ionicons name="map-outline" size={18} color="#374151" />
                <Text style={{ color: '#374151', fontSize: 14, fontWeight: '600', marginLeft: 8 }}>
                  Full Map
                </Text>
              </TouchableOpacity>

              <TouchableOpacity
                onPress={() => handleCall(order.customer.phone, 'customer')}
                style={{
                  flex: 1,
                  flexDirection: 'row',
                  alignItems: 'center',
                  justifyContent: 'center',
                  backgroundColor: '#f3f4f6',
                  paddingVertical: 12,
                  borderRadius: 12,
                  marginLeft: 8,
                }}
              >
                <Ionicons name="call-outline" size={18} color="#374151" />
                <Text style={{ color: '#374151', fontSize: 14, fontWeight: '600', marginLeft: 8 }}>
                  Call Customer
                </Text>
              </TouchableOpacity>
            </View>
          </Card>
        </Animated.View>

        {/* Workflow Action Buttons */}
        {renderWorkflowActions()}

        {/* Bottom spacing */}
        <View style={{ height: 20 }} />
      </ScrollView>

      {/* Map Modal */}
      <Modal
        visible={showMap}
        animationType="slide"
        presentationStyle="fullScreen"
        onRequestClose={() => setShowMap(false)}
      >
        <OrderTrackingMap
          order={order}
          onClose={() => setShowMap(false)}
        />
      </Modal>

      {/* Delivery Proof Modal */}
      <DeliveryProofModal
        visible={showDeliveryProof}
        onClose={() => setShowDeliveryProof(false)}
        onConfirm={handleDeliveryConfirmed}
        orderNumber={order.orderNumber}
      />

      {/* Report Issue Modal */}
      <Modal
        visible={showReportIssue}
        animationType="slide"
        presentationStyle="pageSheet"
        onRequestClose={() => setShowReportIssue(false)}
      >
        <SafeAreaView style={{ flex: 1, backgroundColor: '#f8fafc' }}>
          <LinearGradient
            colors={['#dc2626', '#b91c1c']}
            style={{ paddingVertical: 16, paddingHorizontal: 20 }}
          >
            <View style={{ flexDirection: 'row', alignItems: 'center', justifyContent: 'space-between' }}>
              <View style={{ flexDirection: 'row', alignItems: 'center' }}>
                <Ionicons name="warning" size={24} color="white" />
                <Text style={{ fontSize: 20, fontWeight: 'bold', color: 'white', marginLeft: 12 }}>
                  Report Issue
                </Text>
              </View>
              <TouchableOpacity
                onPress={() => setShowReportIssue(false)}
                style={{
                  backgroundColor: 'rgba(255,255,255,0.2)',
                  borderRadius: 20,
                  padding: 8,
                }}
              >
                <Ionicons name="close" size={20} color="white" />
              </TouchableOpacity>
            </View>
          </LinearGradient>

          <ScrollView style={{ flex: 1, padding: 20 }}>
            <Text style={{ fontSize: 16, color: '#6b7280', marginBottom: 24, textAlign: 'center' }}>
              Select the type of issue you're experiencing with this order
            </Text>

            {[
              { type: 'customer', title: 'Customer Issue', description: 'Customer not responding or misbehavior', icon: 'person-outline', color: '#ef4444' },
              { type: 'restaurant', title: 'Restaurant Issue', description: 'Order not ready or restaurant problems', icon: 'restaurant-outline', color: '#f59e0b' },
              { type: 'address', title: 'Address Issue', description: 'Wrong or incomplete address', icon: 'location-outline', color: '#8b5cf6' },
              { type: 'payment', title: 'Payment Issue', description: 'Payment problems or disputes', icon: 'card-outline', color: '#06b6d4' },
              { type: 'safety', title: 'Safety Concern', description: 'Safety issues or dangerous situations', icon: 'shield-outline', color: '#dc2626' },
              { type: 'other', title: 'Other Issue', description: 'Other problems not listed above', icon: 'ellipsis-horizontal-outline', color: '#6b7280' },
            ].map((issue, index) => (
              <TouchableOpacity
                key={issue.type}
                onPress={() => {
                  Vibration.vibrate(50);
                  Alert.alert(
                    'Issue Reported',
                    `Your ${issue.title.toLowerCase()} has been reported. Our support team will contact you shortly.`,
                    [
                      { text: 'OK', onPress: () => setShowReportIssue(false) }
                    ]
                  );
                }}
                style={{
                  flexDirection: 'row',
                  alignItems: 'center',
                  backgroundColor: 'white',
                  padding: 16,
                  borderRadius: 16,
                  marginBottom: 12,
                  shadowColor: '#000',
                  shadowOffset: { width: 0, height: 2 },
                  shadowOpacity: 0.1,
                  shadowRadius: 4,
                  elevation: 3,
                }}
              >
                <View style={{
                  width: 48,
                  height: 48,
                  borderRadius: 24,
                  backgroundColor: `${issue.color}15`,
                  alignItems: 'center',
                  justifyContent: 'center',
                  marginRight: 16,
                }}>
                  <Ionicons name={issue.icon as any} size={24} color={issue.color} />
                </View>
                <View style={{ flex: 1 }}>
                  <Text style={{ fontSize: 16, fontWeight: '600', color: '#111827', marginBottom: 4 }}>
                    {issue.title}
                  </Text>
                  <Text style={{ fontSize: 14, color: '#6b7280' }}>
                    {issue.description}
                  </Text>
                </View>
                <Ionicons name="chevron-forward" size={20} color="#9ca3af" />
              </TouchableOpacity>
            ))}
          </ScrollView>
        </SafeAreaView>
      </Modal>
    </SafeAreaView>
  );
};

export default OrderDetailsScreen;
