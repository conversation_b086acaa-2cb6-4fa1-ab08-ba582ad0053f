import React from 'react';
import { View, Text, TouchableOpacity, ViewStyle } from 'react-native';
import { Ionicons } from '@expo/vector-icons';

interface EmptyStateProps {
  icon?: keyof typeof Ionicons.glyphMap;
  title: string;
  description?: string;
  actionText?: string;
  onActionPress?: () => void;
  variant?: 'default' | 'minimal' | 'illustration';
  style?: ViewStyle;
}

const EmptyState: React.FC<EmptyStateProps> = ({
  icon = 'document-outline',
  title,
  description,
  actionText,
  onActionPress,
  variant = 'default',
  style,
}) => {
  const getContainerStyle = (): ViewStyle => {
    const baseStyle: ViewStyle = {
      alignItems: 'center',
      justifyContent: 'center',
      padding: 40,
    };

    switch (variant) {
      case 'default':
        return {
          ...baseStyle,
          backgroundColor: 'white',
          borderRadius: 20,
          shadowColor: '#000',
          shadowOffset: { width: 0, height: 6 },
          shadowOpacity: 0.1,
          shadowRadius: 16,
          elevation: 8,
          borderWidth: 2,
          borderColor: '#f1f5f9',
          borderStyle: 'dashed',
        };

      case 'minimal':
        return {
          ...baseStyle,
          backgroundColor: 'transparent',
        };

      case 'illustration':
        return {
          ...baseStyle,
          backgroundColor: '#f8fafc',
          borderRadius: 24,
          borderWidth: 2,
          borderColor: '#e2e8f0',
          borderStyle: 'dashed',
        };

      default:
        return baseStyle;
    }
  };

  const getIconContainerStyle = (): ViewStyle => {
    return {
      width: 80,
      height: 80,
      borderRadius: 40,
      backgroundColor: '#fef2f2',
      alignItems: 'center',
      justifyContent: 'center',
      marginBottom: 20,
      borderWidth: 2,
      borderColor: '#fecaca',
    };
  };

  return (
    <View style={[getContainerStyle(), style]}>
      {/* Icon */}
      <View style={getIconContainerStyle()}>
        <Ionicons name={icon} size={40} color="#dc2626" />
      </View>

      {/* Title */}
      <Text style={{
        fontSize: 20,
        fontWeight: 'bold',
        color: '#111827',
        textAlign: 'center',
        marginBottom: 8,
      }}>
        {title}
      </Text>

      {/* Description */}
      {description && (
        <Text style={{
          fontSize: 16,
          color: '#6b7280',
          textAlign: 'center',
          lineHeight: 24,
          marginBottom: 24,
          maxWidth: 280,
        }}>
          {description}
        </Text>
      )}

      {/* Action Button */}
      {actionText && onActionPress && (
        <TouchableOpacity
          onPress={onActionPress}
          style={{
            backgroundColor: '#dc2626',
            paddingHorizontal: 24,
            paddingVertical: 12,
            borderRadius: 16,
            shadowColor: '#dc2626',
            shadowOffset: { width: 0, height: 4 },
            shadowOpacity: 0.3,
            shadowRadius: 8,
            elevation: 6,
          }}
        >
          <Text style={{
            color: 'white',
            fontSize: 16,
            fontWeight: 'bold',
          }}>
            {actionText}
          </Text>
        </TouchableOpacity>
      )}
    </View>
  );
};

export default EmptyState;
