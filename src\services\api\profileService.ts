import { apiClient, ApiResponse, retryRequest, createFormData } from './apiConfig';

// Profile types
export interface RiderProfile {
  id: string;
  firstName: string;
  lastName: string;
  email: string;
  phone: string;
  profilePhoto?: string;
  dateOfBirth?: string;
  address?: string;
  city: string;
  emergencyContact?: {
    name: string;
    phone: string;
    relationship: string;
  };
  preferences: {
    language: string;
    notifications: {
      orderRequests: boolean;
      earnings: boolean;
      promotions: boolean;
      system: boolean;
    };
    workingHours: {
      start: string;
      end: string;
      days: string[];
    };
  };
  stats: {
    totalOrders: number;
    totalEarnings: number;
    averageRating: number;
    completionRate: number;
    joinDate: string;
    totalDistance: number;
    totalOnlineTime: number;
  };
  achievements: {
    id: string;
    name: string;
    description: string;
    icon: string;
    earnedAt: string;
  }[];
  createdAt: string;
  updatedAt: string;
}

export interface VehicleInfo {
  id: string;
  type: 'bicycle' | 'motorcycle' | 'car' | 'scooter';
  make?: string;
  model?: string;
  year?: string;
  plateNumber?: string;
  color?: string;
  isActive: boolean;
  documents: {
    registration?: {
      status: 'pending' | 'verified' | 'rejected';
      uploadedAt?: string;
      verifiedAt?: string;
      rejectionReason?: string;
    };
    license?: {
      status: 'pending' | 'verified' | 'rejected';
      uploadedAt?: string;
      verifiedAt?: string;
      rejectionReason?: string;
    };
    insurance?: {
      status: 'pending' | 'verified' | 'rejected';
      uploadedAt?: string;
      verifiedAt?: string;
      rejectionReason?: string;
    };
  };
  createdAt: string;
  updatedAt: string;
}

export interface PaymentMethod {
  id: string;
  type: 'bank_account' | 'jazzcash' | 'easypaisa';
  isPrimary: boolean;
  isVerified: boolean;
  details: {
    accountNumber?: string;
    bankName?: string;
    iban?: string;
    mobileNumber?: string;
    accountHolderName: string;
  };
  createdAt: string;
  updatedAt: string;
}

export interface ReferralStats {
  referralCode: string;
  totalReferrals: number;
  successfulReferrals: number;
  pendingReferrals: number;
  totalEarnings: number;
  referrals: {
    id: string;
    referredEmail: string;
    status: 'pending' | 'completed' | 'expired';
    earnings: number;
    referredAt: string;
    completedAt?: string;
  }[];
}

// Profile Service
export class ProfileService {
  // Get rider profile
  static async getProfile(): Promise<ApiResponse<RiderProfile>> {
    return retryRequest(async () => {
      const response = await apiClient.get<ApiResponse<RiderProfile>>('/profile');
      return response.data;
    });
  }

  // Update profile information
  static async updateProfile(profileData: Partial<RiderProfile>): Promise<ApiResponse<RiderProfile>> {
    return retryRequest(async () => {
      const response = await apiClient.put<ApiResponse<RiderProfile>>('/profile', profileData);
      return response.data;
    });
  }

  // Upload profile photo
  static async uploadProfilePhoto(photo: {
    uri: string;
    type: string;
    name: string;
  }): Promise<ApiResponse<{ photoUrl: string; message: string }>> {
    return retryRequest(async () => {
      const formData = createFormData({ photo });
      
      const response = await apiClient.post<ApiResponse<{ photoUrl: string; message: string }>>(
        '/profile/photo',
        formData,
        {
          headers: {
            'Content-Type': 'multipart/form-data',
          },
        }
      );
      return response.data;
    });
  }

  // Get profile statistics
  static async getProfileStats(): Promise<ApiResponse<{
    totalOrders: number;
    totalEarnings: number;
    averageRating: number;
    completionRate: number;
    totalDistance: number;
    totalOnlineTime: number;
    rankPosition: number;
    monthlyStats: {
      orders: number;
      earnings: number;
      rating: number;
    };
  }>> {
    return retryRequest(async () => {
      const response = await apiClient.get<ApiResponse<any>>('/profile/stats');
      return response.data;
    });
  }

  // Get vehicle information
  static async getVehicleInfo(): Promise<ApiResponse<VehicleInfo[]>> {
    return retryRequest(async () => {
      const response = await apiClient.get<ApiResponse<VehicleInfo[]>>('/vehicles');
      return response.data;
    });
  }

  // Add vehicle
  static async addVehicle(vehicleData: Omit<VehicleInfo, 'id' | 'createdAt' | 'updatedAt'>): Promise<ApiResponse<VehicleInfo>> {
    return retryRequest(async () => {
      const response = await apiClient.post<ApiResponse<VehicleInfo>>('/vehicles', vehicleData);
      return response.data;
    });
  }

  // Update vehicle information
  static async updateVehicle(vehicleId: string, vehicleData: Partial<VehicleInfo>): Promise<ApiResponse<VehicleInfo>> {
    return retryRequest(async () => {
      const response = await apiClient.put<ApiResponse<VehicleInfo>>(`/vehicles/${vehicleId}`, vehicleData);
      return response.data;
    });
  }

  // Delete vehicle
  static async deleteVehicle(vehicleId: string): Promise<ApiResponse<{ message: string }>> {
    return retryRequest(async () => {
      const response = await apiClient.delete<ApiResponse<{ message: string }>>(`/vehicles/${vehicleId}`);
      return response.data;
    });
  }

  // Set active vehicle
  static async setActiveVehicle(vehicleId: string): Promise<ApiResponse<{ message: string }>> {
    return retryRequest(async () => {
      const response = await apiClient.put<ApiResponse<{ message: string }>>(`/vehicles/${vehicleId}/activate`);
      return response.data;
    });
  }

  // Get payment methods
  static async getPaymentMethods(): Promise<ApiResponse<PaymentMethod[]>> {
    return retryRequest(async () => {
      const response = await apiClient.get<ApiResponse<PaymentMethod[]>>('/payment-methods');
      return response.data;
    });
  }

  // Add payment method
  static async addPaymentMethod(paymentData: Omit<PaymentMethod, 'id' | 'createdAt' | 'updatedAt'>): Promise<ApiResponse<PaymentMethod>> {
    return retryRequest(async () => {
      const response = await apiClient.post<ApiResponse<PaymentMethod>>('/payment-methods', paymentData);
      return response.data;
    });
  }

  // Update payment method
  static async updatePaymentMethod(methodId: string, paymentData: Partial<PaymentMethod>): Promise<ApiResponse<PaymentMethod>> {
    return retryRequest(async () => {
      const response = await apiClient.put<ApiResponse<PaymentMethod>>(`/payment-methods/${methodId}`, paymentData);
      return response.data;
    });
  }

  // Delete payment method
  static async deletePaymentMethod(methodId: string): Promise<ApiResponse<{ message: string }>> {
    return retryRequest(async () => {
      const response = await apiClient.delete<ApiResponse<{ message: string }>>(`/payment-methods/${methodId}`);
      return response.data;
    });
  }

  // Set primary payment method
  static async setPrimaryPaymentMethod(methodId: string): Promise<ApiResponse<{ message: string }>> {
    return retryRequest(async () => {
      const response = await apiClient.put<ApiResponse<{ message: string }>>(`/payment-methods/${methodId}/primary`);
      return response.data;
    });
  }

  // Get referral statistics
  static async getReferralStats(): Promise<ApiResponse<ReferralStats>> {
    return retryRequest(async () => {
      const response = await apiClient.get<ApiResponse<ReferralStats>>('/profile/referrals');
      return response.data;
    });
  }

  // Share referral code
  static async shareReferralCode(method: 'sms' | 'email' | 'whatsapp', contact: string): Promise<ApiResponse<{ message: string }>> {
    return retryRequest(async () => {
      const response = await apiClient.post<ApiResponse<{ message: string }>>('/profile/referrals/share', {
        method,
        contact,
      });
      return response.data;
    });
  }

  // Update preferences
  static async updatePreferences(preferences: Partial<RiderProfile['preferences']>): Promise<ApiResponse<{ message: string }>> {
    return retryRequest(async () => {
      const response = await apiClient.put<ApiResponse<{ message: string }>>('/profile/preferences', preferences);
      return response.data;
    });
  }

  // Get achievements
  static async getAchievements(): Promise<ApiResponse<{
    earned: RiderProfile['achievements'];
    available: {
      id: string;
      name: string;
      description: string;
      icon: string;
      requirement: string;
      progress: number;
      target: number;
    }[];
  }>> {
    return retryRequest(async () => {
      const response = await apiClient.get<ApiResponse<any>>('/profile/achievements');
      return response.data;
    });
  }

  // Update emergency contact
  static async updateEmergencyContact(contact: RiderProfile['emergencyContact']): Promise<ApiResponse<{ message: string }>> {
    return retryRequest(async () => {
      const response = await apiClient.put<ApiResponse<{ message: string }>>('/profile/emergency-contact', contact);
      return response.data;
    });
  }

  // Change password
  static async changePassword(data: {
    currentPassword: string;
    newPassword: string;
    confirmPassword: string;
  }): Promise<ApiResponse<{ message: string }>> {
    return retryRequest(async () => {
      const response = await apiClient.put<ApiResponse<{ message: string }>>('/profile/change-password', data);
      return response.data;
    });
  }

  // Deactivate account
  static async deactivateAccount(reason: string, password: string): Promise<ApiResponse<{ message: string }>> {
    return retryRequest(async () => {
      const response = await apiClient.post<ApiResponse<{ message: string }>>('/profile/deactivate', {
        reason,
        password,
      });
      return response.data;
    });
  }

  // Get profile summary for dashboard
  static async getProfileSummary(): Promise<ApiResponse<{
    name: string;
    photo?: string;
    rating: number;
    totalOrders: number;
    memberSince: string;
    verificationStatus: string;
    activeVehicle?: {
      type: string;
      plateNumber: string;
    };
    quickStats: {
      todayOrders: number;
      weekEarnings: number;
      monthRating: number;
    };
  }>> {
    return retryRequest(async () => {
      const response = await apiClient.get<ApiResponse<any>>('/profile/summary');
      return response.data;
    });
  }
}

export default ProfileService;
