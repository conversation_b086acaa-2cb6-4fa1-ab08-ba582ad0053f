import {
  HeatmapArea,
  Badge,
  PerformanceStats,
  TrainingVideo,
  VideoProgress,
  TrainingCertificate,
  PushNotification,
  NotificationPreferences,
  LocationTrackingConfig,
  LocationData,
  TrackingStatus,
  DemandLevel,
  AreaType,
  BadgeType,
  BadgeRarity,
  NotificationType,
  NotificationPriority,
  LocationAccuracy,
  BatteryOptimization,
} from '../types/advancedTools';

class AdvancedToolsService {
  private static instance: AdvancedToolsService;

  public static getInstance(): AdvancedToolsService {
    if (!AdvancedToolsService.instance) {
      AdvancedToolsService.instance = new AdvancedToolsService();
    }
    return AdvancedToolsService.instance;
  }

  // Simulate API delay
  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  // Heatmap & Demand Services
  async getHeatmapAreas(): Promise<HeatmapArea[]> {
    await this.delay(1000);
    
    const mockAreas: HeatmapArea[] = [
      {
        id: 'area-1',
        name: '<PERSON><PERSON><PERSON>-<PERSON><PERSON><PERSON><PERSON><PERSON>',
        coordinates: {
          latitude: 24.9056,
          longitude: 67.0822,
        },
        radius: 2000,
        demandLevel: DemandLevel.HIGH,
        areaType: AreaType.RESIDENTIAL,
        averageOrderValue: 850,
        estimatedEarnings: 1200,
        surgeMultiplier: 1.5,
        activeOrders: 15,
        availableRiders: 8,
        peakHours: ['12:00-14:00', '19:00-22:00'],
        lastUpdated: new Date().toISOString(),
      },
      {
        id: 'area-2',
        name: 'Clifton',
        coordinates: {
          latitude: 24.8138,
          longitude: 67.0299,
        },
        radius: 1500,
        demandLevel: DemandLevel.SURGE,
        areaType: AreaType.COMMERCIAL,
        averageOrderValue: 1200,
        estimatedEarnings: 2000,
        surgeMultiplier: 2.0,
        activeOrders: 25,
        availableRiders: 5,
        peakHours: ['11:00-15:00', '18:00-23:00'],
        lastUpdated: new Date().toISOString(),
      },
      {
        id: 'area-3',
        name: 'DHA Phase 5',
        coordinates: {
          latitude: 24.8059,
          longitude: 67.0756,
        },
        radius: 1800,
        demandLevel: DemandLevel.MEDIUM,
        areaType: AreaType.MIXED,
        averageOrderValue: 950,
        estimatedEarnings: 1400,
        surgeMultiplier: 1.2,
        activeOrders: 12,
        availableRiders: 10,
        peakHours: ['13:00-15:00', '20:00-22:00'],
        lastUpdated: new Date().toISOString(),
      },
    ];

    return mockAreas;
  }

  // Performance & Gamification Services
  async getPerformanceStats(): Promise<PerformanceStats> {
    await this.delay(800);
    
    return {
      riderId: 'rider-1',
      level: 12,
      currentXP: 2450,
      nextLevelXP: 3000,
      totalXP: 15450,
      completedDeliveries: 234,
      averageRating: 4.8,
      onTimeDeliveryRate: 94.5,
      customerSatisfactionScore: 96.2,
      totalEarnings: 45600,
      badges: 8,
      rank: 15,
      weeklyStats: {
        deliveries: 28,
        earnings: 4200,
        rating: 4.9,
        onTimeRate: 96.4,
      },
      monthlyStats: {
        deliveries: 112,
        earnings: 16800,
        rating: 4.8,
        onTimeRate: 94.8,
      },
      lastUpdated: new Date().toISOString(),
    };
  }

  async getBadges(): Promise<Badge[]> {
    await this.delay(600);
    
    const mockBadges: Badge[] = [
      {
        id: 'badge-1',
        name: 'Speed Demon',
        description: 'Complete 50 deliveries in under 30 minutes',
        icon: 'flash',
        type: BadgeType.DELIVERY_SPEED,
        rarity: BadgeRarity.RARE,
        requirements: {
          deliveries: 50,
          timeLimit: 30,
        },
        rewards: {
          xp: 500,
          bonus: 200,
        },
        progress: {
          current: 42,
          target: 50,
          percentage: 84,
        },
        isUnlocked: false,
        unlockedAt: null,
      },
      {
        id: 'badge-2',
        name: 'Customer Favorite',
        description: 'Maintain 4.8+ rating for 100 deliveries',
        icon: 'star',
        type: BadgeType.CUSTOMER_RATING,
        rarity: BadgeRarity.EPIC,
        requirements: {
          deliveries: 100,
          minRating: 4.8,
        },
        rewards: {
          xp: 750,
          bonus: 500,
        },
        progress: {
          current: 89,
          target: 100,
          percentage: 89,
        },
        isUnlocked: false,
        unlockedAt: null,
      },
      {
        id: 'badge-3',
        name: 'Early Bird',
        description: 'Complete 25 morning deliveries (6-10 AM)',
        icon: 'sunny',
        type: BadgeType.TIME_BASED,
        rarity: BadgeRarity.COMMON,
        requirements: {
          deliveries: 25,
          timeSlot: '06:00-10:00',
        },
        rewards: {
          xp: 250,
          bonus: 100,
        },
        progress: {
          current: 25,
          target: 25,
          percentage: 100,
        },
        isUnlocked: true,
        unlockedAt: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString(),
      },
    ];

    return mockBadges;
  }

  // Training Services
  async getTrainingVideos(): Promise<TrainingVideo[]> {
    await this.delay(700);
    
    const mockVideos: TrainingVideo[] = [
      {
        id: 'video-1',
        title: 'Safe Delivery Practices',
        description: 'Learn essential safety protocols for food delivery',
        category: 'Safety',
        duration: 480, // 8 minutes
        thumbnailUrl: 'https://example.com/thumb1.jpg',
        videoUrl: 'https://example.com/video1.mp4',
        instructor: 'Ahmed Khan',
        difficulty: 'Beginner',
        xpReward: 50,
        certificateEligible: true,
        tags: ['safety', 'protocols', 'delivery'],
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      },
      {
        id: 'video-2',
        title: 'Customer Service Excellence',
        description: 'Master the art of exceptional customer service',
        category: 'Customer Service',
        duration: 600, // 10 minutes
        thumbnailUrl: 'https://example.com/thumb2.jpg',
        videoUrl: 'https://example.com/video2.mp4',
        instructor: 'Fatima Ali',
        difficulty: 'Intermediate',
        xpReward: 75,
        certificateEligible: true,
        tags: ['customer', 'service', 'communication'],
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      },
    ];

    return mockVideos;
  }

  async getVideoProgress(riderId: string): Promise<VideoProgress[]> {
    await this.delay(400);
    
    return [
      {
        id: 'progress-1',
        riderId,
        videoId: 'video-1',
        watchedDuration: 240,
        totalDuration: 480,
        progress: 50,
        isCompleted: false,
        lastWatchedAt: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),
        createdAt: new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString(),
        updatedAt: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),
      },
    ];
  }

  async getCertificates(riderId: string): Promise<TrainingCertificate[]> {
    await this.delay(500);
    
    return [
      {
        id: 'cert-1',
        riderId,
        videoId: 'video-1',
        certificateName: 'Safe Delivery Practices Certification',
        issuedAt: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString(),
        expiresAt: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000).toISOString(),
        certificateUrl: 'https://example.com/cert1.pdf',
        verificationCode: 'FW-SDL-2024-001',
      },
    ];
  }

  // Notification Services
  async getNotifications(riderId: string): Promise<PushNotification[]> {
    await this.delay(600);
    
    const mockNotifications: PushNotification[] = [
      {
        id: 'notif-1',
        riderId,
        type: NotificationType.ORDER_REQUEST,
        title: 'New Order Available',
        message: 'Order from McDonald\'s - Gulshan for Rs. 850',
        data: { orderId: 'order-123', restaurantId: 'rest-456' },
        priority: NotificationPriority.HIGH,
        isRead: false,
        createdAt: new Date(Date.now() - 5 * 60 * 1000).toISOString(),
      },
      {
        id: 'notif-2',
        riderId,
        type: NotificationType.PAYMENT_RECEIVED,
        title: 'Payment Received',
        message: 'Rs. 1,200 has been added to your wallet',
        data: { amount: 1200, transactionId: 'txn-789' },
        priority: NotificationPriority.MEDIUM,
        isRead: true,
        createdAt: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),
      },
    ];

    return mockNotifications;
  }

  async getNotificationPreferences(riderId: string): Promise<NotificationPreferences> {
    await this.delay(300);
    
    return {
      riderId,
      orderRequests: {
        enabled: true,
        sound: true,
        vibration: true,
        priority: NotificationPriority.HIGH,
      },
      paymentUpdates: {
        enabled: true,
        sound: true,
        vibration: false,
        priority: NotificationPriority.MEDIUM,
      },
      bonusAlerts: {
        enabled: true,
        sound: false,
        vibration: true,
        priority: NotificationPriority.MEDIUM,
      },
      trainingReminders: {
        enabled: true,
        sound: false,
        vibration: false,
        priority: NotificationPriority.LOW,
      },
      safetyAlerts: {
        enabled: true,
        sound: true,
        vibration: true,
        priority: NotificationPriority.HIGH,
      },
      systemUpdates: {
        enabled: true,
        sound: false,
        vibration: false,
        priority: NotificationPriority.LOW,
      },
      performanceUpdates: {
        enabled: true,
        sound: false,
        vibration: false,
        priority: NotificationPriority.LOW,
      },
      quietHours: {
        enabled: true,
        startTime: '22:00',
        endTime: '07:00',
      },
      globalSettings: {
        masterEnabled: true,
        globalSound: true,
        globalVibration: true,
        showBadges: true,
        showPreviews: true,
      },
      updatedAt: new Date().toISOString(),
    };
  }

  // Location Tracking Services
  async getLocationConfig(riderId: string): Promise<LocationTrackingConfig> {
    await this.delay(400);
    
    return {
      id: 'config-1',
      riderId,
      isEnabled: true,
      accuracy: LocationAccuracy.HIGH,
      updateInterval: 30,
      distanceFilter: 10,
      batteryOptimization: BatteryOptimization.BALANCED,
      backgroundTracking: true,
      shareWithCustomers: true,
      shareWithSupport: true,
      emergencySharing: true,
      dataRetentionDays: 30,
      privacySettings: {
        shareLocation: true,
        shareSpeed: false,
        shareRoute: true,
        anonymizeData: false,
      },
      geofencing: {
        enabled: true,
        restaurantRadius: 100,
        customerRadius: 50,
        alertOnEntry: true,
        alertOnExit: true,
      },
      updatedAt: new Date().toISOString(),
    };
  }

  async getCurrentLocation(riderId: string): Promise<LocationData> {
    await this.delay(200);
    
    return {
      id: 'loc-current',
      riderId,
      latitude: 24.8607,
      longitude: 67.0011,
      accuracy: 5.2,
      altitude: 12.5,
      speed: 25.5,
      heading: 45.0,
      timestamp: new Date().toISOString(),
      address: 'Gulshan-e-Iqbal, Block 13-D, Karachi',
      isOnline: true,
      batteryLevel: 78,
      networkType: '4G',
    };
  }
}

export default AdvancedToolsService.getInstance();
