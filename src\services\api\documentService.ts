import { apiClient, ApiResponse, retryRequest, createFormData } from './apiConfig';

// Document types
export interface DocumentUploadRequest {
  documentType: string;
  file: {
    uri: string;
    type: string;
    name: string;
  };
  metadata?: {
    documentNumber?: string;
    expiryDate?: string;
    issueDate?: string;
  };
}

export interface DocumentUploadResponse {
  documentId: string;
  documentType: string;
  status: 'pending' | 'verified' | 'rejected';
  uploadedAt: string;
  message: string;
}

export interface DocumentStatusResponse {
  documentId: string;
  documentType: string;
  status: 'pending' | 'verified' | 'rejected' | 'not_uploaded';
  uploadedAt?: string;
  verifiedAt?: string;
  rejectionReason?: string;
  fileUrl?: string;
  metadata?: {
    documentNumber?: string;
    expiryDate?: string;
    issueDate?: string;
  };
}

export interface VerificationStatusResponse {
  overall: 'pending' | 'verified' | 'rejected' | 'incomplete';
  canGoOnline: boolean;
  documents: {
    [key: string]: DocumentStatusResponse;
  };
  requiredDocuments: string[];
  pendingDocuments: string[];
  verifiedDocuments: string[];
  rejectedDocuments: string[];
  lastUpdated: string;
}

export interface DocumentRequirementsResponse {
  vehicleType: string;
  requiredDocuments: {
    type: string;
    name: string;
    description: string;
    required: boolean;
    acceptedFormats: string[];
    maxSizeBytes: number;
    examples?: string[];
  }[];
}

// Document Service
export class DocumentService {
  // Upload document
  static async uploadDocument(data: DocumentUploadRequest): Promise<ApiResponse<DocumentUploadResponse>> {
    return retryRequest(async () => {
      const formData = createFormData({
        documentType: data.documentType,
        file: data.file,
        metadata: data.metadata ? JSON.stringify(data.metadata) : undefined,
      });

      const response = await apiClient.post<ApiResponse<DocumentUploadResponse>>(
        '/documents/upload',
        formData,
        {
          headers: {
            'Content-Type': 'multipart/form-data',
          },
        }
      );
      return response.data;
    });
  }

  // Get document status
  static async getDocumentStatus(documentType: string): Promise<ApiResponse<DocumentStatusResponse>> {
    return retryRequest(async () => {
      const response = await apiClient.get<ApiResponse<DocumentStatusResponse>>(
        `/documents/status/${documentType}`
      );
      return response.data;
    });
  }

  // Get all documents status
  static async getAllDocumentsStatus(): Promise<ApiResponse<DocumentStatusResponse[]>> {
    return retryRequest(async () => {
      const response = await apiClient.get<ApiResponse<DocumentStatusResponse[]>>('/documents/status');
      return response.data;
    });
  }

  // Get verification status
  static async getVerificationStatus(): Promise<ApiResponse<VerificationStatusResponse>> {
    return retryRequest(async () => {
      const response = await apiClient.get<ApiResponse<VerificationStatusResponse>>('/documents/verification-status');
      return response.data;
    });
  }

  // Get document requirements based on vehicle type
  static async getDocumentRequirements(vehicleType: string): Promise<ApiResponse<DocumentRequirementsResponse>> {
    return retryRequest(async () => {
      const response = await apiClient.get<ApiResponse<DocumentRequirementsResponse>>(
        `/documents/requirements/${vehicleType}`
      );
      return response.data;
    });
  }

  // Delete document
  static async deleteDocument(documentType: string): Promise<ApiResponse<{ message: string }>> {
    return retryRequest(async () => {
      const response = await apiClient.delete<ApiResponse<{ message: string }>>(
        `/documents/${documentType}`
      );
      return response.data;
    });
  }

  // Resubmit rejected document
  static async resubmitDocument(data: DocumentUploadRequest): Promise<ApiResponse<DocumentUploadResponse>> {
    return retryRequest(async () => {
      const formData = createFormData({
        documentType: data.documentType,
        file: data.file,
        metadata: data.metadata ? JSON.stringify(data.metadata) : undefined,
        resubmission: true,
      });

      const response = await apiClient.put<ApiResponse<DocumentUploadResponse>>(
        `/documents/resubmit/${data.documentType}`,
        formData,
        {
          headers: {
            'Content-Type': 'multipart/form-data',
          },
        }
      );
      return response.data;
    });
  }

  // Get document download URL
  static async getDocumentUrl(documentType: string): Promise<ApiResponse<{ url: string; expiresAt: string }>> {
    return retryRequest(async () => {
      const response = await apiClient.get<ApiResponse<{ url: string; expiresAt: string }>>(
        `/documents/download/${documentType}`
      );
      return response.data;
    });
  }

  // Submit for verification (when all required documents are uploaded)
  static async submitForVerification(): Promise<ApiResponse<{ message: string; estimatedTime: string }>> {
    return retryRequest(async () => {
      const response = await apiClient.post<ApiResponse<{ message: string; estimatedTime: string }>>(
        '/documents/submit-for-verification'
      );
      return response.data;
    });
  }

  // Get verification history
  static async getVerificationHistory(): Promise<ApiResponse<{
    submissions: {
      id: string;
      submittedAt: string;
      status: 'pending' | 'verified' | 'rejected';
      reviewedAt?: string;
      reviewedBy?: string;
      notes?: string;
      documents: string[];
    }[];
  }>> {
    return retryRequest(async () => {
      const response = await apiClient.get<ApiResponse<{
        submissions: {
          id: string;
          submittedAt: string;
          status: 'pending' | 'verified' | 'rejected';
          reviewedAt?: string;
          reviewedBy?: string;
          notes?: string;
          documents: string[];
        }[];
      }>>('/documents/verification-history');
      return response.data;
    });
  }
}

export default DocumentService;
