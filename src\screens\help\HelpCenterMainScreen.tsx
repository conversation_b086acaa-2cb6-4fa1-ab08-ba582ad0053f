import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  TextInput,
  StatusBar,
  Animated,
  Alert,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import { useNavigation } from '@react-navigation/native';
import { LinearGradient } from 'expo-linear-gradient';
import { FAQCategory, FAQItem, SupportTicket, TicketStatus } from '../../types/support';

interface FAQCard {
  id: string;
  category: FAQCategory;
  title: string;
  icon: string;
  color: string;
  issueCount: number;
}

interface RecentTicket {
  id: string;
  title: string;
  status: TicketStatus;
  createdAt: string;
  lastUpdate: string;
}

const HelpCenterMainScreen: React.FC = () => {
  const navigation = useNavigation();
  
  const [searchQuery, setSearchQuery] = useState('');
  const [searchResults, setSearchResults] = useState<FAQItem[]>([]);
  const [isSearching, setIsSearching] = useState(false);
  const [recentTickets, setRecentTickets] = useState<RecentTicket[]>([]);

  // Animation refs
  const fadeAnimation = useRef(new Animated.Value(0)).current;
  const slideAnimation = useRef(new Animated.Value(50)).current;
  const pulseAnimation = useRef(new Animated.Value(1)).current;
  const searchAnimation = useRef(new Animated.Value(0)).current;

  // Mock data
  const faqCards: FAQCard[] = [
    {
      id: '1',
      category: FAQCategory.PAYMENTS,
      title: 'Payment Issues',
      icon: 'wallet',
      color: '#10b981',
      issueCount: 12,
    },
    {
      id: '2',
      category: FAQCategory.ORDERS,
      title: 'Delivery Delays',
      icon: 'time',
      color: '#f59e0b',
      issueCount: 8,
    },
    {
      id: '3',
      category: FAQCategory.TECHNICAL,
      title: 'App Problems',
      icon: 'phone-portrait',
      color: '#ef4444',
      issueCount: 6,
    },
    {
      id: '4',
      category: FAQCategory.ACCOUNT,
      title: 'Account Issues',
      icon: 'person',
      color: '#8b5cf6',
      issueCount: 4,
    },
    {
      id: '5',
      category: FAQCategory.VEHICLE,
      title: 'Vehicle & Documents',
      icon: 'car',
      color: '#06b6d4',
      issueCount: 3,
    },
    {
      id: '6',
      category: FAQCategory.GETTING_STARTED,
      title: 'Getting Started',
      icon: 'rocket',
      color: '#f97316',
      issueCount: 15,
    },
  ];

  const mockTickets: RecentTicket[] = [
    {
      id: 'TKT-001',
      title: 'Payment not received for order #12345',
      status: TicketStatus.IN_PROGRESS,
      createdAt: '2024-01-15',
      lastUpdate: '2 hours ago',
    },
    {
      id: 'TKT-002',
      title: 'App crashes when accepting orders',
      status: TicketStatus.OPEN,
      createdAt: '2024-01-14',
      lastUpdate: '1 day ago',
    },
    {
      id: 'TKT-003',
      title: 'Unable to upload vehicle documents',
      status: TicketStatus.RESOLVED,
      createdAt: '2024-01-12',
      lastUpdate: '3 days ago',
    },
  ];

  useEffect(() => {
    // Start entrance animations
    Animated.parallel([
      Animated.timing(fadeAnimation, {
        toValue: 1,
        duration: 800,
        useNativeDriver: true,
      }),
      Animated.timing(slideAnimation, {
        toValue: 0,
        duration: 800,
        useNativeDriver: true,
      }),
    ]).start();

    // Start pulse animation for floating chat button
    const startPulse = () => {
      Animated.sequence([
        Animated.timing(pulseAnimation, {
          toValue: 1.1,
          duration: 1000,
          useNativeDriver: true,
        }),
        Animated.timing(pulseAnimation, {
          toValue: 1,
          duration: 1000,
          useNativeDriver: true,
        }),
      ]).start(() => startPulse());
    };
    startPulse();

    // Load mock data
    setRecentTickets(mockTickets);
  }, []);

  useEffect(() => {
    if (searchQuery.length > 0) {
      setIsSearching(true);
      // Animate search results
      Animated.timing(searchAnimation, {
        toValue: 1,
        duration: 300,
        useNativeDriver: true,
      }).start();
      
      // Simulate search delay
      const timer = setTimeout(() => {
        // Mock search results
        const results: FAQItem[] = [
          {
            id: '1',
            category: FAQCategory.PAYMENTS,
            question: 'When do I get paid?',
            answer: 'Payments are processed daily and transferred within 24 hours.',
            tags: ['payment', 'earnings'],
            isPopular: true,
            helpfulCount: 234,
            notHelpfulCount: 7,
            createdAt: '2023-01-01',
            updatedAt: '2023-01-01',
          },
          {
            id: '2',
            category: FAQCategory.ORDERS,
            question: 'What if customer is not available?',
            answer: 'Try calling the customer. If no response, contact support.',
            tags: ['delivery', 'customer'],
            isPopular: false,
            helpfulCount: 89,
            notHelpfulCount: 3,
            createdAt: '2023-01-01',
            updatedAt: '2023-01-01',
          },
        ];
        setSearchResults(results.filter(item => 
          item.question.toLowerCase().includes(searchQuery.toLowerCase()) ||
          item.answer.toLowerCase().includes(searchQuery.toLowerCase())
        ));
        setIsSearching(false);
      }, 500);

      return () => clearTimeout(timer);
    } else {
      setSearchResults([]);
      Animated.timing(searchAnimation, {
        toValue: 0,
        duration: 300,
        useNativeDriver: true,
      }).start();
    }
  }, [searchQuery]);

  const handleContactSupport = () => {
    Alert.alert(
      'Contact Support',
      'Choose how you would like to contact our support team:',
      [
        { text: 'Live Chat', onPress: () => navigation.navigate('LiveChat' as never) },
        { text: 'Call Support', onPress: () => navigation.navigate('CallSupport' as never) },
        { text: 'Cancel', style: 'cancel' },
      ]
    );
  };

  const handleFloatingChat = () => {
    navigation.navigate('LiveChat' as never);
  };

  const getStatusColor = (status: TicketStatus) => {
    switch (status) {
      case TicketStatus.OPEN:
        return '#ef4444';
      case TicketStatus.IN_PROGRESS:
        return '#f59e0b';
      case TicketStatus.RESOLVED:
        return '#10b981';
      case TicketStatus.CLOSED:
        return '#6b7280';
      default:
        return '#6b7280';
    }
  };

  const getStatusText = (status: TicketStatus) => {
    switch (status) {
      case TicketStatus.OPEN:
        return 'Open';
      case TicketStatus.IN_PROGRESS:
        return 'In Progress';
      case TicketStatus.RESOLVED:
        return 'Resolved';
      case TicketStatus.CLOSED:
        return 'Closed';
      default:
        return 'Unknown';
    }
  };

  const renderHeader = () => (
    <LinearGradient
      colors={['#ef4444', '#dc2626']}
      style={{
        paddingTop: StatusBar.currentHeight || 0,
        paddingBottom: 20,
      }}
    >
      <SafeAreaView>
        <View style={{
          flexDirection: 'row',
          alignItems: 'center',
          justifyContent: 'space-between',
          paddingHorizontal: 20,
          paddingTop: 16,
        }}>
          <TouchableOpacity
            onPress={() => navigation.goBack()}
            style={{
              width: 40,
              height: 40,
              borderRadius: 20,
              backgroundColor: 'rgba(255,255,255,0.2)',
              alignItems: 'center',
              justifyContent: 'center',
            }}
          >
            <Ionicons name="arrow-back" size={24} color="white" />
          </TouchableOpacity>

          <Text style={{
            fontSize: 24,
            fontWeight: 'bold',
            color: 'white',
          }}>
            Help Center
          </Text>

          <TouchableOpacity
            onPress={handleContactSupport}
            style={{
              width: 40,
              height: 40,
              borderRadius: 20,
              backgroundColor: 'rgba(255,255,255,0.2)',
              alignItems: 'center',
              justifyContent: 'center',
            }}
          >
            <Ionicons name="headset" size={20} color="white" />
          </TouchableOpacity>
        </View>
      </SafeAreaView>
    </LinearGradient>
  );

  const renderSearchBar = () => (
    <View style={{
      marginHorizontal: 20,
      marginTop: -30,
      marginBottom: 20,
      backgroundColor: 'white',
      borderRadius: 16,
      shadowColor: '#000',
      shadowOffset: { width: 0, height: 4 },
      shadowOpacity: 0.1,
      shadowRadius: 12,
      elevation: 8,
    }}>
      <View style={{
        flexDirection: 'row',
        alignItems: 'center',
        paddingHorizontal: 16,
        paddingVertical: 12,
      }}>
        <Ionicons name="search" size={20} color="#6b7280" />
        <TextInput
          style={{
            flex: 1,
            marginLeft: 12,
            fontSize: 16,
            color: '#111827',
          }}
          placeholder="Search for help..."
          placeholderTextColor="#9ca3af"
          value={searchQuery}
          onChangeText={setSearchQuery}
        />
        {isSearching && (
          <Animated.View style={{
            transform: [{ rotate: '360deg' }],
          }}>
            <Ionicons name="refresh" size={20} color="#ef4444" />
          </Animated.View>
        )}
      </View>
    </View>
  );

  return (
    <View style={{ flex: 1, backgroundColor: '#f8fafc' }}>
      <StatusBar barStyle="light-content" backgroundColor="#ef4444" />
      
      {renderHeader()}

      <Animated.View
        style={{
          flex: 1,
          opacity: fadeAnimation,
          transform: [{ translateY: slideAnimation }],
        }}
      >
        {renderSearchBar()}

        <ScrollView
          style={{ flex: 1 }}
          showsVerticalScrollIndicator={false}
          contentContainerStyle={{ paddingBottom: 100 }}
        >
          {/* Search Results */}
          {searchResults.length > 0 && (
            <Animated.View
              style={{
                opacity: searchAnimation,
                transform: [{ translateY: searchAnimation.interpolate({
                  inputRange: [0, 1],
                  outputRange: [20, 0],
                }) }],
                marginHorizontal: 20,
                marginBottom: 20,
              }}
            >
              <Text style={{
                fontSize: 18,
                fontWeight: 'bold',
                color: '#111827',
                marginBottom: 12,
              }}>
                Search Results ({searchResults.length})
              </Text>

              {searchResults.map((item) => (
                <TouchableOpacity
                  key={item.id}
                  style={{
                    backgroundColor: 'white',
                    borderRadius: 12,
                    padding: 16,
                    marginBottom: 12,
                    shadowColor: '#000',
                    shadowOffset: { width: 0, height: 2 },
                    shadowOpacity: 0.1,
                    shadowRadius: 8,
                    elevation: 3,
                  }}
                >
                  <Text style={{
                    fontSize: 16,
                    fontWeight: '600',
                    color: '#111827',
                    marginBottom: 8,
                  }}>
                    {item.question}
                  </Text>
                  <Text style={{
                    fontSize: 14,
                    color: '#6b7280',
                    lineHeight: 20,
                  }}>
                    {item.answer.length > 100 ? `${item.answer.substring(0, 100)}...` : item.answer}
                  </Text>
                  <View style={{
                    flexDirection: 'row',
                    alignItems: 'center',
                    marginTop: 8,
                  }}>
                    <Ionicons name="thumbs-up" size={14} color="#10b981" />
                    <Text style={{
                      fontSize: 12,
                      color: '#10b981',
                      marginLeft: 4,
                      marginRight: 12,
                    }}>
                      {item.helpfulCount}
                    </Text>
                    <Ionicons name="chevron-forward" size={14} color="#6b7280" />
                  </View>
                </TouchableOpacity>
              ))}
            </Animated.View>
          )}

          {/* FAQ Categories */}
          {searchResults.length === 0 && (
            <>
              <View style={{
                marginHorizontal: 20,
                marginBottom: 20,
              }}>
                <Text style={{
                  fontSize: 20,
                  fontWeight: 'bold',
                  color: '#111827',
                  marginBottom: 16,
                }}>
                  Common Issues
                </Text>

                <View style={{
                  flexDirection: 'row',
                  flexWrap: 'wrap',
                  gap: 12,
                }}>
                  {faqCards.map((card) => (
                    <TouchableOpacity
                      key={card.id}
                      style={{
                        width: '48%',
                        backgroundColor: 'white',
                        borderRadius: 16,
                        padding: 16,
                        shadowColor: '#000',
                        shadowOffset: { width: 0, height: 2 },
                        shadowOpacity: 0.1,
                        shadowRadius: 8,
                        elevation: 3,
                      }}
                    >
                      <View style={{
                        width: 48,
                        height: 48,
                        borderRadius: 24,
                        backgroundColor: `${card.color}20`,
                        alignItems: 'center',
                        justifyContent: 'center',
                        marginBottom: 12,
                      }}>
                        <Ionicons name={card.icon as any} size={24} color={card.color} />
                      </View>

                      <Text style={{
                        fontSize: 16,
                        fontWeight: '600',
                        color: '#111827',
                        marginBottom: 4,
                      }}>
                        {card.title}
                      </Text>

                      <Text style={{
                        fontSize: 12,
                        color: '#6b7280',
                      }}>
                        {card.issueCount} articles
                      </Text>
                    </TouchableOpacity>
                  ))}
                </View>
              </View>

              {/* Recent Tickets */}
              <View style={{
                marginHorizontal: 20,
                marginBottom: 20,
              }}>
                <View style={{
                  flexDirection: 'row',
                  alignItems: 'center',
                  justifyContent: 'space-between',
                  marginBottom: 16,
                }}>
                  <Text style={{
                    fontSize: 20,
                    fontWeight: 'bold',
                    color: '#111827',
                  }}>
                    Recent Tickets
                  </Text>

                  <TouchableOpacity
                    onPress={() => navigation.navigate('TicketHistory' as never)}
                  >
                    <Text style={{
                      fontSize: 14,
                      color: '#f97316',
                      fontWeight: '600',
                    }}>
                      View All
                    </Text>
                  </TouchableOpacity>
                </View>

                {recentTickets.map((ticket) => (
                  <TouchableOpacity
                    key={ticket.id}
                    style={{
                      backgroundColor: 'white',
                      borderRadius: 12,
                      padding: 16,
                      marginBottom: 12,
                      shadowColor: '#000',
                      shadowOffset: { width: 0, height: 2 },
                      shadowOpacity: 0.1,
                      shadowRadius: 8,
                      elevation: 3,
                    }}
                  >
                    <View style={{
                      flexDirection: 'row',
                      alignItems: 'center',
                      justifyContent: 'space-between',
                      marginBottom: 8,
                    }}>
                      <Text style={{
                        fontSize: 12,
                        color: '#6b7280',
                        fontWeight: '600',
                      }}>
                        {ticket.id}
                      </Text>

                      <View style={{
                        backgroundColor: `${getStatusColor(ticket.status)}20`,
                        paddingHorizontal: 8,
                        paddingVertical: 4,
                        borderRadius: 12,
                      }}>
                        <Text style={{
                          fontSize: 10,
                          color: getStatusColor(ticket.status),
                          fontWeight: '600',
                        }}>
                          {getStatusText(ticket.status)}
                        </Text>
                      </View>
                    </View>

                    <Text style={{
                      fontSize: 16,
                      fontWeight: '600',
                      color: '#111827',
                      marginBottom: 8,
                    }}>
                      {ticket.title}
                    </Text>

                    <Text style={{
                      fontSize: 12,
                      color: '#6b7280',
                    }}>
                      Last updated: {ticket.lastUpdate}
                    </Text>
                  </TouchableOpacity>
                ))}
              </View>

              {/* Contact Support Button */}
              <View style={{
                marginHorizontal: 20,
                marginBottom: 20,
              }}>
                <TouchableOpacity
                  onPress={handleContactSupport}
                  style={{
                    backgroundColor: '#f97316',
                    borderRadius: 16,
                    padding: 20,
                    flexDirection: 'row',
                    alignItems: 'center',
                    justifyContent: 'center',
                    shadowColor: '#f97316',
                    shadowOffset: { width: 0, height: 4 },
                    shadowOpacity: 0.3,
                    shadowRadius: 8,
                    elevation: 6,
                  }}
                >
                  <Ionicons name="headset" size={24} color="white" />
                  <Text style={{
                    fontSize: 18,
                    fontWeight: '600',
                    color: 'white',
                    marginLeft: 12,
                  }}>
                    Contact Support
                  </Text>
                </TouchableOpacity>
              </View>
            </>
          )}
        </ScrollView>
      </Animated.View>

      {/* Floating Chat Button */}
      <Animated.View
        style={{
          position: 'absolute',
          bottom: 30,
          right: 20,
          transform: [{ scale: pulseAnimation }],
        }}
      >
        <TouchableOpacity
          onPress={handleFloatingChat}
          style={{
            width: 60,
            height: 60,
            borderRadius: 30,
            backgroundColor: '#10b981',
            alignItems: 'center',
            justifyContent: 'center',
            shadowColor: '#10b981',
            shadowOffset: { width: 0, height: 4 },
            shadowOpacity: 0.4,
            shadowRadius: 12,
            elevation: 8,
          }}
        >
          <Ionicons name="chatbubbles" size={28} color="white" />
        </TouchableOpacity>
      </Animated.View>
    </View>
  );
};

export default HelpCenterMainScreen;
