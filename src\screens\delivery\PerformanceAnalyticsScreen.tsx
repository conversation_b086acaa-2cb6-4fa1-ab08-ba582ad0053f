import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  ScrollView,
  RefreshControl,
  TouchableOpacity,
  Dimensions,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import { useNavigation } from '@react-navigation/native';
import { Card, Button, Badge, LoadingSpinner } from '../../components/ui';
import { formatCurrency, formatDate } from '../../utils/helpers';
import { 
  PerformanceMetrics,
  DailyPerformance,
  RatingTrend,
  DeliveryTimeAnalysis,
  LocationAnalysis,
} from '../../types/deliveryHistory';

const { width } = Dimensions.get('window');

// Mock performance data
const mockPerformanceMetrics: PerformanceMetrics = {
  averageDeliveryTime: 22.5,
  averagePickupTime: 8.3,
  averageWaitingTime: 5.2,
  averageDistance: 4.8,
  averageSpeed: 18.5,
  totalOrders: 156,
  completedOrders: 142,
  cancelledOrders: 8,
  rejectedOrders: 6,
  acceptanceRate: 96.2,
  completionRate: 91.0,
  cancellationRate: 5.1,
  onTimeDeliveryRate: 88.7,
  averageRating: 4.6,
  totalRatings: 128,
  ratingDistribution: {
    1: 2,
    2: 3,
    3: 8,
    4: 35,
    5: 80,
  },
  totalEarnings: 137250,
  averageEarningsPerOrder: 880,
  totalTips: 25260,
  averageTipPerOrder: 162,
  period: 'month',
  startDate: '2024-01-01',
  endDate: '2024-01-31',
};

const mockDailyPerformance: DailyPerformance[] = [
  { date: '2024-01-15', ordersCompleted: 8, totalEarnings: 7020, averageRating: 4.7, averageDeliveryTime: 21, totalDistance: 38.5, hoursWorked: 6.5 },
  { date: '2024-01-16', ordersCompleted: 12, totalEarnings: 10560, averageRating: 4.5, averageDeliveryTime: 24, totalDistance: 52.3, hoursWorked: 8.0 },
  { date: '2024-01-17', ordersCompleted: 6, totalEarnings: 5340, averageRating: 4.8, averageDeliveryTime: 19, totalDistance: 28.7, hoursWorked: 5.0 },
  { date: '2024-01-18', ordersCompleted: 10, totalEarnings: 8850, averageRating: 4.6, averageDeliveryTime: 23, totalDistance: 45.2, hoursWorked: 7.5 },
  { date: '2024-01-19', ordersCompleted: 14, totalEarnings: 4120, averageRating: 4.4, averageDeliveryTime: 26, totalDistance: 61.8, hoursWorked: 9.0 },
  { date: '2024-01-20', ordersCompleted: 9, totalEarnings: 2650, averageRating: 4.7, averageDeliveryTime: 20, totalDistance: 41.3, hoursWorked: 6.0 },
  { date: '2024-01-21', ordersCompleted: 11, totalEarnings: 3240, averageRating: 4.6, averageDeliveryTime: 22, totalDistance: 48.9, hoursWorked: 7.0 },
];

const mockRatingTrends: RatingTrend[] = [
  { date: '2024-01-15', rating: 4.7, ordersCount: 8 },
  { date: '2024-01-16', rating: 4.5, ordersCount: 12 },
  { date: '2024-01-17', rating: 4.8, ordersCount: 6 },
  { date: '2024-01-18', rating: 4.6, ordersCount: 10 },
  { date: '2024-01-19', rating: 4.4, ordersCount: 14 },
  { date: '2024-01-20', rating: 4.7, ordersCount: 9 },
  { date: '2024-01-21', rating: 4.6, ordersCount: 11 },
];

const mockDeliveryTimeAnalysis: DeliveryTimeAnalysis[] = [
  { timeSlot: '09:00-12:00', averageTime: 18.5, ordersCount: 32, efficiency: 92.3 },
  { timeSlot: '12:00-15:00', averageTime: 24.2, ordersCount: 45, efficiency: 78.5 },
  { timeSlot: '15:00-18:00', averageTime: 21.8, ordersCount: 38, efficiency: 85.7 },
  { timeSlot: '18:00-21:00', averageTime: 26.5, ordersCount: 52, efficiency: 72.1 },
  { timeSlot: '21:00-24:00', averageTime: 19.3, ordersCount: 28, efficiency: 89.4 },
];

const mockLocationAnalysis: LocationAnalysis[] = [
  { area: 'DHA Phase 2', city: 'Lahore', ordersCount: 28, averageDeliveryTime: 20.5, averageDistance: 3.8, averageEarnings: 315, averageRating: 4.7 },
  { area: 'Gulberg III', city: 'Lahore', ordersCount: 22, averageDeliveryTime: 24.2, averageDistance: 5.2, averageEarnings: 285, averageRating: 4.5 },
  { area: 'Model Town', city: 'Lahore', ordersCount: 18, averageDeliveryTime: 22.8, averageDistance: 4.5, averageEarnings: 298, averageRating: 4.6 },
  { area: 'Johar Town', city: 'Lahore', ordersCount: 25, averageDeliveryTime: 26.1, averageDistance: 6.1, averageEarnings: 275, averageRating: 4.4 },
  { area: 'Cantt', city: 'Lahore', ordersCount: 15, averageDeliveryTime: 19.7, averageDistance: 3.2, averageEarnings: 325, averageRating: 4.8 },
];

const PerformanceAnalyticsScreen: React.FC = () => {
  const navigation = useNavigation();
  const [refreshing, setRefreshing] = useState(false);
  const [selectedPeriod, setSelectedPeriod] = useState<'week' | 'month' | 'year'>('month');
  const [selectedTab, setSelectedTab] = useState<'overview' | 'trends' | 'time' | 'location'>('overview');
  
  const [performanceMetrics, setPerformanceMetrics] = useState<PerformanceMetrics>(mockPerformanceMetrics);
  const [dailyPerformance, setDailyPerformance] = useState<DailyPerformance[]>(mockDailyPerformance);
  const [ratingTrends, setRatingTrends] = useState<RatingTrend[]>(mockRatingTrends);
  const [deliveryTimeAnalysis, setDeliveryTimeAnalysis] = useState<DeliveryTimeAnalysis[]>(mockDeliveryTimeAnalysis);
  const [locationAnalysis, setLocationAnalysis] = useState<LocationAnalysis[]>(mockLocationAnalysis);

  const onRefresh = async () => {
    setRefreshing(true);
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 1000));
    setRefreshing(false);
  };

  const renderPeriodSelector = () => (
    <View style={{
      flexDirection: 'row',
      backgroundColor: '#ffffff',
      paddingHorizontal: 20,
      paddingVertical: 12,
      borderBottomWidth: 1,
      borderBottomColor: '#e5e7eb',
      gap: 8,
    }}>
      {[
        { key: 'week', label: 'This Week' },
        { key: 'month', label: 'This Month' },
        { key: 'year', label: 'This Year' },
      ].map((period) => (
        <TouchableOpacity
          key={period.key}
          onPress={() => setSelectedPeriod(period.key as any)}
          style={{
            flex: 1,
            paddingVertical: 8,
            paddingHorizontal: 12,
            borderRadius: 8,
            backgroundColor: selectedPeriod === period.key ? '#f97316' : '#f9fafb',
            alignItems: 'center',
          }}
        >
          <Text style={{
            fontSize: 14,
            fontWeight: '600',
            color: selectedPeriod === period.key ? 'white' : '#6b7280',
          }}>
            {period.label}
          </Text>
        </TouchableOpacity>
      ))}
    </View>
  );

  const renderTabBar = () => (
    <View style={{
      flexDirection: 'row',
      backgroundColor: '#ffffff',
      paddingHorizontal: 20,
      paddingVertical: 12,
      borderBottomWidth: 1,
      borderBottomColor: '#e5e7eb',
    }}>
      {[
        { key: 'overview', label: 'Overview', icon: 'analytics-outline' },
        { key: 'trends', label: 'Trends', icon: 'trending-up-outline' },
        { key: 'time', label: 'Time Analysis', icon: 'time-outline' },
        { key: 'location', label: 'Location', icon: 'location-outline' },
      ].map((tab) => (
        <TouchableOpacity
          key={tab.key}
          onPress={() => setSelectedTab(tab.key as any)}
          style={{
            flex: 1,
            alignItems: 'center',
            paddingVertical: 8,
            borderBottomWidth: 2,
            borderBottomColor: selectedTab === tab.key ? '#ef4444' : 'transparent',
          }}
        >
          <Ionicons
            name={tab.icon as any}
            size={20}
            color={selectedTab === tab.key ? '#f97316' : '#6b7280'}
          />
          <Text style={{
            fontSize: 12,
            fontWeight: selectedTab === tab.key ? '600' : '400',
            color: selectedTab === tab.key ? '#f97316' : '#6b7280',
            marginTop: 4,
          }}>
            {tab.label}
          </Text>
        </TouchableOpacity>
      ))}
    </View>
  );

  const renderOverviewMetrics = () => (
    <View style={{ padding: 20 }}>
      {/* Key Performance Indicators */}
      <Card variant="elevated" padding="lg" style={{ marginBottom: 20 }}>
        <Text style={{ fontSize: 18, fontWeight: 'bold', color: '#111827', marginBottom: 16 }}>
          Key Performance Indicators
        </Text>
        
        <View style={{ flexDirection: 'row', flexWrap: 'wrap', justifyContent: 'space-between' }}>
          <View style={{ width: '48%', alignItems: 'center', backgroundColor: '#f0fdf4', padding: 16, borderRadius: 12, marginBottom: 12 }}>
            <Text style={{ fontSize: 28, fontWeight: 'bold', color: '#16a34a' }}>
              {performanceMetrics.completionRate.toFixed(1)}%
            </Text>
            <Text style={{ fontSize: 13, color: '#6b7280', textAlign: 'center', marginTop: 4 }}>Completion Rate</Text>
          </View>

          <View style={{ width: '48%', alignItems: 'center', backgroundColor: '#fef3e2', padding: 16, borderRadius: 12, marginBottom: 12 }}>
            <Text style={{ fontSize: 28, fontWeight: 'bold', color: '#f97316' }}>
              {performanceMetrics.averageRating.toFixed(1)}
            </Text>
            <Text style={{ fontSize: 13, color: '#6b7280', textAlign: 'center', marginTop: 4 }}>Average Rating</Text>
          </View>

          <View style={{ width: '48%', alignItems: 'center', backgroundColor: '#eff6ff', padding: 16, borderRadius: 12 }}>
            <Text style={{ fontSize: 28, fontWeight: 'bold', color: '#3b82f6' }}>
              {performanceMetrics.averageDeliveryTime.toFixed(0)} min
            </Text>
            <Text style={{ fontSize: 13, color: '#6b7280', textAlign: 'center', marginTop: 4 }}>Avg Delivery Time</Text>
          </View>

          <View style={{ width: '48%', alignItems: 'center', backgroundColor: '#f3e8ff', padding: 16, borderRadius: 12 }}>
            <Text style={{ fontSize: 28, fontWeight: 'bold', color: '#8b5cf6' }}>
              {performanceMetrics.acceptanceRate.toFixed(1)}%
            </Text>
            <Text style={{ fontSize: 13, color: '#6b7280', textAlign: 'center', marginTop: 4 }}>Acceptance Rate</Text>
          </View>
        </View>
      </Card>

      {/* Order Statistics */}
      <Card variant="elevated" padding="lg" style={{ marginBottom: 20 }}>
        <Text style={{ fontSize: 18, fontWeight: 'bold', color: '#111827', marginBottom: 16 }}>
          Order Statistics
        </Text>
        
        <View style={{ flexDirection: 'row', justifyContent: 'space-between', marginBottom: 16 }}>
          <View style={{ alignItems: 'center', flex: 1 }}>
            <Text style={{ fontSize: 20, fontWeight: 'bold', color: '#111827' }}>
              {performanceMetrics.totalOrders}
            </Text>
            <Text style={{ fontSize: 12, color: '#6b7280' }}>Total Orders</Text>
          </View>
          <View style={{ alignItems: 'center', flex: 1 }}>
            <Text style={{ fontSize: 20, fontWeight: 'bold', color: '#10b981' }}>
              {performanceMetrics.completedOrders}
            </Text>
            <Text style={{ fontSize: 12, color: '#6b7280' }}>Completed</Text>
          </View>
          <View style={{ alignItems: 'center', flex: 1 }}>
            <Text style={{ fontSize: 20, fontWeight: 'bold', color: '#ef4444' }}>
              {performanceMetrics.cancelledOrders}
            </Text>
            <Text style={{ fontSize: 12, color: '#6b7280' }}>Cancelled</Text>
          </View>
          <View style={{ alignItems: 'center', flex: 1 }}>
            <Text style={{ fontSize: 20, fontWeight: 'bold', color: '#f59e0b' }}>
              {performanceMetrics.rejectedOrders}
            </Text>
            <Text style={{ fontSize: 12, color: '#6b7280' }}>Rejected</Text>
          </View>
        </View>

        {/* Progress bars for completion rate */}
        <View style={{ marginTop: 8 }}>
          <View style={{ flexDirection: 'row', justifyContent: 'space-between', marginBottom: 4 }}>
            <Text style={{ fontSize: 12, color: '#6b7280' }}>Completion Rate</Text>
            <Text style={{ fontSize: 12, fontWeight: '600', color: '#111827' }}>
              {performanceMetrics.completionRate.toFixed(1)}%
            </Text>
          </View>
          <View style={{ height: 6, backgroundColor: '#e5e7eb', borderRadius: 3 }}>
            <View style={{
              height: 6,
              backgroundColor: '#10b981',
              borderRadius: 3,
              width: `${performanceMetrics.completionRate}%`,
            }} />
          </View>
        </View>
      </Card>

      {/* Earnings Summary */}
      <Card variant="elevated" padding="lg" style={{ marginBottom: 20 }}>
        <Text style={{ fontSize: 18, fontWeight: 'bold', color: '#111827', marginBottom: 16 }}>
          Earnings Summary
        </Text>
        
        <View style={{ flexDirection: 'row', justifyContent: 'space-between', marginBottom: 12 }}>
          <View style={{ alignItems: 'center', flex: 1 }}>
            <Text style={{ fontSize: 18, fontWeight: 'bold', color: '#10b981' }}>
              {formatCurrency(performanceMetrics.totalEarnings)}
            </Text>
            <Text style={{ fontSize: 12, color: '#6b7280' }}>Total Earnings</Text>
          </View>
          <View style={{ alignItems: 'center', flex: 1 }}>
            <Text style={{ fontSize: 18, fontWeight: 'bold', color: '#f97316' }}>
              {formatCurrency(performanceMetrics.totalTips)}
            </Text>
            <Text style={{ fontSize: 12, color: '#6b7280' }}>Total Tips</Text>
          </View>
        </View>
        
        <View style={{ flexDirection: 'row', justifyContent: 'space-between', paddingTop: 12, borderTopWidth: 1, borderTopColor: '#e5e7eb' }}>
          <View style={{ alignItems: 'center', flex: 1 }}>
            <Text style={{ fontSize: 14, fontWeight: '600', color: '#111827' }}>
              {formatCurrency(performanceMetrics.averageEarningsPerOrder)}
            </Text>
            <Text style={{ fontSize: 12, color: '#6b7280' }}>Avg per Order</Text>
          </View>
          <View style={{ alignItems: 'center', flex: 1 }}>
            <Text style={{ fontSize: 14, fontWeight: '600', color: '#111827' }}>
              {formatCurrency(performanceMetrics.averageTipPerOrder)}
            </Text>
            <Text style={{ fontSize: 12, color: '#6b7280' }}>Avg Tip</Text>
          </View>
        </View>
      </Card>

      {/* Rating Distribution */}
      <Card variant="elevated" padding="lg">
        <Text style={{ fontSize: 18, fontWeight: 'bold', color: '#111827', marginBottom: 16 }}>
          Rating Distribution
        </Text>
        
        <View style={{ alignItems: 'center', marginBottom: 16 }}>
          <Text style={{ fontSize: 32, fontWeight: 'bold', color: '#f59e0b' }}>
            {performanceMetrics.averageRating.toFixed(1)}
          </Text>
          <View style={{ flexDirection: 'row', marginVertical: 4 }}>
            {[1, 2, 3, 4, 5].map((star) => (
              <Ionicons
                key={star}
                name={star <= Math.round(performanceMetrics.averageRating) ? 'star' : 'star-outline'}
                size={20}
                color="#f59e0b"
                style={{ marginHorizontal: 1 }}
              />
            ))}
          </View>
          <Text style={{ fontSize: 14, color: '#6b7280' }}>
            Based on {performanceMetrics.totalRatings} ratings
          </Text>
        </View>

        {[5, 4, 3, 2, 1].map((rating) => {
          const count = performanceMetrics.ratingDistribution[rating as keyof typeof performanceMetrics.ratingDistribution];
          const percentage = (count / performanceMetrics.totalRatings) * 100;
          
          return (
            <View key={rating} style={{ flexDirection: 'row', alignItems: 'center', marginBottom: 8 }}>
              <Text style={{ fontSize: 12, color: '#6b7280', width: 20 }}>{rating}</Text>
              <Ionicons name="star" size={12} color="#f59e0b" style={{ marginHorizontal: 4 }} />
              <View style={{ flex: 1, height: 6, backgroundColor: '#e5e7eb', borderRadius: 3, marginHorizontal: 8 }}>
                <View style={{
                  height: 6,
                  backgroundColor: '#f59e0b',
                  borderRadius: 3,
                  width: `${percentage}%`,
                }} />
              </View>
              <Text style={{ fontSize: 12, color: '#6b7280', width: 30, textAlign: 'right' }}>
                {count}
              </Text>
            </View>
          );
        })}
      </Card>
    </View>
  );

  const renderTrendsAnalysis = () => (
    <View style={{ padding: 20 }}>
      {/* Daily Performance Chart */}
      <Card variant="elevated" padding="lg" style={{ marginBottom: 20 }}>
        <Text style={{ fontSize: 18, fontWeight: 'bold', color: '#111827', marginBottom: 16 }}>
          Daily Performance Trends
        </Text>

        {/* Simple chart representation */}
        <View style={{ height: 200, backgroundColor: '#f9fafb', borderRadius: 8, padding: 16, marginBottom: 16 }}>
          <Text style={{ fontSize: 14, color: '#6b7280', textAlign: 'center', marginTop: 80 }}>
            📊 Performance Chart
          </Text>
          <Text style={{ fontSize: 12, color: '#9ca3af', textAlign: 'center', marginTop: 8 }}>
            Interactive chart showing daily trends
          </Text>
        </View>

        {/* Performance Insights */}
        <View style={{ flexDirection: 'row', justifyContent: 'space-between' }}>
          <View style={{ alignItems: 'center', flex: 1 }}>
            <Text style={{ fontSize: 18, fontWeight: 'bold', color: '#10b981' }}>
              {performanceMetrics.averageRating.toFixed(1)}
            </Text>
            <Text style={{ fontSize: 12, color: '#6b7280' }}>Avg Rating</Text>
          </View>
          <View style={{ alignItems: 'center', flex: 1 }}>
            <Text style={{ fontSize: 18, fontWeight: 'bold', color: '#f59e0b' }}>
              {performanceMetrics.averageDeliveryTime}m
            </Text>
            <Text style={{ fontSize: 12, color: '#6b7280' }}>Avg Time</Text>
          </View>
          <View style={{ alignItems: 'center', flex: 1 }}>
            <Text style={{ fontSize: 18, fontWeight: 'bold', color: '#3b82f6' }}>
              {performanceMetrics.onTimeDeliveryRate}%
            </Text>
            <Text style={{ fontSize: 12, color: '#6b7280' }}>On Time</Text>
          </View>
        </View>
      </Card>

      {/* Rating Trends */}
      <Card variant="elevated" padding="lg" style={{ marginBottom: 20 }}>
        <Text style={{ fontSize: 18, fontWeight: 'bold', color: '#111827', marginBottom: 16 }}>
          Rating Trends
        </Text>

        <View style={{ height: 150, backgroundColor: '#f9fafb', borderRadius: 8, padding: 16, marginBottom: 16 }}>
          <Text style={{ fontSize: 14, color: '#6b7280', textAlign: 'center', marginTop: 50 }}>
            ⭐ Rating Trend Chart
          </Text>
          <Text style={{ fontSize: 12, color: '#9ca3af', textAlign: 'center', marginTop: 8 }}>
            Weekly rating progression
          </Text>
        </View>

        <View style={{ backgroundColor: '#fef3e2', padding: 12, borderRadius: 8 }}>
          <Text style={{ fontSize: 14, fontWeight: '600', color: '#f97316', marginBottom: 4 }}>
            📈 Improvement Trend
          </Text>
          <Text style={{ fontSize: 12, color: '#6b7280' }}>
            Your rating has improved by 0.3 points this month. Keep up the excellent service!
          </Text>
        </View>
      </Card>
    </View>
  );

  const renderTimeAnalysis = () => (
    <View style={{ padding: 20 }}>
      {/* Time Slot Performance */}
      <Card variant="elevated" padding="lg" style={{ marginBottom: 20 }}>
        <Text style={{ fontSize: 18, fontWeight: 'bold', color: '#111827', marginBottom: 16 }}>
          Performance by Time Slot
        </Text>

        {deliveryTimeAnalysis.map((slot, index) => (
          <View key={index} style={{ marginBottom: 16 }}>
            <View style={{ flexDirection: 'row', justifyContent: 'space-between', alignItems: 'center', marginBottom: 8 }}>
              <Text style={{ fontSize: 14, fontWeight: '600', color: '#111827' }}>
                {slot.timeSlot}
              </Text>
              <Text style={{ fontSize: 14, fontWeight: 'bold', color: '#f97316' }}>
                {slot.averageTime.toFixed(1)}m avg
              </Text>
            </View>

            <View style={{ flexDirection: 'row', alignItems: 'center', marginBottom: 8 }}>
              <View style={{ flex: 1, height: 6, backgroundColor: '#e5e7eb', borderRadius: 3, marginRight: 8 }}>
                <View style={{
                  width: `${Math.min((slot.averageTime / 30) * 100, 100)}%`,
                  height: '100%',
                  backgroundColor: slot.averageTime <= 20 ? '#10b981' : slot.averageTime <= 25 ? '#f59e0b' : '#ef4444',
                  borderRadius: 3,
                }} />
              </View>
              <Text style={{ fontSize: 12, color: '#6b7280', width: 60 }}>
                {slot.deliveries} orders
              </Text>
            </View>

            <View style={{ flexDirection: 'row', justifyContent: 'space-between' }}>
              <Text style={{ fontSize: 12, color: '#6b7280' }}>
                Best: {slot.bestTime}m
              </Text>
              <Text style={{ fontSize: 12, color: '#6b7280' }}>
                Worst: {slot.worstTime}m
              </Text>
            </View>
          </View>
        ))}
      </Card>

      {/* Peak Hours Analysis */}
      <Card variant="elevated" padding="lg" style={{ marginBottom: 20 }}>
        <Text style={{ fontSize: 18, fontWeight: 'bold', color: '#111827', marginBottom: 16 }}>
          Peak Hours Analysis
        </Text>

        <View style={{ backgroundColor: '#f0f9ff', padding: 16, borderRadius: 8, marginBottom: 16 }}>
          <Text style={{ fontSize: 14, fontWeight: '600', color: '#0369a1', marginBottom: 8 }}>
            🕐 Busiest Time: 7:00 PM - 9:00 PM
          </Text>
          <Text style={{ fontSize: 12, color: '#6b7280' }}>
            Average delivery time during peak: 28.5 minutes
          </Text>
        </View>

        <View style={{ backgroundColor: '#f0fdf4', padding: 16, borderRadius: 8 }}>
          <Text style={{ fontSize: 14, fontWeight: '600', color: '#15803d', marginBottom: 8 }}>
            ⚡ Fastest Time: 11:00 AM - 1:00 PM
          </Text>
          <Text style={{ fontSize: 12, color: '#6b7280' }}>
            Average delivery time: 18.2 minutes
          </Text>
        </View>
      </Card>
    </View>
  );

  const renderLocationAnalysis = () => (
    <View style={{ padding: 20 }}>
      {/* Top Performing Areas */}
      <Card variant="elevated" padding="lg" style={{ marginBottom: 20 }}>
        <Text style={{ fontSize: 18, fontWeight: 'bold', color: '#111827', marginBottom: 16 }}>
          Top Performing Areas
        </Text>

        {locationAnalysis.slice(0, 5).map((area, index) => (
          <View key={index} style={{ marginBottom: 16 }}>
            <View style={{ flexDirection: 'row', justifyContent: 'space-between', alignItems: 'center', marginBottom: 8 }}>
              <View style={{ flex: 1 }}>
                <Text style={{ fontSize: 14, fontWeight: '600', color: '#111827' }}>
                  {area.area}, {area.city}
                </Text>
                <Text style={{ fontSize: 12, color: '#6b7280' }}>
                  {area.ordersCount} orders • {area.averageDistance.toFixed(1)}km avg
                </Text>
              </View>
              <View style={{ alignItems: 'flex-end' }}>
                <Text style={{ fontSize: 14, fontWeight: 'bold', color: '#f97316' }}>
                  {formatCurrency(area.averageEarnings)}
                </Text>
                <View style={{ flexDirection: 'row', alignItems: 'center' }}>
                  <Ionicons name="star" size={12} color="#f59e0b" />
                  <Text style={{ fontSize: 12, color: '#6b7280', marginLeft: 2 }}>
                    {area.averageRating.toFixed(1)}
                  </Text>
                </View>
              </View>
            </View>

            <View style={{ flexDirection: 'row', alignItems: 'center' }}>
              <View style={{ flex: 1, height: 4, backgroundColor: '#e5e7eb', borderRadius: 2, marginRight: 8 }}>
                <View style={{
                  width: `${(area.averageEarnings / Math.max(...locationAnalysis.map(a => a.averageEarnings))) * 100}%`,
                  height: '100%',
                  backgroundColor: '#f97316',
                  borderRadius: 2,
                }} />
              </View>
              <Text style={{ fontSize: 10, color: '#6b7280' }}>
                {area.averageDeliveryTime.toFixed(1)}m
              </Text>
            </View>
          </View>
        ))}
      </Card>

      {/* Location Insights */}
      <Card variant="elevated" padding="lg" style={{ marginBottom: 20 }}>
        <Text style={{ fontSize: 18, fontWeight: 'bold', color: '#111827', marginBottom: 16 }}>
          Location Insights
        </Text>

        <View style={{ backgroundColor: '#fef3e2', padding: 12, borderRadius: 8, marginBottom: 12 }}>
          <Text style={{ fontSize: 14, fontWeight: '600', color: '#f97316', marginBottom: 4 }}>
            💰 Highest Earning Area
          </Text>
          <Text style={{ fontSize: 16, fontWeight: 'bold', color: '#111827' }}>
            {locationAnalysis.reduce((highest, area) => area.averageEarnings > highest.averageEarnings ? area : highest).area}
          </Text>
          <Text style={{ fontSize: 12, color: '#6b7280' }}>
            {formatCurrency(locationAnalysis.reduce((highest, area) => area.averageEarnings > highest.averageEarnings ? area : highest).averageEarnings)} average per delivery
          </Text>
        </View>

        <View style={{ backgroundColor: '#fef3e2', padding: 12, borderRadius: 8 }}>
          <Text style={{ fontSize: 14, fontWeight: '600', color: '#f97316', marginBottom: 4 }}>
            ⭐ Highest Rated Area
          </Text>
          <Text style={{ fontSize: 16, fontWeight: 'bold', color: '#111827' }}>
            {locationAnalysis.reduce((highest, area) => area.averageRating > highest.averageRating ? area : highest).area}
          </Text>
          <Text style={{ fontSize: 12, color: '#6b7280' }}>
            {locationAnalysis.reduce((highest, area) => area.averageRating > highest.averageRating ? area : highest).averageRating.toFixed(1)} stars average
          </Text>
        </View>
      </Card>
    </View>
  );

  return (
    <SafeAreaView style={{ flex: 1, backgroundColor: '#f8fafc' }}>
      <View style={{
        paddingHorizontal: 20,
        paddingVertical: 16,
        backgroundColor: '#ffffff',
        borderBottomWidth: 1,
        borderBottomColor: '#e5e7eb',
        flexDirection: 'row',
        alignItems: 'center',
      }}>
        <TouchableOpacity
          onPress={() => navigation.goBack()}
          style={{ marginRight: 16 }}
        >
          <Ionicons name="arrow-back" size={24} color="#111827" />
        </TouchableOpacity>
        <Text style={{ fontSize: 24, fontWeight: 'bold', color: '#111827' }}>
          Performance Analytics
        </Text>
      </View>

      {renderPeriodSelector()}
      {renderTabBar()}

      <ScrollView
        style={{ flex: 1 }}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
        showsVerticalScrollIndicator={false}
      >
        {selectedTab === 'overview' && renderOverviewMetrics()}
        {selectedTab === 'trends' && renderTrendsAnalysis()}
        {selectedTab === 'time' && renderTimeAnalysis()}
        {selectedTab === 'location' && renderLocationAnalysis()}
      </ScrollView>
    </SafeAreaView>
  );
};

export default PerformanceAnalyticsScreen;
