import React, { useCallback, useRef, useState } from 'react';
import {
  ScrollView,
  ScrollViewProps,
  NativeScrollEvent,
  NativeSyntheticEvent,
  RefreshControl,
} from 'react-native';
import { throttle } from '../../utils/performanceUtils';
import { PERFORMANCE_CONFIG } from '../../config/performanceConfig';

interface OptimizedScrollViewProps extends ScrollViewProps {
  onRefresh?: () => Promise<void>;
  enableOptimization?: boolean;
  scrollEventThrottle?: number;
  onScrollOptimized?: (event: NativeSyntheticEvent<NativeScrollEvent>) => void;
}

export const OptimizedScrollView: React.FC<OptimizedScrollViewProps> = ({
  children,
  onRefresh,
  enableOptimization = true,
  scrollEventThrottle = 16,
  onScrollOptimized,
  onScroll,
  refreshControl,
  ...props
}) => {
  const [refreshing, setRefreshing] = useState(false);
  const scrollViewRef = useRef<ScrollView>(null);

  // Throttled scroll handler for better performance
  const throttledScrollHandler = useCallback(
    throttle((event: NativeSyntheticEvent<NativeScrollEvent>) => {
      onScrollOptimized?.(event);
    }, PERFORMANCE_CONFIG.UI.DEBOUNCE_DELAYS.SCROLL),
    [onScrollOptimized]
  );

  // Handle refresh with loading state
  const handleRefresh = useCallback(async () => {
    if (!onRefresh || refreshing) return;

    setRefreshing(true);
    try {
      await onRefresh();
    } catch (error) {
      console.error('Refresh error:', error);
    } finally {
      setRefreshing(false);
    }
  }, [onRefresh, refreshing]);

  // Combined scroll handler
  const handleScroll = useCallback(
    (event: NativeSyntheticEvent<NativeScrollEvent>) => {
      // Call original onScroll if provided
      onScroll?.(event);
      
      // Call optimized scroll handler if optimization is enabled
      if (enableOptimization) {
        throttledScrollHandler(event);
      }
    },
    [onScroll, throttledScrollHandler, enableOptimization]
  );

  // Optimized refresh control
  const optimizedRefreshControl = refreshControl || (onRefresh ? (
    <RefreshControl
      refreshing={refreshing}
      onRefresh={handleRefresh}
      tintColor="#ef4444"
      colors={['#ef4444']}
    />
  ) : undefined);

  return (
    <ScrollView
      ref={scrollViewRef}
      onScroll={handleScroll}
      scrollEventThrottle={scrollEventThrottle}
      refreshControl={optimizedRefreshControl}
      // Performance optimizations
      removeClippedSubviews={enableOptimization}
      keyboardShouldPersistTaps="handled"
      showsVerticalScrollIndicator={false}
      showsHorizontalScrollIndicator={false}
      // Memory optimizations
      maintainVisibleContentPosition={
        enableOptimization ? {
          minIndexForVisible: 0,
          autoscrollToTopThreshold: 10,
        } : undefined
      }
      {...props}
    >
      {children}
    </ScrollView>
  );
};

export default OptimizedScrollView;
