import React, { createContext, useContext, useReducer, useEffect } from 'react';
import AsyncStorage from '@react-native-async-storage/async-storage';
import {
  AuthState,
  AuthContextType,
  LoginCredentials,
  PhoneLoginCredentials,
  AuthUser,
  AuthTokens,
  RegistrationData,
} from '../types/auth';
import { mockAuthService } from '../services/auth/mockAuthService';
import AuthService, { LoginResponse } from '../services/api/authService';
import DocumentService from '../services/api/documentService';
import { STORAGE_KEYS, setAuthToken, clearAuthToken } from '../services/api/apiConfig';
import { sessionManager } from '../services/auth/sessionManager';

// Initial state
const initialState: AuthState = {
  user: null,
  tokens: null,
  isLoading: true,
  isAuthenticated: false,
  error: null,
};

// Action types
type AuthAction =
  | { type: 'SET_LOADING'; payload: boolean }
  | { type: 'SET_ERROR'; payload: string | null }
  | { type: 'LOGIN_SUCCESS'; payload: { user: AuthUser; tokens: AuthTokens } }
  | { type: 'LOGOUT' }
  | { type: 'REFRESH_TOKEN_SUCCESS'; payload: AuthTokens }
  | { type: 'UPDATE_USER'; payload: Partial<AuthUser> }
  | { type: 'UPDATE_VERIFICATION_STATUS'; payload: { verificationStatus: string; documentStatus: any; canGoOnline: boolean } }
  | { type: 'CLEAR_ERROR' };

// Reducer
const authReducer = (state: AuthState, action: AuthAction): AuthState => {
  switch (action.type) {
    case 'SET_LOADING':
      return {
        ...state,
        isLoading: action.payload,
      };
    case 'SET_ERROR':
      return {
        ...state,
        error: action.payload,
        isLoading: false,
      };
    case 'LOGIN_SUCCESS':
      return {
        ...state,
        user: action.payload.user,
        tokens: action.payload.tokens,
        isAuthenticated: true,
        isLoading: false,
        error: null,
      };
    case 'LOGOUT':
      return {
        ...state,
        user: null,
        tokens: null,
        isAuthenticated: false,
        isLoading: false,
        error: null,
      };
    case 'REFRESH_TOKEN_SUCCESS':
      return {
        ...state,
        tokens: action.payload,
        error: null,
      };
    case 'UPDATE_USER':
      return {
        ...state,
        user: state.user ? { ...state.user, ...action.payload } : null,
      };
    case 'UPDATE_VERIFICATION_STATUS':
      return {
        ...state,
        user: state.user ? {
          ...state.user,
          verificationStatus: action.payload.verificationStatus,
          documentStatus: action.payload.documentStatus,
          canGoOnline: action.payload.canGoOnline,
        } : null,
      };
    case 'CLEAR_ERROR':
      return {
        ...state,
        error: null,
      };
    default:
      return state;
  }
};

// Create context
const AuthContext = createContext<AuthContextType | undefined>(undefined);

// Provider component
interface AuthProviderProps {
  children: React.ReactNode;
}

export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const [state, dispatch] = useReducer(authReducer, initialState);

  // Helper function to map API user to AuthUser
  const mapApiUserToAuthUser = (apiUser: LoginResponse['user']): AuthUser => ({
    id: apiUser.id,
    email: apiUser.email,
    firstName: apiUser.firstName,
    lastName: apiUser.lastName,
    phone: apiUser.phone,
    isVerified: apiUser.isVerified,
    verificationStatus: apiUser.verificationStatus,
    documentStatus: apiUser.documentStatus,
    profile: apiUser.profile,
    vehicle: apiUser.vehicle,
    bankInfo: apiUser.bankInfo,
    isDemoAccount: apiUser.isDemoAccount,
    canGoOnline: apiUser.verificationStatus === 'verified' || apiUser.isDemoAccount,
    createdAt: apiUser.createdAt,
    updatedAt: apiUser.updatedAt,
  });

  // Helper function to clear auth data
  const clearAuthData = async () => {
    await AsyncStorage.multiRemove([
      STORAGE_KEYS.ACCESS_TOKEN,
      STORAGE_KEYS.REFRESH_TOKEN,
      STORAGE_KEYS.USER_DATA,
    ]);
    await clearAuthToken();
  };

  // Check authentication status on app start
  useEffect(() => {
    checkAuthStatus();
  }, []);

  const checkAuthStatus = async () => {
    try {
      dispatch({ type: 'SET_LOADING', payload: true });

      console.log('🔍 Checking authentication status...');

      // Try to load session using SessionManager
      const session = await sessionManager.loadSession();

      if (session) {
        console.log('👤 Found stored session:', session.user.email || session.user.phone);

        // Set auth token for API calls
        await setAuthToken(session.tokens.accessToken);

        // Try to verify token with API first
        try {
          const response = await AuthService.getCurrentUser();

          if (response.success) {
            console.log('✅ Token verified with API');
            const user = response.data;

            // Update activity timestamp
            await sessionManager.updateActivity();

            dispatch({
              type: 'LOGIN_SUCCESS',
              payload: { user: mapApiUserToAuthUser(user), tokens: session.tokens },
            });

            // Start session monitoring
            sessionManager.startSessionMonitoring();
            return;
          }
        } catch (apiError) {
          console.log('⚠️ API verification failed, trying mock service...');
        }

        // If API fails, check if it's a demo account
        const isAuth = await mockAuthService.isAuthenticated();
        if (isAuth) {
          console.log('✅ Demo account session found');
          const user = await mockAuthService.getUserData();
          const tokens = await mockAuthService.getTokens();

          if (user && tokens) {
            // Update activity timestamp
            await sessionManager.updateActivity();

            dispatch({
              type: 'LOGIN_SUCCESS',
              payload: { user, tokens },
            });

            // Start session monitoring
            sessionManager.startSessionMonitoring();
            return;
          }
        }

        // If both fail, try to use stored session data directly (offline mode)
        console.log('📱 Using stored session data (offline mode)');

        // Update activity timestamp
        await sessionManager.updateActivity();

        dispatch({
          type: 'LOGIN_SUCCESS',
          payload: {
            user: session.user,
            tokens: session.tokens
          },
        });

        // Start session monitoring
        sessionManager.startSessionMonitoring();

      } else {
        console.log('🚫 No valid session found');
        dispatch({ type: 'LOGOUT' });
      }
    } catch (error) {
      console.error('❌ Auth check error:', error);
      await sessionManager.clearSession();
      dispatch({ type: 'LOGOUT' });
    } finally {
      dispatch({ type: 'SET_LOADING', payload: false });
    }
  };

  const login = async (credentials: LoginCredentials) => {
    try {
      dispatch({ type: 'SET_LOADING', payload: true });
      dispatch({ type: 'CLEAR_ERROR' });

      // Check if it's a demo account first
      if (credentials.email.includes('demo') || credentials.email.includes('test')) {
        console.log('🎭 Demo account login');
        const response = await mockAuthService.login(credentials);

        // Store demo account session using SessionManager
        await sessionManager.saveSession(response.user, response.tokens);
        await setAuthToken(response.tokens.accessToken);

        console.log('✅ Demo session stored');

        dispatch({
          type: 'LOGIN_SUCCESS',
          payload: {
            user: response.user,
            tokens: response.tokens,
          },
        });
        return;
      }

      // Use API for real accounts
      const deviceId = await AsyncStorage.getItem(STORAGE_KEYS.DEVICE_ID) ||
                      Math.random().toString(36).substring(7);

      if (!await AsyncStorage.getItem(STORAGE_KEYS.DEVICE_ID)) {
        await AsyncStorage.setItem(STORAGE_KEYS.DEVICE_ID, deviceId);
      }

      const response = await AuthService.login({
        email: credentials.email,
        password: credentials.password,
        deviceId,
      });

      if (response.success) {
        const { user, tokens } = response.data;

        // Store session using SessionManager
        const authUser = mapApiUserToAuthUser(user);
        await sessionManager.saveSession(authUser, tokens);
        await setAuthToken(tokens.accessToken);

        dispatch({
          type: 'LOGIN_SUCCESS',
          payload: {
            user: authUser,
            tokens,
          },
        });

        // Start session monitoring
        sessionManager.startSessionMonitoring();
      } else {
        throw new Error(response.message || 'Login failed');
      }
    } catch (error: any) {
      dispatch({
        type: 'SET_ERROR',
        payload: error.message || 'Login failed',
      });
      throw error;
    }
  };

  const loginWithPhone = async (credentials: PhoneLoginCredentials) => {
    try {
      dispatch({ type: 'SET_LOADING', payload: true });
      dispatch({ type: 'CLEAR_ERROR' });

      console.log('📱 Phone login');
      const response = await mockAuthService.loginWithPhone(credentials);

      // Store phone login session using SessionManager
      await sessionManager.saveSession(response.user, response.tokens);
      await setAuthToken(response.tokens.accessToken);

      console.log('✅ Phone session stored');

      dispatch({
        type: 'LOGIN_SUCCESS',
        payload: {
          user: response.user,
          tokens: response.tokens,
        },
      });

      // Start session monitoring
      sessionManager.startSessionMonitoring();
    } catch (error: any) {
      dispatch({
        type: 'SET_ERROR',
        payload: error.message || 'Phone login failed',
      });
      throw error;
    } finally {
      dispatch({ type: 'SET_LOADING', payload: false });
    }
  };

  const register = async (userData: RegistrationData) => {
    try {
      dispatch({ type: 'SET_LOADING', payload: true });
      dispatch({ type: 'CLEAR_ERROR' });

      const deviceId = await AsyncStorage.getItem(STORAGE_KEYS.DEVICE_ID) ||
                      Math.random().toString(36).substring(7);

      if (!await AsyncStorage.getItem(STORAGE_KEYS.DEVICE_ID)) {
        await AsyncStorage.setItem(STORAGE_KEYS.DEVICE_ID, deviceId);
      }

      const response = await AuthService.register({
        ...userData,
        deviceId,
      });

      if (response.success) {
        return response.data;
      } else {
        throw new Error(response.message || 'Registration failed');
      }
    } catch (error: any) {
      dispatch({
        type: 'SET_ERROR',
        payload: error.message || 'Registration failed',
      });
      throw error;
    } finally {
      dispatch({ type: 'SET_LOADING', payload: false });
    }
  };

  const logout = async () => {
    try {
      dispatch({ type: 'SET_LOADING', payload: true });
      console.log('🚪 Logging out...');

      // Try API logout first
      try {
        if (state.user && !state.user.isDemoAccount) {
          await AuthService.logout();
        } else {
          await mockAuthService.logout();
        }
      } catch (apiError) {
        console.warn('⚠️ API logout failed:', apiError);
      }

      // Stop session monitoring
      sessionManager.stopSessionMonitoring();

      // Clear session using SessionManager
      await sessionManager.clearSession();

      // Clear local storage (fallback)
      await clearAuthData();

      // Clear API auth token
      await clearAuthToken();

      console.log('✅ Session cleared');

      dispatch({ type: 'LOGOUT' });
    } catch (error: any) {
      console.error('❌ Logout error:', error);
      // Force logout even if API call fails
      sessionManager.stopSessionMonitoring();
      await sessionManager.clearSession();
      await clearAuthData();
      await clearAuthToken();
      dispatch({ type: 'LOGOUT' });
    } finally {
      dispatch({ type: 'SET_LOADING', payload: false });
    }
  };

  const refreshToken = async () => {
    try {
      const newTokens = await mockAuthService.refreshToken();
      
      dispatch({
        type: 'REFRESH_TOKEN_SUCCESS',
        payload: newTokens,
      });
    } catch (error: any) {
      console.error('Token refresh error:', error);
      dispatch({ type: 'LOGOUT' });
      throw error;
    }
  };

  const clearError = () => {
    dispatch({ type: 'CLEAR_ERROR' });
  };

  // Check verification status
  const checkVerificationStatus = async () => {
    try {
      if (!state.user || state.user.isDemoAccount) {
        return;
      }

      const response = await DocumentService.getVerificationStatus();

      if (response.success) {
        const { overall, canGoOnline } = response.data;

        dispatch({
          type: 'UPDATE_VERIFICATION_STATUS',
          payload: {
            verificationStatus: overall,
            documentStatus: response.data,
            canGoOnline,
          },
        });
      }
    } catch (error) {
      console.error('Verification status check error:', error);
    }
  };

  // Check if user can go online
  const canGoOnline = (): boolean => {
    if (!state.user) return false;

    // Demo accounts can always go online
    if (state.user.isDemoAccount) return true;

    // Real accounts need verification
    return state.user.verificationStatus === 'verified';
  };

  // Upload document
  const uploadDocument = async (documentType: string, file: any, metadata?: any) => {
    try {
      if (!state.user || state.user.isDemoAccount) {
        throw new Error('Document upload not available for demo accounts');
      }

      const response = await DocumentService.uploadDocument({
        documentType,
        file,
        metadata,
      });

      if (response.success) {
        // Refresh verification status after upload
        await checkVerificationStatus();
        return response.data;
      } else {
        throw new Error(response.message || 'Document upload failed');
      }
    } catch (error: any) {
      throw error;
    }
  };

  const contextValue: AuthContextType = {
    state,
    login,
    loginWithPhone,
    logout,
    refreshToken,
    clearError,
    register,
    checkVerificationStatus,
    canGoOnline,
    uploadDocument,
  };

  return (
    <AuthContext.Provider value={contextValue}>
      {children}
    </AuthContext.Provider>
  );
};

// Hook to use auth context
export const useAuth = (): AuthContextType => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};
