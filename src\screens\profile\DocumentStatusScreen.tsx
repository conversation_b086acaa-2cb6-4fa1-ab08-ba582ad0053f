import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  Alert,
  RefreshControl,
  ActivityIndicator,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import { LinearGradient } from 'expo-linear-gradient';
import { useNavigation } from '@react-navigation/native';
import { useAuth } from '../../context/AuthContext';
import DocumentService from '../../services/api/documentService';

interface DocumentStatus {
  documentId?: string;
  documentType: string;
  status: 'pending' | 'verified' | 'rejected' | 'not_uploaded';
  uploadedAt?: string;
  verifiedAt?: string;
  rejectionReason?: string;
  fileUrl?: string;
}

const DocumentStatusScreen: React.FC = () => {
  const navigation = useNavigation();
  const { state, checkVerificationStatus, canGoOnline } = useAuth();
  const [documents, setDocuments] = useState<DocumentStatus[]>([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [overallStatus, setOverallStatus] = useState<'pending' | 'verified' | 'rejected' | 'incomplete'>('incomplete');

  useEffect(() => {
    loadDocumentStatus();
  }, []);

  const loadDocumentStatus = async () => {
    try {
      if (state.user?.isDemoAccount) {
        // For demo accounts, show mock verified status
        setDocuments([
          { documentType: 'cnic', status: 'verified', verifiedAt: new Date().toISOString() },
          { documentType: 'driving_license', status: 'verified', verifiedAt: new Date().toISOString() },
          { documentType: 'vehicle_registration', status: 'verified', verifiedAt: new Date().toISOString() },
        ]);
        setOverallStatus('verified');
        setLoading(false);
        return;
      }

      const response = await DocumentService.getAllDocumentsStatus();
      
      if (response.success) {
        setDocuments(response.data);
        
        // Determine overall status
        const hasRejected = response.data.some(doc => doc.status === 'rejected');
        const hasNotUploaded = response.data.some(doc => doc.status === 'not_uploaded');
        const hasPending = response.data.some(doc => doc.status === 'pending');
        const allVerified = response.data.every(doc => doc.status === 'verified');

        if (allVerified) {
          setOverallStatus('verified');
        } else if (hasRejected) {
          setOverallStatus('rejected');
        } else if (hasNotUploaded) {
          setOverallStatus('incomplete');
        } else if (hasPending) {
          setOverallStatus('pending');
        }
      }
    } catch (error) {
      console.error('Error loading document status:', error);
      Alert.alert('Error', 'Failed to load document status. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const onRefresh = async () => {
    setRefreshing(true);
    await loadDocumentStatus();
    await checkVerificationStatus();
    setRefreshing(false);
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'verified': return '#22c55e';
      case 'pending': return '#f59e0b';
      case 'rejected': return '#ef4444';
      case 'not_uploaded': return '#6b7280';
      default: return '#6b7280';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'verified': return 'checkmark-circle';
      case 'pending': return 'time';
      case 'rejected': return 'close-circle';
      case 'not_uploaded': return 'cloud-upload-outline';
      default: return 'help-circle';
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'verified': return 'Verified';
      case 'pending': return 'Under Review';
      case 'rejected': return 'Rejected';
      case 'not_uploaded': return 'Not Uploaded';
      default: return 'Unknown';
    }
  };

  const getDocumentDisplayName = (type: string) => {
    switch (type) {
      case 'cnic': return 'CNIC/National ID';
      case 'passport': return 'Passport';
      case 'driving_license': return 'Driving License';
      case 'vehicle_registration': return 'Vehicle Registration';
      case 'profile_photo': return 'Profile Photo';
      default: return type.replace('_', ' ').toUpperCase();
    }
  };

  const handleUploadDocument = (documentType: string) => {
    navigation.navigate('VehicleInfo' as never);
  };

  const renderOverallStatus = () => (
    <View style={{
      marginHorizontal: 20,
      marginTop: -12,
      padding: 24,
      backgroundColor: 'white',
      borderRadius: 20,
      shadowColor: '#dc2626',
      shadowOffset: { width: 0, height: 8 },
      shadowOpacity: 0.1,
      shadowRadius: 16,
      elevation: 8,
      borderWidth: 1,
      borderColor: 'rgba(220, 38, 38, 0.1)',
    }}>
      <View style={{ flexDirection: 'row', alignItems: 'center', marginBottom: 12 }}>
        <Ionicons 
          name={getStatusIcon(overallStatus)} 
          size={24} 
          color={getStatusColor(overallStatus)} 
        />
        <Text style={{
          fontSize: 18,
          fontWeight: 'bold',
          color: '#111827',
          marginLeft: 12,
        }}>
          Verification Status
        </Text>
      </View>
      
      <Text style={{
        fontSize: 16,
        color: getStatusColor(overallStatus),
        fontWeight: '600',
        marginBottom: 8,
      }}>
        {getStatusText(overallStatus)}
      </Text>

      {overallStatus === 'verified' && (
        <Text style={{ fontSize: 14, color: '#6b7280' }}>
          ✅ All documents verified. You can go online and start receiving orders!
        </Text>
      )}

      {overallStatus === 'pending' && (
        <Text style={{ fontSize: 14, color: '#6b7280' }}>
          ⏳ Your documents are under review. This usually takes 24-48 hours.
        </Text>
      )}

      {overallStatus === 'rejected' && (
        <Text style={{ fontSize: 14, color: '#6b7280' }}>
          ❌ Some documents were rejected. Please re-upload the required documents.
        </Text>
      )}

      {overallStatus === 'incomplete' && (
        <Text style={{ fontSize: 14, color: '#6b7280' }}>
          📋 Please upload all required documents to complete verification.
        </Text>
      )}

      {!canGoOnline() && !state.user?.isDemoAccount && (
        <View style={{
          marginTop: 12,
          padding: 12,
          backgroundColor: '#fef2f2',
          borderRadius: 8,
          borderLeftWidth: 4,
          borderLeftColor: '#ef4444',
        }}>
          <Text style={{ fontSize: 14, color: '#dc2626', fontWeight: '600' }}>
            ⚠️ You cannot go online until all documents are verified.
          </Text>
        </View>
      )}
    </View>
  );

  const renderDocumentItem = (document: DocumentStatus) => (
    <View
      key={document.documentType}
      style={{
        backgroundColor: 'white',
        marginHorizontal: 20,
        marginVertical: 8,
        borderRadius: 20,
        padding: 20,
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between',
        shadowColor: getStatusColor(document.status),
        shadowOffset: { width: 0, height: 8 },
        shadowOpacity: 0.1,
        shadowRadius: 16,
        elevation: 8,
        borderWidth: 1,
        borderColor: `${getStatusColor(document.status)}20`,
      }}
    >
      <View style={{ flex: 1 }}>
        <Text style={{
          fontSize: 16,
          fontWeight: '600',
          color: '#111827',
          marginBottom: 4,
        }}>
          {getDocumentDisplayName(document.documentType)}
        </Text>
        
        <View style={{ flexDirection: 'row', alignItems: 'center' }}>
          <Ionicons 
            name={getStatusIcon(document.status)} 
            size={16} 
            color={getStatusColor(document.status)} 
          />
          <Text style={{
            fontSize: 14,
            color: getStatusColor(document.status),
            fontWeight: '500',
            marginLeft: 6,
          }}>
            {getStatusText(document.status)}
          </Text>
        </View>

        {document.rejectionReason && (
          <Text style={{
            fontSize: 12,
            color: '#ef4444',
            marginTop: 4,
            fontStyle: 'italic',
          }}>
            Reason: {document.rejectionReason}
          </Text>
        )}

        {document.uploadedAt && (
          <Text style={{
            fontSize: 12,
            color: '#6b7280',
            marginTop: 2,
          }}>
            Uploaded: {new Date(document.uploadedAt).toLocaleDateString()}
          </Text>
        )}
      </View>

      {(document.status === 'not_uploaded' || document.status === 'rejected') && (
        <TouchableOpacity
          onPress={() => handleUploadDocument(document.documentType)}
          style={{
            backgroundColor: '#ef4444',
            paddingHorizontal: 16,
            paddingVertical: 8,
            borderRadius: 8,
          }}
        >
          <Text style={{
            color: 'white',
            fontSize: 14,
            fontWeight: '600',
          }}>
            {document.status === 'rejected' ? 'Re-upload' : 'Upload'}
          </Text>
        </TouchableOpacity>
      )}
    </View>
  );

  if (loading) {
    return (
      <SafeAreaView style={{ flex: 1, backgroundColor: '#f9fafb' }}>
        <LinearGradient
          colors={['#ef4444', '#dc2626']}
          style={{
            paddingHorizontal: 20,
            paddingVertical: 16,
            flexDirection: 'row',
            alignItems: 'center',
          }}
        >
          <TouchableOpacity onPress={() => navigation.goBack()}>
            <Ionicons name="arrow-back" size={24} color="white" />
          </TouchableOpacity>
          <Text style={{
            fontSize: 18,
            fontWeight: '600',
            color: 'white',
            marginLeft: 16,
          }}>
            Document Status
          </Text>
        </LinearGradient>

        <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center' }}>
          <ActivityIndicator size="large" color="#ef4444" />
          <Text style={{ marginTop: 16, color: '#6b7280' }}>Loading document status...</Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <View style={{ flex: 1, backgroundColor: '#f1f5f9' }}>
      <SafeAreaView style={{ flex: 1 }}>
        {/* Enhanced Header */}
        <View style={{
          backgroundColor: '#dc2626',
          paddingHorizontal: 20,
          paddingTop: 20,
          paddingBottom: 28,
          borderBottomLeftRadius: 28,
          borderBottomRightRadius: 28,
          shadowColor: '#dc2626',
          shadowOffset: { width: 0, height: 12 },
          shadowOpacity: 0.3,
          shadowRadius: 20,
          elevation: 16,
        }}>
          <View style={{
            flexDirection: 'row',
            alignItems: 'center',
            marginBottom: 20,
          }}>
            <TouchableOpacity
              onPress={() => navigation.goBack()}
              style={{
                width: 48,
                height: 48,
                borderRadius: 24,
                backgroundColor: 'rgba(255,255,255,0.2)',
                justifyContent: 'center',
                alignItems: 'center',
                marginRight: 16,
                borderWidth: 2,
                borderColor: 'rgba(255,255,255,0.3)',
              }}
            >
              <Ionicons name="arrow-back" size={24} color="white" />
            </TouchableOpacity>

            <View style={{ flex: 1 }}>
              <Text style={{
                fontSize: 24,
                fontWeight: 'bold',
                color: 'white',
                marginBottom: 4,
              }}>
                📄 Documents
              </Text>
              <Text style={{
                fontSize: 14,
                color: 'rgba(255,255,255,0.8)',
                fontWeight: '500',
              }}>
                Manage your documents
              </Text>
            </View>
          </View>
        </View>

      <ScrollView
        style={{ flex: 1 }}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
      >
        {renderOverallStatus()}
        
        <View style={{ marginTop: 8 }}>
          {documents.map(renderDocumentItem)}
        </View>

        <View style={{ height: 32 }} />
      </ScrollView>
      </SafeAreaView>
    </View>
  );
};

export default DocumentStatusScreen;
