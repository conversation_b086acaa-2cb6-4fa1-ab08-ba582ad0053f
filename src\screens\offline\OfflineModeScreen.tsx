import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  ScrollView,
  StatusBar,
  Animated,
  RefreshControl,
  Alert,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import { useNavigation } from '@react-navigation/native';
import NetInfo from '@react-native-async-storage/async-storage';
import { LinearGradient } from 'expo-linear-gradient';

interface CachedData {
  earnings: {
    today: number;
    week: number;
    month: number;
    lastUpdated: string;
  };
  orders: {
    completed: number;
    cancelled: number;
    lastUpdated: string;
  };
  profile: {
    name: string;
    rating: number;
    lastUpdated: string;
  };
}

interface QueuedAction {
  id: string;
  type: 'order_update' | 'profile_update' | 'earnings_sync';
  description: string;
  timestamp: string;
  data: any;
}

const OfflineModeScreen: React.FC = () => {
  const navigation = useNavigation();
  const [isConnected, setIsConnected] = useState(false);
  const [refreshing, setRefreshing] = useState(false);
  const [fadeAnimation] = useState(new Animated.Value(0));
  const [pulseAnimation] = useState(new Animated.Value(1));
  
  const [cachedData] = useState<CachedData>({
    earnings: {
      today: 1250,
      week: 8750,
      month: 32500,
      lastUpdated: '2024-01-20T10:30:00Z',
    },
    orders: {
      completed: 45,
      cancelled: 2,
      lastUpdated: '2024-01-20T09:15:00Z',
    },
    profile: {
      name: 'Ahmed Khan',
      rating: 4.8,
      lastUpdated: '2024-01-19T18:45:00Z',
    },
  });

  const [queuedActions] = useState<QueuedAction[]>([
    {
      id: '1',
      type: 'order_update',
      description: 'Order #12345 status update',
      timestamp: '2024-01-20T11:00:00Z',
      data: { orderId: '12345', status: 'delivered' },
    },
    {
      id: '2',
      type: 'earnings_sync',
      description: 'Earnings data sync',
      timestamp: '2024-01-20T10:45:00Z',
      data: { amount: 450, orderId: '12344' },
    },
  ]);

  useEffect(() => {
    checkNetworkStatus();
    startAnimations();
    
    const unsubscribe = NetInfo.addEventListener(state => {
      setIsConnected(state.isConnected ?? false);
    });

    return () => unsubscribe();
  }, []);

  const checkNetworkStatus = async () => {
    const state = await NetInfo.fetch();
    setIsConnected(state.isConnected ?? false);
  };

  const startAnimations = () => {
    Animated.timing(fadeAnimation, {
      toValue: 1,
      duration: 800,
      useNativeDriver: true,
    }).start();

    // Pulse animation for sync indicator
    Animated.loop(
      Animated.sequence([
        Animated.timing(pulseAnimation, {
          toValue: 1.2,
          duration: 1000,
          useNativeDriver: true,
        }),
        Animated.timing(pulseAnimation, {
          toValue: 1,
          duration: 1000,
          useNativeDriver: true,
        }),
      ])
    ).start();
  };

  const onRefresh = async () => {
    setRefreshing(true);
    await checkNetworkStatus();
    // Simulate checking for cached data updates
    await new Promise(resolve => setTimeout(resolve, 1000));
    setRefreshing(false);
  };

  const handleRetryConnection = async () => {
    await checkNetworkStatus();
    if (isConnected) {
      Alert.alert(
        '🟢 Connection Restored',
        'You\'re back online! Syncing your data now...',
        [{ text: 'Great!' }]
      );
      // Simulate sync process
      setTimeout(() => {
        navigation.goBack();
      }, 2000);
    } else {
      Alert.alert(
        '📶 Still Offline',
        'Please check your internet connection and try again.',
        [{ text: 'OK' }]
      );
    }
  };

  const formatLastUpdated = (timestamp: string) => {
    const date = new Date(timestamp);
    const now = new Date();
    const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60));
    
    if (diffInMinutes < 60) {
      return `${diffInMinutes} minutes ago`;
    } else if (diffInMinutes < 1440) {
      return `${Math.floor(diffInMinutes / 60)} hours ago`;
    } else {
      return `${Math.floor(diffInMinutes / 1440)} days ago`;
    }
  };

  const renderCachedDataCard = (title: string, data: any, icon: string, color: string) => (
    <View style={{
      backgroundColor: 'white',
      borderRadius: 16,
      padding: 20,
      marginBottom: 16,
      shadowColor: color,
      shadowOffset: { width: 0, height: 4 },
      shadowOpacity: 0.1,
      shadowRadius: 8,
      elevation: 4,
      borderWidth: 1,
      borderColor: `${color}20`,
    }}>
      <View style={{
        flexDirection: 'row',
        alignItems: 'center',
        marginBottom: 16,
      }}>
        <View style={{
          width: 48,
          height: 48,
          backgroundColor: `${color}20`,
          borderRadius: 24,
          alignItems: 'center',
          justifyContent: 'center',
          marginRight: 16,
        }}>
          <Ionicons name={icon as any} size={24} color={color} />
        </View>
        
        <View style={{ flex: 1 }}>
          <Text style={{
            fontSize: 18,
            fontWeight: 'bold',
            color: '#111827',
            marginBottom: 4,
          }}>
            {title}
          </Text>
          <View style={{
            flexDirection: 'row',
            alignItems: 'center',
          }}>
            <Ionicons name="time" size={12} color="#9ca3af" />
            <Text style={{
              fontSize: 12,
              color: '#9ca3af',
              marginLeft: 4,
            }}>
              Last updated: {formatLastUpdated(data.lastUpdated)}
            </Text>
          </View>
        </View>
      </View>

      {title === 'Earnings' && (
        <View style={{
          flexDirection: 'row',
          justifyContent: 'space-between',
        }}>
          <View style={{ alignItems: 'center' }}>
            <Text style={{ fontSize: 20, fontWeight: 'bold', color: '#111827' }}>
              PKR {data.today}
            </Text>
            <Text style={{ fontSize: 12, color: '#6b7280' }}>Today</Text>
          </View>
          <View style={{ alignItems: 'center' }}>
            <Text style={{ fontSize: 20, fontWeight: 'bold', color: '#111827' }}>
              PKR {data.week}
            </Text>
            <Text style={{ fontSize: 12, color: '#6b7280' }}>This Week</Text>
          </View>
          <View style={{ alignItems: 'center' }}>
            <Text style={{ fontSize: 20, fontWeight: 'bold', color: '#111827' }}>
              PKR {data.month}
            </Text>
            <Text style={{ fontSize: 12, color: '#6b7280' }}>This Month</Text>
          </View>
        </View>
      )}

      {title === 'Orders' && (
        <View style={{
          flexDirection: 'row',
          justifyContent: 'space-around',
        }}>
          <View style={{ alignItems: 'center' }}>
            <Text style={{ fontSize: 24, fontWeight: 'bold', color: '#10b981' }}>
              {data.completed}
            </Text>
            <Text style={{ fontSize: 12, color: '#6b7280' }}>Completed</Text>
          </View>
          <View style={{ alignItems: 'center' }}>
            <Text style={{ fontSize: 24, fontWeight: 'bold', color: '#f59e0b' }}>
              {data.cancelled}
            </Text>
            <Text style={{ fontSize: 12, color: '#6b7280' }}>Cancelled</Text>
          </View>
        </View>
      )}

      {title === 'Profile' && (
        <View style={{
          flexDirection: 'row',
          justifyContent: 'space-around',
        }}>
          <View style={{ alignItems: 'center' }}>
            <Text style={{ fontSize: 24, fontWeight: 'bold', color: '#111827' }}>
              {data.name}
            </Text>
            <Text style={{ fontSize: 12, color: '#6b7280' }}>Rider Name</Text>
          </View>
          <View style={{ alignItems: 'center' }}>
            <View style={{ flexDirection: 'row', alignItems: 'center' }}>
              <Ionicons name="star" size={20} color="#f59e0b" />
              <Text style={{ fontSize: 24, fontWeight: 'bold', color: '#111827', marginLeft: 4 }}>
                {data.rating}
              </Text>
            </View>
            <Text style={{ fontSize: 12, color: '#6b7280' }}>Rating</Text>
          </View>
        </View>
      )}
    </View>
  );

  const renderQueuedAction = (action: QueuedAction) => (
    <View key={action.id} style={{
      flexDirection: 'row',
      alignItems: 'center',
      paddingVertical: 12,
      paddingHorizontal: 16,
      borderBottomWidth: 1,
      borderBottomColor: '#f3f4f6',
    }}>
      <Animated.View style={{
        transform: [{ scale: pulseAnimation }],
      }}>
        <View style={{
          width: 12,
          height: 12,
          backgroundColor: '#f59e0b',
          borderRadius: 6,
          marginRight: 12,
        }} />
      </Animated.View>
      
      <View style={{ flex: 1 }}>
        <Text style={{
          fontSize: 14,
          fontWeight: '600',
          color: '#111827',
          marginBottom: 2,
        }}>
          {action.description}
        </Text>
        <Text style={{
          fontSize: 12,
          color: '#6b7280',
        }}>
          {formatLastUpdated(action.timestamp)}
        </Text>
      </View>
      
      <View style={{
        backgroundColor: '#f59e0b20',
        paddingHorizontal: 8,
        paddingVertical: 4,
        borderRadius: 12,
      }}>
        <Text style={{
          fontSize: 10,
          fontWeight: 'bold',
          color: '#f59e0b',
          textTransform: 'uppercase',
        }}>
          Pending
        </Text>
      </View>
    </View>
  );

  return (
    <View style={{ flex: 1, backgroundColor: '#f1f5f9' }}>
      <SafeAreaView style={{ flex: 1 }}>
        <StatusBar barStyle="light-content" backgroundColor="#dc2626" />
        
        {/* Enhanced Header */}
        <View style={{
          backgroundColor: '#dc2626',
          paddingHorizontal: 20,
          paddingTop: 20,
          paddingBottom: 28,
          borderBottomLeftRadius: 28,
          borderBottomRightRadius: 28,
          shadowColor: '#dc2626',
          shadowOffset: { width: 0, height: 12 },
          shadowOpacity: 0.3,
          shadowRadius: 20,
          elevation: 16,
        }}>
          <View style={{
            flexDirection: 'row',
            alignItems: 'center',
            marginBottom: 20,
          }}>
            <TouchableOpacity
              onPress={() => navigation.goBack()}
              style={{
                width: 48,
                height: 48,
                borderRadius: 24,
                backgroundColor: 'rgba(255,255,255,0.2)',
                justifyContent: 'center',
                alignItems: 'center',
                marginRight: 16,
                borderWidth: 2,
                borderColor: 'rgba(255,255,255,0.3)',
              }}
            >
              <Ionicons name="arrow-back" size={24} color="white" />
            </TouchableOpacity>
            
            <View style={{ flex: 1 }}>
              <Text style={{
                fontSize: 24,
                fontWeight: 'bold',
                color: 'white',
                marginBottom: 4,
              }}>
                📶 Offline Mode
              </Text>
              <Text style={{
                fontSize: 14,
                color: 'rgba(255,255,255,0.8)',
                fontWeight: '500',
              }}>
                {isConnected ? 'Connection restored!' : 'Working with cached data'}
              </Text>
            </View>
            
            <View style={{
              backgroundColor: isConnected ? '#10b98120' : 'rgba(255,255,255,0.2)',
              paddingHorizontal: 12,
              paddingVertical: 6,
              borderRadius: 16,
              borderWidth: 2,
              borderColor: isConnected ? '#10b981' : 'rgba(255,255,255,0.3)',
            }}>
              <Text style={{
                fontSize: 12,
                fontWeight: 'bold',
                color: isConnected ? '#10b981' : 'white',
              }}>
                {isConnected ? 'ONLINE' : 'OFFLINE'}
              </Text>
            </View>
          </View>
        </View>

        <ScrollView
          style={{ flex: 1 }}
          refreshControl={<RefreshControl refreshing={refreshing} onRefresh={onRefresh} />}
          showsVerticalScrollIndicator={false}
        >
          <Animated.View style={{ opacity: fadeAnimation }}>
            {/* Connection Status Card */}
            <View style={{
              backgroundColor: isConnected ? '#10b981' : '#f59e0b',
              marginHorizontal: 20,
              marginTop: -12,
              borderRadius: 20,
              padding: 20,
              shadowColor: isConnected ? '#10b981' : '#f59e0b',
              shadowOffset: { width: 0, height: 8 },
              shadowOpacity: 0.2,
              shadowRadius: 16,
              elevation: 8,
            }}>
              <View style={{
                flexDirection: 'row',
                alignItems: 'center',
                marginBottom: 12,
              }}>
                <Ionicons
                  name={isConnected ? 'wifi' : 'wifi-off'}
                  size={24}
                  color="white"
                  style={{ marginRight: 12 }}
                />
                <Text style={{
                  fontSize: 18,
                  fontWeight: 'bold',
                  color: 'white',
                }}>
                  {isConnected ? 'Connection Restored' : 'No Internet Connection'}
                </Text>
              </View>
              
              <Text style={{
                fontSize: 14,
                color: 'rgba(255,255,255,0.9)',
                marginBottom: 16,
                lineHeight: 20,
              }}>
                {isConnected
                  ? 'Your data will sync automatically. You can continue using the app normally.'
                  : 'Don\'t worry! You can still view your cached data and the app will sync when you\'re back online.'
                }
              </Text>

              <TouchableOpacity
                onPress={handleRetryConnection}
                style={{
                  backgroundColor: 'rgba(255,255,255,0.2)',
                  borderRadius: 12,
                  paddingVertical: 12,
                  paddingHorizontal: 16,
                  alignItems: 'center',
                  borderWidth: 1,
                  borderColor: 'rgba(255,255,255,0.3)',
                }}
              >
                <Text style={{
                  fontSize: 14,
                  fontWeight: 'bold',
                  color: 'white',
                }}>
                  {isConnected ? 'Sync Now' : 'Retry Connection'}
                </Text>
              </TouchableOpacity>
            </View>

            {/* Cached Data Section */}
            <View style={{ paddingHorizontal: 20, marginTop: 24 }}>
              <Text style={{
                fontSize: 20,
                fontWeight: 'bold',
                color: '#111827',
                marginBottom: 16,
              }}>
                📊 Cached Data
              </Text>

              {renderCachedDataCard('Earnings', cachedData.earnings, 'cash', '#10b981')}
              {renderCachedDataCard('Orders', cachedData.orders, 'list', '#3b82f6')}
              {renderCachedDataCard('Profile', cachedData.profile, 'person', '#dc2626')}
            </View>

            {/* Queued Actions */}
            {queuedActions.length > 0 && (
              <View style={{
                backgroundColor: 'white',
                marginHorizontal: 20,
                marginTop: 16,
                borderRadius: 20,
                shadowColor: '#000',
                shadowOffset: { width: 0, height: 8 },
                shadowOpacity: 0.1,
                shadowRadius: 16,
                elevation: 8,
                borderWidth: 1,
                borderColor: 'rgba(0, 0, 0, 0.05)',
              }}>
                <View style={{
                  paddingHorizontal: 20,
                  paddingVertical: 20,
                  borderBottomWidth: 1,
                  borderBottomColor: '#f3f4f6',
                }}>
                  <Text style={{
                    fontSize: 18,
                    fontWeight: 'bold',
                    color: '#111827',
                  }}>
                    ⏳ Pending Sync
                  </Text>
                  <Text style={{
                    fontSize: 14,
                    color: '#6b7280',
                    marginTop: 4,
                  }}>
                    These actions will sync when you're back online
                  </Text>
                </View>

                {queuedActions.map(renderQueuedAction)}
              </View>
            )}

            <View style={{ height: 32 }} />
          </Animated.View>
        </ScrollView>
      </SafeAreaView>
    </View>
  );
};

export default OfflineModeScreen;

// Export a reusable Network Status Banner component
export const NetworkStatusBanner: React.FC<{
  isConnected: boolean;
  onRetry?: () => void;
  style?: any;
}> = ({ isConnected, onRetry, style }) => {
  const [visible, setVisible] = useState(!isConnected);
  const [slideAnimation] = useState(new Animated.Value(isConnected ? -60 : 0));

  useEffect(() => {
    if (isConnected) {
      // Show "Connected" message briefly, then hide
      setVisible(true);
      Animated.sequence([
        Animated.timing(slideAnimation, {
          toValue: 0,
          duration: 300,
          useNativeDriver: true,
        }),
        Animated.delay(2000),
        Animated.timing(slideAnimation, {
          toValue: -60,
          duration: 300,
          useNativeDriver: true,
        }),
      ]).start(() => setVisible(false));
    } else {
      // Show offline banner
      setVisible(true);
      Animated.timing(slideAnimation, {
        toValue: 0,
        duration: 300,
        useNativeDriver: true,
      }).start();
    }
  }, [isConnected]);

  if (!visible) return null;

  return (
    <Animated.View
      style={[
        {
          position: 'absolute',
          top: 0,
          left: 0,
          right: 0,
          backgroundColor: isConnected ? '#10b981' : '#f59e0b',
          paddingVertical: 12,
          paddingHorizontal: 20,
          flexDirection: 'row',
          alignItems: 'center',
          justifyContent: 'space-between',
          zIndex: 1000,
          transform: [{ translateY: slideAnimation }],
        },
        style,
      ]}
    >
      <View style={{ flexDirection: 'row', alignItems: 'center', flex: 1 }}>
        <Ionicons
          name={isConnected ? 'wifi' : 'wifi-off'}
          size={16}
          color="white"
          style={{ marginRight: 8 }}
        />
        <Text style={{
          fontSize: 14,
          fontWeight: '600',
          color: 'white',
          flex: 1,
        }}>
          {isConnected ? 'Back online - Data synced' : 'No internet connection'}
        </Text>
      </View>

      {!isConnected && onRetry && (
        <TouchableOpacity
          onPress={onRetry}
          style={{
            backgroundColor: 'rgba(255,255,255,0.2)',
            paddingHorizontal: 12,
            paddingVertical: 6,
            borderRadius: 12,
          }}
        >
          <Text style={{
            fontSize: 12,
            fontWeight: 'bold',
            color: 'white',
          }}>
            Retry
          </Text>
        </TouchableOpacity>
      )}
    </Animated.View>
  );
};
