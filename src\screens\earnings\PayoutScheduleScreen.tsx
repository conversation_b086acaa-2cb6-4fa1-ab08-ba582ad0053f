import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  ScrollView,
  RefreshControl,
  TouchableOpacity,
  Alert,
  Modal,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import { useNavigation } from '@react-navigation/native';
import { Card, Button, Badge, LoadingSpinner } from '../../components/ui';
import { useEarnings } from '../../context/EarningsContext';
import { formatCurrency, formatDate } from '../../utils/helpers';
import { PayoutStatus, PayoutFrequency, PaymentMethod } from '../../types/earnings';

const PayoutScheduleScreen: React.FC = () => {
  const navigation = useNavigation();
  const { 
    state, 
    fetchPayoutSchedule,
    fetchPayoutSettings,
    updatePayoutSettings,
  } = useEarnings();
  
  const [refreshing, setRefreshing] = useState(false);
  const [showSettingsModal, setShowSettingsModal] = useState(false);

  useEffect(() => {
    loadPayoutData();
  }, []);

  const loadPayoutData = async () => {
    try {
      await Promise.all([
        fetchPayoutSchedule(),
        fetchPayoutSettings(),
      ]);
    } catch (error) {
      console.error('Error loading payout data:', error);
    }
  };

  const onRefresh = async () => {
    setRefreshing(true);
    await loadPayoutData();
    setRefreshing(false);
  };

  const getStatusColor = (status: PayoutStatus) => {
    switch (status) {
      case PayoutStatus.COMPLETED:
        return '#10b981';
      case PayoutStatus.SCHEDULED:
        return '#3b82f6';
      case PayoutStatus.PROCESSING:
        return '#f59e0b';
      case PayoutStatus.FAILED:
      case PayoutStatus.CANCELLED:
        return '#ef4444';
      default:
        return '#6b7280';
    }
  };

  const getStatusText = (status: PayoutStatus) => {
    return status.charAt(0).toUpperCase() + status.slice(1).replace('_', ' ');
  };

  const getMethodIcon = (type: PaymentMethod) => {
    switch (type) {
      case PaymentMethod.BANK_TRANSFER:
        return 'card-outline';
      case PaymentMethod.JAZZCASH:
        return 'phone-portrait-outline';
      case PaymentMethod.EASYPAISA:
        return 'phone-portrait-outline';
      default:
        return 'wallet-outline';
    }
  };

  const getFrequencyText = (frequency: PayoutFrequency) => {
    switch (frequency) {
      case PayoutFrequency.DAILY:
        return 'Daily';
      case PayoutFrequency.WEEKLY:
        return 'Weekly';
      case PayoutFrequency.BIWEEKLY:
        return 'Bi-weekly';
      case PayoutFrequency.MONTHLY:
        return 'Monthly';
      default:
        return 'Unknown';
    }
  };

  const renderNextPayout = () => {
    const nextPayout = state.payoutSchedule.find(p => p.status === PayoutStatus.SCHEDULED);
    
    if (!nextPayout) {
      return (
        <Card variant="elevated" margin="md" padding="lg">
          <View style={{ alignItems: 'center', paddingVertical: 20 }}>
            <Ionicons name="calendar-outline" size={48} color="#d1d5db" />
            <Text style={{ fontSize: 18, fontWeight: 'bold', color: '#111827', marginTop: 12 }}>
              No Scheduled Payouts
            </Text>
            <Text style={{ fontSize: 14, color: '#6b7280', textAlign: 'center', marginTop: 4 }}>
              Complete more orders to schedule your next payout
            </Text>
          </View>
        </Card>
      );
    }

    return (
      <Card variant="elevated" margin="md" padding="lg">
        <View style={{ flexDirection: 'row', justifyContent: 'space-between', alignItems: 'center', marginBottom: 16 }}>
          <Text style={{ fontSize: 18, fontWeight: 'bold', color: '#111827' }}>
            Next Payout
          </Text>
          <Badge
            text={getStatusText(nextPayout.status)}
            style={{ backgroundColor: getStatusColor(nextPayout.status) }}
          />
        </View>

        <View style={{ alignItems: 'center', marginBottom: 20 }}>
          <Text style={{ fontSize: 36, fontWeight: 'bold', color: '#10b981' }}>
            {formatCurrency(nextPayout.netAmount)}
          </Text>
          <Text style={{ fontSize: 16, color: '#6b7280' }}>
            Scheduled for {formatDate(nextPayout.scheduledDate)}
          </Text>
        </View>

        <View style={{ 
          flexDirection: 'row', 
          alignItems: 'center', 
          backgroundColor: '#f9fafb', 
          padding: 12, 
          borderRadius: 8,
          marginBottom: 16,
        }}>
          <View style={{
            width: 32,
            height: 32,
            borderRadius: 16,
            backgroundColor: '#f97316',
            alignItems: 'center',
            justifyContent: 'center',
            marginRight: 12,
          }}>
            <Ionicons name={getMethodIcon(nextPayout.paymentMethod.type)} size={16} color="white" />
          </View>
          <View style={{ flex: 1 }}>
            <Text style={{ fontSize: 14, fontWeight: '600', color: '#111827' }}>
              {nextPayout.paymentMethod.name}
            </Text>
            <Text style={{ fontSize: 12, color: '#6b7280' }}>
              {nextPayout.paymentMethod.accountNumber || nextPayout.paymentMethod.phoneNumber}
            </Text>
          </View>
        </View>

        <View style={{ gap: 8 }}>
          <View style={{ flexDirection: 'row', justifyContent: 'space-between' }}>
            <Text style={{ fontSize: 14, color: '#6b7280' }}>Earnings Included:</Text>
            <Text style={{ fontSize: 14, fontWeight: '600', color: '#111827' }}>
              {nextPayout.earningsIncluded.length} orders
            </Text>
          </View>
          <View style={{ flexDirection: 'row', justifyContent: 'space-between' }}>
            <Text style={{ fontSize: 14, color: '#6b7280' }}>Gross Amount:</Text>
            <Text style={{ fontSize: 14, fontWeight: '600', color: '#111827' }}>
              {formatCurrency(nextPayout.amount)}
            </Text>
          </View>
          {nextPayout.deductions.length > 0 && (
            <View style={{ flexDirection: 'row', justifyContent: 'space-between' }}>
              <Text style={{ fontSize: 14, color: '#ef4444' }}>Deductions:</Text>
              <Text style={{ fontSize: 14, fontWeight: '600', color: '#ef4444' }}>
                -{formatCurrency(nextPayout.deductions.reduce((sum, d) => sum + d.amount, 0))}
              </Text>
            </View>
          )}
          {nextPayout.bonuses.length > 0 && (
            <View style={{ flexDirection: 'row', justifyContent: 'space-between' }}>
              <Text style={{ fontSize: 14, color: '#10b981' }}>Bonuses:</Text>
              <Text style={{ fontSize: 14, fontWeight: '600', color: '#10b981' }}>
                +{formatCurrency(nextPayout.bonuses.reduce((sum, b) => sum + b.amount, 0))}
              </Text>
            </View>
          )}
        </View>
      </Card>
    );
  };

  const renderPayoutSettings = () => {
    if (!state.payoutSettings) return null;

    return (
      <Card variant="elevated" margin="md" padding="lg">
        <View style={{ flexDirection: 'row', justifyContent: 'space-between', alignItems: 'center', marginBottom: 16 }}>
          <Text style={{ fontSize: 18, fontWeight: 'bold', color: '#111827' }}>
            Payout Settings
          </Text>
          <TouchableOpacity
            onPress={() => setShowSettingsModal(true)}
            style={{
              flexDirection: 'row',
              alignItems: 'center',
              backgroundColor: '#f97316',
              paddingHorizontal: 12,
              paddingVertical: 6,
              borderRadius: 8,
            }}
          >
            <Ionicons name="settings" size={16} color="white" />
            <Text style={{ color: 'white', fontWeight: '600', marginLeft: 4 }}>
              Edit
            </Text>
          </TouchableOpacity>
        </View>

        <View style={{ gap: 12 }}>
          <View style={{ flexDirection: 'row', justifyContent: 'space-between' }}>
            <Text style={{ fontSize: 14, color: '#6b7280' }}>Frequency:</Text>
            <Text style={{ fontSize: 14, fontWeight: '600', color: '#111827' }}>
              {getFrequencyText(state.payoutSettings.frequency)}
            </Text>
          </View>
          <View style={{ flexDirection: 'row', justifyContent: 'space-between' }}>
            <Text style={{ fontSize: 14, color: '#6b7280' }}>Minimum Amount:</Text>
            <Text style={{ fontSize: 14, fontWeight: '600', color: '#111827' }}>
              {formatCurrency(state.payoutSettings.minimumAmount)}
            </Text>
          </View>
          <View style={{ flexDirection: 'row', justifyContent: 'space-between' }}>
            <Text style={{ fontSize: 14, color: '#6b7280' }}>Auto Withdraw:</Text>
            <Text style={{ fontSize: 14, fontWeight: '600', color: '#111827' }}>
              {state.payoutSettings.autoWithdraw ? 'Enabled' : 'Disabled'}
            </Text>
          </View>
          {state.payoutSettings.preferredMethod && (
            <View style={{ flexDirection: 'row', justifyContent: 'space-between' }}>
              <Text style={{ fontSize: 14, color: '#6b7280' }}>Preferred Method:</Text>
              <Text style={{ fontSize: 14, fontWeight: '600', color: '#111827' }}>
                {state.payoutSettings.preferredMethod.name}
              </Text>
            </View>
          )}
        </View>
      </Card>
    );
  };

  const renderPayoutHistory = () => {
    const completedPayouts = state.payoutSchedule.filter(p => 
      p.status === PayoutStatus.COMPLETED || p.status === PayoutStatus.FAILED
    );

    return (
      <Card variant="elevated" margin="md" padding="lg">
        <Text style={{ fontSize: 18, fontWeight: 'bold', color: '#111827', marginBottom: 16 }}>
          Payout History
        </Text>

        {completedPayouts.length === 0 ? (
          <View style={{ alignItems: 'center', paddingVertical: 20 }}>
            <Ionicons name="time-outline" size={48} color="#d1d5db" />
            <Text style={{ fontSize: 16, color: '#6b7280', marginTop: 8 }}>
              No payout history
            </Text>
          </View>
        ) : (
          <View style={{ gap: 12 }}>
            {completedPayouts.slice(0, 5).map((payout) => (
              <View
                key={payout.id}
                style={{
                  flexDirection: 'row',
                  justifyContent: 'space-between',
                  alignItems: 'center',
                  paddingVertical: 12,
                  borderBottomWidth: 1,
                  borderBottomColor: '#f3f4f6',
                }}
              >
                <View style={{ flex: 1 }}>
                  <Text style={{ fontSize: 16, fontWeight: '600', color: '#111827' }}>
                    {formatCurrency(payout.netAmount)}
                  </Text>
                  <Text style={{ fontSize: 14, color: '#6b7280' }}>
                    {payout.paymentMethod.name} • {formatDate(payout.scheduledDate)}
                  </Text>
                  <Text style={{ fontSize: 12, color: '#9ca3af' }}>
                    {payout.earningsIncluded.length} orders included
                  </Text>
                </View>
                <Badge
                  text={getStatusText(payout.status)}
                  style={{ backgroundColor: getStatusColor(payout.status) }}
                />
              </View>
            ))}
          </View>
        )}
      </Card>
    );
  };

  if (state.isLoading && !refreshing) {
    return (
      <SafeAreaView style={{ flex: 1, backgroundColor: '#f8fafc' }}>
        <LoadingSpinner message="Loading payout data..." />
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={{ flex: 1, backgroundColor: '#f8fafc' }}>
      <View style={{
        paddingHorizontal: 20,
        paddingVertical: 16,
        backgroundColor: '#ffffff',
        borderBottomWidth: 1,
        borderBottomColor: '#e5e7eb',
        flexDirection: 'row',
        alignItems: 'center',
      }}>
        <TouchableOpacity
          onPress={() => navigation.goBack()}
          style={{ marginRight: 16 }}
        >
          <Ionicons name="arrow-back" size={24} color="#111827" />
        </TouchableOpacity>
        <Text style={{ fontSize: 24, fontWeight: 'bold', color: '#111827' }}>
          Payout Schedule
        </Text>
      </View>

      <ScrollView
        style={{ flex: 1 }}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
        showsVerticalScrollIndicator={false}
      >
        {renderNextPayout()}
        {renderPayoutSettings()}
        {renderPayoutHistory()}

        {/* Bottom spacing */}
        <View style={{ height: 20 }} />
      </ScrollView>
    </SafeAreaView>
  );
};

export default PayoutScheduleScreen;
