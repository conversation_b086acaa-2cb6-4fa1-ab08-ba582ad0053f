import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  Image,
  Alert,
  StatusBar,
  Animated,
  Vibration,
  Modal,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import { useNavigation } from '@react-navigation/native';
import * as ImagePicker from 'expo-image-picker';
import { LinearGradient } from 'expo-linear-gradient';

interface CameraPhotoScreenProps {
  title: string;
  subtitle: string;
  photoType: 'profile' | 'document' | 'order_proof' | 'delivery_proof';
  aspectRatio?: [number, number];
  onPhotoTaken: (uri: string) => void;
  onCancel: () => void;
  existingPhoto?: string;
  showPreview?: boolean;
}

const CameraPhotoScreen: React.FC<CameraPhotoScreenProps> = ({
  title,
  subtitle,
  photoType,
  aspectRatio = [4, 3],
  onPhotoTaken,
  onCancel,
  existingPhoto,
  showPreview = true,
}) => {
  const navigation = useNavigation();
  const [capturedPhoto, setCapturedPhoto] = useState<string | null>(existingPhoto || null);
  const [isProcessing, setIsProcessing] = useState(false);
  const [showSourceModal, setShowSourceModal] = useState(false);
  const [fadeAnimation] = useState(new Animated.Value(0));
  const [scaleAnimation] = useState(new Animated.Value(0.8));

  useEffect(() => {
    Animated.parallel([
      Animated.timing(fadeAnimation, {
        toValue: 1,
        duration: 600,
        useNativeDriver: true,
      }),
      Animated.spring(scaleAnimation, {
        toValue: 1,
        tension: 50,
        friction: 8,
        useNativeDriver: true,
      }),
    ]).start();
  }, []);

  const requestCameraPermission = async () => {
    const { status } = await ImagePicker.requestCameraPermissionsAsync();
    if (status !== 'granted') {
      Alert.alert(
        'Camera Permission Required',
        'Please grant camera permission to take photos.',
        [{ text: 'OK' }]
      );
      return false;
    }
    return true;
  };

  const requestLibraryPermission = async () => {
    const { status } = await ImagePicker.requestMediaLibraryPermissionsAsync();
    if (status !== 'granted') {
      Alert.alert(
        'Photo Library Permission Required',
        'Please grant photo library permission to select images.',
        [{ text: 'OK' }]
      );
      return false;
    }
    return true;
  };

  const takePhoto = async () => {
    const hasPermission = await requestCameraPermission();
    if (!hasPermission) return;

    try {
      setIsProcessing(true);
      const result = await ImagePicker.launchCameraAsync({
        mediaTypes: ImagePicker.MediaTypeOptions.Images,
        allowsEditing: true,
        aspect: aspectRatio,
        quality: 0.8,
      });

      if (!result.canceled && result.assets[0]) {
        setCapturedPhoto(result.assets[0].uri);
        Vibration.vibrate(100);
        setShowSourceModal(false);
      }
    } catch (error) {
      console.error('Error taking photo:', error);
      Alert.alert('Error', 'Could not take photo. Please try again.');
    } finally {
      setIsProcessing(false);
    }
  };

  const selectFromLibrary = async () => {
    const hasPermission = await requestLibraryPermission();
    if (!hasPermission) return;

    try {
      setIsProcessing(true);
      const result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: ImagePicker.MediaTypeOptions.Images,
        allowsEditing: true,
        aspect: aspectRatio,
        quality: 0.8,
      });

      if (!result.canceled && result.assets[0]) {
        setCapturedPhoto(result.assets[0].uri);
        setShowSourceModal(false);
      }
    } catch (error) {
      console.error('Error selecting photo:', error);
      Alert.alert('Error', 'Could not select photo. Please try again.');
    } finally {
      setIsProcessing(false);
    }
  };

  const handleConfirmPhoto = () => {
    if (capturedPhoto) {
      onPhotoTaken(capturedPhoto);
    }
  };

  const handleRetakePhoto = () => {
    setCapturedPhoto(null);
    setShowSourceModal(true);
  };

  const getPhotoTypeIcon = () => {
    switch (photoType) {
      case 'profile': return 'person-circle';
      case 'document': return 'document-text';
      case 'order_proof': return 'restaurant';
      case 'delivery_proof': return 'checkmark-circle';
      default: return 'camera';
    }
  };

  const renderSourceModal = () => (
    <Modal
      visible={showSourceModal}
      animationType="slide"
      presentationStyle="pageSheet"
      onRequestClose={() => setShowSourceModal(false)}
    >
      <View style={{ flex: 1, backgroundColor: '#f1f5f9' }}>
        <SafeAreaView style={{ flex: 1 }}>
          {/* Modal Header */}
          <View style={{
            backgroundColor: '#dc2626',
            paddingHorizontal: 20,
            paddingTop: 20,
            paddingBottom: 28,
            borderBottomLeftRadius: 28,
            borderBottomRightRadius: 28,
          }}>
            <View style={{
              flexDirection: 'row',
              alignItems: 'center',
              justifyContent: 'space-between',
            }}>
              <TouchableOpacity
                onPress={() => setShowSourceModal(false)}
                style={{
                  width: 48,
                  height: 48,
                  borderRadius: 24,
                  backgroundColor: 'rgba(255,255,255,0.2)',
                  justifyContent: 'center',
                  alignItems: 'center',
                }}
              >
                <Ionicons name="close" size={24} color="white" />
              </TouchableOpacity>
              
              <Text style={{
                fontSize: 20,
                fontWeight: 'bold',
                color: 'white',
              }}>
                Select Photo Source
              </Text>
              
              <View style={{ width: 48 }} />
            </View>
          </View>

          {/* Source Options */}
          <View style={{
            flex: 1,
            justifyContent: 'center',
            paddingHorizontal: 40,
          }}>
            <TouchableOpacity
              onPress={takePhoto}
              disabled={isProcessing}
              style={{
                backgroundColor: 'white',
                borderRadius: 20,
                padding: 32,
                alignItems: 'center',
                marginBottom: 20,
                shadowColor: '#dc2626',
                shadowOffset: { width: 0, height: 8 },
                shadowOpacity: 0.1,
                shadowRadius: 16,
                elevation: 8,
              }}
            >
              <View style={{
                width: 80,
                height: 80,
                backgroundColor: '#dc262620',
                borderRadius: 40,
                alignItems: 'center',
                justifyContent: 'center',
                marginBottom: 16,
              }}>
                <Ionicons name="camera" size={40} color="#dc2626" />
              </View>
              <Text style={{
                fontSize: 20,
                fontWeight: 'bold',
                color: '#111827',
                marginBottom: 8,
              }}>
                Take Photo
              </Text>
              <Text style={{
                fontSize: 14,
                color: '#6b7280',
                textAlign: 'center',
              }}>
                Use camera to capture a new photo
              </Text>
            </TouchableOpacity>

            <TouchableOpacity
              onPress={selectFromLibrary}
              disabled={isProcessing}
              style={{
                backgroundColor: 'white',
                borderRadius: 20,
                padding: 32,
                alignItems: 'center',
                shadowColor: '#3b82f6',
                shadowOffset: { width: 0, height: 8 },
                shadowOpacity: 0.1,
                shadowRadius: 16,
                elevation: 8,
              }}
            >
              <View style={{
                width: 80,
                height: 80,
                backgroundColor: '#3b82f620',
                borderRadius: 40,
                alignItems: 'center',
                justifyContent: 'center',
                marginBottom: 16,
              }}>
                <Ionicons name="images" size={40} color="#3b82f6" />
              </View>
              <Text style={{
                fontSize: 20,
                fontWeight: 'bold',
                color: '#111827',
                marginBottom: 8,
              }}>
                Choose from Library
              </Text>
              <Text style={{
                fontSize: 14,
                color: '#6b7280',
                textAlign: 'center',
              }}>
                Select an existing photo from your gallery
              </Text>
            </TouchableOpacity>
          </View>
        </SafeAreaView>
      </View>
    </Modal>
  );

  return (
    <View style={{ flex: 1, backgroundColor: '#f1f5f9' }}>
      <SafeAreaView style={{ flex: 1 }}>
        <StatusBar barStyle="light-content" backgroundColor="#dc2626" />
        
        {/* Enhanced Header */}
        <View style={{
          backgroundColor: '#dc2626',
          paddingHorizontal: 20,
          paddingTop: 20,
          paddingBottom: 28,
          borderBottomLeftRadius: 28,
          borderBottomRightRadius: 28,
          shadowColor: '#dc2626',
          shadowOffset: { width: 0, height: 12 },
          shadowOpacity: 0.3,
          shadowRadius: 20,
          elevation: 16,
        }}>
          <View style={{
            flexDirection: 'row',
            alignItems: 'center',
            marginBottom: 20,
          }}>
            <TouchableOpacity
              onPress={onCancel}
              style={{
                width: 48,
                height: 48,
                borderRadius: 24,
                backgroundColor: 'rgba(255,255,255,0.2)',
                justifyContent: 'center',
                alignItems: 'center',
                marginRight: 16,
                borderWidth: 2,
                borderColor: 'rgba(255,255,255,0.3)',
              }}
            >
              <Ionicons name="arrow-back" size={24} color="white" />
            </TouchableOpacity>
            
            <View style={{ flex: 1 }}>
              <Text style={{
                fontSize: 24,
                fontWeight: 'bold',
                color: 'white',
                marginBottom: 4,
              }}>
                📸 {title}
              </Text>
              <Text style={{
                fontSize: 14,
                color: 'rgba(255,255,255,0.8)',
                fontWeight: '500',
              }}>
                {subtitle}
              </Text>
            </View>
          </View>
        </View>

        <Animated.View style={{
          flex: 1,
          opacity: fadeAnimation,
          transform: [{ scale: scaleAnimation }],
        }}>
          {/* Photo Preview Area */}
          <View style={{
            flex: 1,
            justifyContent: 'center',
            alignItems: 'center',
            paddingHorizontal: 40,
          }}>
            {capturedPhoto ? (
              <View style={{
                backgroundColor: 'white',
                borderRadius: 20,
                padding: 20,
                shadowColor: '#000',
                shadowOffset: { width: 0, height: 8 },
                shadowOpacity: 0.1,
                shadowRadius: 16,
                elevation: 8,
                width: '100%',
                alignItems: 'center',
              }}>
                <Image
                  source={{ uri: capturedPhoto }}
                  style={{
                    width: 280,
                    height: 210,
                    borderRadius: 16,
                    marginBottom: 20,
                  }}
                  resizeMode="cover"
                />
                
                <View style={{
                  flexDirection: 'row',
                  alignItems: 'center',
                  marginBottom: 20,
                }}>
                  <Ionicons name="checkmark-circle" size={24} color="#10b981" />
                  <Text style={{
                    fontSize: 18,
                    fontWeight: 'bold',
                    color: '#10b981',
                    marginLeft: 8,
                  }}>
                    Photo Captured Successfully!
                  </Text>
                </View>

                <View style={{
                  flexDirection: 'row',
                  gap: 12,
                  width: '100%',
                }}>
                  <TouchableOpacity
                    onPress={handleRetakePhoto}
                    style={{
                      flex: 1,
                      backgroundColor: '#6b7280',
                      borderRadius: 16,
                      paddingVertical: 14,
                      alignItems: 'center',
                    }}
                  >
                    <Text style={{
                      fontSize: 16,
                      fontWeight: 'bold',
                      color: 'white',
                    }}>
                      Retake
                    </Text>
                  </TouchableOpacity>

                  <TouchableOpacity
                    onPress={handleConfirmPhoto}
                    style={{
                      flex: 1,
                      backgroundColor: '#dc2626',
                      borderRadius: 16,
                      paddingVertical: 14,
                      alignItems: 'center',
                    }}
                  >
                    <Text style={{
                      fontSize: 16,
                      fontWeight: 'bold',
                      color: 'white',
                    }}>
                      Confirm
                    </Text>
                  </TouchableOpacity>
                </View>
              </View>
            ) : (
              <TouchableOpacity
                onPress={() => setShowSourceModal(true)}
                style={{
                  backgroundColor: 'white',
                  borderRadius: 20,
                  padding: 40,
                  alignItems: 'center',
                  shadowColor: '#dc2626',
                  shadowOffset: { width: 0, height: 8 },
                  shadowOpacity: 0.1,
                  shadowRadius: 16,
                  elevation: 8,
                  width: '100%',
                  borderWidth: 2,
                  borderColor: '#f3f4f6',
                  borderStyle: 'dashed',
                }}
              >
                <View style={{
                  width: 100,
                  height: 100,
                  backgroundColor: '#dc262620',
                  borderRadius: 50,
                  alignItems: 'center',
                  justifyContent: 'center',
                  marginBottom: 24,
                }}>
                  <Ionicons name={getPhotoTypeIcon() as any} size={50} color="#dc2626" />
                </View>
                
                <Text style={{
                  fontSize: 20,
                  fontWeight: 'bold',
                  color: '#111827',
                  marginBottom: 8,
                  textAlign: 'center',
                }}>
                  Tap to Add Photo
                </Text>
                
                <Text style={{
                  fontSize: 14,
                  color: '#6b7280',
                  textAlign: 'center',
                  lineHeight: 20,
                }}>
                  Take a photo or select from your gallery
                </Text>
              </TouchableOpacity>
            )}
          </View>
        </Animated.View>

        {renderSourceModal()}
      </SafeAreaView>
    </View>
  );
};

export default CameraPhotoScreen;

// Export a reusable Photo Capture Modal component
export const PhotoCaptureModal: React.FC<{
  visible: boolean;
  onClose: () => void;
  onPhotoTaken: (uri: string) => void;
  title: string;
  photoType: 'profile' | 'document' | 'order_proof' | 'delivery_proof';
}> = ({ visible, onClose, onPhotoTaken, title, photoType }) => {
  return (
    <Modal
      visible={visible}
      animationType="slide"
      presentationStyle="fullScreen"
      onRequestClose={onClose}
    >
      <CameraPhotoScreen
        title={title}
        subtitle="Capture or select a photo"
        photoType={photoType}
        onPhotoTaken={(uri) => {
          onPhotoTaken(uri);
          onClose();
        }}
        onCancel={onClose}
      />
    </Modal>
  );
};
