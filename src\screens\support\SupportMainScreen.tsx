import React from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  ScrollView,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import { useNavigation } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import { SupportStackParamList } from '../../types/navigation';

type SupportMainScreenNavigationProp = StackNavigationProp<SupportStackParamList, 'SupportMain'>;

const SupportMainScreen: React.FC = () => {
  const navigation = useNavigation<SupportMainScreenNavigationProp>();

  const supportOptions = [
    {
      id: 'live-chat',
      title: 'Live Chat with Support',
      description: 'Real-time messaging with our support team',
      icon: 'chatbubbles',
      color: '#3b82f6',
      onPress: () => navigation.navigate('LiveChat'),
    },
    {
      id: 'help-center',
      title: 'Help Center & FAQ',
      description: 'Find answers to common questions',
      icon: 'help-circle',
      color: '#10b981',
      onPress: () => navigation.navigate('HelpCenter'),
    },
    {
      id: 'call-support',
      title: 'Call Support',
      description: 'Speak directly with our support team',
      icon: 'call',
      color: '#f59e0b',
      onPress: () => navigation.navigate('CallSupport'),
    },
    {
      id: 'report-issue',
      title: 'Report Issue with Order',
      description: 'Report problems with deliveries or customers',
      icon: 'warning',
      color: '#ef4444',
      onPress: () => navigation.navigate('ReportIssue'),
    },
    {
      id: 'app-feedback',
      title: 'App Feedback',
      description: 'Suggest features or report bugs',
      icon: 'star',
      color: '#8b5cf6',
      onPress: () => navigation.navigate('AppFeedback'),
    },
  ];

  const quickActions = [
    {
      id: 'emergency',
      title: 'Emergency Support',
      description: '24/7 emergency assistance',
      icon: 'medical',
      color: '#dc2626',
      onPress: () => navigation.navigate('CallSupport'),
    },
    {
      id: 'earnings',
      title: 'Earnings Issue',
      description: 'Payment and earnings problems',
      icon: 'wallet',
      color: '#059669',
      onPress: () => navigation.navigate('ReportIssue'),
    },
    {
      id: 'app-bug',
      title: 'App Not Working',
      description: 'Technical issues with the app',
      icon: 'bug',
      color: '#dc2626',
      onPress: () => navigation.navigate('AppFeedback'),
    },
  ];

  const renderSupportOption = (option: typeof supportOptions[0]) => (
    <TouchableOpacity
      key={option.id}
      onPress={option.onPress}
      style={{
        backgroundColor: 'white',
        marginHorizontal: 16,
        marginVertical: 8,
        borderRadius: 12,
        padding: 16,
        flexDirection: 'row',
        alignItems: 'center',
        borderWidth: 1,
        borderColor: '#e5e7eb',
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 1 },
        shadowOpacity: 0.05,
        shadowRadius: 2,
        elevation: 1,
      }}
    >
      <View style={{
        width: 48,
        height: 48,
        backgroundColor: `${option.color}15`,
        borderRadius: 24,
        alignItems: 'center',
        justifyContent: 'center',
        marginRight: 16,
      }}>
        <Ionicons
          name={option.icon as any}
          size={24}
          color={option.color}
        />
      </View>
      
      <View style={{ flex: 1 }}>
        <Text style={{
          fontSize: 16,
          fontWeight: '600',
          color: '#1f2937',
          marginBottom: 4,
        }}>
          {option.title}
        </Text>
        <Text style={{
          fontSize: 14,
          color: '#6b7280',
          lineHeight: 20,
        }}>
          {option.description}
        </Text>
      </View>
      
      <Ionicons
        name="chevron-forward"
        size={20}
        color="#9ca3af"
      />
    </TouchableOpacity>
  );

  const renderQuickAction = (action: typeof quickActions[0]) => (
    <TouchableOpacity
      key={action.id}
      onPress={action.onPress}
      style={{
        backgroundColor: 'white',
        borderRadius: 12,
        padding: 16,
        alignItems: 'center',
        borderWidth: 1,
        borderColor: '#e5e7eb',
        minWidth: 120,
        marginRight: 12,
      }}
    >
      <View style={{
        width: 40,
        height: 40,
        backgroundColor: `${action.color}15`,
        borderRadius: 20,
        alignItems: 'center',
        justifyContent: 'center',
        marginBottom: 8,
      }}>
        <Ionicons
          name={action.icon as any}
          size={20}
          color={action.color}
        />
      </View>
      
      <Text style={{
        fontSize: 14,
        fontWeight: '600',
        color: '#1f2937',
        textAlign: 'center',
        marginBottom: 4,
      }}>
        {action.title}
      </Text>
      
      <Text style={{
        fontSize: 12,
        color: '#6b7280',
        textAlign: 'center',
        lineHeight: 16,
      }}>
        {action.description}
      </Text>
    </TouchableOpacity>
  );

  return (
    <SafeAreaView style={{ flex: 1, backgroundColor: '#f9fafb' }}>
      {/* Header */}
      <View style={{
        flexDirection: 'row',
        alignItems: 'center',
        paddingHorizontal: 16,
        paddingVertical: 12,
        backgroundColor: 'white',
        borderBottomWidth: 1,
        borderBottomColor: '#e5e7eb',
      }}>
        <TouchableOpacity
          onPress={() => navigation.goBack()}
          style={{
            width: 40,
            height: 40,
            alignItems: 'center',
            justifyContent: 'center',
            marginRight: 8,
          }}
        >
          <Ionicons name="arrow-back" size={24} color="#374151" />
        </TouchableOpacity>
        
        <Text style={{
          fontSize: 18,
          fontWeight: '600',
          color: '#1f2937',
          flex: 1,
        }}>
          Support & Help
        </Text>
        
        <View style={{
          flexDirection: 'row',
          alignItems: 'center',
        }}>
          <View style={{
            width: 8,
            height: 8,
            backgroundColor: '#10b981',
            borderRadius: 4,
            marginRight: 6,
          }} />
          <Text style={{
            fontSize: 12,
            color: '#10b981',
            fontWeight: '500',
          }}>
            Online
          </Text>
        </View>
      </View>

      <ScrollView style={{ flex: 1 }}>
        {/* Welcome Message */}
        <View style={{
          backgroundColor: '#f0f9ff',
          marginHorizontal: 16,
          marginTop: 16,
          borderRadius: 12,
          padding: 16,
          borderWidth: 1,
          borderColor: '#bae6fd',
        }}>
          <View style={{
            flexDirection: 'row',
            alignItems: 'center',
            marginBottom: 8,
          }}>
            <Ionicons name="information-circle" size={20} color="#0284c7" />
            <Text style={{
              fontSize: 16,
              fontWeight: '600',
              color: '#0284c7',
              marginLeft: 8,
            }}>
              We're here to help!
            </Text>
          </View>
          <Text style={{
            fontSize: 14,
            color: '#0369a1',
            lineHeight: 20,
          }}>
            Our support team is available 24/7 to assist you with any questions or issues you may have.
          </Text>
        </View>

        {/* Quick Actions */}
        <View style={{ marginTop: 24 }}>
          <Text style={{
            fontSize: 18,
            fontWeight: '600',
            color: '#1f2937',
            paddingHorizontal: 16,
            marginBottom: 12,
          }}>
            Quick Actions
          </Text>
          
          <ScrollView
            horizontal
            showsHorizontalScrollIndicator={false}
            style={{ paddingLeft: 16 }}
          >
            {quickActions.map(action => renderQuickAction(action))}
          </ScrollView>
        </View>

        {/* Support Options */}
        <View style={{ marginTop: 32 }}>
          <Text style={{
            fontSize: 18,
            fontWeight: '600',
            color: '#1f2937',
            paddingHorizontal: 16,
            marginBottom: 12,
          }}>
            Support Options
          </Text>
          
          {supportOptions.map(option => renderSupportOption(option))}
        </View>

        {/* Contact Info */}
        <View style={{
          backgroundColor: 'white',
          marginHorizontal: 16,
          marginTop: 24,
          marginBottom: 32,
          borderRadius: 12,
          padding: 16,
          borderWidth: 1,
          borderColor: '#e5e7eb',
        }}>
          <Text style={{
            fontSize: 16,
            fontWeight: '600',
            color: '#1f2937',
            marginBottom: 12,
          }}>
            Contact Information
          </Text>
          
          <View style={{
            flexDirection: 'row',
            alignItems: 'center',
            marginBottom: 8,
          }}>
            <Ionicons name="call" size={16} color="#6b7280" />
            <Text style={{
              fontSize: 14,
              color: '#374151',
              marginLeft: 8,
            }}>
              Emergency: +92-21-111-HELP (4357)
            </Text>
          </View>
          
          <View style={{
            flexDirection: 'row',
            alignItems: 'center',
            marginBottom: 8,
          }}>
            <Ionicons name="mail" size={16} color="#6b7280" />
            <Text style={{
              fontSize: 14,
              color: '#374151',
              marginLeft: 8,
            }}>
              <EMAIL>
            </Text>
          </View>
          
          <View style={{
            flexDirection: 'row',
            alignItems: 'center',
          }}>
            <Ionicons name="time" size={16} color="#6b7280" />
            <Text style={{
              fontSize: 14,
              color: '#374151',
              marginLeft: 8,
            }}>
              Support Hours: 24/7 for emergencies, 9 AM - 9 PM for general support
            </Text>
          </View>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

export default SupportMainScreen;
