// Safety & Legal Types

export enum EmergencyType {
  MEDICAL = 'medical',
  ACCIDENT = 'accident',
  THEFT = 'theft',
  HARASSMENT = 'harassment',
  VEHICLE_BREAKDOWN = 'vehicle_breakdown',
  THREAT = 'threat',
  LOST = 'lost',
  OTHER = 'other',
}

export enum IncidentType {
  TRAFFIC_ACCIDENT = 'traffic_accident',
  VEHICLE_BREAKDOWN = 'vehicle_breakdown',
  THEFT_ROBBERY = 'theft_robbery',
  CUSTOMER_INCIDENT = 'customer_incident',
  FOOD_SPILLAGE = 'food_spillage',
  DELIVERY_DELAY = 'delivery_delay',
  HARASSMENT = 'harassment',
  PROPERTY_DAMAGE = 'property_damage',
  INJURY = 'injury',
  WEATHER_RELATED = 'weather_related',
  OTHER = 'other',
}

export enum IncidentSeverity {
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high',
  CRITICAL = 'critical',
}

export enum IncidentStatus {
  REPORTED = 'reported',
  UNDER_REVIEW = 'under_review',
  INVESTIGATING = 'investigating',
  RESOLVED = 'resolved',
  CLOSED = 'closed',
}

export enum EmergencyContactType {
  POLICE = 'police',
  AMBULANCE = 'ambulance',
  FIRE_DEPARTMENT = 'fire_department',
  COMPANY_EMERGENCY = 'company_emergency',
  PERSONAL_EMERGENCY = 'personal_emergency',
  ROADSIDE_ASSISTANCE = 'roadside_assistance',
  INSURANCE = 'insurance',
}

export enum DocumentType {
  RIDER_AGREEMENT = 'rider_agreement',
  PRIVACY_POLICY = 'privacy_policy',
  TERMS_OF_SERVICE = 'terms_of_service',
  SAFETY_GUIDELINES = 'safety_guidelines',
  INSURANCE_POLICY = 'insurance_policy',
  CODE_OF_CONDUCT = 'code_of_conduct',
  DATA_PROTECTION = 'data_protection',
  DELIVERY_STANDARDS = 'delivery_standards',
}

export enum LocationSharingStatus {
  INACTIVE = 'inactive',
  ACTIVE = 'active',
  EMERGENCY_ONLY = 'emergency_only',
  PAUSED = 'paused',
}

// Emergency SOS interfaces
export interface EmergencyContact {
  id: string;
  type: EmergencyContactType;
  name: string;
  phoneNumber: string;
  description: string;
  isActive: boolean;
  isPrimary: boolean;
  responseTime?: string; // Expected response time
  coverage?: string; // Coverage area
  languages?: string[]; // Supported languages
  availability: {
    is24x7: boolean;
    hours?: {
      start: string;
      end: string;
    };
    days?: string[];
  };
}

export interface SOSSession {
  id: string;
  riderId: string;
  emergencyType: EmergencyType;
  location: {
    latitude: number;
    longitude: number;
    address: string;
    accuracy: number;
  };
  contactedNumbers: string[];
  startTime: string;
  endTime?: string;
  status: 'active' | 'resolved' | 'cancelled';
  notes?: string;
  responseTime?: number; // in seconds
  adminNotified: boolean;
  emergencyContactsNotified: string[];
}

export interface LocationShare {
  id: string;
  riderId: string;
  recipientId: string;
  recipientType: 'admin' | 'emergency_contact' | 'family';
  recipientName: string;
  status: LocationSharingStatus;
  startTime: string;
  endTime?: string;
  duration?: number; // in minutes
  location: {
    latitude: number;
    longitude: number;
    address: string;
    timestamp: string;
  };
  shareReason: string;
  autoStop: boolean;
}

// Incident reporting interfaces
export interface IncidentReport {
  id: string;
  riderId: string;
  orderId?: string;
  type: IncidentType;
  severity: IncidentSeverity;
  status: IncidentStatus;
  title: string;
  description: string;
  location: {
    latitude: number;
    longitude: number;
    address: string;
  };
  dateTime: string;
  reportedAt: string;
  updatedAt: string;
  evidence: {
    photos: string[];
    videos?: string[];
    documents?: string[];
  };
  involvedParties: {
    customers?: string[];
    otherDrivers?: string[];
    witnesses?: string[];
    authorities?: string[];
  };
  damages: {
    vehicleDamage: boolean;
    personalInjury: boolean;
    propertyDamage: boolean;
    foodDamage: boolean;
    estimatedCost?: number;
  };
  policeReport: {
    filed: boolean;
    reportNumber?: string;
    officerName?: string;
    stationName?: string;
  };
  insurance: {
    claimFiled: boolean;
    claimNumber?: string;
    insuranceCompany?: string;
  };
  followUpRequired: boolean;
  adminNotes?: string;
  resolution?: string;
  compensationAmount?: number;
}

// Legal documents interfaces
export interface LegalDocument {
  id: string;
  type: DocumentType;
  title: string;
  version: string;
  content: string;
  lastUpdated: string;
  effectiveDate: string;
  isRequired: boolean;
  category: string;
  language: string;
  wordCount: number;
  estimatedReadTime: number; // in minutes
  sections: DocumentSection[];
}

export interface DocumentSection {
  id: string;
  title: string;
  content: string;
  order: number;
  subsections?: DocumentSubsection[];
}

export interface DocumentSubsection {
  id: string;
  title: string;
  content: string;
  order: number;
}

export interface DocumentAcceptance {
  id: string;
  riderId: string;
  documentId: string;
  documentVersion: string;
  acceptedAt: string;
  ipAddress: string;
  deviceInfo: {
    platform: string;
    version: string;
    model: string;
  };
  isRequired: boolean;
  expiresAt?: string;
}

// Safety context interfaces
export interface SafetyState {
  emergencyContacts: EmergencyContact[];
  activeSOS: SOSSession | null;
  sosHistory: SOSSession[];
  locationShares: LocationShare[];
  incidentReports: IncidentReport[];
  legalDocuments: LegalDocument[];
  documentAcceptances: DocumentAcceptance[];
  currentLocation: {
    latitude: number;
    longitude: number;
    address: string;
    timestamp: string;
  } | null;
  locationSharingStatus: LocationSharingStatus;
  loading: boolean;
  error: string | null;
}

export interface SafetyAction {
  type: 'SET_LOADING' | 'SET_ERROR' | 'SET_EMERGENCY_CONTACTS' | 'ADD_EMERGENCY_CONTACT' | 
        'UPDATE_EMERGENCY_CONTACT' | 'DELETE_EMERGENCY_CONTACT' | 'START_SOS' | 'END_SOS' | 
        'SET_SOS_HISTORY' | 'ADD_LOCATION_SHARE' | 'UPDATE_LOCATION_SHARE' | 'SET_LOCATION_SHARES' |
        'SET_INCIDENT_REPORTS' | 'ADD_INCIDENT_REPORT' | 'UPDATE_INCIDENT_REPORT' | 
        'SET_LEGAL_DOCUMENTS' | 'SET_DOCUMENT_ACCEPTANCES' | 'ADD_DOCUMENT_ACCEPTANCE' |
        'SET_CURRENT_LOCATION' | 'SET_LOCATION_SHARING_STATUS';
  payload: any;
}

export interface SafetyContextType {
  // State
  emergencyContacts: EmergencyContact[];
  activeSOS: SOSSession | null;
  sosHistory: SOSSession[];
  locationShares: LocationShare[];
  incidentReports: IncidentReport[];
  legalDocuments: LegalDocument[];
  documentAcceptances: DocumentAcceptance[];
  currentLocation: {
    latitude: number;
    longitude: number;
    address: string;
    timestamp: string;
  } | null;
  locationSharingStatus: LocationSharingStatus;
  loading: boolean;
  error: string | null;

  // Emergency SOS functions
  startSOS: (emergencyType: EmergencyType, location: any) => Promise<SOSSession>;
  endSOS: (sosId: string, notes?: string) => Promise<void>;
  addEmergencyContact: (contact: Omit<EmergencyContact, 'id'>) => Promise<EmergencyContact>;
  updateEmergencyContact: (contactId: string, updates: Partial<EmergencyContact>) => Promise<void>;
  deleteEmergencyContact: (contactId: string) => Promise<void>;

  // Location sharing functions
  startLocationShare: (recipientId: string, recipientType: string, recipientName: string, reason: string, duration?: number) => Promise<LocationShare>;
  stopLocationShare: (shareId: string) => Promise<void>;
  updateLocationSharingStatus: (status: LocationSharingStatus) => Promise<void>;

  // Incident reporting functions
  submitIncidentReport: (report: Omit<IncidentReport, 'id' | 'reportedAt' | 'updatedAt' | 'status'>) => Promise<IncidentReport>;
  updateIncidentReport: (reportId: string, updates: Partial<IncidentReport>) => Promise<void>;

  // Legal documents functions
  loadLegalDocuments: () => Promise<void>;
  acceptDocument: (documentId: string, documentVersion: string) => Promise<DocumentAcceptance>;
  getDocumentAcceptance: (documentId: string) => DocumentAcceptance | null;

  // Location functions
  getCurrentLocation: () => Promise<void>;
  updateCurrentLocation: (location: any) => void;
}
