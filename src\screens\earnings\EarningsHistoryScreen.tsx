import React, { useEffect, useState } from 'react';
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  RefreshControl,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import { useNavigation } from '@react-navigation/native';
import { Card, Badge, LoadingSpinner } from '../../components/ui';
import { useEarnings } from '../../context/EarningsContext';
import { EarningsData, EarningsStatus } from '../../types/earnings';
import { formatCurrency, formatDate, timeAgo } from '../../utils/helpers';

const EarningsHistoryScreen: React.FC = () => {
  const navigation = useNavigation();
  const { state, fetchRecentEarnings } = useEarnings();
  const [refreshing, setRefreshing] = useState(false);

  useEffect(() => {
    fetchRecentEarnings(50);
  }, []);

  const onRefresh = async () => {
    setRefreshing(true);
    await fetchRecentEarnings(50);
    setRefreshing(false);
  };

  const getStatusColor = (status: EarningsStatus) => {
    switch (status) {
      case EarningsStatus.PAID:
        return '#10b981';
      case EarningsStatus.PENDING:
        return '#f59e0b';
      case EarningsStatus.PROCESSING:
        return '#3b82f6';
      case EarningsStatus.FAILED:
        return '#ef4444';
      default:
        return '#6b7280';
    }
  };

  const getStatusText = (status: EarningsStatus) => {
    switch (status) {
      case EarningsStatus.PAID:
        return 'Paid';
      case EarningsStatus.PENDING:
        return 'Pending';
      case EarningsStatus.PROCESSING:
        return 'Processing';
      case EarningsStatus.FAILED:
        return 'Failed';
      default:
        return status;
    }
  };

  const renderEarningsCard = (earnings: EarningsData) => (
    <Card key={earnings.id} variant="elevated" margin="sm" padding="lg">
      <View style={{ flexDirection: 'row', justifyContent: 'space-between', marginBottom: 8 }}>
        <View style={{ flex: 1 }}>
          <Text style={{ fontSize: 16, fontWeight: 'bold', color: '#111827' }}>
            {earnings.restaurant.name}
          </Text>
          <Text style={{ fontSize: 14, color: '#6b7280' }}>
            Order #{earnings.orderNumber}
          </Text>
        </View>
        <View style={{ alignItems: 'flex-end' }}>
          <Text style={{ fontSize: 18, fontWeight: 'bold', color: '#111827' }}>
            {formatCurrency(earnings.total)}
          </Text>
          <Badge
            text={getStatusText(earnings.status)}
            style={{
              backgroundColor: getStatusColor(earnings.status),
              marginTop: 4,
            }}
          />
        </View>
      </View>

      <View style={{
        flexDirection: 'row',
        alignItems: 'center',
        marginBottom: 8,
      }}>
        <Ionicons name="person-outline" size={16} color="#6b7280" />
        <Text style={{ fontSize: 14, color: '#6b7280', marginLeft: 4 }}>
          {earnings.customer.name}
        </Text>
      </View>

      <View style={{
        flexDirection: 'row',
        justifyContent: 'space-between',
        paddingTop: 12,
        borderTopWidth: 1,
        borderTopColor: '#e5e7eb',
      }}>
        <View style={{ flex: 1 }}>
          <Text style={{ fontSize: 12, color: '#6b7280' }}>Base</Text>
          <Text style={{ fontSize: 14, fontWeight: '500', color: '#111827' }}>
            {formatCurrency(earnings.amount)}
          </Text>
        </View>

        <View style={{ flex: 1 }}>
          <Text style={{ fontSize: 12, color: '#6b7280' }}>Tips</Text>
          <Text style={{ fontSize: 14, fontWeight: '500', color: '#111827' }}>
            {formatCurrency(earnings.tips)}
          </Text>
        </View>

        <View style={{ flex: 1 }}>
          <Text style={{ fontSize: 12, color: '#6b7280' }}>Bonus</Text>
          <Text style={{ fontSize: 14, fontWeight: '500', color: '#111827' }}>
            {formatCurrency(earnings.bonuses)}
          </Text>
        </View>

        <View style={{ flex: 1, alignItems: 'flex-end' }}>
          <Text style={{ fontSize: 12, color: '#6b7280' }}>Date</Text>
          <Text style={{ fontSize: 14, fontWeight: '500', color: '#111827' }}>
            {formatDate(earnings.date, 'SHORT_DATE')}
          </Text>
        </View>
      </View>
    </Card>
  );

  const renderHeader = () => (
    <View style={{
      flexDirection: 'row',
      alignItems: 'center',
      paddingHorizontal: 20,
      paddingVertical: 16,
      backgroundColor: '#ffffff',
      borderBottomWidth: 1,
      borderBottomColor: '#e5e7eb',
    }}>
      <TouchableOpacity
        onPress={() => navigation.goBack()}
        style={{ marginRight: 16 }}
      >
        <Ionicons name="arrow-back" size={24} color="#374151" />
      </TouchableOpacity>
      
      <Text style={{ fontSize: 24, fontWeight: 'bold', color: '#111827' }}>
        Earnings History
      </Text>
    </View>
  );

  const renderSummaryCard = () => {
    const totalEarnings = state.recentEarnings.reduce((sum, earning) => sum + earning.total, 0);
    const totalOrders = state.recentEarnings.length;
    const totalTips = state.recentEarnings.reduce((sum, earning) => sum + earning.tips, 0);
    const averagePerOrder = totalOrders > 0 ? totalEarnings / totalOrders : 0;

    return (
      <Card variant="elevated" margin="md" padding="lg">
        <Text style={{ fontSize: 18, fontWeight: 'bold', color: '#111827', marginBottom: 16 }}>
          Recent Summary
        </Text>
        
        <View style={{
          flexDirection: 'row',
          justifyContent: 'space-between',
        }}>
          <View style={{ alignItems: 'center', flex: 1 }}>
            <Text style={{ fontSize: 18, fontWeight: 'bold', color: '#111827' }}>
              {formatCurrency(totalEarnings)}
            </Text>
            <Text style={{ fontSize: 12, color: '#6b7280' }}>Total</Text>
          </View>

          <View style={{ alignItems: 'center', flex: 1 }}>
            <Text style={{ fontSize: 18, fontWeight: 'bold', color: '#111827' }}>
              {totalOrders}
            </Text>
            <Text style={{ fontSize: 12, color: '#6b7280' }}>Orders</Text>
          </View>

          <View style={{ alignItems: 'center', flex: 1 }}>
            <Text style={{ fontSize: 18, fontWeight: 'bold', color: '#111827' }}>
              {formatCurrency(totalTips)}
            </Text>
            <Text style={{ fontSize: 12, color: '#6b7280' }}>Tips</Text>
          </View>

          <View style={{ alignItems: 'center', flex: 1 }}>
            <Text style={{ fontSize: 18, fontWeight: 'bold', color: '#111827' }}>
              {formatCurrency(averagePerOrder)}
            </Text>
            <Text style={{ fontSize: 12, color: '#6b7280' }}>Avg/Order</Text>
          </View>
        </View>
      </Card>
    );
  };

  const renderEmptyState = () => (
    <View style={{
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
      paddingHorizontal: 40,
    }}>
      <Ionicons name="receipt-outline" size={64} color="#d1d5db" />
      <Text style={{
        fontSize: 18,
        fontWeight: 'bold',
        color: '#374151',
        marginTop: 16,
        marginBottom: 8,
        textAlign: 'center',
      }}>
        No Earnings History
      </Text>
      <Text style={{
        fontSize: 14,
        color: '#6b7280',
        textAlign: 'center',
        lineHeight: 20,
      }}>
        Your earnings from completed deliveries will appear here.
      </Text>
    </View>
  );

  if (state.isLoading && !refreshing) {
    return (
      <SafeAreaView style={{ flex: 1, backgroundColor: '#f8fafc' }}>
        {renderHeader()}
        <LoadingSpinner message="Loading earnings history..." />
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={{ flex: 1, backgroundColor: '#f8fafc' }}>
      {renderHeader()}
      
      {state.recentEarnings.length === 0 ? (
        <ScrollView
          contentContainerStyle={{ flex: 1 }}
          refreshControl={
            <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
          }
        >
          {renderEmptyState()}
        </ScrollView>
      ) : (
        <ScrollView
          style={{ flex: 1 }}
          refreshControl={
            <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
          }
          showsVerticalScrollIndicator={false}
        >
          {renderSummaryCard()}
          
          <View style={{ paddingVertical: 8 }}>
            {state.recentEarnings.map(renderEarningsCard)}
          </View>
          
          {/* Bottom spacing */}
          <View style={{ height: 20 }} />
        </ScrollView>
      )}
    </SafeAreaView>
  );
};

export default EarningsHistoryScreen;
