import React, { useEffect, useRef, useState } from 'react';
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  Animated,
  Dimensions,
  StatusBar,
  Vibration,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import { useNavigation, useRoute } from '@react-navigation/native';
import { LinearGradient } from 'expo-linear-gradient';
import { BlurView } from 'expo-blur';
import { PieChart } from 'react-native-chart-kit';

const { width: screenWidth, height: screenHeight } = Dimensions.get('window');

interface TripSummaryData {
  orderId: string;
  orderNumber: string;
  deliveryTime: number; // in minutes
  distance: number; // in kilometers
  baseFare: number;
  tip: number;
  bonus: number;
  totalPayout: number;
  customerRating?: number;
  restaurant: {
    name: string;
    address: string;
  };
  customer: {
    name: string;
    address: string;
  };
  completedAt: string;
}

const TripSummaryScreen: React.FC = () => {
  const navigation = useNavigation();
  const route = useRoute();
  const { tripData } = route.params as { tripData: TripSummaryData };

  // Animation refs
  const fadeAnimation = useRef(new Animated.Value(0)).current;
  const slideAnimation = useRef(new Animated.Value(50)).current;
  const scaleAnimation = useRef(new Animated.Value(0.8)).current;
  const successAnimation = useRef(new Animated.Value(0)).current;
  const payoutAnimation = useRef(new Animated.Value(0)).current;
  const chartAnimation = useRef(new Animated.Value(0)).current;
  const tipAnimation = useRef(new Animated.Value(1)).current;

  // State
  const [showSuccessAnimation, setShowSuccessAnimation] = useState(false);
  const [showTipAnimation, setShowTipAnimation] = useState(false);

  useEffect(() => {
    // Start entrance animations
    Animated.parallel([
      Animated.timing(fadeAnimation, {
        toValue: 1,
        duration: 800,
        useNativeDriver: true,
      }),
      Animated.timing(slideAnimation, {
        toValue: 0,
        duration: 800,
        useNativeDriver: true,
      }),
      Animated.spring(scaleAnimation, {
        toValue: 1,
        tension: 50,
        friction: 7,
        useNativeDriver: true,
      }),
    ]).start();

    // Delayed animations for content
    setTimeout(() => {
      setShowSuccessAnimation(true);
      Vibration.vibrate([0, 100, 50, 100]);
      
      Animated.sequence([
        Animated.timing(successAnimation, {
          toValue: 1,
          duration: 600,
          useNativeDriver: true,
        }),
        Animated.timing(payoutAnimation, {
          toValue: 1,
          duration: 800,
          useNativeDriver: true,
        }),
        Animated.timing(chartAnimation, {
          toValue: 1,
          duration: 600,
          useNativeDriver: true,
        }),
      ]).start();
    }, 500);

    // Show tip animation if tip > 0
    if (tripData.tip > 0) {
      setTimeout(() => {
        setShowTipAnimation(true);
        Animated.loop(
          Animated.sequence([
            Animated.timing(tipAnimation, {
              toValue: 1.2,
              duration: 500,
              useNativeDriver: true,
            }),
            Animated.timing(tipAnimation, {
              toValue: 1,
              duration: 500,
              useNativeDriver: true,
            }),
          ]),
          { iterations: 3 }
        ).start();
      }, 1500);
    }
  }, []);

  const formatCurrency = (amount: number) => {
    return `₨${Math.round(amount)}`;
  };

  const formatTime = (minutes: number) => {
    if (minutes < 60) {
      return `${minutes}m`;
    }
    const hours = Math.floor(minutes / 60);
    const remainingMinutes = minutes % 60;
    return `${hours}h ${remainingMinutes}m`;
  };

  const formatDistance = (km: number) => {
    return `${km.toFixed(1)} km`;
  };

  // Chart data for fare breakdown
  const chartData = [
    {
      name: 'Base Fare',
      amount: tripData.baseFare,
      color: '#3b82f6',
      legendFontColor: '#374151',
      legendFontSize: 14,
    },
    {
      name: 'Tip',
      amount: tripData.tip,
      color: '#10b981',
      legendFontColor: '#374151',
      legendFontSize: 14,
    },
    {
      name: 'Bonus',
      amount: tripData.bonus,
      color: '#f59e0b',
      legendFontColor: '#374151',
      legendFontSize: 14,
    },
  ].filter(item => item.amount > 0);

  const chartConfig = {
    backgroundColor: '#ffffff',
    backgroundGradientFrom: '#ffffff',
    backgroundGradientTo: '#ffffff',
    color: (opacity = 1) => `rgba(0, 0, 0, ${opacity})`,
    strokeWidth: 2,
    barPercentage: 0.5,
    useShadowColorFromDataset: false,
  };

  const renderHeader = () => (
    <LinearGradient
      colors={['#10b981', '#059669']}
      style={{
        paddingTop: StatusBar.currentHeight || 0,
        paddingBottom: 20,
      }}
    >
      <SafeAreaView>
        <View style={{
          flexDirection: 'row',
          alignItems: 'center',
          justifyContent: 'space-between',
          paddingHorizontal: 20,
          paddingTop: 16,
        }}>
          <TouchableOpacity
            onPress={() => navigation.goBack()}
            style={{
              width: 40,
              height: 40,
              borderRadius: 20,
              backgroundColor: 'rgba(255,255,255,0.2)',
              alignItems: 'center',
              justifyContent: 'center',
            }}
          >
            <Ionicons name="arrow-back" size={24} color="white" />
          </TouchableOpacity>

          <Text style={{
            fontSize: 20,
            fontWeight: 'bold',
            color: 'white',
          }}>
            Trip Summary
          </Text>

          <View style={{ width: 40 }} />
        </View>
      </SafeAreaView>
    </LinearGradient>
  );

  return (
    <View style={{ flex: 1, backgroundColor: '#f8fafc' }}>
      {renderHeader()}
      
      <Animated.View
        style={{
          flex: 1,
          opacity: fadeAnimation,
          transform: [
            { translateY: slideAnimation },
            { scale: scaleAnimation },
          ],
        }}
      >
        <ScrollView
          style={{ flex: 1 }}
          showsVerticalScrollIndicator={false}
          contentContainerStyle={{ paddingBottom: 100 }}
        >
          {/* Success Animation */}
          {showSuccessAnimation && (
            <Animated.View
              style={{
                alignItems: 'center',
                paddingVertical: 30,
                opacity: successAnimation,
                transform: [{ scale: successAnimation }],
              }}
            >
              <View style={{
                width: 100,
                height: 100,
                borderRadius: 50,
                backgroundColor: '#10b981',
                alignItems: 'center',
                justifyContent: 'center',
                marginBottom: 16,
                shadowColor: '#10b981',
                shadowOffset: { width: 0, height: 8 },
                shadowOpacity: 0.3,
                shadowRadius: 16,
                elevation: 8,
              }}>
                <Ionicons name="checkmark" size={48} color="white" />
              </View>

              <Text style={{
                fontSize: 24,
                fontWeight: 'bold',
                color: '#111827',
                marginBottom: 8,
              }}>
                Delivery Complete! 🎉
              </Text>

              <Text style={{
                fontSize: 16,
                color: '#6b7280',
                textAlign: 'center',
                paddingHorizontal: 40,
              }}>
                Great job! Order #{tripData.orderNumber} delivered successfully
              </Text>
            </Animated.View>
          )}

          {/* Total Payout Card */}
          <Animated.View
            style={{
              marginHorizontal: 20,
              marginBottom: 20,
              opacity: payoutAnimation,
              transform: [{ scale: payoutAnimation }],
            }}
          >
            <LinearGradient
              colors={['#ef4444', '#dc2626']}
              style={{
                borderRadius: 20,
                padding: 24,
                alignItems: 'center',
                shadowColor: '#ef4444',
                shadowOffset: { width: 0, height: 8 },
                shadowOpacity: 0.3,
                shadowRadius: 16,
                elevation: 8,
              }}
            >
              <Text style={{
                fontSize: 16,
                color: 'rgba(255,255,255,0.9)',
                marginBottom: 8,
              }}>
                Total Payout
              </Text>

              <Text style={{
                fontSize: 48,
                fontWeight: 'bold',
                color: 'white',
                marginBottom: 8,
              }}>
                {formatCurrency(tripData.totalPayout)}
              </Text>

              {tripData.tip > 0 && showTipAnimation && (
                <Animated.View
                  style={{
                    flexDirection: 'row',
                    alignItems: 'center',
                    backgroundColor: 'rgba(255,255,255,0.2)',
                    paddingHorizontal: 12,
                    paddingVertical: 6,
                    borderRadius: 20,
                    transform: [{ scale: tipAnimation }],
                  }}
                >
                  <Ionicons name="heart" size={16} color="#fbbf24" />
                  <Text style={{
                    fontSize: 14,
                    color: 'white',
                    marginLeft: 6,
                    fontWeight: '600',
                  }}>
                    +{formatCurrency(tripData.tip)} tip!
                  </Text>
                </Animated.View>
              )}
            </LinearGradient>
          </Animated.View>

          {/* Trip Summary Stats */}
          <View style={{
            marginHorizontal: 20,
            marginBottom: 20,
          }}>
            <View style={{
              backgroundColor: 'white',
              borderRadius: 16,
              padding: 20,
              shadowColor: '#000',
              shadowOffset: { width: 0, height: 2 },
              shadowOpacity: 0.1,
              shadowRadius: 8,
              elevation: 3,
            }}>
              <Text style={{
                fontSize: 18,
                fontWeight: 'bold',
                color: '#111827',
                marginBottom: 16,
              }}>
                Trip Summary
              </Text>

              <View style={{ gap: 16 }}>
                {/* Time */}
                <View style={{
                  flexDirection: 'row',
                  alignItems: 'center',
                  justifyContent: 'space-between',
                }}>
                  <View style={{ flexDirection: 'row', alignItems: 'center' }}>
                    <View style={{
                      width: 40,
                      height: 40,
                      borderRadius: 20,
                      backgroundColor: '#dbeafe',
                      alignItems: 'center',
                      justifyContent: 'center',
                      marginRight: 12,
                    }}>
                      <Ionicons name="time" size={20} color="#3b82f6" />
                    </View>
                    <Text style={{
                      fontSize: 16,
                      color: '#374151',
                      fontWeight: '500',
                    }}>
                      Delivery Time
                    </Text>
                  </View>
                  <Text style={{
                    fontSize: 16,
                    fontWeight: 'bold',
                    color: '#111827',
                  }}>
                    {formatTime(tripData.deliveryTime)}
                  </Text>
                </View>

                {/* Distance */}
                <View style={{
                  flexDirection: 'row',
                  alignItems: 'center',
                  justifyContent: 'space-between',
                }}>
                  <View style={{ flexDirection: 'row', alignItems: 'center' }}>
                    <View style={{
                      width: 40,
                      height: 40,
                      borderRadius: 20,
                      backgroundColor: '#dcfce7',
                      alignItems: 'center',
                      justifyContent: 'center',
                      marginRight: 12,
                    }}>
                      <Ionicons name="location" size={20} color="#22c55e" />
                    </View>
                    <Text style={{
                      fontSize: 16,
                      color: '#374151',
                      fontWeight: '500',
                    }}>
                      Distance
                    </Text>
                  </View>
                  <Text style={{
                    fontSize: 16,
                    fontWeight: 'bold',
                    color: '#111827',
                  }}>
                    {formatDistance(tripData.distance)}
                  </Text>
                </View>
              </View>
            </View>
          </View>

          {/* Fare Breakdown Chart */}
          <Animated.View
            style={{
              marginHorizontal: 20,
              marginBottom: 20,
              opacity: chartAnimation,
              transform: [{ scale: chartAnimation }],
            }}
          >
            <View style={{
              backgroundColor: 'white',
              borderRadius: 16,
              padding: 20,
              shadowColor: '#000',
              shadowOffset: { width: 0, height: 2 },
              shadowOpacity: 0.1,
              shadowRadius: 8,
              elevation: 3,
            }}>
              <Text style={{
                fontSize: 18,
                fontWeight: 'bold',
                color: '#111827',
                marginBottom: 16,
              }}>
                Fare Breakdown
              </Text>

              {chartData.length > 0 && (
                <View style={{ alignItems: 'center' }}>
                  <PieChart
                    data={chartData}
                    width={screenWidth - 80}
                    height={200}
                    chartConfig={chartConfig}
                    accessor="amount"
                    backgroundColor="transparent"
                    paddingLeft="15"
                    center={[10, 0]}
                    absolute
                  />
                </View>
              )}

              {/* Breakdown Details */}
              <View style={{ marginTop: 20, gap: 12 }}>
                <View style={{
                  flexDirection: 'row',
                  alignItems: 'center',
                  justifyContent: 'space-between',
                }}>
                  <View style={{ flexDirection: 'row', alignItems: 'center' }}>
                    <View style={{
                      width: 12,
                      height: 12,
                      borderRadius: 6,
                      backgroundColor: '#3b82f6',
                      marginRight: 8,
                    }} />
                    <Text style={{
                      fontSize: 14,
                      color: '#374151',
                    }}>
                      Base Fare
                    </Text>
                  </View>
                  <Text style={{
                    fontSize: 14,
                    fontWeight: '600',
                    color: '#111827',
                  }}>
                    {formatCurrency(tripData.baseFare)}
                  </Text>
                </View>

                {tripData.tip > 0 && (
                  <View style={{
                    flexDirection: 'row',
                    alignItems: 'center',
                    justifyContent: 'space-between',
                  }}>
                    <View style={{ flexDirection: 'row', alignItems: 'center' }}>
                      <View style={{
                        width: 12,
                        height: 12,
                        borderRadius: 6,
                        backgroundColor: '#10b981',
                        marginRight: 8,
                      }} />
                      <Text style={{
                        fontSize: 14,
                        color: '#374151',
                      }}>
                        Customer Tip
                      </Text>
                    </View>
                    <Text style={{
                      fontSize: 14,
                      fontWeight: '600',
                      color: '#10b981',
                    }}>
                      {formatCurrency(tripData.tip)}
                    </Text>
                  </View>
                )}

                {tripData.bonus > 0 && (
                  <View style={{
                    flexDirection: 'row',
                    alignItems: 'center',
                    justifyContent: 'space-between',
                  }}>
                    <View style={{ flexDirection: 'row', alignItems: 'center' }}>
                      <View style={{
                        width: 12,
                        height: 12,
                        borderRadius: 6,
                        backgroundColor: '#f59e0b',
                        marginRight: 8,
                      }} />
                      <Text style={{
                        fontSize: 14,
                        color: '#374151',
                      }}>
                        Bonus
                      </Text>
                    </View>
                    <Text style={{
                      fontSize: 14,
                      fontWeight: '600',
                      color: '#f59e0b',
                    }}>
                      {formatCurrency(tripData.bonus)}
                    </Text>
                  </View>
                )}

                {/* Total */}
                <View style={{
                  borderTopWidth: 1,
                  borderTopColor: '#e5e7eb',
                  paddingTop: 12,
                  marginTop: 8,
                }}>
                  <View style={{
                    flexDirection: 'row',
                    alignItems: 'center',
                    justifyContent: 'space-between',
                  }}>
                    <Text style={{
                      fontSize: 16,
                      fontWeight: 'bold',
                      color: '#111827',
                    }}>
                      Total Earned
                    </Text>
                    <Text style={{
                      fontSize: 18,
                      fontWeight: 'bold',
                      color: '#f97316',
                    }}>
                      {formatCurrency(tripData.totalPayout)}
                    </Text>
                  </View>
                </View>
              </View>
            </View>
          </Animated.View>

          {/* Customer Rating */}
          {tripData.customerRating && (
            <View style={{
              marginHorizontal: 20,
              marginBottom: 20,
            }}>
              <View style={{
                backgroundColor: 'white',
                borderRadius: 16,
                padding: 20,
                shadowColor: '#000',
                shadowOffset: { width: 0, height: 2 },
                shadowOpacity: 0.1,
                shadowRadius: 8,
                elevation: 3,
              }}>
                <Text style={{
                  fontSize: 18,
                  fontWeight: 'bold',
                  color: '#111827',
                  marginBottom: 16,
                }}>
                  Customer Rating
                </Text>

                <View style={{
                  flexDirection: 'row',
                  alignItems: 'center',
                  justifyContent: 'center',
                }}>
                  {[1, 2, 3, 4, 5].map((star) => (
                    <Ionicons
                      key={star}
                      name={star <= tripData.customerRating! ? "star" : "star-outline"}
                      size={32}
                      color={star <= tripData.customerRating! ? "#fbbf24" : "#d1d5db"}
                      style={{ marginHorizontal: 4 }}
                    />
                  ))}
                </View>

                <Text style={{
                  fontSize: 16,
                  color: '#6b7280',
                  textAlign: 'center',
                  marginTop: 8,
                }}>
                  {tripData.customerRating}/5 stars
                </Text>
              </View>
            </View>
          )}
        </ScrollView>

        {/* Continue Button */}
        <View style={{
          position: 'absolute',
          bottom: 0,
          left: 0,
          right: 0,
          backgroundColor: 'white',
          paddingHorizontal: 20,
          paddingVertical: 20,
          borderTopWidth: 1,
          borderTopColor: '#e5e7eb',
        }}>
          <TouchableOpacity
            onPress={() => navigation.navigate('Dashboard' as never)}
            style={{
              backgroundColor: '#f97316',
              borderRadius: 16,
              padding: 16,
              flexDirection: 'row',
              alignItems: 'center',
              justifyContent: 'center',
              shadowColor: '#f97316',
              shadowOffset: { width: 0, height: 4 },
              shadowOpacity: 0.3,
              shadowRadius: 8,
              elevation: 5,
            }}
          >
            <Ionicons name="home" size={20} color="white" style={{ marginRight: 8 }} />
            <Text style={{
              color: 'white',
              fontSize: 16,
              fontWeight: '600',
            }}>
              Continue to Dashboard
            </Text>
          </TouchableOpacity>
        </View>
      </Animated.View>
    </View>
  );
};

export default TripSummaryScreen;
