import React, { createContext, useContext, useReducer, useEffect, ReactNode } from 'react';
import {
  HeatmapArea,
  Badge,
  PerformanceStats,
  TrainingVideo,
  VideoProgress,
  TrainingCertificate,
  PushNotification,
  NotificationPreferences,
  LocationTrackingConfig,
  LocationData,
  TrackingStatus,
  AdvancedToolsContextType,
} from '../types/advancedTools';

// Advanced Tools State Interface
interface AdvancedToolsState {
  // Heatmap & Demand
  heatmapAreas: HeatmapArea[];
  selectedArea: HeatmapArea | null;
  
  // Performance & Gamification
  badges: Badge[];
  performanceStats: PerformanceStats | null;
  leaderboard: any[];
  
  // Training Videos
  trainingVideos: TrainingVideo[];
  videoProgress: VideoProgress[];
  certificates: TrainingCertificate[];
  
  // Push Notifications
  notifications: PushNotification[];
  notificationPreferences: NotificationPreferences | null;
  unreadCount: number;
  
  // Location Tracking
  locationConfig: LocationTrackingConfig | null;
  currentLocation: LocationData | null;
  locationHistory: LocationData[];
  trackingStatus: TrackingStatus;
  
  // General
  loading: boolean;
  error: string | null;
}

// Action Types
type AdvancedToolsAction =
  | { type: 'SET_LOADING'; payload: boolean }
  | { type: 'SET_ERROR'; payload: string | null }
  
  // Heatmap Actions
  | { type: 'SET_HEATMAP_AREAS'; payload: HeatmapArea[] }
  | { type: 'SET_SELECTED_AREA'; payload: HeatmapArea | null }
  
  // Performance Actions
  | { type: 'SET_BADGES'; payload: Badge[] }
  | { type: 'SET_PERFORMANCE_STATS'; payload: PerformanceStats }
  | { type: 'SET_LEADERBOARD'; payload: any[] }
  | { type: 'ADD_BADGE'; payload: Badge }
  
  // Training Actions
  | { type: 'SET_TRAINING_VIDEOS'; payload: TrainingVideo[] }
  | { type: 'SET_VIDEO_PROGRESS'; payload: VideoProgress[] }
  | { type: 'SET_CERTIFICATES'; payload: TrainingCertificate[] }
  | { type: 'UPDATE_VIDEO_PROGRESS'; payload: VideoProgress }
  | { type: 'ADD_CERTIFICATE'; payload: TrainingCertificate }
  
  // Notification Actions
  | { type: 'SET_NOTIFICATIONS'; payload: PushNotification[] }
  | { type: 'SET_NOTIFICATION_PREFERENCES'; payload: NotificationPreferences }
  | { type: 'ADD_NOTIFICATION'; payload: PushNotification }
  | { type: 'MARK_NOTIFICATION_READ'; payload: string }
  | { type: 'DELETE_NOTIFICATION'; payload: string }
  | { type: 'CLEAR_ALL_NOTIFICATIONS' }
  | { type: 'UPDATE_UNREAD_COUNT'; payload: number }
  
  // Location Actions
  | { type: 'SET_LOCATION_CONFIG'; payload: LocationTrackingConfig }
  | { type: 'SET_CURRENT_LOCATION'; payload: LocationData }
  | { type: 'SET_LOCATION_HISTORY'; payload: LocationData[] }
  | { type: 'ADD_LOCATION_HISTORY'; payload: LocationData }
  | { type: 'SET_TRACKING_STATUS'; payload: TrackingStatus };

// Initial State
const initialState: AdvancedToolsState = {
  // Heatmap & Demand
  heatmapAreas: [],
  selectedArea: null,
  
  // Performance & Gamification
  badges: [],
  performanceStats: null,
  leaderboard: [],
  
  // Training Videos
  trainingVideos: [],
  videoProgress: [],
  certificates: [],
  
  // Push Notifications
  notifications: [],
  notificationPreferences: null,
  unreadCount: 0,
  
  // Location Tracking
  locationConfig: null,
  currentLocation: null,
  locationHistory: [],
  trackingStatus: TrackingStatus.STOPPED,
  
  // General
  loading: false,
  error: null,
};

// Reducer Function
const advancedToolsReducer = (state: AdvancedToolsState, action: AdvancedToolsAction): AdvancedToolsState => {
  switch (action.type) {
    case 'SET_LOADING':
      return { ...state, loading: action.payload };
    
    case 'SET_ERROR':
      return { ...state, error: action.payload };
    
    // Heatmap Actions
    case 'SET_HEATMAP_AREAS':
      return { ...state, heatmapAreas: action.payload };
    
    case 'SET_SELECTED_AREA':
      return { ...state, selectedArea: action.payload };
    
    // Performance Actions
    case 'SET_BADGES':
      return { ...state, badges: action.payload };
    
    case 'SET_PERFORMANCE_STATS':
      return { ...state, performanceStats: action.payload };
    
    case 'SET_LEADERBOARD':
      return { ...state, leaderboard: action.payload };
    
    case 'ADD_BADGE':
      return { ...state, badges: [...state.badges, action.payload] };
    
    // Training Actions
    case 'SET_TRAINING_VIDEOS':
      return { ...state, trainingVideos: action.payload };
    
    case 'SET_VIDEO_PROGRESS':
      return { ...state, videoProgress: action.payload };
    
    case 'SET_CERTIFICATES':
      return { ...state, certificates: action.payload };
    
    case 'UPDATE_VIDEO_PROGRESS':
      return {
        ...state,
        videoProgress: state.videoProgress.map(progress =>
          progress.videoId === action.payload.videoId ? action.payload : progress
        ),
      };
    
    case 'ADD_CERTIFICATE':
      return { ...state, certificates: [...state.certificates, action.payload] };
    
    // Notification Actions
    case 'SET_NOTIFICATIONS':
      return { 
        ...state, 
        notifications: action.payload,
        unreadCount: action.payload.filter(n => !n.isRead).length,
      };
    
    case 'SET_NOTIFICATION_PREFERENCES':
      return { ...state, notificationPreferences: action.payload };
    
    case 'ADD_NOTIFICATION':
      const newNotifications = [action.payload, ...state.notifications];
      return {
        ...state,
        notifications: newNotifications,
        unreadCount: newNotifications.filter(n => !n.isRead).length,
      };
    
    case 'MARK_NOTIFICATION_READ':
      const updatedNotifications = state.notifications.map(notification =>
        notification.id === action.payload ? { ...notification, isRead: true } : notification
      );
      return {
        ...state,
        notifications: updatedNotifications,
        unreadCount: updatedNotifications.filter(n => !n.isRead).length,
      };
    
    case 'DELETE_NOTIFICATION':
      const filteredNotifications = state.notifications.filter(n => n.id !== action.payload);
      return {
        ...state,
        notifications: filteredNotifications,
        unreadCount: filteredNotifications.filter(n => !n.isRead).length,
      };
    
    case 'CLEAR_ALL_NOTIFICATIONS':
      return { ...state, notifications: [], unreadCount: 0 };
    
    case 'UPDATE_UNREAD_COUNT':
      return { ...state, unreadCount: action.payload };
    
    // Location Actions
    case 'SET_LOCATION_CONFIG':
      return { ...state, locationConfig: action.payload };
    
    case 'SET_CURRENT_LOCATION':
      return { ...state, currentLocation: action.payload };
    
    case 'SET_LOCATION_HISTORY':
      return { ...state, locationHistory: action.payload };
    
    case 'ADD_LOCATION_HISTORY':
      return {
        ...state,
        locationHistory: [action.payload, ...state.locationHistory.slice(0, 19)], // Keep last 20
      };
    
    case 'SET_TRACKING_STATUS':
      return { ...state, trackingStatus: action.payload };
    
    default:
      return state;
  }
};

// Context Creation
const AdvancedToolsContext = createContext<AdvancedToolsContextType | undefined>(undefined);

// Provider Component
interface AdvancedToolsProviderProps {
  children: ReactNode;
}

export const AdvancedToolsProvider: React.FC<AdvancedToolsProviderProps> = ({ children }) => {
  const [state, dispatch] = useReducer(advancedToolsReducer, initialState);

  // Initialize data on mount
  useEffect(() => {
    initializeAdvancedTools();
  }, []);

  const initializeAdvancedTools = async () => {
    dispatch({ type: 'SET_LOADING', payload: true });
    try {
      // Initialize all advanced tools data
      // This would typically load from API or local storage
      await Promise.all([
        loadHeatmapData(),
        loadPerformanceData(),
        loadTrainingData(),
        loadNotificationData(),
        loadLocationData(),
      ]);
    } catch (error) {
      console.error('Error initializing advanced tools:', error);
      dispatch({ type: 'SET_ERROR', payload: 'Failed to load advanced tools data' });
    } finally {
      dispatch({ type: 'SET_LOADING', payload: false });
    }
  };

  const loadHeatmapData = async () => {
    // Mock heatmap data loading
    // In real app, this would fetch from API
  };

  const loadPerformanceData = async () => {
    // Mock performance data loading
    // In real app, this would fetch from API
  };

  const loadTrainingData = async () => {
    // Mock training data loading
    // In real app, this would fetch from API
  };

  const loadNotificationData = async () => {
    // Mock notification data loading
    // In real app, this would fetch from API
  };

  const loadLocationData = async () => {
    // Mock location data loading
    // In real app, this would fetch from API
  };

  // Context Value with all methods
  const contextValue: AdvancedToolsContextType = {
    // State
    ...state,

    // Heatmap Methods
    setHeatmapAreas: (areas: HeatmapArea[]) =>
      dispatch({ type: 'SET_HEATMAP_AREAS', payload: areas }),
    setSelectedArea: (area: HeatmapArea | null) =>
      dispatch({ type: 'SET_SELECTED_AREA', payload: area }),

    // Performance Methods
    setBadges: (badges: Badge[]) =>
      dispatch({ type: 'SET_BADGES', payload: badges }),
    setPerformanceStats: (stats: PerformanceStats) =>
      dispatch({ type: 'SET_PERFORMANCE_STATS', payload: stats }),
    setLeaderboard: (leaderboard: any[]) =>
      dispatch({ type: 'SET_LEADERBOARD', payload: leaderboard }),
    addBadge: (badge: Badge) =>
      dispatch({ type: 'ADD_BADGE', payload: badge }),

    // Training Methods
    setTrainingVideos: (videos: TrainingVideo[]) =>
      dispatch({ type: 'SET_TRAINING_VIDEOS', payload: videos }),
    setVideoProgress: (progress: VideoProgress[]) =>
      dispatch({ type: 'SET_VIDEO_PROGRESS', payload: progress }),
    setCertificates: (certificates: TrainingCertificate[]) =>
      dispatch({ type: 'SET_CERTIFICATES', payload: certificates }),
    updateVideoProgress: (progress: VideoProgress) =>
      dispatch({ type: 'UPDATE_VIDEO_PROGRESS', payload: progress }),
    addCertificate: (certificate: TrainingCertificate) =>
      dispatch({ type: 'ADD_CERTIFICATE', payload: certificate }),

    // Notification Methods
    setNotifications: (notifications: PushNotification[]) =>
      dispatch({ type: 'SET_NOTIFICATIONS', payload: notifications }),
    setNotificationPreferences: (preferences: NotificationPreferences) =>
      dispatch({ type: 'SET_NOTIFICATION_PREFERENCES', payload: preferences }),
    addNotification: (notification: PushNotification) =>
      dispatch({ type: 'ADD_NOTIFICATION', payload: notification }),
    markNotificationRead: (notificationId: string) =>
      dispatch({ type: 'MARK_NOTIFICATION_READ', payload: notificationId }),
    deleteNotification: (notificationId: string) =>
      dispatch({ type: 'DELETE_NOTIFICATION', payload: notificationId }),
    clearAllNotifications: () =>
      dispatch({ type: 'CLEAR_ALL_NOTIFICATIONS' }),

    // Location Methods
    setLocationConfig: (config: LocationTrackingConfig) =>
      dispatch({ type: 'SET_LOCATION_CONFIG', payload: config }),
    setCurrentLocation: (location: LocationData) =>
      dispatch({ type: 'SET_CURRENT_LOCATION', payload: location }),
    setLocationHistory: (history: LocationData[]) =>
      dispatch({ type: 'SET_LOCATION_HISTORY', payload: history }),
    addLocationHistory: (location: LocationData) =>
      dispatch({ type: 'ADD_LOCATION_HISTORY', payload: location }),
    setTrackingStatus: (status: TrackingStatus) =>
      dispatch({ type: 'SET_TRACKING_STATUS', payload: status }),

    // General Methods
    setLoading: (loading: boolean) =>
      dispatch({ type: 'SET_LOADING', payload: loading }),
    setError: (error: string | null) =>
      dispatch({ type: 'SET_ERROR', payload: error }),
    refreshData: initializeAdvancedTools,
  };

  return (
    <AdvancedToolsContext.Provider value={contextValue}>
      {children}
    </AdvancedToolsContext.Provider>
  );
};

// Custom Hook
export const useAdvancedTools = (): AdvancedToolsContextType => {
  const context = useContext(AdvancedToolsContext);
  if (context === undefined) {
    throw new Error('useAdvancedTools must be used within an AdvancedToolsProvider');
  }
  return context;
};

export default AdvancedToolsContext;
