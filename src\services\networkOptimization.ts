import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse } from 'axios';
import AsyncStorage from '@react-native-async-storage/async-storage';
import NetInfo from '@react-native-community/netinfo';

interface CacheConfig {
  ttl: number; // Time to live in milliseconds
  key: string;
}

interface RequestConfig extends AxiosRequestConfig {
  cache?: CacheConfig;
  retry?: {
    attempts: number;
    delay: number;
  };
  offline?: boolean;
}

class NetworkOptimizationService {
  private axiosInstance: AxiosInstance;
  private requestQueue: Array<() => Promise<any>> = [];
  private isOnline: boolean = true;
  private pendingRequests: Map<string, Promise<any>> = new Map();

  constructor() {
    this.axiosInstance = axios.create({
      timeout: 10000,
      headers: {
        'Content-Type': 'application/json',
      },
    });

    this.setupInterceptors();
    this.setupNetworkListener();
  }

  private setupInterceptors() {
    // Request interceptor
    this.axiosInstance.interceptors.request.use(
      async (config: any) => {
        // Add request deduplication
        const requestKey = this.getRequestKey(config);
        if (this.pendingRequests.has(requestKey)) {
          return this.pendingRequests.get(requestKey);
        }

        // Check cache first
        if (config.cache) {
          const cachedData = await this.getCachedData(config.cache.key);
          if (cachedData) {
            return Promise.resolve({ data: cachedData, fromCache: true });
          }
        }

        // Add compression header
        config.headers['Accept-Encoding'] = 'gzip, deflate, br';

        return config;
      },
      (error) => Promise.reject(error)
    );

    // Response interceptor
    this.axiosInstance.interceptors.response.use(
      async (response: AxiosResponse) => {
        const config = response.config as RequestConfig;
        const requestKey = this.getRequestKey(config);
        
        // Remove from pending requests
        this.pendingRequests.delete(requestKey);

        // Cache successful responses
        if (config.cache && response.status === 200) {
          await this.setCachedData(config.cache.key, response.data, config.cache.ttl);
        }

        return response;
      },
      async (error) => {
        const config = error.config as RequestConfig;
        const requestKey = this.getRequestKey(config);
        
        // Remove from pending requests
        this.pendingRequests.delete(requestKey);

        // Retry logic
        if (config.retry && config.retry.attempts > 0) {
          config.retry.attempts--;
          await this.delay(config.retry.delay);
          return this.axiosInstance.request(config);
        }

        // Queue request if offline and offline support is enabled
        if (!this.isOnline && config.offline) {
          this.queueRequest(() => this.axiosInstance.request(config));
          return Promise.reject(new Error('Request queued for when online'));
        }

        return Promise.reject(error);
      }
    );
  }

  private setupNetworkListener() {
    NetInfo.addEventListener((state) => {
      const wasOffline = !this.isOnline;
      this.isOnline = state.isConnected ?? false;

      // Process queued requests when coming back online
      if (wasOffline && this.isOnline) {
        this.processQueuedRequests();
      }
    });
  }

  private getRequestKey(config: AxiosRequestConfig): string {
    return `${config.method}-${config.url}-${JSON.stringify(config.params || {})}-${JSON.stringify(config.data || {})}`;
  }

  private async getCachedData(key: string): Promise<any> {
    try {
      const cached = await AsyncStorage.getItem(`cache_${key}`);
      if (!cached) return null;

      const { data, expiry } = JSON.parse(cached);
      if (Date.now() > expiry) {
        await AsyncStorage.removeItem(`cache_${key}`);
        return null;
      }

      return data;
    } catch (error) {
      console.error('Cache read error:', error);
      return null;
    }
  }

  private async setCachedData(key: string, data: any, ttl: number): Promise<void> {
    try {
      const cacheData = {
        data,
        expiry: Date.now() + ttl,
      };
      await AsyncStorage.setItem(`cache_${key}`, JSON.stringify(cacheData));
    } catch (error) {
      console.error('Cache write error:', error);
    }
  }

  private queueRequest(requestFn: () => Promise<any>): void {
    this.requestQueue.push(requestFn);
  }

  private async processQueuedRequests(): Promise<void> {
    const queue = [...this.requestQueue];
    this.requestQueue = [];

    for (const requestFn of queue) {
      try {
        await requestFn();
      } catch (error) {
        console.error('Queued request failed:', error);
      }
    }
  }

  private delay(ms: number): Promise<void> {
    return new Promise((resolve) => setTimeout(resolve, ms));
  }

  // Public methods
  async get<T = any>(url: string, config?: RequestConfig): Promise<AxiosResponse<T>> {
    const requestKey = this.getRequestKey({ method: 'GET', url, ...config });
    
    if (this.pendingRequests.has(requestKey)) {
      return this.pendingRequests.get(requestKey);
    }

    const request = this.axiosInstance.get<T>(url, config);
    this.pendingRequests.set(requestKey, request);
    
    return request;
  }

  async post<T = any>(url: string, data?: any, config?: RequestConfig): Promise<AxiosResponse<T>> {
    return this.axiosInstance.post<T>(url, data, config);
  }

  async put<T = any>(url: string, data?: any, config?: RequestConfig): Promise<AxiosResponse<T>> {
    return this.axiosInstance.put<T>(url, data, config);
  }

  async delete<T = any>(url: string, config?: RequestConfig): Promise<AxiosResponse<T>> {
    return this.axiosInstance.delete<T>(url, config);
  }

  // Batch requests for better performance
  async batchRequests<T = any>(requests: Array<() => Promise<AxiosResponse<T>>>): Promise<AxiosResponse<T>[]> {
    const batchSize = 5; // Process 5 requests at a time
    const results: AxiosResponse<T>[] = [];

    for (let i = 0; i < requests.length; i += batchSize) {
      const batch = requests.slice(i, i + batchSize);
      const batchResults = await Promise.allSettled(batch.map(req => req()));
      
      batchResults.forEach((result) => {
        if (result.status === 'fulfilled') {
          results.push(result.value);
        }
      });
    }

    return results;
  }

  // Clear cache
  async clearCache(): Promise<void> {
    try {
      const keys = await AsyncStorage.getAllKeys();
      const cacheKeys = keys.filter(key => key.startsWith('cache_'));
      await AsyncStorage.multiRemove(cacheKeys);
    } catch (error) {
      console.error('Cache clear error:', error);
    }
  }

  // Get network status
  getNetworkStatus(): boolean {
    return this.isOnline;
  }

  // Preload data for better UX
  async preloadData(endpoints: Array<{ url: string; cacheKey: string; ttl: number }>): Promise<void> {
    const requests = endpoints.map(endpoint => () =>
      this.get(endpoint.url, {
        cache: { key: endpoint.cacheKey, ttl: endpoint.ttl },
      })
    );

    await this.batchRequests(requests);
  }
}

// Singleton instance
export const networkService = new NetworkOptimizationService();

// Helper functions for common API patterns
export const createOptimizedApiCall = <T = any>(
  url: string,
  options: {
    method?: 'GET' | 'POST' | 'PUT' | 'DELETE';
    cache?: CacheConfig;
    retry?: { attempts: number; delay: number };
    offline?: boolean;
  } = {}
) => {
  const { method = 'GET', cache, retry, offline } = options;

  return async (data?: any, params?: any): Promise<T> => {
    const config: RequestConfig = {
      params,
      cache,
      retry,
      offline,
    };

    let response: AxiosResponse<T>;

    switch (method) {
      case 'GET':
        response = await networkService.get<T>(url, config);
        break;
      case 'POST':
        response = await networkService.post<T>(url, data, config);
        break;
      case 'PUT':
        response = await networkService.put<T>(url, data, config);
        break;
      case 'DELETE':
        response = await networkService.delete<T>(url, config);
        break;
      default:
        throw new Error(`Unsupported method: ${method}`);
    }

    return response.data;
  };
};

export default networkService;
