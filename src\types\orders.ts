export interface OrderItem {
  id: string;
  name: string;
  quantity: number;
  price: number;
  specialInstructions?: string;
  modifiers?: OrderModifier[];
}

export interface OrderModifier {
  id: string;
  name: string;
  price: number;
}

export interface Address {
  id: string;
  street: string;
  city: string;
  state: string;
  zipCode: string;
  country: string;
  latitude: number;
  longitude: number;
  instructions?: string;
  landmark?: string;
}

export interface Customer {
  id: string;
  firstName: string;
  lastName: string;
  phone: string;
  phoneNumber: string;
  email?: string;
  profileImage?: string;
}

export interface Restaurant {
  id: string;
  name: string;
  address: Address;
  phone: string;
  phoneNumber: string;
  image?: string;
  rating: number;
  preparationTime: number; // in minutes
}

export enum OrderStatus {
  AVAILABLE = 'available',
  ACCEPTED = 'accepted',
  ARRIVED_AT_RESTAURANT = 'arrived_at_restaurant',
  PICKED_UP = 'picked_up',
  DELIVERED = 'delivered',
  CANCELLED = 'cancelled',
}

export enum PaymentMethod {
  CASH = 'cash',
  CARD = 'card',
  DIGITAL_WALLET = 'digital_wallet',
}

export interface Order {
  id: string;
  orderNumber: string;
  customer: Customer;
  restaurant: Restaurant;
  items: OrderItem[];
  pickupAddress: Address;
  deliveryAddress: Address;
  status: OrderStatus;
  paymentMethod: PaymentMethod;
  subtotal: number;
  deliveryFee: number;
  tip: number;
  total: number;
  estimatedEarnings: number;
  estimatedDistance: number; // in kilometers
  estimatedDuration: number; // in minutes
  specialInstructions?: string;
  createdAt: string;
  updatedAt: string;
  acceptedAt?: string;
  pickedUpAt?: string;
  deliveredAt?: string;
}

export interface OrderState {
  availableOrders: Order[];
  acceptedOrders: Order[];
  completedOrders: Order[];
  orderHistory: Order[];
  currentOrder: Order | null;
  isLoading: boolean;
  error: string | null;
}

export interface OrderContextType {
  state: OrderState;
  fetchAvailableOrders: () => Promise<void>;
  acceptOrder: (orderId: string) => Promise<void>;
  declineOrder: (orderId: string) => Promise<void>;
  updateOrderStatus: (orderId: string, status: OrderStatus) => Promise<void>;
  startPickupTimer: (orderId: string) => Promise<void>;
  markAsPickedUp: (orderId: string, waitTime: number) => Promise<void>;
  markAsDelivered: (orderId: string, proofData: any) => Promise<void>;
  fetchOrderHistory: (filters?: OrderHistoryFilters) => Promise<void>;
  clearError: () => void;
}

export interface OrderHistoryFilters {
  startDate?: string;
  endDate?: string;
  status?: OrderStatus[];
  limit?: number;
  offset?: number;
}

export interface OrderStats {
  totalOrders: number;
  completedOrders: number;
  totalEarnings: number;
  totalDistance: number;
  averageRating: number;
  completionRate: number;
}
