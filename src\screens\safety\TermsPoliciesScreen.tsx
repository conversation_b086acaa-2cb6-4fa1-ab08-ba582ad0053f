import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  ScrollView,
  TextInput,
  Alert,
  Modal,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import { useNavigation } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import { SafetyStackParamList } from '../../types';
import {
  DocumentType,
  LegalDocument,
  DocumentAcceptance,
  DocumentSection,
} from '../../types/safety';

type TermsPoliciesScreenNavigationProp = StackNavigationProp<SafetyStackParamList, 'TermsPolicies'>;

const TermsPoliciesScreen: React.FC = () => {
  const navigation = useNavigation<TermsPoliciesScreenNavigationProp>();
  const [documents, setDocuments] = useState<LegalDocument[]>([]);
  const [acceptances, setAcceptances] = useState<DocumentAcceptance[]>([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedDocument, setSelectedDocument] = useState<LegalDocument | null>(null);
  const [showDocumentModal, setShowDocumentModal] = useState(false);
  const [loading, setLoading] = useState(true);

  // Mock legal documents
  const mockDocuments: LegalDocument[] = [
    {
      id: 'rider-agreement',
      type: DocumentType.RIDER_AGREEMENT,
      title: 'Rider Agreement',
      version: '2.1',
      content: `FOODWAY RIDER AGREEMENT

1. INTRODUCTION
This Rider Agreement ("Agreement") is entered into between FoodWay Pakistan ("Company") and the individual rider ("Rider") who wishes to provide delivery services through the FoodWay platform.

2. RIDER RESPONSIBILITIES
2.1 Vehicle Requirements
- Maintain a roadworthy vehicle with valid registration
- Ensure vehicle insurance is current and valid
- Keep vehicle clean and in good condition

2.2 Documentation
- Provide valid CNIC or passport
- Maintain valid driving license
- Submit required vehicle documents

2.3 Service Standards
- Maintain professional behavior with customers
- Follow delivery instructions accurately
- Handle food items with care and hygiene
- Wear FoodWay uniform during deliveries

3. COMPENSATION
3.1 Delivery Fees
- Base delivery fee per order
- Distance-based additional charges
- Peak hour bonuses when applicable

3.2 Payment Terms
- Weekly payment cycles
- Direct bank transfer or mobile wallet
- Deductions for damages or violations

4. SAFETY REQUIREMENTS
4.1 Traffic Rules
- Follow all traffic laws and regulations
- Use designated parking areas
- Wear helmet (for motorcycle riders)

4.2 Emergency Procedures
- Report accidents immediately
- Contact support for assistance
- Follow incident reporting procedures

5. TERMINATION
Either party may terminate this agreement with 7 days written notice.

Last Updated: January 15, 2024
Effective Date: February 1, 2024`,
      lastUpdated: '2024-01-15T00:00:00Z',
      effectiveDate: '2024-02-01T00:00:00Z',
      isRequired: true,
      category: 'Employment',
      language: 'English',
      wordCount: 285,
      estimatedReadTime: 3,
      sections: [
        {
          id: 'intro',
          title: 'Introduction',
          content: 'This Rider Agreement is entered into between FoodWay Pakistan and the individual rider.',
          order: 1,
        },
        {
          id: 'responsibilities',
          title: 'Rider Responsibilities',
          content: 'Vehicle requirements, documentation, and service standards.',
          order: 2,
        },
        {
          id: 'compensation',
          title: 'Compensation',
          content: 'Delivery fees, payment terms, and bonus structure.',
          order: 3,
        },
        {
          id: 'safety',
          title: 'Safety Requirements',
          content: 'Traffic rules and emergency procedures.',
          order: 4,
        },
        {
          id: 'termination',
          title: 'Termination',
          content: 'Agreement termination procedures.',
          order: 5,
        },
      ],
    },
    {
      id: 'privacy-policy',
      type: DocumentType.PRIVACY_POLICY,
      title: 'Privacy Policy',
      version: '1.8',
      content: `FOODWAY PRIVACY POLICY

1. INFORMATION WE COLLECT
1.1 Personal Information
- Name, phone number, email address
- CNIC/passport number
- Bank account details
- Vehicle information

1.2 Location Data
- GPS location during deliveries
- Route information
- Delivery addresses

1.3 Usage Data
- App usage patterns
- Performance metrics
- Device information

2. HOW WE USE YOUR INFORMATION
2.1 Service Provision
- Process delivery requests
- Calculate payments
- Provide customer support

2.2 Safety and Security
- Verify identity
- Monitor compliance
- Investigate incidents

2.3 Improvements
- Analyze performance
- Enhance user experience
- Develop new features

3. INFORMATION SHARING
3.1 With Customers
- Name and contact information
- Location during delivery
- Delivery status updates

3.2 With Authorities
- Legal compliance
- Safety investigations
- Regulatory requirements

4. DATA SECURITY
We implement appropriate security measures to protect your personal information against unauthorized access, alteration, disclosure, or destruction.

5. YOUR RIGHTS
- Access your personal data
- Request corrections
- Delete your account
- Opt-out of communications

6. CONTACT US
For privacy-related questions, contact <NAME_EMAIL>

Last Updated: December 10, 2023
Effective Date: January 1, 2024`,
      lastUpdated: '2023-12-10T00:00:00Z',
      effectiveDate: '2024-01-01T00:00:00Z',
      isRequired: true,
      category: 'Privacy',
      language: 'English',
      wordCount: 245,
      estimatedReadTime: 2,
      sections: [
        {
          id: 'collection',
          title: 'Information We Collect',
          content: 'Personal information, location data, and usage data.',
          order: 1,
        },
        {
          id: 'usage',
          title: 'How We Use Your Information',
          content: 'Service provision, safety, and improvements.',
          order: 2,
        },
        {
          id: 'sharing',
          title: 'Information Sharing',
          content: 'Sharing with customers and authorities.',
          order: 3,
        },
        {
          id: 'security',
          title: 'Data Security',
          content: 'Security measures and protection.',
          order: 4,
        },
        {
          id: 'rights',
          title: 'Your Rights',
          content: 'Data access, correction, and deletion rights.',
          order: 5,
        },
      ],
    },
    {
      id: 'terms-of-service',
      type: DocumentType.TERMS_OF_SERVICE,
      title: 'Terms of Service',
      version: '3.0',
      content: `FOODWAY TERMS OF SERVICE

1. ACCEPTANCE OF TERMS
By using the FoodWay platform, you agree to be bound by these Terms of Service.

2. PLATFORM DESCRIPTION
FoodWay is a technology platform that connects riders with customers for food delivery services.

3. RIDER OBLIGATIONS
3.1 Eligibility
- Must be at least 18 years old
- Valid driving license
- Legal right to work in Pakistan

3.2 Compliance
- Follow all applicable laws
- Adhere to platform policies
- Maintain professional conduct

4. PLATFORM USAGE
4.1 Account Security
- Keep login credentials secure
- Report unauthorized access
- Update information promptly

4.2 Prohibited Activities
- Fraudulent activities
- Harassment of customers
- Misuse of platform features

5. INTELLECTUAL PROPERTY
All platform content, trademarks, and technology are owned by FoodWay Pakistan.

6. LIMITATION OF LIABILITY
FoodWay's liability is limited to the maximum extent permitted by law.

7. DISPUTE RESOLUTION
Disputes will be resolved through arbitration in accordance with Pakistani law.

8. MODIFICATIONS
FoodWay reserves the right to modify these terms with reasonable notice.

Last Updated: November 20, 2023
Effective Date: December 1, 2023`,
      lastUpdated: '2023-11-20T00:00:00Z',
      effectiveDate: '2023-12-01T00:00:00Z',
      isRequired: true,
      category: 'Legal',
      language: 'English',
      wordCount: 220,
      estimatedReadTime: 2,
      sections: [
        {
          id: 'acceptance',
          title: 'Acceptance of Terms',
          content: 'Agreement to be bound by terms.',
          order: 1,
        },
        {
          id: 'platform',
          title: 'Platform Description',
          content: 'Technology platform for food delivery.',
          order: 2,
        },
        {
          id: 'obligations',
          title: 'Rider Obligations',
          content: 'Eligibility and compliance requirements.',
          order: 3,
        },
        {
          id: 'usage',
          title: 'Platform Usage',
          content: 'Account security and prohibited activities.',
          order: 4,
        },
      ],
    },
    {
      id: 'safety-guidelines',
      type: DocumentType.SAFETY_GUIDELINES,
      title: 'Safety Guidelines',
      version: '1.5',
      content: `FOODWAY SAFETY GUIDELINES

1. ROAD SAFETY
1.1 Traffic Rules
- Always follow traffic signals and signs
- Maintain safe following distance
- Use indicators when changing lanes
- Avoid using mobile phone while driving

1.2 Protective Equipment
- Wear helmet at all times (motorcycle riders)
- Use reflective vest during night deliveries
- Ensure vehicle lights are working

2. PERSONAL SAFETY
2.1 Situational Awareness
- Stay alert to surroundings
- Trust your instincts
- Avoid isolated areas when possible

2.2 Emergency Procedures
- Keep emergency contacts accessible
- Report suspicious activities
- Use SOS feature when needed

3. FOOD SAFETY
3.1 Hygiene
- Wash hands regularly
- Use sanitizer before handling food
- Keep delivery bag clean

3.2 Temperature Control
- Use insulated bags for hot food
- Minimize delivery time
- Check food packaging integrity

4. CUSTOMER INTERACTION
4.1 Professional Conduct
- Be polite and respectful
- Maintain appropriate distance
- Follow contactless delivery when requested

4.2 Conflict Resolution
- Remain calm in difficult situations
- Contact support for assistance
- Document incidents properly

5. VEHICLE MAINTENANCE
5.1 Regular Checks
- Check tire pressure weekly
- Ensure brakes are working
- Maintain adequate fuel/charge

5.2 Emergency Kit
- First aid kit
- Emergency contact numbers
- Basic tools for minor repairs

Last Updated: October 5, 2023
Effective Date: October 15, 2023`,
      lastUpdated: '2023-10-05T00:00:00Z',
      effectiveDate: '2023-10-15T00:00:00Z',
      isRequired: false,
      category: 'Safety',
      language: 'English',
      wordCount: 280,
      estimatedReadTime: 3,
      sections: [
        {
          id: 'road-safety',
          title: 'Road Safety',
          content: 'Traffic rules and protective equipment.',
          order: 1,
        },
        {
          id: 'personal-safety',
          title: 'Personal Safety',
          content: 'Situational awareness and emergency procedures.',
          order: 2,
        },
        {
          id: 'food-safety',
          title: 'Food Safety',
          content: 'Hygiene and temperature control.',
          order: 3,
        },
        {
          id: 'customer-interaction',
          title: 'Customer Interaction',
          content: 'Professional conduct and conflict resolution.',
          order: 4,
        },
        {
          id: 'vehicle-maintenance',
          title: 'Vehicle Maintenance',
          content: 'Regular checks and emergency kit.',
          order: 5,
        },
      ],
    },
  ];

  // Mock acceptances
  const mockAcceptances: DocumentAcceptance[] = [
    {
      id: 'acc-1',
      riderId: 'rider-1',
      documentId: 'rider-agreement',
      documentVersion: '2.1',
      acceptedAt: '2024-02-01T10:00:00Z',
      ipAddress: '***********',
      deviceInfo: {
        platform: 'iOS',
        version: '17.0',
        model: 'iPhone 12',
      },
      isRequired: true,
    },
    {
      id: 'acc-2',
      riderId: 'rider-1',
      documentId: 'privacy-policy',
      documentVersion: '1.8',
      acceptedAt: '2024-01-01T10:00:00Z',
      ipAddress: '***********',
      deviceInfo: {
        platform: 'iOS',
        version: '17.0',
        model: 'iPhone 12',
      },
      isRequired: true,
    },
  ];

  useEffect(() => {
    loadDocuments();
  }, []);

  const loadDocuments = async () => {
    setLoading(true);
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      setDocuments(mockDocuments);
      setAcceptances(mockAcceptances);
    } catch (error) {
      console.error('Error loading documents:', error);
    } finally {
      setLoading(false);
    }
  };

  const filteredDocuments = documents.filter(doc =>
    doc.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
    doc.content.toLowerCase().includes(searchQuery.toLowerCase())
  );

  const isDocumentAccepted = (documentId: string, version: string) => {
    return acceptances.some(acc => 
      acc.documentId === documentId && acc.documentVersion === version
    );
  };

  const getDocumentAcceptance = (documentId: string) => {
    return acceptances.find(acc => acc.documentId === documentId);
  };

  const acceptDocument = async (document: LegalDocument) => {
    try {
      const acceptance: DocumentAcceptance = {
        id: `acc-${Date.now()}`,
        riderId: 'rider-1',
        documentId: document.id,
        documentVersion: document.version,
        acceptedAt: new Date().toISOString(),
        ipAddress: '***********',
        deviceInfo: {
          platform: 'iOS',
          version: '17.0',
          model: 'iPhone 12',
        },
        isRequired: document.isRequired,
      };

      setAcceptances([...acceptances.filter(acc => acc.documentId !== document.id), acceptance]);
      
      Alert.alert(
        'Document Accepted',
        `You have successfully accepted the ${document.title}.`
      );
    } catch (error) {
      console.error('Error accepting document:', error);
      Alert.alert('Error', 'Failed to accept document. Please try again.');
    }
  };

  const openDocument = (document: LegalDocument) => {
    setSelectedDocument(document);
    setShowDocumentModal(true);
  };

  const renderDocument = (document: LegalDocument) => {
    const isAccepted = isDocumentAccepted(document.id, document.version);
    const acceptance = getDocumentAcceptance(document.id);
    
    return (
      <TouchableOpacity
        key={document.id}
        onPress={() => openDocument(document)}
        style={{
          backgroundColor: 'white',
          marginHorizontal: 16,
          marginVertical: 8,
          borderRadius: 12,
          padding: 16,
          borderWidth: 1,
          borderColor: isAccepted ? '#10b981' : '#e5e7eb',
          shadowColor: '#000',
          shadowOffset: { width: 0, height: 2 },
          shadowOpacity: 0.1,
          shadowRadius: 4,
          elevation: 3,
        }}
      >
        <View style={{
          flexDirection: 'row',
          alignItems: 'flex-start',
          justifyContent: 'space-between',
          marginBottom: 8,
        }}>
          <View style={{ flex: 1 }}>
            <Text style={{
              fontSize: 16,
              fontWeight: '600',
              color: '#1f2937',
              marginBottom: 4,
            }}>
              {document.title}
            </Text>
            <Text style={{
              fontSize: 12,
              color: '#6b7280',
              marginBottom: 4,
            }}>
              Version {document.version} • {document.category}
            </Text>
            <Text style={{
              fontSize: 12,
              color: '#6b7280',
            }}>
              {document.wordCount} words • {document.estimatedReadTime} min read
            </Text>
          </View>
          
          <View style={{ alignItems: 'flex-end' }}>
            {document.isRequired && (
              <View style={{
                backgroundColor: '#fef3c7',
                paddingHorizontal: 8,
                paddingVertical: 4,
                borderRadius: 12,
                marginBottom: 8,
              }}>
                <Text style={{
                  fontSize: 10,
                  color: '#d97706',
                  fontWeight: '500',
                }}>
                  REQUIRED
                </Text>
              </View>
            )}
            
            {isAccepted ? (
              <View style={{
                flexDirection: 'row',
                alignItems: 'center',
                backgroundColor: '#10b98115',
                paddingHorizontal: 8,
                paddingVertical: 4,
                borderRadius: 12,
              }}>
                <Ionicons name="checkmark-circle" size={16} color="#10b981" />
                <Text style={{
                  fontSize: 12,
                  color: '#10b981',
                  fontWeight: '500',
                  marginLeft: 4,
                }}>
                  Accepted
                </Text>
              </View>
            ) : (
              <View style={{
                backgroundColor: '#f3f4f6',
                paddingHorizontal: 8,
                paddingVertical: 4,
                borderRadius: 12,
              }}>
                <Text style={{
                  fontSize: 12,
                  color: '#6b7280',
                  fontWeight: '500',
                }}>
                  Not Accepted
                </Text>
              </View>
            )}
          </View>
        </View>
        
        {isAccepted && acceptance && (
          <View style={{
            backgroundColor: '#f0fdf4',
            padding: 12,
            borderRadius: 8,
            marginTop: 8,
          }}>
            <Text style={{
              fontSize: 12,
              color: '#166534',
              marginBottom: 4,
            }}>
              Accepted on {new Date(acceptance.acceptedAt).toLocaleDateString()}
            </Text>
            <Text style={{
              fontSize: 12,
              color: '#166534',
            }}>
              Version {acceptance.documentVersion}
            </Text>
          </View>
        )}
        
        <View style={{
          flexDirection: 'row',
          alignItems: 'center',
          marginTop: 12,
        }}>
          <Ionicons name="document-text" size={16} color="#6b7280" />
          <Text style={{
            fontSize: 12,
            color: '#6b7280',
            marginLeft: 6,
            flex: 1,
          }}>
            Last updated: {new Date(document.lastUpdated).toLocaleDateString()}
          </Text>
          <Ionicons name="chevron-forward" size={16} color="#6b7280" />
        </View>
      </TouchableOpacity>
    );
  };

  return (
    <SafeAreaView style={{ flex: 1, backgroundColor: '#f9fafb' }}>
      {/* Header */}
      <View style={{
        flexDirection: 'row',
        alignItems: 'center',
        paddingHorizontal: 16,
        paddingVertical: 12,
        backgroundColor: 'white',
        borderBottomWidth: 1,
        borderBottomColor: '#e5e7eb',
      }}>
        <TouchableOpacity
          onPress={() => navigation.goBack()}
          style={{
            width: 40,
            height: 40,
            alignItems: 'center',
            justifyContent: 'center',
            marginRight: 8,
          }}
        >
          <Ionicons name="arrow-back" size={24} color="#374151" />
        </TouchableOpacity>
        
        <Text style={{
          fontSize: 18,
          fontWeight: '600',
          color: '#1f2937',
          flex: 1,
        }}>
          Terms & Policies
        </Text>
      </View>

      {/* Search */}
      <View style={{
        paddingHorizontal: 16,
        paddingVertical: 12,
        backgroundColor: 'white',
        borderBottomWidth: 1,
        borderBottomColor: '#e5e7eb',
      }}>
        <View style={{
          flexDirection: 'row',
          alignItems: 'center',
          backgroundColor: '#f3f4f6',
          borderRadius: 8,
          paddingHorizontal: 12,
          paddingVertical: 8,
        }}>
          <Ionicons name="search" size={20} color="#6b7280" />
          <TextInput
            value={searchQuery}
            onChangeText={setSearchQuery}
            placeholder="Search documents..."
            style={{
              flex: 1,
              marginLeft: 8,
              fontSize: 14,
              color: '#1f2937',
            }}
          />
        </View>
      </View>

      {/* Documents List */}
      <ScrollView style={{ flex: 1 }}>
        {loading ? (
          <View style={{
            flex: 1,
            alignItems: 'center',
            justifyContent: 'center',
            paddingVertical: 40,
          }}>
            <Text style={{
              fontSize: 16,
              color: '#6b7280',
            }}>
              Loading documents...
            </Text>
          </View>
        ) : (
          <>
            {filteredDocuments.length === 0 ? (
              <View style={{
                alignItems: 'center',
                justifyContent: 'center',
                paddingVertical: 40,
              }}>
                <Ionicons name="document-text-outline" size={48} color="#d1d5db" />
                <Text style={{
                  fontSize: 16,
                  color: '#6b7280',
                  marginTop: 16,
                }}>
                  No documents found
                </Text>
              </View>
            ) : (
              <>
                <View style={{ paddingTop: 16 }}>
                  {filteredDocuments.map(document => renderDocument(document))}
                </View>
                
                {/* Summary */}
                <View style={{
                  backgroundColor: '#f0f9ff',
                  marginHorizontal: 16,
                  marginTop: 24,
                  marginBottom: 32,
                  borderRadius: 12,
                  padding: 16,
                  borderWidth: 1,
                  borderColor: '#bae6fd',
                }}>
                  <View style={{
                    flexDirection: 'row',
                    alignItems: 'center',
                    marginBottom: 12,
                  }}>
                    <Ionicons name="information-circle" size={20} color="#0284c7" />
                    <Text style={{
                      fontSize: 16,
                      fontWeight: '600',
                      color: '#0284c7',
                      marginLeft: 8,
                    }}>
                      Document Summary
                    </Text>
                  </View>
                  
                  <Text style={{
                    fontSize: 14,
                    color: '#0369a1',
                    lineHeight: 20,
                    marginBottom: 8,
                  }}>
                    Total Documents: {documents.length}
                  </Text>
                  <Text style={{
                    fontSize: 14,
                    color: '#0369a1',
                    lineHeight: 20,
                    marginBottom: 8,
                  }}>
                    Required Documents: {documents.filter(d => d.isRequired).length}
                  </Text>
                  <Text style={{
                    fontSize: 14,
                    color: '#0369a1',
                    lineHeight: 20,
                  }}>
                    Accepted Documents: {acceptances.length}
                  </Text>
                </View>
              </>
            )}
          </>
        )}
      </ScrollView>

      {/* Document Modal */}
      <Modal
        visible={showDocumentModal}
        animationType="slide"
        presentationStyle="pageSheet"
      >
        {selectedDocument && (
          <SafeAreaView style={{ flex: 1, backgroundColor: '#f9fafb' }}>
            {/* Modal Header */}
            <View style={{
              flexDirection: 'row',
              alignItems: 'center',
              paddingHorizontal: 16,
              paddingVertical: 12,
              backgroundColor: 'white',
              borderBottomWidth: 1,
              borderBottomColor: '#e5e7eb',
            }}>
              <TouchableOpacity
                onPress={() => setShowDocumentModal(false)}
                style={{
                  width: 40,
                  height: 40,
                  alignItems: 'center',
                  justifyContent: 'center',
                  marginRight: 8,
                }}
              >
                <Ionicons name="close" size={24} color="#374151" />
              </TouchableOpacity>
              
              <View style={{ flex: 1 }}>
                <Text style={{
                  fontSize: 18,
                  fontWeight: '600',
                  color: '#1f2937',
                }}>
                  {selectedDocument.title}
                </Text>
                <Text style={{
                  fontSize: 14,
                  color: '#6b7280',
                }}>
                  Version {selectedDocument.version}
                </Text>
              </View>
              
              {!isDocumentAccepted(selectedDocument.id, selectedDocument.version) && (
                <TouchableOpacity
                  onPress={() => {
                    acceptDocument(selectedDocument);
                    setShowDocumentModal(false);
                  }}
                  style={{
                    backgroundColor: '#10b981',
                    paddingHorizontal: 16,
                    paddingVertical: 8,
                    borderRadius: 8,
                  }}
                >
                  <Text style={{
                    color: 'white',
                    fontWeight: '600',
                    fontSize: 14,
                  }}>
                    Accept
                  </Text>
                </TouchableOpacity>
              )}
            </View>

            {/* Document Content */}
            <ScrollView style={{ flex: 1, padding: 16 }}>
              <View style={{
                backgroundColor: 'white',
                borderRadius: 12,
                padding: 20,
              }}>
                <Text style={{
                  fontSize: 14,
                  color: '#1f2937',
                  lineHeight: 22,
                  fontFamily: 'monospace',
                }}>
                  {selectedDocument.content}
                </Text>
              </View>
              
              {/* Document Info */}
              <View style={{
                backgroundColor: '#f3f4f6',
                borderRadius: 12,
                padding: 16,
                marginTop: 16,
                marginBottom: 32,
              }}>
                <Text style={{
                  fontSize: 14,
                  color: '#6b7280',
                  marginBottom: 8,
                }}>
                  Last Updated: {new Date(selectedDocument.lastUpdated).toLocaleDateString()}
                </Text>
                <Text style={{
                  fontSize: 14,
                  color: '#6b7280',
                  marginBottom: 8,
                }}>
                  Effective Date: {new Date(selectedDocument.effectiveDate).toLocaleDateString()}
                </Text>
                <Text style={{
                  fontSize: 14,
                  color: '#6b7280',
                }}>
                  Word Count: {selectedDocument.wordCount} words
                </Text>
              </View>
            </ScrollView>
          </SafeAreaView>
        )}
      </Modal>
    </SafeAreaView>
  );
};

export default TermsPoliciesScreen;
