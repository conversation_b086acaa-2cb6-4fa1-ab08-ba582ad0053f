import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  Alert,
  Modal,
  Animated,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { Card, Switch, Badge, Button, Screen, Header } from '../../components/ui';
import { useAuth } from '../../context/AuthContext';
import { useOrders } from '../../context/OrderContext';
import { useEarnings } from '../../context/EarningsContext';
import { useNavigation } from '@react-navigation/native';
import { formatCurrency, formatDistance, getInitials } from '../../utils/helpers';
import OrderRequestModal from '../../components/orders/OrderRequestModal';



interface DashboardStats {
  todayEarnings: number;
  todayOrders: number;
  todayDistance: number;
  weeklyEarnings: number;
  rating: number;
  completionRate: number;
  activeOrders: number;
  upcomingBonuses: {
    title: string;
    amount: number;
    progress: number;
    target: number;
  }[];
}

const DashboardScreen: React.FC = () => {
  const navigation = useNavigation();
  const { state: authState } = useAuth();
  const { state: orderState, fetchAvailableOrders } = useOrders();
  const { state: earningsState, fetchEarningsSummary } = useEarnings();
  const [isOnline, setIsOnline] = useState(false);
  const [refreshing, setRefreshing] = useState(false);
  const [showOrderRequest, setShowOrderRequest] = useState(false);

  // Animation refs
  const toggleAnimation = useRef(new Animated.Value(0)).current;
  const [stats, setStats] = useState<DashboardStats>({
    todayEarnings: 3825,
    todayOrders: 8,
    todayDistance: 45.2,
    weeklyEarnings: 26770,
    rating: 4.8,
    completionRate: 96,
    activeOrders: 2,
    upcomingBonuses: [
      {
        title: 'Complete 10 orders',
        amount: 750,
        progress: 8,
        target: 10,
      },
      {
        title: 'Weekend Warrior',
        amount: 1500,
        progress: 3,
        target: 15,
      },
    ],
  });

  useEffect(() => {
    // Fetch initial data
    fetchAvailableOrders();
    fetchEarningsSummary();

    // Initialize animations
    Animated.stagger(200, [
      Animated.timing(statsAnimation, {
        toValue: 1,
        duration: 800,
        useNativeDriver: true,
      }),
      Animated.timing(fabAnimation, {
        toValue: 1,
        duration: 600,
        useNativeDriver: true,
      }),
    ]).start();

    // Start pulse animation for FAB
    const startPulse = () => {
      Animated.sequence([
        Animated.timing(pulseAnimation, {
          toValue: 1.1,
          duration: 1000,
          useNativeDriver: true,
        }),
        Animated.timing(pulseAnimation, {
          toValue: 1,
          duration: 1000,
          useNativeDriver: true,
        }),
      ]).start(() => startPulse());
    };
    startPulse();

    // Simulate real-time order requests when online
    let orderInterval: NodeJS.Timeout;
    if (isOnline) {
      orderInterval = setInterval(() => {
        // Simulate incoming order request (30% chance every 10 seconds)
        if (Math.random() < 0.3 && orderState.availableOrders.length > 0) {
          setShowOrderRequest(true);
        }
      }, 10000);
    }

    return () => {
      if (orderInterval) {
        clearInterval(orderInterval);
      }
    };
  }, [isOnline]);

  const onRefresh = async () => {
    setRefreshing(true);
    try {
      await Promise.all([
        fetchAvailableOrders(),
        fetchEarningsSummary(),
      ]);

      // Update stats with real data
      setStats(prev => ({
        ...prev,
        activeOrders: orderState.acceptedOrders.length,
        todayEarnings: earningsState.summary?.todayEarnings || prev.todayEarnings,
      }));
    } catch (error) {
      console.error('Error refreshing dashboard:', error);
    } finally {
      setRefreshing(false);
    }
  };

  const toggleOnlineStatus = async () => {
    const { state, canGoOnline, checkVerificationStatus } = useAuth();

    // If trying to go online, check verification status first
    if (!isOnline) {
      // Check if user can go online
      if (!canGoOnline()) {
        // Refresh verification status
        await checkVerificationStatus();

        // Check again after refresh
        if (!canGoOnline()) {
          Alert.alert(
            '⚠️ Verification Required',
            'You need to complete document verification before going online. Please upload and verify all required documents.',
            [
              { text: 'Cancel', style: 'cancel' },
              {
                text: 'View Documents',
                onPress: () => navigation.navigate('DocumentStatus')
              }
            ]
          );
          return;
        }
      }
    }

    const newStatus = !isOnline;
    setIsOnline(newStatus);

    // Animate toggle
    Animated.timing(toggleAnimation, {
      toValue: newStatus ? 1 : 0,
      duration: 300,
      useNativeDriver: false,
    }).start();

    if (newStatus) {
      Alert.alert(
        '🟢 Going Online',
        'You are now available to receive order requests and start earning! 💰',
        [{ text: 'Let\'s Go! 🚀' }]
      );
      // Start fetching available orders
      fetchAvailableOrders();
    } else {
      Alert.alert(
        '🔴 Going Offline',
        'You will no longer receive new order requests. Take a well-deserved break! 😊',
        [{ text: 'OK' }]
      );
    }
  };



  const renderOnlineToggle = () => (
    <Card variant="elevated" style={{ marginBottom: 20 }}>
      <View style={{
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between',
        padding: 20,
      }}>
        <View style={{ flex: 1 }}>
          <View style={{ flexDirection: 'row', alignItems: 'center', marginBottom: 8 }}>
            <View style={{
              width: 12,
              height: 12,
              borderRadius: 6,
              backgroundColor: isOnline ? '#22c55e' : '#dc2626',
              marginRight: 12,
            }} />
            <Text style={{ fontSize: 20, fontWeight: 'bold', color: '#111827' }}>
              {isOnline ? 'Online' : 'Offline'}
            </Text>
          </View>
          <Text style={{ fontSize: 16, color: '#6b7280' }}>
            {isOnline
              ? 'You are receiving order requests'
              : 'Turn on to start receiving orders'
            }
          </Text>
        </View>

        <Switch
          value={isOnline}
          onValueChange={toggleOnlineStatus}
          size="lg"
        />
      </View>
    </Card>
  );

  const renderStatsGrid = () => (
    <View style={{ marginBottom: 20 }}>
      <View style={{
        flexDirection: 'row',
        flexWrap: 'wrap',
        justifyContent: 'space-between',
        gap: 12,
      }}>
        {/* Today's Earnings */}
        <Card variant="elevated" style={{ width: '48%' }}>
          <View style={{ padding: 16 }}>
            <View style={{
              flexDirection: 'row',
              alignItems: 'center',
              marginBottom: 12,
            }}>
              <View style={{
                width: 40,
                height: 40,
                backgroundColor: '#dc2626',
                borderRadius: 20,
                justifyContent: 'center',
                alignItems: 'center',
                marginRight: 12,
              }}>
                <Ionicons name="wallet" size={20} color="white" />
              </View>
              <Text style={{ fontSize: 14, color: '#6b7280', fontWeight: '500' }}>
                Today's Earnings
              </Text>
            </View>
            <Text style={{ fontSize: 24, fontWeight: 'bold', color: '#111827' }}>
              {formatCurrency(stats.todayEarnings)}
            </Text>
          </View>
        </Card>

        {/* Today's Orders */}
        <Card variant="elevated" style={{ width: '48%' }}>
          <View style={{ padding: 16 }}>
            <View style={{
              flexDirection: 'row',
              alignItems: 'center',
              marginBottom: 12,
            }}>
              <View style={{
                width: 40,
                height: 40,
                backgroundColor: '#dc2626',
                borderRadius: 20,
                justifyContent: 'center',
                alignItems: 'center',
                marginRight: 12,
              }}>
                <Ionicons name="receipt" size={20} color="white" />
              </View>
              <Text style={{ fontSize: 14, color: '#6b7280', fontWeight: '500' }}>
                Orders
              </Text>
            </View>
            <Text style={{ fontSize: 24, fontWeight: 'bold', color: '#111827' }}>
              {stats.todayOrders}
            </Text>
          </View>
        </Card>

        {/* Distance Traveled */}
        <Card variant="elevated" style={{ width: '48%' }}>
          <View style={{ padding: 16 }}>
            <View style={{
              flexDirection: 'row',
              alignItems: 'center',
              marginBottom: 12,
            }}>
              <View style={{
                width: 40,
                height: 40,
                backgroundColor: '#dc2626',
                borderRadius: 20,
                justifyContent: 'center',
                alignItems: 'center',
                marginRight: 12,
              }}>
                <Ionicons name="speedometer" size={20} color="white" />
              </View>
              <Text style={{ fontSize: 14, color: '#6b7280', fontWeight: '500' }}>
                Distance
              </Text>
            </View>
            <Text style={{ fontSize: 24, fontWeight: 'bold', color: '#111827' }}>
              {formatDistance(stats.todayDistance)}
            </Text>
          </View>
        </Card>

        {/* Rating */}
        <Card variant="elevated" style={{ width: '48%' }}>
          <View style={{ padding: 16 }}>
            <View style={{
              flexDirection: 'row',
              alignItems: 'center',
              marginBottom: 12,
            }}>
              <View style={{
                width: 40,
                height: 40,
                backgroundColor: '#dc2626',
                borderRadius: 20,
                justifyContent: 'center',
                alignItems: 'center',
                marginRight: 12,
              }}>
                <Ionicons name="star" size={20} color="white" />
              </View>
              <Text style={{ fontSize: 14, color: '#6b7280', fontWeight: '500' }}>
                Rating
              </Text>
            </View>
            <Text style={{ fontSize: 24, fontWeight: 'bold', color: '#111827' }}>
              {stats.rating.toFixed(1)}
            </Text>
          </View>
        </Card>
      </View>
    </View>
  );


  const renderQuickActions = () => (
    <Card variant="elevated" style={{ marginBottom: 20 }}>
      <View style={{ padding: 20 }}>
        <Text style={{ fontSize: 20, fontWeight: 'bold', color: '#111827', marginBottom: 16 }}>
          Quick Actions
        </Text>

        <View style={{ flexDirection: 'row', justifyContent: 'space-between', gap: 12 }}>
          <TouchableOpacity
            style={{
              flex: 1,
              backgroundColor: '#dc2626',
              padding: 16,
              borderRadius: 12,
              alignItems: 'center',
              shadowColor: '#dc2626',
              shadowOffset: { width: 0, height: 2 },
              shadowOpacity: 0.2,
              shadowRadius: 4,
              elevation: 3,
            }}
            onPress={() => (navigation as any).navigate('OrderRequests')}
          >
            <Ionicons name="notifications" size={24} color="white" style={{ marginBottom: 8 }} />
            <Text style={{ fontSize: 14, fontWeight: '600', color: 'white', textAlign: 'center' }}>
              Order Requests
            </Text>
            {orderState.availableOrders.length > 0 && (
              <View style={{
                position: 'absolute',
                top: 8,
                right: 8,
                backgroundColor: 'white',
                borderRadius: 10,
                minWidth: 20,
                height: 20,
                justifyContent: 'center',
                alignItems: 'center',
              }}>
                <Text style={{ fontSize: 12, color: '#dc2626', fontWeight: 'bold' }}>
                  {orderState.availableOrders.length}
                </Text>
              </View>
            )}
          </TouchableOpacity>

          <TouchableOpacity
            style={{
              flex: 1,
              backgroundColor: 'white',
              padding: 16,
              borderRadius: 12,
              alignItems: 'center',
              borderWidth: 2,
              borderColor: '#d1d5db',
            }}
            onPress={() => (navigation as any).navigate('Orders')}
          >
            <Ionicons name="receipt" size={24} color="#dc2626" style={{ marginBottom: 8 }} />
            <Text style={{ fontSize: 14, fontWeight: '600', color: '#111827', textAlign: 'center' }}>
              My Orders
            </Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={{
              flex: 1,
              backgroundColor: 'white',
              padding: 16,
              borderRadius: 12,
              alignItems: 'center',
              borderWidth: 2,
              borderColor: '#d1d5db',
            }}
            onPress={() => (navigation as any).navigate('Earnings')}
          >
            <Ionicons name="wallet" size={24} color="#dc2626" style={{ marginBottom: 8 }} />
            <Text style={{ fontSize: 14, fontWeight: '600', color: '#111827', textAlign: 'center' }}>
              Earnings
            </Text>
          </TouchableOpacity>
        </View>

        <View style={{ flexDirection: 'row', justifyContent: 'space-between', gap: 12, marginTop: 12 }}>
          <TouchableOpacity
            onPress={() => (navigation as any).navigate('Support')}
            style={{
              flex: 1,
              backgroundColor: 'white',
              padding: 16,
              borderRadius: 12,
              alignItems: 'center',
              borderWidth: 2,
              borderColor: '#d1d5db',
            }}
          >
            <Ionicons name="help-circle" size={24} color="#dc2626" style={{ marginBottom: 8 }} />
            <Text style={{ fontSize: 14, fontWeight: '600', color: '#111827', textAlign: 'center' }}>
              Support
            </Text>
          </TouchableOpacity>

          <TouchableOpacity
            onPress={() => (navigation as any).navigate('Profile')}
            style={{
              flex: 1,
              backgroundColor: 'white',
              borderRadius: 12,
              alignItems: 'center',
              borderWidth: 2,
              borderColor: '#d1d5db',
            }}
          >
            <Ionicons name="settings" size={24} color="#dc2626" style={{ marginBottom: 8 }} />
            <Text style={{ fontSize: 14, fontWeight: '600', color: '#111827', textAlign: 'center' }}>
              Settings
            </Text>
          </TouchableOpacity>
        </View>
      </View>
    </Card>
  );

  const renderActiveOrders = () => (
    <Card variant="elevated" style={{ marginBottom: 20 }}>
      <View style={{ padding: 20 }}>
        <View style={{
          flexDirection: 'row',
          justifyContent: 'space-between',
          alignItems: 'center',
          marginBottom: 16,
        }}>
          <Text style={{ fontSize: 20, fontWeight: 'bold', color: '#111827' }}>
            Active Orders
          </Text>
          <Badge
            text={`${stats.activeOrders} active`}
            style={{
              backgroundColor: stats.activeOrders > 0 ? '#dc2626' : '#6b7280',
            }}
          />
        </View>

      {stats.activeOrders > 0 ? (
        <View style={{
          backgroundColor: '#fef3c7',
          padding: 16,
          borderRadius: 12,
          borderLeftWidth: 4,
          borderLeftColor: '#f59e0b',
        }}>
          <Text style={{ fontSize: 16, fontWeight: '600', color: '#92400e', marginBottom: 4 }}>
            You have {stats.activeOrders} order{stats.activeOrders > 1 ? 's' : ''} in progress
          </Text>
          <Text style={{ fontSize: 14, color: '#b45309' }}>
            Tap to view details and continue delivery
          </Text>
        </View>
      ) : (
        <View style={{
          backgroundColor: '#f3f4f6',
          padding: 16,
          borderRadius: 12,
          alignItems: 'center',
        }}>
          <Ionicons name="checkmark-circle-outline" size={32} color="#6b7280" />
          <Text style={{ fontSize: 16, color: '#6b7280', marginTop: 8 }}>
            No active orders
          </Text>
          <Text style={{ fontSize: 14, color: '#9ca3af', textAlign: 'center' }}>
            {isOnline ? 'Waiting for new requests...' : 'Go online to receive orders'}
          </Text>
        </View>
      )}
      </View>
    </Card>
  );

  const renderUpcomingBonuses = () => (
    <Card variant="elevated" style={{ marginBottom: 20 }}>
      <View style={{ padding: 20 }}>
      <Text style={{ fontSize: 20, fontWeight: 'bold', color: '#111827', marginBottom: 16 }}>
        Upcoming Bonuses
      </Text>

      {stats.upcomingBonuses.map((bonus, index) => (
        <View key={index} style={{
          backgroundColor: '#f0f9ff',
          padding: 16,
          borderRadius: 12,
          marginBottom: index < stats.upcomingBonuses.length - 1 ? 12 : 0,
          borderLeftWidth: 4,
          borderLeftColor: '#0ea5e9',
        }}>
          <View style={{
            flexDirection: 'row',
            justifyContent: 'space-between',
            alignItems: 'center',
            marginBottom: 8,
          }}>
            <Text style={{ fontSize: 16, fontWeight: '600', color: '#0c4a6e' }}>
              {bonus.title}
            </Text>
            <Text style={{ fontSize: 16, fontWeight: 'bold', color: '#059669' }}>
              +{formatCurrency(bonus.amount)}
            </Text>
          </View>

          <View style={{
            flexDirection: 'row',
            alignItems: 'center',
            marginBottom: 8,
          }}>
            <View style={{
              flex: 1,
              height: 6,
              backgroundColor: '#e0f2fe',
              borderRadius: 3,
              marginRight: 12,
            }}>
              <View style={{
                width: `${(bonus.progress / bonus.target) * 100}%`,
                height: '100%',
                backgroundColor: '#0ea5e9',
                borderRadius: 3,
              }} />
            </View>
            <Text style={{ fontSize: 12, color: '#0c4a6e', fontWeight: '500' }}>
              {bonus.progress}/{bonus.target}
            </Text>
          </View>

          <Text style={{ fontSize: 12, color: '#0369a1' }}>
            {bonus.target - bonus.progress} more to unlock bonus
          </Text>
        </View>
      ))}
      </View>
    </Card>
  );

  const renderWeeklyOverview = () => (
    <Card variant="elevated" style={{ marginBottom: 20 }}>
      <View style={{ padding: 20 }}>
      <Text style={{ fontSize: 20, fontWeight: 'bold', color: '#111827', marginBottom: 16 }}>
        This Week
      </Text>

      <View style={{
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
      }}>
        <View>
          <Text style={{ fontSize: 14, color: '#6b7280', marginBottom: 4 }}>
            Total Earnings
          </Text>
          <Text style={{ fontSize: 24, fontWeight: 'bold', color: '#111827' }}>
            {formatCurrency(stats.weeklyEarnings)}
          </Text>
        </View>

        <View style={{ alignItems: 'flex-end' }}>
          <Badge text={`${stats.completionRate}% completion`} variant="success" />
          <Text style={{ fontSize: 12, color: '#6b7280', marginTop: 4 }}>
            Great performance!
          </Text>
        </View>
      </View>
      </View>
    </Card>
  );



  return (
    <Screen scrollable backgroundColor="#f8fafc" padding="none">
      <Header
        title="Dashboard"
        subtitle={`Welcome back, ${authState.user?.firstName || 'Rider'}!`}
        rightIcon="notifications"
        onRightPress={() => navigation.navigate('Notifications' as never)}
      />

      <View style={{ padding: 20 }}>
        {renderOnlineToggle()}
        {renderStatsGrid()}
        {renderActiveOrders()}
        {renderUpcomingBonuses()}
        {renderQuickActions()}
        {renderWeeklyOverview()}

        {/* Bottom spacing */}
        <View style={{ height: 20 }} />
      </View>

      {/* Order Request Modal */}
      <Modal
        visible={showOrderRequest}
        animationType="slide"
        presentationStyle="pageSheet"
      >
        <OrderRequestModal
          visible={showOrderRequest}
          onClose={() => setShowOrderRequest(false)}
          orders={orderState.availableOrders}
        />
      </Modal>
    </Screen>
  );
};

export default DashboardScreen;
