import React, { useState, useRef, useEffect } from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  Alert,
  Keyboard,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import { But<PERSON>, LoadingSpinner } from '../../components/ui';

interface OTPVerificationScreenProps {
  phoneNumber: string;
  onVerificationSuccess: (code: string) => void;
  onResendCode: () => void;
  onBack: () => void;
  isLoading?: boolean;
}

const OTPVerificationScreen: React.FC<OTPVerificationScreenProps> = ({
  phoneNumber,
  onVerificationSuccess,
  onResendCode,
  onBack,
  isLoading = false,
}) => {
  const [otp, setOtp] = useState(['', '', '', '', '', '']);
  const [timer, setTimer] = useState(60);
  const [canResend, setCanResend] = useState(false);
  const inputRefs = useRef<any[]>([]);

  useEffect(() => {
    if (timer > 0) {
      const interval = setInterval(() => {
        setTimer((prev) => prev - 1);
      }, 1000);
      return () => clearInterval(interval);
    } else {
      setCanResend(true);
    }
  }, [timer]);

  const handleOtpChange = (value: string, index: number) => {
    if (value.length > 1) return; // Prevent multiple characters

    const newOtp = [...otp];
    newOtp[index] = value;
    setOtp(newOtp);

    // Auto-focus next input
    if (value && index < 5) {
      inputRefs.current[index + 1]?.focus();
    }

    // Auto-submit when all fields are filled
    if (newOtp.every(digit => digit !== '') && newOtp.join('').length === 6) {
      Keyboard.dismiss();
      handleVerify(newOtp.join(''));
    }
  };

  const handleKeyPress = (key: string, index: number) => {
    if (key === 'Backspace' && !otp[index] && index > 0) {
      inputRefs.current[index - 1]?.focus();
    }
  };

  const handleVerify = (code?: string) => {
    const otpCode = code || otp.join('');
    if (otpCode.length !== 6) {
      Alert.alert('Invalid Code', 'Please enter a 6-digit verification code.');
      return;
    }
    onVerificationSuccess(otpCode);
  };

  const handleResend = () => {
    if (!canResend) return;
    
    setTimer(60);
    setCanResend(false);
    setOtp(['', '', '', '', '', '']);
    inputRefs.current[0]?.focus();
    onResendCode();
  };

  const formatPhoneNumber = (phone: string) => {
    // Format phone number for display
    if (phone.startsWith('+92')) {
      return phone.replace('+92', '+92 ').replace(/(\d{3})(\d{7})/, '$1 $2');
    }
    return phone;
  };

  return (
    <SafeAreaView className="flex-1 bg-white">
      {/* Header */}
      <View className="flex-row items-center p-4 border-b border-gray-100">
        <TouchableOpacity onPress={onBack} className="mr-4">
          <Ionicons name="arrow-back" size={24} color="#374151" />
        </TouchableOpacity>
        <Text className="text-lg font-semibold text-gray-800">Verify Phone Number</Text>
      </View>

      <View className="flex-1 px-6 py-8">
        {/* Icon */}
        <View className="items-center mb-8">
          <View className="w-20 h-20 bg-red-100 rounded-full items-center justify-center mb-4">
            <Ionicons name="phone-portrait" size={40} color="#EF4444" />
          </View>
          <Text className="text-2xl font-bold text-gray-800 mb-2">Enter Verification Code</Text>
          <Text className="text-gray-600 text-center leading-6">
            We've sent a 6-digit code to{'\n'}
            <Text className="font-semibold text-gray-800">{formatPhoneNumber(phoneNumber)}</Text>
          </Text>
        </View>

        {/* OTP Input */}
        <View className="flex-row justify-center mb-8">
          {otp.map((digit, index) => (
            <TextInput
              key={index}
              ref={(ref) => (inputRefs.current[index] = ref)}
              value={digit}
              onChangeText={(value) => handleOtpChange(value, index)}
              onKeyPress={({ nativeEvent }) => handleKeyPress(nativeEvent.key, index)}
              keyboardType="numeric"
              maxLength={1}
              selectTextOnFocus
              className={`w-12 h-14 mx-2 text-center text-xl font-bold border-2 rounded-lg ${
                digit ? 'border-red-500 bg-red-50' : 'border-gray-300 bg-white'
              }`}
              style={{ textAlignVertical: 'center' }}
            />
          ))}
        </View>

        {/* Timer and Resend */}
        <View className="items-center mb-8">
          {!canResend ? (
            <Text className="text-gray-600">
              Resend code in <Text className="font-semibold text-red-500">{timer}s</Text>
            </Text>
          ) : (
            <TouchableOpacity onPress={handleResend} className="flex-row items-center">
              <Ionicons name="refresh" size={16} color="#EF4444" />
              <Text className="text-red-500 font-semibold ml-1">Resend Code</Text>
            </TouchableOpacity>
          )}
        </View>

        {/* Verify Button */}
        <Button
          title="Verify & Continue"
          onPress={() => handleVerify()}
          disabled={otp.join('').length !== 6 || isLoading}
          loading={isLoading}
          className="mb-4"
        />

        {/* Help Text */}
        <View className="bg-blue-50 border border-blue-200 rounded-lg p-4">
          <View className="flex-row items-start">
            <Ionicons name="information-circle" size={20} color="#3B82F6" />
            <View className="flex-1 ml-3">
              <Text className="text-blue-800 font-semibold mb-1">Didn't receive the code?</Text>
              <Text className="text-blue-700 text-sm leading-5">
                • Check your SMS messages{'\n'}
                • Ensure you have network coverage{'\n'}
                • Try resending the code{'\n'}
                • Contact support if issues persist
              </Text>
            </View>
          </View>
        </View>
      </View>
    </SafeAreaView>
  );
};

export default OTPVerificationScreen;
