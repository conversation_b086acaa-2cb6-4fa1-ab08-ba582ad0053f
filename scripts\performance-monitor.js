#!/usr/bin/env node

/**
 * Performance monitoring script for React Native Food Delivery Rider App
 * This script helps monitor and analyze app performance metrics
 */

const fs = require('fs');
const path = require('path');

// Performance thresholds
const PERFORMANCE_THRESHOLDS = {
  APP_STARTUP_TIME: 3000, // 3 seconds
  SCREEN_TRANSITION_TIME: 300, // 300ms
  MEMORY_USAGE_LIMIT: 150 * 1024 * 1024, // 150MB
  NETWORK_REQUEST_TIME: 2000, // 2 seconds
  IMAGE_LOAD_TIME: 1000, // 1 second
  BATTERY_DRAIN_RATE: 3, // 3% per hour
};

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m',
};

function colorize(text, color) {
  return `${colors[color]}${text}${colors.reset}`;
}

function logHeader(text) {
  console.log('\n' + colorize('='.repeat(60), 'cyan'));
  console.log(colorize(`  ${text}`, 'bright'));
  console.log(colorize('='.repeat(60), 'cyan') + '\n');
}

function logSuccess(text) {
  console.log(colorize(`✓ ${text}`, 'green'));
}

function logWarning(text) {
  console.log(colorize(`⚠ ${text}`, 'yellow'));
}

function logError(text) {
  console.log(colorize(`✗ ${text}`, 'red'));
}

function logInfo(text) {
  console.log(colorize(`ℹ ${text}`, 'blue'));
}

// Check if performance config exists
function checkPerformanceConfig() {
  const configPath = path.join(process.cwd(), 'src/config/performanceConfig.ts');
  
  if (fs.existsSync(configPath)) {
    logSuccess('Performance configuration found');
    return true;
  } else {
    logError('Performance configuration not found');
    return false;
  }
}

// Check if optimized components exist
function checkOptimizedComponents() {
  const components = [
    'src/components/ui/OptimizedImage.tsx',
    'src/components/ui/OptimizedList.tsx',
    'src/components/ui/OptimizedScrollView.tsx',
  ];

  let allExist = true;
  
  components.forEach(component => {
    const componentPath = path.join(process.cwd(), component);
    if (fs.existsSync(componentPath)) {
      logSuccess(`${component} exists`);
    } else {
      logError(`${component} missing`);
      allExist = false;
    }
  });

  return allExist;
}

// Check if optimization services exist
function checkOptimizationServices() {
  const services = [
    'src/services/performanceMonitoring.ts',
    'src/services/networkOptimization.ts',
    'src/services/optimizedStorage.ts',
    'src/services/optimizedAnimations.ts',
    'src/services/optimizationInitializer.ts',
  ];

  let allExist = true;
  
  services.forEach(service => {
    const servicePath = path.join(process.cwd(), service);
    if (fs.existsSync(servicePath)) {
      logSuccess(`${service} exists`);
    } else {
      logError(`${service} missing`);
      allExist = false;
    }
  });

  return allExist;
}

// Check build configuration
function checkBuildConfig() {
  const appJsonPath = path.join(process.cwd(), 'app.json');
  const babelConfigPath = path.join(process.cwd(), 'babel.config.js');
  const proguardPath = path.join(process.cwd(), 'android/proguard-rules.pro');

  let configValid = true;

  // Check app.json
  if (fs.existsSync(appJsonPath)) {
    try {
      const appJson = JSON.parse(fs.readFileSync(appJsonPath, 'utf8'));
      const androidConfig = appJson.expo?.android;
      
      if (androidConfig?.enableProguardInReleaseBuilds) {
        logSuccess('ProGuard enabled in app.json');
      } else {
        logWarning('ProGuard not enabled in app.json');
        configValid = false;
      }

      if (androidConfig?.enableHermes) {
        logSuccess('Hermes enabled in app.json');
      } else {
        logWarning('Hermes not enabled in app.json');
      }
    } catch (error) {
      logError('Error reading app.json');
      configValid = false;
    }
  } else {
    logError('app.json not found');
    configValid = false;
  }

  // Check babel.config.js
  if (fs.existsSync(babelConfigPath)) {
    logSuccess('babel.config.js exists');
  } else {
    logError('babel.config.js not found');
    configValid = false;
  }

  // Check ProGuard rules
  if (fs.existsSync(proguardPath)) {
    logSuccess('ProGuard rules file exists');
  } else {
    logWarning('ProGuard rules file not found');
  }

  return configValid;
}

// Generate performance report
function generatePerformanceReport() {
  const report = {
    timestamp: new Date().toISOString(),
    checks: {
      performanceConfig: checkPerformanceConfig(),
      optimizedComponents: checkOptimizedComponents(),
      optimizationServices: checkOptimizationServices(),
      buildConfig: checkBuildConfig(),
    },
    recommendations: [],
    thresholds: PERFORMANCE_THRESHOLDS,
  };

  // Generate recommendations
  if (!report.checks.performanceConfig) {
    report.recommendations.push('Create performance configuration file');
  }

  if (!report.checks.optimizedComponents) {
    report.recommendations.push('Implement missing optimized components');
  }

  if (!report.checks.optimizationServices) {
    report.recommendations.push('Implement missing optimization services');
  }

  if (!report.checks.buildConfig) {
    report.recommendations.push('Configure build optimizations (ProGuard, Hermes)');
  }

  // Save report
  const reportPath = path.join(process.cwd(), 'performance-report.json');
  fs.writeFileSync(reportPath, JSON.stringify(report, null, 2));
  
  return report;
}

// Display performance recommendations
function displayRecommendations() {
  logHeader('PERFORMANCE RECOMMENDATIONS');

  const recommendations = [
    'Use OptimizedImage component for all images',
    'Use OptimizedList component for large datasets',
    'Implement lazy loading for heavy components',
    'Enable ProGuard for production builds',
    'Use Hermes engine for better performance',
    'Implement proper memory cleanup in useEffect',
    'Use performance monitoring in development',
    'Optimize network requests with caching',
    'Use efficient location tracking',
    'Implement proper animation cleanup',
  ];

  recommendations.forEach(rec => {
    logInfo(rec);
  });
}

// Display performance budgets
function displayPerformanceBudgets() {
  logHeader('PERFORMANCE BUDGETS');

  Object.entries(PERFORMANCE_THRESHOLDS).forEach(([metric, threshold]) => {
    const formattedMetric = metric.replace(/_/g, ' ').toLowerCase();
    const formattedThreshold = typeof threshold === 'number' && threshold > 1000 
      ? `${(threshold / 1000).toFixed(1)}s`
      : `${threshold}ms`;
    
    logInfo(`${formattedMetric}: ${formattedThreshold}`);
  });
}

// Main function
function main() {
  const args = process.argv.slice(2);
  const command = args[0] || 'check';

  switch (command) {
    case 'check':
      logHeader('PERFORMANCE OPTIMIZATION CHECK');
      const report = generatePerformanceReport();
      
      const allPassed = Object.values(report.checks).every(check => check);
      
      if (allPassed) {
        logSuccess('All performance optimizations are in place!');
      } else {
        logWarning('Some optimizations are missing. Check the report above.');
      }

      logInfo(`Report saved to: performance-report.json`);
      break;

    case 'recommendations':
      displayRecommendations();
      break;

    case 'budgets':
      displayPerformanceBudgets();
      break;

    case 'help':
    default:
      logHeader('PERFORMANCE MONITOR HELP');
      console.log('Usage: node scripts/performance-monitor.js [command]');
      console.log('\nCommands:');
      console.log('  check          Check performance optimizations (default)');
      console.log('  recommendations Show performance recommendations');
      console.log('  budgets        Show performance budgets');
      console.log('  help           Show this help message');
      break;
  }
}

// Run the script
if (require.main === module) {
  main();
}

module.exports = {
  checkPerformanceConfig,
  checkOptimizedComponents,
  checkOptimizationServices,
  checkBuildConfig,
  generatePerformanceReport,
  PERFORMANCE_THRESHOLDS,
};
