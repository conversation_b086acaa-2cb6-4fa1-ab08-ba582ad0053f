export interface RiderProfile {
  id: string;
  userId: string;
  firstName: string;
  lastName: string;
  email: string;
  phoneNumber: string;
  profileImage?: string;
  dateOfBirth?: string;
  emergencyContact?: EmergencyContact;
  isOnline: boolean;
  isAvailable: boolean;
  rating: number;
  totalDeliveries: number;
  joinedAt: string;
  lastActiveAt: string;
  status: RiderStatus;
}

export interface EmergencyContact {
  name: string;
  phoneNumber: string;
  relationship: string;
}

export enum RiderStatus {
  ACTIVE = 'active',
  INACTIVE = 'inactive',
  SUSPENDED = 'suspended',
  PENDING_VERIFICATION = 'pending_verification',
}

export enum VehicleType {
  BICYCLE = 'bicycle',
  MOTORCYCLE = 'motorcycle',
  CAR = 'car',
  SCOOTER = 'scooter',
}

export interface Vehicle {
  id: string;
  riderId: string;
  type: VehicleType;
  make: string;
  model: string;
  year: number;
  color: string;
  licensePlate: string;
  insuranceNumber?: string;
  registrationNumber?: string;
  isVerified: boolean;
  documents: VehicleDocument[];
}

export interface VehicleDocument {
  id: string;
  type: DocumentType;
  url: string;
  expiryDate?: string;
  isVerified: boolean;
  uploadedAt: string;
}

export enum DocumentType {
  DRIVERS_LICENSE = 'drivers_license',
  VEHICLE_REGISTRATION = 'vehicle_registration',
  INSURANCE_CERTIFICATE = 'insurance_certificate',
  VEHICLE_INSPECTION = 'vehicle_inspection',
}

export interface BankAccount {
  id: string;
  riderId: string;
  accountHolderName: string;
  bankName: string;
  accountNumber: string;
  routingNumber: string;
  accountType: AccountType;
  isVerified: boolean;
  isPrimary: boolean;
}

export enum AccountType {
  CHECKING = 'checking',
  SAVINGS = 'savings',
}

export interface EarningsData {
  today: DailyEarnings;
  week: WeeklyEarnings;
  month: MonthlyEarnings;
  total: TotalEarnings;
}

export interface DailyEarnings {
  date: string;
  totalEarnings: number;
  deliveryFees: number;
  tips: number;
  bonuses: number;
  ordersCompleted: number;
  hoursWorked: number;
  distanceTraveled: number;
}

export interface WeeklyEarnings {
  weekStart: string;
  weekEnd: string;
  totalEarnings: number;
  dailyBreakdown: DailyEarnings[];
  averagePerDay: number;
  peakDayEarnings: number;
}

export interface MonthlyEarnings {
  month: string;
  year: number;
  totalEarnings: number;
  weeklyBreakdown: WeeklyEarnings[];
  averagePerWeek: number;
  peakWeekEarnings: number;
}

export interface TotalEarnings {
  allTimeEarnings: number;
  totalDeliveries: number;
  averagePerDelivery: number;
  totalHoursWorked: number;
  averagePerHour: number;
}

export interface PaymentHistory {
  id: string;
  riderId: string;
  amount: number;
  type: PaymentType;
  status: PaymentStatus;
  description: string;
  transactionDate: string;
  processedDate?: string;
  referenceNumber: string;
}

export enum PaymentType {
  DELIVERY_FEE = 'delivery_fee',
  TIP = 'tip',
  BONUS = 'bonus',
  ADJUSTMENT = 'adjustment',
  PAYOUT = 'payout',
}

export enum PaymentStatus {
  PENDING = 'pending',
  COMPLETED = 'completed',
  FAILED = 'failed',
  CANCELLED = 'cancelled',
}

export interface AppSettings {
  notifications: NotificationSettings;
  location: LocationSettings;
  privacy: PrivacySettings;
  language: string;
  theme: 'light' | 'dark' | 'system';
}

export interface NotificationSettings {
  orderAlerts: boolean;
  promotions: boolean;
  earnings: boolean;
  systemUpdates: boolean;
  sound: boolean;
  vibration: boolean;
}

export interface LocationSettings {
  shareLocation: boolean;
  backgroundTracking: boolean;
  accuracyLevel: 'high' | 'medium' | 'low';
}

export interface PrivacySettings {
  shareDataForAnalytics: boolean;
  shareLocationWithCustomers: boolean;
  allowContactFromCustomers: boolean;
}
