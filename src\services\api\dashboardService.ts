import { apiClient, ApiResponse, retryRequest } from './apiConfig';

// Dashboard types
export interface DashboardStats {
  todayEarnings: number;
  todayOrders: number;
  averageRating: number;
  completionRate: number;
  onlineTime: number;
  totalDistance: number;
  upcomingBonuses: {
    type: string;
    amount: number;
    target: number;
    current: number;
    deadline: string;
  }[];
}

export interface RiderStatus {
  isOnline: boolean;
  currentLocation?: {
    latitude: number;
    longitude: number;
  };
  lastActiveAt: string;
  shiftStartTime?: string;
  totalOnlineTime: number;
}

export interface AvailableOrder {
  id: string;
  restaurantName: string;
  customerAddress: string;
  distance: number;
  estimatedEarnings: number;
  estimatedTime: number;
  priority: 'normal' | 'high' | 'urgent';
  expiresAt: string;
}

export interface Notification {
  id: string;
  type: 'order_request' | 'payment_received' | 'bonus_earned' | 'system_update' | 'promotion';
  title: string;
  message: string;
  data?: any;
  isRead: boolean;
  createdAt: string;
}

export interface HeatmapData {
  zones: {
    id: string;
    coordinates: { latitude: number; longitude: number }[];
    demandLevel: 'low' | 'medium' | 'high' | 'very_high';
    surgeMultiplier: number;
    estimatedEarnings: number;
    activeOrders: number;
  }[];
  lastUpdated: string;
}

// Dashboard Service
export class DashboardService {
  // Get dashboard statistics
  static async getDashboardStats(): Promise<ApiResponse<DashboardStats>> {
    return retryRequest(async () => {
      const response = await apiClient.get<ApiResponse<DashboardStats>>('/dashboard/stats');
      return response.data;
    });
  }

  // Get today's earnings
  static async getTodayEarnings(): Promise<ApiResponse<{
    total: number;
    breakdown: {
      orders: number;
      tips: number;
      bonuses: number;
      surge: number;
    };
    hourlyBreakdown: {
      hour: number;
      earnings: number;
    }[];
  }>> {
    return retryRequest(async () => {
      const response = await apiClient.get<ApiResponse<any>>('/dashboard/earnings/today');
      return response.data;
    });
  }

  // Update rider online/offline status
  static async updateRiderStatus(
    isOnline: boolean,
    location?: { latitude: number; longitude: number }
  ): Promise<ApiResponse<{ message: string; status: RiderStatus }>> {
    return retryRequest(async () => {
      const response = await apiClient.post<ApiResponse<{ message: string; status: RiderStatus }>>(
        '/rider/status',
        { isOnline, location }
      );
      return response.data;
    });
  }

  // Get rider status
  static async getRiderStatus(): Promise<ApiResponse<RiderStatus>> {
    return retryRequest(async () => {
      const response = await apiClient.get<ApiResponse<RiderStatus>>('/rider/status');
      return response.data;
    });
  }

  // Get available orders
  static async getAvailableOrders(): Promise<ApiResponse<AvailableOrder[]>> {
    return retryRequest(async () => {
      const response = await apiClient.get<ApiResponse<AvailableOrder[]>>('/orders/available');
      return response.data;
    });
  }

  // Get unread notifications
  static async getUnreadNotifications(): Promise<ApiResponse<Notification[]>> {
    return retryRequest(async () => {
      const response = await apiClient.get<ApiResponse<Notification[]>>('/notifications/unread');
      return response.data;
    });
  }

  // Mark notification as read
  static async markNotificationRead(notificationId: string): Promise<ApiResponse<{ message: string }>> {
    return retryRequest(async () => {
      const response = await apiClient.put<ApiResponse<{ message: string }>>(
        `/notifications/${notificationId}/read`
      );
      return response.data;
    });
  }

  // Get demand heatmap
  static async getHeatmapData(): Promise<ApiResponse<HeatmapData>> {
    return retryRequest(async () => {
      const response = await apiClient.get<ApiResponse<HeatmapData>>('/heatmap/demand');
      return response.data;
    });
  }

  // Update rider location
  static async updateLocation(location: {
    latitude: number;
    longitude: number;
    accuracy?: number;
    heading?: number;
    speed?: number;
  }): Promise<ApiResponse<{ message: string }>> {
    return retryRequest(async () => {
      const response = await apiClient.post<ApiResponse<{ message: string }>>(
        '/rider/location',
        location
      );
      return response.data;
    });
  }

  // Get shift summary
  static async getShiftSummary(): Promise<ApiResponse<{
    startTime: string;
    endTime?: string;
    totalOnlineTime: number;
    totalEarnings: number;
    totalOrders: number;
    totalDistance: number;
    averageOrderTime: number;
    fuelCost?: number;
    netEarnings: number;
  }>> {
    return retryRequest(async () => {
      const response = await apiClient.get<ApiResponse<any>>('/dashboard/shift-summary');
      return response.data;
    });
  }

  // Start shift
  static async startShift(location: {
    latitude: number;
    longitude: number;
  }): Promise<ApiResponse<{ message: string; shiftId: string }>> {
    return retryRequest(async () => {
      const response = await apiClient.post<ApiResponse<{ message: string; shiftId: string }>>(
        '/rider/shift/start',
        { location }
      );
      return response.data;
    });
  }

  // End shift
  static async endShift(location: {
    latitude: number;
    longitude: number;
  }): Promise<ApiResponse<{ message: string; summary: any }>> {
    return retryRequest(async () => {
      const response = await apiClient.post<ApiResponse<{ message: string; summary: any }>>(
        '/rider/shift/end',
        { location }
      );
      return response.data;
    });
  }

  // Get weekly goals
  static async getWeeklyGoals(): Promise<ApiResponse<{
    earnings: {
      target: number;
      current: number;
      percentage: number;
    };
    orders: {
      target: number;
      current: number;
      percentage: number;
    };
    rating: {
      target: number;
      current: number;
    };
    onlineTime: {
      target: number;
      current: number;
      percentage: number;
    };
  }>> {
    return retryRequest(async () => {
      const response = await apiClient.get<ApiResponse<any>>('/dashboard/weekly-goals');
      return response.data;
    });
  }

  // Update weekly goals
  static async updateWeeklyGoals(goals: {
    earningsTarget?: number;
    ordersTarget?: number;
    ratingTarget?: number;
    onlineTimeTarget?: number;
  }): Promise<ApiResponse<{ message: string }>> {
    return retryRequest(async () => {
      const response = await apiClient.put<ApiResponse<{ message: string }>>(
        '/dashboard/weekly-goals',
        goals
      );
      return response.data;
    });
  }

  // Get performance insights
  static async getPerformanceInsights(): Promise<ApiResponse<{
    insights: {
      type: 'tip' | 'warning' | 'achievement';
      title: string;
      description: string;
      actionable?: boolean;
      action?: string;
    }[];
    recommendations: {
      title: string;
      description: string;
      potentialEarningsIncrease: number;
    }[];
  }>> {
    return retryRequest(async () => {
      const response = await apiClient.get<ApiResponse<any>>('/dashboard/insights');
      return response.data;
    });
  }

  // Get nearby restaurants
  static async getNearbyRestaurants(location: {
    latitude: number;
    longitude: number;
    radius?: number;
  }): Promise<ApiResponse<{
    restaurants: {
      id: string;
      name: string;
      address: string;
      distance: number;
      averageWaitTime: number;
      activeOrders: number;
      rating: number;
    }[];
  }>> {
    return retryRequest(async () => {
      const response = await apiClient.get<ApiResponse<any>>('/dashboard/nearby-restaurants', {
        params: location
      });
      return response.data;
    });
  }
}

export default DashboardService;
