import React from 'react';
import { View, TouchableOpacity, Text, Dimensions } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { BottomTabBarProps } from '@react-navigation/bottom-tabs';

const { width: screenWidth } = Dimensions.get('window');

interface TabConfig {
  name: string;
  label: string;
  activeIcon: keyof typeof Ionicons.glyphMap;
  inactiveIcon: keyof typeof Ionicons.glyphMap;
}

const tabConfigs: TabConfig[] = [
  {
    name: 'Dashboard',
    label: 'Home',
    activeIcon: 'home',
    inactiveIcon: 'home-outline',
  },
  {
    name: 'Orders',
    label: 'Orders',
    activeIcon: 'receipt',
    inactiveIcon: 'receipt-outline',
  },
  {
    name: 'Earnings',
    label: 'Wallet',
    activeIcon: 'wallet',
    inactiveIcon: 'wallet-outline',
  },
  {
    name: 'Profile',
    label: 'Profile',
    activeIcon: 'person',
    inactiveIcon: 'person-outline',
  },
  {
    name: 'Safety',
    label: 'Safety',
    activeIcon: 'shield-checkmark',
    inactiveIcon: 'shield-outline',
  },
];

const CustomTabBar: React.FC<BottomTabBarProps> = ({ state, descriptors, navigation }) => {
  const visibleRoutes = state.routes.filter(route => {
    const { options } = descriptors[route.key];
    return options.tabBarButton !== null;
  });

  const tabWidth = screenWidth / visibleRoutes.length;

  return (
    <View style={{
      flexDirection: 'row',
      backgroundColor: '#ffffff',
      borderTopWidth: 1,
      borderTopColor: '#e5e7eb',
      paddingBottom: 10,
      paddingTop: 10,
      height: 75,
      shadowColor: '#000',
      shadowOffset: { width: 0, height: -2 },
      shadowOpacity: 0.1,
      shadowRadius: 8,
      elevation: 8,
      justifyContent: 'space-around',
      alignItems: 'center',
    }}>
      {visibleRoutes.map((route, index) => {
        const { options } = descriptors[route.key];
        const isFocused = state.index === state.routes.findIndex(r => r.key === route.key);
        
        const tabConfig = tabConfigs.find(config => config.name === route.name);
        if (!tabConfig) return null;

        const onPress = () => {
          const event = navigation.emit({
            type: 'tabPress',
            target: route.key,
            canPreventDefault: true,
          });

          if (!isFocused && !event.defaultPrevented) {
            navigation.navigate(route.name, route.params);
          }
        };

        const onLongPress = () => {
          navigation.emit({
            type: 'tabLongPress',
            target: route.key,
          });
        };

        return (
          <TouchableOpacity
            key={route.key}
            accessibilityRole="button"
            accessibilityState={isFocused ? { selected: true } : {}}
            accessibilityLabel={options.tabBarAccessibilityLabel}
            testID={options.tabBarTestID}
            onPress={onPress}
            onLongPress={onLongPress}
            style={{
              flex: 1,
              alignItems: 'center',
              justifyContent: 'center',
              paddingVertical: 8,
              paddingHorizontal: 8,
              minWidth: tabWidth * 0.9,
              maxWidth: tabWidth * 1.1,
            }}
          >
            {/* Icon Container with Background */}
            <View style={{
              alignItems: 'center',
              justifyContent: 'center',
              width: 36,
              height: 36,
              borderRadius: 18,
              backgroundColor: isFocused ? '#ef4444' + '20' : 'transparent',
              marginBottom: 6,
              transform: [{ scale: isFocused ? 1.05 : 1 }],
            }}>
              <Ionicons
                name={isFocused ? tabConfig.activeIcon : tabConfig.inactiveIcon}
                size={isFocused ? 24 : 22}
                color={isFocused ? '#ef4444' : '#9ca3af'}
              />
            </View>

            {/* Label */}
            <Text style={{
              fontSize: 11,
              fontWeight: isFocused ? '700' : '600',
              color: isFocused ? '#ef4444' : '#9ca3af',
              textAlign: 'center',
              marginTop: 2,
              letterSpacing: 0.2,
            }}>
              {tabConfig.label}
            </Text>

            {/* Active Indicator Dot */}
            {isFocused && (
              <View style={{
                width: 5,
                height: 5,
                borderRadius: 2.5,
                backgroundColor: '#ef4444',
                marginTop: 3,
                opacity: 0.8,
              }} />
            )}
          </TouchableOpacity>
        );
      })}
    </View>
  );
};

export default CustomTabBar;
