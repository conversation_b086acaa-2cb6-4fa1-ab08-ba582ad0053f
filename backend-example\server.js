// Example Express.js Backend Server for FoodWay Rider App
// This is a basic implementation to help you get started

const express = require('express');
const cors = require('cors');
const multer = require('multer');
const jwt = require('jsonwebtoken');
const bcrypt = require('bcryptjs');
const path = require('path');

const app = express();
const PORT = process.env.PORT || 3000;
const JWT_SECRET = process.env.JWT_SECRET || 'your-secret-key';

// Middleware
app.use(cors());
app.use(express.json());
app.use('/uploads', express.static('uploads'));

// Configure multer for file uploads
const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    cb(null, 'uploads/');
  },
  filename: (req, file, cb) => {
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
    cb(null, file.fieldname + '-' + uniqueSuffix + path.extname(file.originalname));
  }
});

const upload = multer({ 
  storage: storage,
  limits: { fileSize: 5 * 1024 * 1024 }, // 5MB limit
  fileFilter: (req, file, cb) => {
    const allowedTypes = /jpeg|jpg|png|pdf/;
    const extname = allowedTypes.test(path.extname(file.originalname).toLowerCase());
    const mimetype = allowedTypes.test(file.mimetype);
    
    if (mimetype && extname) {
      return cb(null, true);
    } else {
      cb(new Error('Only images and PDF files are allowed'));
    }
  }
});

// In-memory database (replace with real database)
let users = [];
let documents = [];
let refreshTokens = [];

// Helper functions
const generateTokens = (userId) => {
  const accessToken = jwt.sign({ userId }, JWT_SECRET, { expiresIn: '1h' });
  const refreshToken = jwt.sign({ userId }, JWT_SECRET, { expiresIn: '7d' });
  return { accessToken, refreshToken };
};

const authenticateToken = (req, res, next) => {
  const authHeader = req.headers['authorization'];
  const token = authHeader && authHeader.split(' ')[1];

  if (!token) {
    return res.status(401).json({ success: false, message: 'Access token required' });
  }

  jwt.verify(token, JWT_SECRET, (err, user) => {
    if (err) {
      return res.status(403).json({ success: false, message: 'Invalid or expired token' });
    }
    req.user = user;
    next();
  });
};

// Routes

// Health check
app.get('/api/health', (req, res) => {
  res.json({ success: true, message: 'FoodWay API is running' });
});

// Register
app.post('/api/auth/register', upload.fields([
  { name: 'cnic', maxCount: 1 },
  { name: 'driving_license', maxCount: 1 },
  { name: 'vehicle_registration', maxCount: 1 },
  { name: 'profile_photo', maxCount: 1 }
]), async (req, res) => {
  try {
    const {
      firstName,
      lastName,
      email,
      phone,
      password,
      vehicleType,
      vehiclePlateNumber,
      bankAccountType,
      accountNumber,
      agreeToTerms,
      deviceId
    } = req.body;

    // Check if user already exists
    const existingUser = users.find(u => u.email === email);
    if (existingUser) {
      return res.status(400).json({
        success: false,
        message: 'User with this email already exists'
      });
    }

    // Hash password
    const hashedPassword = await bcrypt.hash(password, 10);

    // Create user
    const userId = 'user_' + Date.now();
    const user = {
      id: userId,
      firstName,
      lastName,
      email,
      phone,
      password: hashedPassword,
      isVerified: false,
      verificationStatus: 'pending',
      vehicle: {
        type: vehicleType,
        plateNumber: vehiclePlateNumber
      },
      bankInfo: {
        accountType: bankAccountType,
        accountNumber: accountNumber
      },
      isDemoAccount: false,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };

    users.push(user);

    // Process uploaded documents
    if (req.files) {
      Object.keys(req.files).forEach(documentType => {
        const file = req.files[documentType][0];
        const document = {
          id: 'doc_' + Date.now() + '_' + documentType,
          userId,
          documentType,
          status: 'pending',
          filename: file.filename,
          originalName: file.originalname,
          uploadedAt: new Date().toISOString()
        };
        documents.push(document);
      });
    }

    res.status(201).json({
      success: true,
      data: {
        user: {
          id: user.id,
          email: user.email,
          firstName: user.firstName,
          lastName: user.lastName,
          phone: user.phone,
          verificationStatus: user.verificationStatus,
          isDemoAccount: user.isDemoAccount
        },
        message: 'Registration successful. Please check your email for verification.',
        nextSteps: [
          'Verify your email address',
          'Wait for document verification',
          'Complete profile setup'
        ]
      },
      message: 'Registration successful'
    });

  } catch (error) {
    console.error('Registration error:', error);
    res.status(500).json({
      success: false,
      message: 'Registration failed. Please try again.'
    });
  }
});

// Login
app.post('/api/auth/login', async (req, res) => {
  try {
    const { email, password, deviceId } = req.body;

    // Find user
    const user = users.find(u => u.email === email);
    if (!user) {
      return res.status(401).json({
        success: false,
        message: 'Invalid email or password'
      });
    }

    // Check password
    const validPassword = await bcrypt.compare(password, user.password);
    if (!validPassword) {
      return res.status(401).json({
        success: false,
        message: 'Invalid email or password'
      });
    }

    // Generate tokens
    const tokens = generateTokens(user.id);
    refreshTokens.push(tokens.refreshToken);

    // Get user documents
    const userDocuments = documents.filter(d => d.userId === user.id);
    const documentStatus = {
      overall: userDocuments.length > 0 && userDocuments.every(d => d.status === 'verified') ? 'verified' : 'pending',
      documents: {}
    };

    userDocuments.forEach(doc => {
      documentStatus.documents[doc.documentType] = {
        status: doc.status,
        uploadedAt: doc.uploadedAt,
        verifiedAt: doc.verifiedAt,
        rejectionReason: doc.rejectionReason
      };
    });

    res.json({
      success: true,
      data: {
        user: {
          id: user.id,
          email: user.email,
          firstName: user.firstName,
          lastName: user.lastName,
          phone: user.phone,
          isVerified: user.isVerified,
          verificationStatus: user.verificationStatus,
          documentStatus,
          profile: user.profile || {},
          vehicle: user.vehicle || {},
          bankInfo: user.bankInfo || {},
          isDemoAccount: user.isDemoAccount,
          createdAt: user.createdAt,
          updatedAt: user.updatedAt
        },
        tokens: {
          accessToken: tokens.accessToken,
          refreshToken: tokens.refreshToken,
          expiresIn: 3600
        }
      },
      message: 'Login successful'
    });

  } catch (error) {
    console.error('Login error:', error);
    res.status(500).json({
      success: false,
      message: 'Login failed. Please try again.'
    });
  }
});

// Get current user
app.get('/api/auth/me', authenticateToken, (req, res) => {
  const user = users.find(u => u.id === req.user.userId);
  if (!user) {
    return res.status(404).json({
      success: false,
      message: 'User not found'
    });
  }

  // Get user documents
  const userDocuments = documents.filter(d => d.userId === user.id);
  const documentStatus = {
    overall: userDocuments.length > 0 && userDocuments.every(d => d.status === 'verified') ? 'verified' : 'pending',
    documents: {}
  };

  userDocuments.forEach(doc => {
    documentStatus.documents[doc.documentType] = {
      status: doc.status,
      uploadedAt: doc.uploadedAt,
      verifiedAt: doc.verifiedAt,
      rejectionReason: doc.rejectionReason
    };
  });

  res.json({
    success: true,
    data: {
      id: user.id,
      email: user.email,
      firstName: user.firstName,
      lastName: user.lastName,
      phone: user.phone,
      isVerified: user.isVerified,
      verificationStatus: user.verificationStatus,
      documentStatus,
      profile: user.profile || {},
      vehicle: user.vehicle || {},
      bankInfo: user.bankInfo || {},
      isDemoAccount: user.isDemoAccount,
      createdAt: user.createdAt,
      updatedAt: user.updatedAt
    }
  });
});

// Refresh token
app.post('/api/auth/refresh', (req, res) => {
  const { refreshToken } = req.body;

  if (!refreshToken || !refreshTokens.includes(refreshToken)) {
    return res.status(403).json({
      success: false,
      message: 'Invalid refresh token'
    });
  }

  jwt.verify(refreshToken, JWT_SECRET, (err, user) => {
    if (err) {
      return res.status(403).json({
        success: false,
        message: 'Invalid refresh token'
      });
    }

    const tokens = generateTokens(user.userId);
    
    // Remove old refresh token and add new one
    const tokenIndex = refreshTokens.indexOf(refreshToken);
    refreshTokens.splice(tokenIndex, 1);
    refreshTokens.push(tokens.refreshToken);

    res.json({
      success: true,
      data: {
        accessToken: tokens.accessToken,
        refreshToken: tokens.refreshToken,
        expiresIn: 3600
      }
    });
  });
});

// Logout
app.post('/api/auth/logout', authenticateToken, (req, res) => {
  const authHeader = req.headers['authorization'];
  const token = authHeader && authHeader.split(' ')[1];
  
  // In a real implementation, you'd add the token to a blacklist
  // For now, we'll just remove refresh tokens
  refreshTokens = refreshTokens.filter(t => {
    try {
      const decoded = jwt.verify(t, JWT_SECRET);
      return decoded.userId !== req.user.userId;
    } catch {
      return false;
    }
  });

  res.json({
    success: true,
    data: { message: 'Logged out successfully' }
  });
});

// Get verification status
app.get('/api/documents/verification-status', authenticateToken, (req, res) => {
  const userDocuments = documents.filter(d => d.userId === req.user.userId);
  
  const documentStatus = {};
  const requiredDocuments = ['cnic', 'driving_license', 'vehicle_registration'];
  const pendingDocuments = [];
  const verifiedDocuments = [];
  const rejectedDocuments = [];

  userDocuments.forEach(doc => {
    documentStatus[doc.documentType] = {
      documentId: doc.id,
      documentType: doc.documentType,
      status: doc.status,
      uploadedAt: doc.uploadedAt,
      verifiedAt: doc.verifiedAt,
      rejectionReason: doc.rejectionReason
    };

    if (doc.status === 'pending') pendingDocuments.push(doc.documentType);
    if (doc.status === 'verified') verifiedDocuments.push(doc.documentType);
    if (doc.status === 'rejected') rejectedDocuments.push(doc.documentType);
  });

  const overall = verifiedDocuments.length === requiredDocuments.length ? 'verified' : 
                  rejectedDocuments.length > 0 ? 'rejected' :
                  pendingDocuments.length > 0 ? 'pending' : 'incomplete';

  res.json({
    success: true,
    data: {
      overall,
      canGoOnline: overall === 'verified',
      documents: documentStatus,
      requiredDocuments,
      pendingDocuments,
      verifiedDocuments,
      rejectedDocuments,
      lastUpdated: new Date().toISOString()
    }
  });
});

// Upload document
app.post('/api/documents/upload', authenticateToken, upload.single('file'), (req, res) => {
  try {
    const { documentType, metadata } = req.body;
    
    if (!req.file) {
      return res.status(400).json({
        success: false,
        message: 'No file uploaded'
      });
    }

    const document = {
      id: 'doc_' + Date.now(),
      userId: req.user.userId,
      documentType,
      status: 'pending',
      filename: req.file.filename,
      originalName: req.file.originalname,
      uploadedAt: new Date().toISOString(),
      metadata: metadata ? JSON.parse(metadata) : {}
    };

    // Remove existing document of same type
    documents = documents.filter(d => !(d.userId === req.user.userId && d.documentType === documentType));
    
    // Add new document
    documents.push(document);

    res.json({
      success: true,
      data: {
        documentId: document.id,
        documentType: document.documentType,
        status: document.status,
        uploadedAt: document.uploadedAt,
        message: 'Document uploaded successfully and is under review'
      }
    });

  } catch (error) {
    console.error('Document upload error:', error);
    res.status(500).json({
      success: false,
      message: 'Document upload failed. Please try again.'
    });
  }
});

// Get all documents status
app.get('/api/documents/status', authenticateToken, (req, res) => {
  const userDocuments = documents.filter(d => d.userId === req.user.userId);
  
  const documentList = userDocuments.map(doc => ({
    documentId: doc.id,
    documentType: doc.documentType,
    status: doc.status,
    uploadedAt: doc.uploadedAt,
    verifiedAt: doc.verifiedAt,
    rejectionReason: doc.rejectionReason,
    fileUrl: doc.filename ? `/uploads/${doc.filename}` : null
  }));

  res.json({
    success: true,
    data: documentList
  });
});

// Start server
app.listen(PORT, () => {
  console.log(`FoodWay API Server running on port ${PORT}`);
  console.log(`Health check: http://localhost:${PORT}/api/health`);
});

module.exports = app;
