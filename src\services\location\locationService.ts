import * as Location from 'expo-location';
import { LocationData, LocationPermissionStatus } from '../../types/location';
import { LOCATION_CONFIG } from '../../utils/constants';

class LocationService {
  private watchId: Location.LocationSubscription | null = null;
  private currentLocation: LocationData | null = null;

  // Request location permissions
  async requestPermissions(): Promise<LocationPermissionStatus> {
    try {
      const { status } = await Location.requestForegroundPermissionsAsync();
      
      if (status === 'granted') {
        // Also request background permissions for delivery tracking
        const backgroundStatus = await Location.requestBackgroundPermissionsAsync();
        return backgroundStatus.status === Location.PermissionStatus.GRANTED ? Location.PermissionStatus.GRANTED : Location.PermissionStatus.DENIED;
      }
      
      return status as LocationPermissionStatus;
    } catch (error) {
      console.error('Error requesting location permissions:', error);
      return Location.PermissionStatus.DENIED;
    }
  }

  // Check current permission status
  async getPermissionStatus(): Promise<LocationPermissionStatus> {
    try {
      const { status } = await Location.getForegroundPermissionsAsync();
      
      if (status === 'granted') {
        const backgroundStatus = await Location.getBackgroundPermissionsAsync();
        return backgroundStatus.status === Location.PermissionStatus.GRANTED ? Location.PermissionStatus.GRANTED : Location.PermissionStatus.DENIED;
      }
      
      return status as LocationPermissionStatus;
    } catch (error) {
      console.error('Error checking location permissions:', error);
      return Location.PermissionStatus.DENIED;
    }
  }

  // Get current location once
  async getCurrentLocation(): Promise<LocationData> {
    try {
      const permissionStatus = await this.getPermissionStatus();
      
      if (permissionStatus === 'denied') {
        throw new Error('Location permission denied');
      }

      const location = await Location.getCurrentPositionAsync({
        accuracy: Location.Accuracy.High,
      });

      const locationData: LocationData = {
        coords: {
          latitude: location.coords.latitude,
          longitude: location.coords.longitude,
        },
        accuracy: location.coords.accuracy || 0,
        altitude: location.coords.altitude || 0,
        heading: location.coords.heading || 0,
        speed: location.coords.speed || 0,
        timestamp: location.timestamp,
      };

      this.currentLocation = locationData;
      return locationData;
    } catch (error: any) {
      console.error('Error getting current location:', error);
      throw new Error(`Failed to get location: ${error.message}`);
    }
  }

  // Start watching location changes
  async startLocationTracking(
    onLocationUpdate: (location: LocationData) => void,
    onError?: (error: Error) => void
  ): Promise<void> {
    try {
      const permissionStatus = await this.getPermissionStatus();
      
      if (permissionStatus === 'denied') {
        throw new Error('Location permission denied');
      }

      // Stop existing tracking if any
      await this.stopLocationTracking();

      this.watchId = await Location.watchPositionAsync(
        {
          accuracy: Location.Accuracy.High,
          timeInterval: LOCATION_CONFIG.UPDATE_INTERVAL,
          distanceInterval: LOCATION_CONFIG.DISTANCE_FILTER,
        },
        (location) => {
          const locationData: LocationData = {
            coords: {
              latitude: location.coords.latitude,
              longitude: location.coords.longitude,
            },
            accuracy: location.coords.accuracy || 0,
            altitude: location.coords.altitude || 0,
            heading: location.coords.heading || 0,
            speed: location.coords.speed || 0,
            timestamp: location.timestamp,
          };

          this.currentLocation = locationData;
          onLocationUpdate(locationData);
        }
      );
    } catch (error: any) {
      console.error('Error starting location tracking:', error);
      if (onError) {
        onError(new Error(`Failed to start location tracking: ${error.message}`));
      }
    }
  }

  // Stop watching location changes
  async stopLocationTracking(): Promise<void> {
    try {
      if (this.watchId) {
        this.watchId.remove();
        this.watchId = null;
      }
    } catch (error) {
      console.error('Error stopping location tracking:', error);
    }
  }

  // Get cached current location
  getCachedLocation(): LocationData | null {
    return this.currentLocation;
  }

  // Calculate distance between two points (Haversine formula)
  calculateDistance(
    lat1: number,
    lon1: number,
    lat2: number,
    lon2: number
  ): number {
    const R = 6371; // Earth's radius in kilometers
    const dLat = this.toRadians(lat2 - lat1);
    const dLon = this.toRadians(lon2 - lon1);
    
    const a =
      Math.sin(dLat / 2) * Math.sin(dLat / 2) +
      Math.cos(this.toRadians(lat1)) *
        Math.cos(this.toRadians(lat2)) *
        Math.sin(dLon / 2) *
        Math.sin(dLon / 2);
    
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
    return R * c;
  }

  // Calculate bearing between two points
  calculateBearing(
    lat1: number,
    lon1: number,
    lat2: number,
    lon2: number
  ): number {
    const dLon = this.toRadians(lon2 - lon1);
    const lat1Rad = this.toRadians(lat1);
    const lat2Rad = this.toRadians(lat2);
    
    const y = Math.sin(dLon) * Math.cos(lat2Rad);
    const x =
      Math.cos(lat1Rad) * Math.sin(lat2Rad) -
      Math.sin(lat1Rad) * Math.cos(lat2Rad) * Math.cos(dLon);
    
    const bearing = Math.atan2(y, x);
    return (this.toDegrees(bearing) + 360) % 360;
  }

  // Check if location is within geofence
  isWithinGeofence(
    currentLat: number,
    currentLon: number,
    targetLat: number,
    targetLon: number,
    radiusInMeters: number
  ): boolean {
    const distance = this.calculateDistance(currentLat, currentLon, targetLat, targetLon);
    return distance * 1000 <= radiusInMeters; // Convert km to meters
  }

  // Get address from coordinates (reverse geocoding)
  async getAddressFromCoordinates(
    latitude: number,
    longitude: number
  ): Promise<string> {
    try {
      // Add timeout to prevent hanging
      const timeoutPromise = new Promise<never>((_, reject) => {
        setTimeout(() => reject(new Error('Reverse geocoding timeout')), 5000);
      });

      const geocodePromise = Location.reverseGeocodeAsync({
        latitude,
        longitude,
      });

      const addresses = await Promise.race([geocodePromise, timeoutPromise]);

      if (addresses.length > 0) {
        const address = addresses[0];
        const parts = [
          address.streetNumber,
          address.street,
          address.city,
          address.region,
          address.postalCode,
        ].filter(Boolean);

        return parts.join(', ') || `${latitude.toFixed(6)}, ${longitude.toFixed(6)}`;
      }

      return `${latitude.toFixed(6)}, ${longitude.toFixed(6)}`;
    } catch (error) {
      console.error('Error getting address from coordinates:', error);
      // Return coordinates as fallback
      return `${latitude.toFixed(6)}, ${longitude.toFixed(6)}`;
    }
  }

  // Get coordinates from address (geocoding)
  async getCoordinatesFromAddress(address: string): Promise<LocationData | null> {
    try {
      // Add timeout to prevent hanging
      const timeoutPromise = new Promise<never>((_, reject) => {
        setTimeout(() => reject(new Error('Geocoding timeout')), 5000);
      });

      const geocodePromise = Location.geocodeAsync(address);
      const locations = await Promise.race([geocodePromise, timeoutPromise]);

      if (locations.length > 0) {
        const location = locations[0];
        return {
          coords: {
            latitude: location.latitude,
            longitude: location.longitude,
          },
          accuracy: 0,
          altitude: 0,
          heading: 0,
          speed: 0,
          timestamp: Date.now(),
        };
      }

      return null;
    } catch (error) {
      console.error('Error getting coordinates from address:', error);
      return null;
    }
  }

  // Helper methods
  private toRadians(degrees: number): number {
    return degrees * (Math.PI / 180);
  }

  private toDegrees(radians: number): number {
    return radians * (180 / Math.PI);
  }

  // Cleanup
  async cleanup(): Promise<void> {
    await this.stopLocationTracking();
    this.currentLocation = null;
  }
}

export const locationService = new LocationService();
