import React, { createContext, useContext, useReducer, useMemo, useCallback } from 'react';
import { useBatchedUpdates } from '../utils/performanceUtils';

/**
 * Optimized context provider that reduces unnecessary re-renders
 * and batches state updates for better performance
 */

interface OptimizedContextState {
  [key: string]: any;
}

interface OptimizedContextActions {
  updateState: (updates: Partial<OptimizedContextState>) => void;
  batchUpdate: (updateFn: () => void) => void;
  resetState: () => void;
}

interface OptimizedContextValue {
  state: OptimizedContextState;
  actions: OptimizedContextActions;
}

type OptimizedContextAction = 
  | { type: 'UPDATE_STATE'; payload: Partial<OptimizedContextState> }
  | { type: 'RESET_STATE' };

// Generic optimized context creator
export function createOptimizedContext<T extends OptimizedContextState>(
  initialState: T,
  contextName: string = 'OptimizedContext'
) {
  const Context = createContext<OptimizedContextValue | undefined>(undefined);

  const reducer = (state: T, action: OptimizedContextAction): T => {
    switch (action.type) {
      case 'UPDATE_STATE':
        return { ...state, ...action.payload };
      case 'RESET_STATE':
        return initialState;
      default:
        return state;
    }
  };

  const Provider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
    const [state, dispatch] = useReducer(reducer, initialState);
    const batchUpdate = useBatchedUpdates();

    // Memoized actions to prevent unnecessary re-renders
    const actions = useMemo<OptimizedContextActions>(() => ({
      updateState: (updates: Partial<T>) => {
        dispatch({ type: 'UPDATE_STATE', payload: updates });
      },
      batchUpdate: (updateFn: () => void) => {
        batchUpdate(updateFn);
      },
      resetState: () => {
        dispatch({ type: 'RESET_STATE' });
      },
    }), [batchUpdate]);

    // Memoized context value to prevent unnecessary re-renders
    const contextValue = useMemo<OptimizedContextValue>(() => ({
      state,
      actions,
    }), [state, actions]);

    return (
      <Context.Provider value={contextValue}>
        {children}
      </Context.Provider>
    );
  };

  const useOptimizedContext = () => {
    const context = useContext(Context);
    if (context === undefined) {
      throw new Error(`useOptimizedContext must be used within a ${contextName}Provider`);
    }
    return context;
  };

  // Selector hook for fine-grained subscriptions
  const useOptimizedSelector = <R>(selector: (state: T) => R) => {
    const { state } = useOptimizedContext();
    return useMemo(() => selector(state as T), [state, selector]);
  };

  return {
    Provider,
    useOptimizedContext,
    useOptimizedSelector,
  };
}

// Performance-optimized state manager
export class OptimizedStateManager<T extends Record<string, any>> {
  private state: T;
  private listeners: Set<(state: T) => void> = new Set();
  private batchedUpdates: Partial<T> = {};
  private updateTimeout: NodeJS.Timeout | null = null;

  constructor(initialState: T) {
    this.state = initialState;
  }

  // Get current state
  getState(): T {
    return this.state;
  }

  // Subscribe to state changes
  subscribe(listener: (state: T) => void): () => void {
    this.listeners.add(listener);
    return () => {
      this.listeners.delete(listener);
    };
  }

  // Update state with batching
  updateState(updates: Partial<T>): void {
    Object.assign(this.batchedUpdates, updates);
    
    if (this.updateTimeout) {
      clearTimeout(this.updateTimeout);
    }

    this.updateTimeout = setTimeout(() => {
      this.flushUpdates();
    }, 16); // Next frame
  }

  // Immediately flush batched updates
  flushUpdates(): void {
    if (Object.keys(this.batchedUpdates).length === 0) return;

    const newState = { ...this.state, ...this.batchedUpdates };
    const hasChanges = Object.keys(this.batchedUpdates).some(
      key => this.state[key] !== this.batchedUpdates[key]
    );

    if (hasChanges) {
      this.state = newState;
      this.batchedUpdates = {};
      
      // Notify listeners
      this.listeners.forEach(listener => {
        try {
          listener(this.state);
        } catch (error) {
          console.error('Error in state listener:', error);
        }
      });
    }

    if (this.updateTimeout) {
      clearTimeout(this.updateTimeout);
      this.updateTimeout = null;
    }
  }

  // Reset state
  resetState(newState: T): void {
    this.state = newState;
    this.batchedUpdates = {};
    this.listeners.forEach(listener => listener(this.state));
  }

  // Cleanup
  cleanup(): void {
    this.listeners.clear();
    if (this.updateTimeout) {
      clearTimeout(this.updateTimeout);
    }
  }
}

// React hook for optimized state manager
export function useOptimizedStateManager<T extends Record<string, any>>(
  initialState: T
): [T, (updates: Partial<T>) => void, () => void] {
  const stateManager = useMemo(() => new OptimizedStateManager(initialState), []);
  const [state, setState] = React.useState(initialState);

  React.useEffect(() => {
    const unsubscribe = stateManager.subscribe(setState);
    return () => {
      unsubscribe();
      stateManager.cleanup();
    };
  }, [stateManager]);

  const updateState = useCallback((updates: Partial<T>) => {
    stateManager.updateState(updates);
  }, [stateManager]);

  const resetState = useCallback(() => {
    stateManager.resetState(initialState);
  }, [stateManager, initialState]);

  return [state, updateState, resetState];
}

// Higher-order component for performance optimization
export function withOptimizedContext<P extends object>(
  WrappedComponent: React.ComponentType<P>,
  contextName?: string
) {
  const displayName = contextName || WrappedComponent.displayName || WrappedComponent.name || 'Component';
  
  return React.memo(
    React.forwardRef<any, P>((props, ref) => {
      return React.createElement(WrappedComponent, { ...props, ref });
    }),
    (prevProps, nextProps) => {
      // Custom comparison logic for better performance
      return JSON.stringify(prevProps) === JSON.stringify(nextProps);
    }
  );
}

export default createOptimizedContext;
