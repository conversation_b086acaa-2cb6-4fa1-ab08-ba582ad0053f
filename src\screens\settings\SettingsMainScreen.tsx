import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  Alert,
  Modal,
  Linking,
  Animated,
  StatusBar,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import { useNavigation } from '@react-navigation/native';
import { LinearGradient } from 'expo-linear-gradient';
import { useAuth } from '../../context/AuthContext';
import { Language } from '../../types/profile';

interface SettingsState {
  darkMode: boolean;
  notifications: boolean;
  language: Language;
}

const SettingsMainScreen: React.FC = () => {
  const navigation = useNavigation();
  const { logout } = useAuth();
  
  const [settings, setSettings] = useState<SettingsState>({
    darkMode: false,
    notifications: true,
    language: Language.ENGLISH,
  });
  
  const [showLanguageModal, setShowLanguageModal] = useState(false);

  // Animation refs (only for entrance animations)
  const fadeAnimation = useRef(new Animated.Value(0)).current;
  const slideAnimation = useRef(new Animated.Value(50)).current;

  const languages = [
    { code: Language.ENGLISH, name: 'English', nativeName: 'English', flag: '🇺🇸', available: true },
    { code: Language.URDU, name: 'Urdu', nativeName: 'اردو', flag: '🇵🇰', available: false },
    { code: Language.PUNJABI, name: 'Punjabi', nativeName: 'ਪੰਜਾਬੀ', flag: '🇵🇰', available: false },
  ];

  useEffect(() => {
    // Start entrance animations (using native driver)
    Animated.parallel([
      Animated.timing(fadeAnimation, {
        toValue: 1,
        duration: 800,
        useNativeDriver: true,
      }),
      Animated.timing(slideAnimation, {
        toValue: 0,
        duration: 800,
        useNativeDriver: true,
      }),
    ]).start();
  }, []);

  const handleDarkModeToggle = () => {
    Alert.alert(
      'Coming Soon',
      'Dark mode will be available in a future update. Stay tuned!',
      [{ text: 'OK' }]
    );
  };

  const handleNotificationToggle = () => {
    const newValue = !settings.notifications;
    setSettings(prev => ({ ...prev, notifications: newValue }));
  };

  const handleLanguageSelect = (language: Language, available: boolean) => {
    if (!available) {
      Alert.alert(
        'Coming Soon',
        'This language will be available in a future update. Stay tuned!',
        [{ text: 'OK' }]
      );
      return;
    }
    setSettings(prev => ({ ...prev, language }));
    setShowLanguageModal(false);
  };

  const handleLogout = () => {
    Alert.alert(
      'Logout',
      'Are you sure you want to logout?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Logout',
          style: 'destructive',
          onPress: async () => {
            try {
              await logout();
            } catch (error: any) {
              Alert.alert('Error', error.message || 'Failed to logout');
            }
          }
        },
      ]
    );
  };

  const openPrivacyPolicy = () => {
    Linking.openURL('https://foodway.pk/privacy');
  };

  const openTermsOfService = () => {
    Linking.openURL('https://foodway.pk/terms');
  };

  const renderHeader = () => (
    <LinearGradient
      colors={['#f97316', '#ea580c']}
      style={{
        paddingTop: StatusBar.currentHeight || 0,
        paddingBottom: 20,
      }}
    >
      <SafeAreaView>
        <View style={{
          flexDirection: 'row',
          alignItems: 'center',
          justifyContent: 'space-between',
          paddingHorizontal: 20,
          paddingTop: 16,
        }}>
          <TouchableOpacity
            onPress={() => navigation.goBack()}
            style={{
              width: 40,
              height: 40,
              borderRadius: 20,
              backgroundColor: 'rgba(255,255,255,0.2)',
              alignItems: 'center',
              justifyContent: 'center',
            }}
          >
            <Ionicons name="arrow-back" size={24} color="white" />
          </TouchableOpacity>

          <Text style={{
            fontSize: 24,
            fontWeight: 'bold',
            color: 'white',
          }}>
            Settings
          </Text>

          <View style={{ width: 40 }} />
        </View>
      </SafeAreaView>
    </LinearGradient>
  );

  const renderSimpleToggle = (
    value: boolean,
    onToggle: () => void,
    icon: string,
    activeColor: string
  ) => {
    const backgroundColor = value ? activeColor : '#e5e7eb';
    const thumbPosition = value ? 20 : 0;

    return (
      <TouchableOpacity
        onPress={onToggle}
        style={{
          width: 52,
          height: 32,
          borderRadius: 16,
          backgroundColor,
          justifyContent: 'center',
          padding: 2,
        }}
        activeOpacity={0.8}
      >
        <View
          style={{
            width: 28,
            height: 28,
            borderRadius: 14,
            backgroundColor: 'white',
            alignItems: 'center',
            justifyContent: 'center',
            transform: [{ translateX: thumbPosition }],
            shadowColor: '#000',
            shadowOffset: { width: 0, height: 2 },
            shadowOpacity: 0.2,
            shadowRadius: 4,
            elevation: 4,
          }}
        >
          <Ionicons
            name={icon as any}
            size={16}
            color={value ? activeColor : '#6b7280'}
          />
        </View>
      </TouchableOpacity>
    );
  };

  const renderSettingItem = (
    icon: string,
    title: string,
    subtitle?: string,
    onPress?: () => void,
    rightComponent?: React.ReactNode,
    showChevron: boolean = true
  ) => (
    <TouchableOpacity
      onPress={onPress}
      style={{
        flexDirection: 'row',
        alignItems: 'center',
        paddingVertical: 16,
        paddingHorizontal: 20,
        backgroundColor: 'white',
        borderBottomWidth: 1,
        borderBottomColor: '#f3f4f6',
      }}
      disabled={!onPress}
    >
      <View style={{
        width: 40,
        height: 40,
        borderRadius: 20,
        backgroundColor: '#f8fafc',
        alignItems: 'center',
        justifyContent: 'center',
        marginRight: 16,
      }}>
        <Ionicons name={icon as any} size={20} color="#f97316" />
      </View>

      <View style={{ flex: 1 }}>
        <Text style={{
          fontSize: 16,
          fontWeight: '600',
          color: '#111827',
          marginBottom: subtitle ? 2 : 0,
        }}>
          {title}
        </Text>
        {subtitle && (
          <Text style={{
            fontSize: 14,
            color: '#6b7280',
          }}>
            {subtitle}
          </Text>
        )}
      </View>

      {rightComponent || (showChevron && (
        <Ionicons name="chevron-forward" size={20} color="#6b7280" />
      ))}
    </TouchableOpacity>
  );

  return (
    <View style={{ flex: 1, backgroundColor: '#f8fafc' }}>
      <StatusBar barStyle="light-content" backgroundColor="#f97316" />
      
      {renderHeader()}

      <Animated.View
        style={{
          flex: 1,
          opacity: fadeAnimation,
          transform: [{ translateY: slideAnimation }],
        }}
      >
        <ScrollView
          style={{ flex: 1 }}
          showsVerticalScrollIndicator={false}
          contentContainerStyle={{ paddingBottom: 100 }}
        >
          {/* Appearance Section */}
          <View style={{
            backgroundColor: 'white',
            marginTop: 20,
            marginHorizontal: 20,
            borderRadius: 16,
            shadowColor: '#000',
            shadowOffset: { width: 0, height: 2 },
            shadowOpacity: 0.1,
            shadowRadius: 8,
            elevation: 3,
          }}>
            <View style={{
              paddingHorizontal: 20,
              paddingVertical: 16,
              borderBottomWidth: 1,
              borderBottomColor: '#f3f4f6',
            }}>
              <Text style={{
                fontSize: 18,
                fontWeight: 'bold',
                color: '#111827',
              }}>
                Appearance
              </Text>
            </View>

            {renderSettingItem(
              'moon',
              'Dark Mode',
              'Switch between light and dark theme',
              handleDarkModeToggle,
              <View style={{
                backgroundColor: '#fbbf24',
                paddingHorizontal: 12,
                paddingVertical: 6,
                borderRadius: 16,
                opacity: 0.8,
              }}>
                <Text style={{
                  fontSize: 12,
                  fontWeight: '600',
                  color: 'white',
                }}>
                  Coming Soon
                </Text>
              </View>,
              false
            )}
          </View>

          {/* Notifications Section */}
          <View style={{
            backgroundColor: 'white',
            marginTop: 16,
            marginHorizontal: 20,
            borderRadius: 16,
            shadowColor: '#000',
            shadowOffset: { width: 0, height: 2 },
            shadowOpacity: 0.1,
            shadowRadius: 8,
            elevation: 3,
          }}>
            <View style={{
              paddingHorizontal: 20,
              paddingVertical: 16,
              borderBottomWidth: 1,
              borderBottomColor: '#f3f4f6',
            }}>
              <Text style={{
                fontSize: 18,
                fontWeight: 'bold',
                color: '#111827',
              }}>
                Notifications
              </Text>
            </View>

            {renderSettingItem(
              'notifications',
              'Push Notifications',
              'Receive order alerts and updates',
              handleNotificationToggle,
              renderSimpleToggle(
                settings.notifications,
                handleNotificationToggle,
                'notifications',
                '#10b981'
              ),
              false
            )}
          </View>

          {/* Language Section */}
          <View style={{
            backgroundColor: 'white',
            marginTop: 16,
            marginHorizontal: 20,
            borderRadius: 16,
            shadowColor: '#000',
            shadowOffset: { width: 0, height: 2 },
            shadowOpacity: 0.1,
            shadowRadius: 8,
            elevation: 3,
          }}>
            <View style={{
              paddingHorizontal: 20,
              paddingVertical: 16,
              borderBottomWidth: 1,
              borderBottomColor: '#f3f4f6',
            }}>
              <Text style={{
                fontSize: 18,
                fontWeight: 'bold',
                color: '#111827',
              }}>
                Language & Region
              </Text>
            </View>

            {renderSettingItem(
              'language',
              'Language',
              languages.find(l => l.code === settings.language)?.nativeName,
              () => setShowLanguageModal(true),
              <View style={{
                flexDirection: 'row',
                alignItems: 'center',
              }}>
                <Text style={{
                  fontSize: 20,
                  marginRight: 8,
                }}>
                  {languages.find(l => l.code === settings.language)?.flag}
                </Text>
                <Ionicons name="chevron-forward" size={20} color="#6b7280" />
              </View>,
              false
            )}
          </View>

          {/* Advanced Features Section */}
          <View style={{
            backgroundColor: 'white',
            marginTop: 16,
            marginHorizontal: 20,
            borderRadius: 16,
            shadowColor: '#000',
            shadowOffset: { width: 0, height: 2 },
            shadowOpacity: 0.1,
            shadowRadius: 8,
            elevation: 3,
          }}>
            <View style={{
              paddingHorizontal: 20,
              paddingVertical: 16,
              borderBottomWidth: 1,
              borderBottomColor: '#f3f4f6',
            }}>
              <Text style={{
                fontSize: 18,
                fontWeight: 'bold',
                color: '#111827',
              }}>
                Advanced Features
              </Text>
            </View>

            {renderSettingItem(
              'construct',
              'Advanced Tools',
              'Access heatmap, performance badges, and training',
              () => navigation.navigate('AdvancedTools' as never)
            )}

            {renderSettingItem(
              'analytics',
              'Performance Analytics',
              'View detailed performance insights',
              () => navigation.navigate('PerformanceAnalytics' as never)
            )}

            {renderSettingItem(
              'school',
              'Training Center',
              'Access training videos and certifications',
              () => navigation.navigate('AdvancedTools' as never, { screen: 'Training' })
            )}
          </View>

          {/* Privacy & Legal Section */}
          <View style={{
            backgroundColor: 'white',
            marginTop: 16,
            marginHorizontal: 20,
            borderRadius: 16,
            shadowColor: '#000',
            shadowOffset: { width: 0, height: 2 },
            shadowOpacity: 0.1,
            shadowRadius: 8,
            elevation: 3,
          }}>
            <View style={{
              paddingHorizontal: 20,
              paddingVertical: 16,
              borderBottomWidth: 1,
              borderBottomColor: '#f3f4f6',
            }}>
              <Text style={{
                fontSize: 18,
                fontWeight: 'bold',
                color: '#111827',
              }}>
                Privacy & Legal
              </Text>
            </View>

            {renderSettingItem(
              'shield-checkmark',
              'Privacy Policy',
              'Read our privacy policy',
              openPrivacyPolicy
            )}

            {renderSettingItem(
              'document-text',
              'Terms of Service',
              'Read our terms and conditions',
              openTermsOfService
            )}
          </View>

          {/* Account Section */}
          <View style={{
            backgroundColor: 'white',
            marginTop: 16,
            marginHorizontal: 20,
            borderRadius: 16,
            shadowColor: '#000',
            shadowOffset: { width: 0, height: 2 },
            shadowOpacity: 0.1,
            shadowRadius: 8,
            elevation: 3,
          }}>
            <View style={{
              paddingHorizontal: 20,
              paddingVertical: 16,
              borderBottomWidth: 1,
              borderBottomColor: '#f3f4f6',
            }}>
              <Text style={{
                fontSize: 18,
                fontWeight: 'bold',
                color: '#111827',
              }}>
                Account
              </Text>
            </View>

            {renderSettingItem(
              'log-out',
              'Logout',
              'Sign out of your account',
              handleLogout,
              <Ionicons name="log-out" size={20} color="#ef4444" />,
              false
            )}
          </View>
        </ScrollView>
      </Animated.View>

      {/* Language Selection Modal */}
      <Modal
        visible={showLanguageModal}
        transparent={true}
        animationType="slide"
        onRequestClose={() => setShowLanguageModal(false)}
      >
        <View style={{
          flex: 1,
          backgroundColor: 'rgba(0,0,0,0.5)',
          justifyContent: 'flex-end',
        }}>
          <View style={{
            backgroundColor: 'white',
            borderTopLeftRadius: 24,
            borderTopRightRadius: 24,
            paddingBottom: 40,
          }}>
            <View style={{
              flexDirection: 'row',
              alignItems: 'center',
              justifyContent: 'space-between',
              paddingHorizontal: 20,
              paddingVertical: 20,
              borderBottomWidth: 1,
              borderBottomColor: '#f3f4f6',
            }}>
              <Text style={{
                fontSize: 20,
                fontWeight: 'bold',
                color: '#111827',
              }}>
                Select Language
              </Text>

              <TouchableOpacity
                onPress={() => setShowLanguageModal(false)}
                style={{
                  width: 32,
                  height: 32,
                  borderRadius: 16,
                  backgroundColor: '#f3f4f6',
                  alignItems: 'center',
                  justifyContent: 'center',
                }}
              >
                <Ionicons name="close" size={20} color="#6b7280" />
              </TouchableOpacity>
            </View>

            {languages.map((language) => (
              <TouchableOpacity
                key={language.code}
                onPress={() => handleLanguageSelect(language.code, language.available)}
                style={{
                  flexDirection: 'row',
                  alignItems: 'center',
                  paddingHorizontal: 20,
                  paddingVertical: 16,
                  backgroundColor: settings.language === language.code ? '#fef3c7' : 'transparent',
                  opacity: language.available ? 1 : 0.6,
                }}
              >
                <Text style={{
                  fontSize: 24,
                  marginRight: 16,
                }}>
                  {language.flag}
                </Text>

                <View style={{ flex: 1 }}>
                  <View style={{ flexDirection: 'row', alignItems: 'center' }}>
                    <Text style={{
                      fontSize: 16,
                      fontWeight: '600',
                      color: language.available ? '#111827' : '#9ca3af',
                      marginBottom: 2,
                    }}>
                      {language.name}
                    </Text>
                    {!language.available && (
                      <View style={{
                        backgroundColor: '#fbbf24',
                        paddingHorizontal: 8,
                        paddingVertical: 2,
                        borderRadius: 12,
                        marginLeft: 8,
                      }}>
                        <Text style={{
                          fontSize: 10,
                          fontWeight: '600',
                          color: 'white',
                        }}>
                          Coming Soon
                        </Text>
                      </View>
                    )}
                  </View>
                  <Text style={{
                    fontSize: 14,
                    color: language.available ? '#6b7280' : '#9ca3af',
                  }}>
                    {language.nativeName}
                  </Text>
                </View>

                {settings.language === language.code && language.available && (
                  <Ionicons name="checkmark-circle" size={24} color="#f97316" />
                )}

                {!language.available && (
                  <Ionicons name="lock-closed" size={20} color="#9ca3af" />
                )}
              </TouchableOpacity>
            ))}
          </View>
        </View>
      </Modal>
    </View>
  );
};

export default SettingsMainScreen;
