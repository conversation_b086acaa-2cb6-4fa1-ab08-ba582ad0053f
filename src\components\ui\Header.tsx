import React from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  ViewStyle,
  TextStyle,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';

interface HeaderProps {
  title: string;
  subtitle?: string;
  leftIcon?: keyof typeof Ionicons.glyphMap;
  rightIcon?: keyof typeof Ionicons.glyphMap;
  onLeftPress?: () => void;
  onRightPress?: () => void;
  variant?: 'default' | 'gradient' | 'elevated' | 'minimal';
  backgroundColor?: string;
  titleColor?: string;
  iconColor?: string;
  showBorder?: boolean;
  style?: ViewStyle;
}

const Header: React.FC<HeaderProps> = ({
  title,
  subtitle,
  leftIcon,
  rightIcon,
  onLeftPress,
  onRightPress,
  variant = 'default',
  backgroundColor,
  titleColor,
  iconColor,
  showBorder = true,
  style,
}) => {
  const getHeaderStyle = (): ViewStyle => {
    const baseStyle: ViewStyle = {
      paddingHorizontal: 20,
      paddingVertical: 20,
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
    };

    const variantStyles: Record<string, ViewStyle> = {
      default: {
        backgroundColor: backgroundColor || 'white',
        borderBottomWidth: showBorder ? 1 : 0,
        borderBottomColor: '#e2e8f0',
      },
      gradient: {
        backgroundColor: backgroundColor || '#dc2626',
        borderBottomLeftRadius: 24,
        borderBottomRightRadius: 24,
        shadowColor: '#dc2626',
        shadowOffset: { width: 0, height: 8 },
        shadowOpacity: 0.3,
        shadowRadius: 16,
        elevation: 12,
      },
      elevated: {
        backgroundColor: backgroundColor || 'white',
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 6 },
        shadowOpacity: 0.1,
        shadowRadius: 16,
        elevation: 8,
        borderBottomWidth: showBorder ? 1 : 0,
        borderBottomColor: 'rgba(0, 0, 0, 0.05)',
      },
      minimal: {
        backgroundColor: backgroundColor || 'transparent',
        paddingVertical: 16,
      },
    };

    return {
      ...baseStyle,
      ...variantStyles[variant],
    };
  };

  const titleContainerStyle: ViewStyle = {
    flex: 1,
    alignItems: 'center',
  };

  const getTitleStyle = (): TextStyle => {
    const defaultColor = variant === 'gradient' ? 'white' : '#111827';
    return {
      fontSize: 22,
      fontWeight: 'bold',
      color: titleColor || defaultColor,
    };
  };

  const getSubtitleStyle = (): TextStyle => {
    const defaultColor = variant === 'gradient' ? 'rgba(255,255,255,0.8)' : '#6b7280';
    return {
      fontSize: 14,
      color: defaultColor,
      marginTop: 4,
    };
  };

  const getIconButtonStyle = (): ViewStyle => {
    const baseStyle: ViewStyle = {
      width: 48,
      height: 48,
      borderRadius: 24,
      alignItems: 'center',
      justifyContent: 'center',
    };

    if (variant === 'gradient') {
      return {
        ...baseStyle,
        backgroundColor: 'rgba(255,255,255,0.2)',
        borderWidth: 2,
        borderColor: 'rgba(255,255,255,0.3)',
      };
    }

    return {
      ...baseStyle,
      backgroundColor: '#f8fafc',
      borderWidth: 2,
      borderColor: '#e2e8f0',
      shadowColor: '#000',
      shadowOffset: { width: 0, height: 2 },
      shadowOpacity: 0.05,
      shadowRadius: 4,
      elevation: 2,
    };
  };

  const getIconColor = (): string => {
    if (iconColor) return iconColor;
    return variant === 'gradient' ? 'white' : '#dc2626';
  };

  return (
    <View style={[getHeaderStyle(), style]}>
      {/* Left Icon */}
      <View style={{ width: 48 }}>
        {leftIcon && (
          <TouchableOpacity
            onPress={onLeftPress}
            style={getIconButtonStyle()}
          >
            <Ionicons name={leftIcon} size={24} color={getIconColor()} />
          </TouchableOpacity>
        )}
      </View>

      {/* Title */}
      <View style={titleContainerStyle}>
        <Text style={getTitleStyle()}>{title}</Text>
        {subtitle && <Text style={getSubtitleStyle()}>{subtitle}</Text>}
      </View>

      {/* Right Icon */}
      <View style={{ width: 48, alignItems: 'flex-end' }}>
        {rightIcon && (
          <TouchableOpacity
            onPress={onRightPress}
            style={getIconButtonStyle()}
          >
            <Ionicons name={rightIcon} size={24} color={getIconColor()} />
          </TouchableOpacity>
        )}
      </View>
    </View>
  );
};

export default Header;
