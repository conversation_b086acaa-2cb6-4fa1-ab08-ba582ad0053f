import React, { useState } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  Image,
  Alert,
  ScrollView,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import * as ImagePicker from 'expo-image-picker';
import { <PERSON><PERSON>, <PERSON> } from '../../components/ui';

interface DocumentUploadScreenProps {
  documentType: string;
  documentTitle: string;
  onUpload: (uri: string) => void;
  onBack: () => void;
  existingImage?: string;
}

const DocumentUploadScreen: React.FC<DocumentUploadScreenProps> = ({
  documentType,
  documentTitle,
  onUpload,
  onBack,
  existingImage,
}) => {
  const [selectedImage, setSelectedImage] = useState<string | null>(existingImage || null);
  const [isUploading, setIsUploading] = useState(false);

  const requestPermissions = async () => {
    const { status } = await ImagePicker.requestMediaLibraryPermissionsAsync();
    if (status !== 'granted') {
      Alert.alert(
        'Permission Required',
        'Please grant camera roll permissions to upload documents.',
        [{ text: 'OK' }]
      );
      return false;
    }
    return true;
  };

  const pickImage = async (source: 'camera' | 'library') => {
    const hasPermission = await requestPermissions();
    if (!hasPermission) return;

    try {
      let result;
      
      if (source === 'camera') {
        const { status } = await ImagePicker.requestCameraPermissionsAsync();
        if (status !== 'granted') {
          Alert.alert('Permission Required', 'Camera permission is required to take photos.');
          return;
        }
        
        result = await ImagePicker.launchCameraAsync({
          mediaTypes: ImagePicker.MediaTypeOptions.Images,
          allowsEditing: true,
          aspect: [4, 3],
          quality: 0.8,
        });
      } else {
        result = await ImagePicker.launchImageLibraryAsync({
          mediaTypes: ImagePicker.MediaTypeOptions.Images,
          allowsEditing: true,
          aspect: [4, 3],
          quality: 0.8,
        });
      }

      if (!result.canceled && result.assets[0]) {
        setSelectedImage(result.assets[0].uri);
      }
    } catch (error) {
      Alert.alert('Error', 'Failed to pick image. Please try again.');
    }
  };

  const handleUpload = async () => {
    if (!selectedImage) {
      Alert.alert('No Image Selected', 'Please select an image to upload.');
      return;
    }

    setIsUploading(true);
    try {
      // Simulate upload delay
      await new Promise(resolve => setTimeout(resolve, 2000));
      onUpload(selectedImage);
    } catch (error) {
      Alert.alert('Upload Failed', 'Failed to upload document. Please try again.');
    } finally {
      setIsUploading(false);
    }
  };

  const showImageSourceOptions = () => {
    Alert.alert(
      'Select Image Source',
      'Choose how you want to add the document',
      [
        { text: 'Camera', onPress: () => pickImage('camera') },
        { text: 'Photo Library', onPress: () => pickImage('library') },
        { text: 'Cancel', style: 'cancel' },
      ]
    );
  };

  const getDocumentInstructions = (type: string) => {
    switch (type) {
      case 'cnic':
        return [
          'Take clear photos of both front and back sides',
          'Ensure all text is readable',
          'Avoid glare and shadows',
          'CNIC should be valid and not expired',
        ];
      case 'drivingLicense':
        return [
          'Upload a clear photo of your driving license',
          'License should be valid for your vehicle type',
          'Ensure expiry date is visible and valid',
          'All text should be clearly readable',
        ];
      case 'vehicleRegistration':
        return [
          'Upload vehicle registration certificate',
          'Document should match your vehicle details',
          'Ensure all information is clearly visible',
          'Registration should be current and valid',
        ];
      case 'profilePhoto':
        return [
          'Take a clear photo of yourself',
          'Face should be clearly visible',
          'Good lighting with no shadows',
          'Look directly at the camera',
        ];
      case 'passport':
        return [
          'Upload a clear photo of passport main page',
          'All text should be readable',
          'Passport should be valid and not expired',
          'Ensure good lighting without glare',
        ];
      default:
        return ['Upload a clear, readable photo of the document'];
    }
  };

  return (
    <View style={{ flex: 1, backgroundColor: '#f1f5f9' }}>
      <SafeAreaView style={{ flex: 1 }}>
        {/* Enhanced Header */}
        <View style={{
          backgroundColor: '#dc2626',
          paddingHorizontal: 20,
          paddingTop: 20,
          paddingBottom: 28,
          borderBottomLeftRadius: 28,
          borderBottomRightRadius: 28,
          shadowColor: '#dc2626',
          shadowOffset: { width: 0, height: 12 },
          shadowOpacity: 0.3,
          shadowRadius: 20,
          elevation: 16,
        }}>
          <View style={{
            flexDirection: 'row',
            alignItems: 'center',
            marginBottom: 20,
          }}>
            <TouchableOpacity
              onPress={onBack}
              style={{
                width: 48,
                height: 48,
                borderRadius: 24,
                backgroundColor: 'rgba(255,255,255,0.2)',
                justifyContent: 'center',
                alignItems: 'center',
                marginRight: 16,
                borderWidth: 2,
                borderColor: 'rgba(255,255,255,0.3)',
              }}
            >
              <Ionicons name="arrow-back" size={24} color="white" />
            </TouchableOpacity>
            <View style={{ flex: 1 }}>
              <Text style={{
                fontSize: 24,
                fontWeight: 'bold',
                color: 'white',
                marginBottom: 4,
              }}>
                Upload {documentTitle}
              </Text>
              <Text style={{
                fontSize: 14,
                color: 'rgba(255,255,255,0.8)',
              }}>
                Take or select a clear photo
              </Text>
            </View>
          </View>
        </View>

        <ScrollView style={{ flex: 1, paddingHorizontal: 20, paddingTop: 24 }}>
          {/* Enhanced Instructions */}
          <View style={{
            backgroundColor: 'white',
            borderRadius: 20,
            padding: 24,
            marginBottom: 24,
            shadowColor: '#3b82f6',
            shadowOffset: { width: 0, height: 8 },
            shadowOpacity: 0.1,
            shadowRadius: 16,
            elevation: 8,
            borderWidth: 2,
            borderColor: '#dbeafe',
          }}>
            <View style={{
              flexDirection: 'row',
              alignItems: 'center',
              marginBottom: 20,
            }}>
              <View style={{
                width: 48,
                height: 48,
                backgroundColor: '#3b82f6',
                borderRadius: 24,
                justifyContent: 'center',
                alignItems: 'center',
                marginRight: 16,
                shadowColor: '#3b82f6',
                shadowOffset: { width: 0, height: 4 },
                shadowOpacity: 0.3,
                shadowRadius: 8,
                elevation: 6,
              }}>
                <Ionicons name="information-circle" size={24} color="white" />
              </View>
              <Text style={{
                fontSize: 20,
                fontWeight: 'bold',
                color: '#1e40af',
              }}>
                Upload Instructions
              </Text>
            </View>
            {getDocumentInstructions(documentType).map((instruction, index) => (
              <View key={index} style={{
                flexDirection: 'row',
                alignItems: 'flex-start',
                marginBottom: 12,
              }}>
                <View style={{
                  width: 8,
                  height: 8,
                  backgroundColor: '#dc2626',
                  borderRadius: 4,
                  marginTop: 6,
                  marginRight: 12,
                }} />
                <Text style={{
                  fontSize: 16,
                  color: '#374151',
                  lineHeight: 24,
                  flex: 1,
                }}>
                  {instruction}
                </Text>
              </View>
            ))}
          </View>

          {/* Enhanced Image Preview */}
          <View style={{
            backgroundColor: 'white',
            borderRadius: 20,
            padding: 24,
            marginBottom: 24,
            shadowColor: '#dc2626',
            shadowOffset: { width: 0, height: 8 },
            shadowOpacity: 0.1,
            shadowRadius: 16,
            elevation: 8,
            borderWidth: 1,
            borderColor: 'rgba(220, 38, 38, 0.05)',
          }}>
            <View style={{
              flexDirection: 'row',
              alignItems: 'center',
              marginBottom: 20,
            }}>
              <View style={{
                width: 48,
                height: 48,
                backgroundColor: '#dc2626',
                borderRadius: 24,
                justifyContent: 'center',
                alignItems: 'center',
                marginRight: 16,
                shadowColor: '#dc2626',
                shadowOffset: { width: 0, height: 4 },
                shadowOpacity: 0.3,
                shadowRadius: 8,
                elevation: 6,
              }}>
                <Ionicons name="document" size={24} color="white" />
              </View>
              <Text style={{
                fontSize: 20,
                fontWeight: 'bold',
                color: '#111827',
              }}>
                Document Preview
              </Text>
            </View>

            {selectedImage ? (
              <View>
                <Image
                  source={{ uri: selectedImage }}
                  style={{
                    width: '100%',
                    height: 240,
                    borderRadius: 16,
                    marginBottom: 20,
                  }}
                  resizeMode="contain"
                />
                <TouchableOpacity
                  onPress={showImageSourceOptions}
                  style={{
                    backgroundColor: '#f8fafc',
                    borderWidth: 2,
                    borderColor: '#dc2626',
                    borderStyle: 'dashed',
                    borderRadius: 16,
                    padding: 20,
                    alignItems: 'center',
                  }}
                >
                  <Ionicons name="camera" size={28} color="#dc2626" />
                  <Text style={{
                    color: '#dc2626',
                    fontWeight: '600',
                    marginTop: 8,
                    fontSize: 16,
                  }}>
                    Change Image
                  </Text>
                </TouchableOpacity>
              </View>
            ) : (
              <TouchableOpacity
                onPress={showImageSourceOptions}
                style={{
                  backgroundColor: '#f8fafc',
                  borderWidth: 3,
                  borderColor: '#dc2626',
                  borderStyle: 'dashed',
                  borderRadius: 20,
                  padding: 40,
                  alignItems: 'center',
                }}
              >
                <View style={{
                  width: 80,
                  height: 80,
                  backgroundColor: '#fef2f2',
                  borderRadius: 40,
                  justifyContent: 'center',
                  alignItems: 'center',
                  marginBottom: 20,
                }}>
                  <Ionicons name="camera" size={40} color="#dc2626" />
                </View>
                <Text style={{
                  color: '#111827',
                  fontWeight: 'bold',
                  fontSize: 18,
                  marginBottom: 8,
                }}>
                  Add Document Photo
                </Text>
                <Text style={{
                  color: '#6b7280',
                  fontSize: 16,
                  textAlign: 'center',
                  lineHeight: 24,
                }}>
                  Tap to take a photo or select from gallery
                </Text>
              </TouchableOpacity>
            )}
          </View>

          {/* Enhanced Tips */}
          <View style={{
            backgroundColor: 'white',
            borderRadius: 20,
            padding: 24,
            marginBottom: 24,
            shadowColor: '#f59e0b',
            shadowOffset: { width: 0, height: 8 },
            shadowOpacity: 0.1,
            shadowRadius: 16,
            elevation: 8,
            borderWidth: 2,
            borderColor: '#fef3c7',
          }}>
            <View style={{
              flexDirection: 'row',
              alignItems: 'center',
              marginBottom: 20,
            }}>
              <View style={{
                width: 48,
                height: 48,
                backgroundColor: '#f59e0b',
                borderRadius: 24,
                justifyContent: 'center',
                alignItems: 'center',
                marginRight: 16,
                shadowColor: '#f59e0b',
                shadowOffset: { width: 0, height: 4 },
                shadowOpacity: 0.3,
                shadowRadius: 8,
                elevation: 6,
              }}>
                <Ionicons name="bulb" size={24} color="white" />
              </View>
              <Text style={{
                fontSize: 20,
                fontWeight: 'bold',
                color: '#92400e',
              }}>
                Tips for Best Results
              </Text>
            </View>

            {[
              { emoji: '📱', text: 'Use good lighting - natural light works best' },
              { emoji: '📐', text: 'Keep the document flat and straight' },
              { emoji: '🔍', text: 'Make sure all text is clearly readable' },
              { emoji: '✨', text: 'Avoid shadows, glare, and blurry images' },
            ].map((tip, index) => (
              <View key={index} style={{
                flexDirection: 'row',
                alignItems: 'center',
                marginBottom: 12,
              }}>
                <Text style={{ fontSize: 20, marginRight: 12 }}>{tip.emoji}</Text>
                <Text style={{
                  fontSize: 16,
                  color: '#374151',
                  lineHeight: 24,
                  flex: 1,
                }}>
                  {tip.text}
                </Text>
              </View>
            ))}
          </View>
        </ScrollView>

        {/* Enhanced Bottom Actions */}
        <View style={{
          backgroundColor: 'white',
          paddingHorizontal: 20,
          paddingVertical: 24,
          borderTopWidth: 1,
          borderTopColor: '#f1f5f9',
          shadowColor: '#000',
          shadowOffset: { width: 0, height: -4 },
          shadowOpacity: 0.1,
          shadowRadius: 8,
          elevation: 8,
        }}>
          <Button
            title="Upload Document"
            onPress={handleUpload}
            disabled={!selectedImage}
            loading={isUploading}
            variant="primary"
            size="lg"
            fullWidth
            style={{ marginBottom: 16 }}
          />
          <TouchableOpacity
            onPress={onBack}
            style={{
              paddingVertical: 12,
              alignItems: 'center',
            }}
          >
            <Text style={{
              color: '#6b7280',
              fontWeight: '600',
              fontSize: 16,
            }}>
              Cancel
            </Text>
          </TouchableOpacity>
        </View>
      </SafeAreaView>
    </View>
  );
};

export default DocumentUploadScreen;
