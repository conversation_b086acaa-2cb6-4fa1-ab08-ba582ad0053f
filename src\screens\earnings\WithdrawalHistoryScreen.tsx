import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  ScrollView,
  RefreshControl,
  TouchableOpacity,
  TextInput,
  Modal,
  Alert,
  StatusBar,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import { useNavigation } from '@react-navigation/native';
import { Card, Button, Badge, LoadingSpinner } from '../../components/ui';
import { useEarnings } from '../../context/EarningsContext';
import { formatCurrency, formatDate, formatTime } from '../../utils/helpers';
import { WithdrawalStatus, PaymentMethod } from '../../types/earnings';

interface WithdrawalRecord {
  id: string;
  amount: number;
  method: PaymentMethod;
  status: WithdrawalStatus;
  date: string;
  transactionId: string;
  accountInfo: string;
  processingTime?: string;
  fee?: number;
  notes?: string;
}

const WithdrawalHistoryScreen: React.FC = () => {
  const navigation = useNavigation();
  const { state, fetchWithdrawalHistory } = useEarnings();
  
  const [refreshing, setRefreshing] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedStatus, setSelectedStatus] = useState<WithdrawalStatus | 'all'>('all');
  const [selectedMethod, setSelectedMethod] = useState<PaymentMethod | 'all'>('all');
  const [showFilters, setShowFilters] = useState(false);
  const [selectedWithdrawal, setSelectedWithdrawal] = useState<WithdrawalRecord | null>(null);
  const [showDetailModal, setShowDetailModal] = useState(false);

  // Mock data for comprehensive withdrawal history
  const mockWithdrawals: WithdrawalRecord[] = [
    {
      id: '1',
      amount: 4500,
      method: PaymentMethod.BANK_TRANSFER,
      status: WithdrawalStatus.COMPLETED,
      date: '2024-01-25',
      transactionId: 'TXN123456789',
      accountInfo: 'HBL ****1234',
      processingTime: '2-3 business days',
      fee: 75,
      notes: 'Successfully transferred to your bank account'
    },
    {
      id: '2',
      amount: 2250,
      method: PaymentMethod.JAZZCASH,
      status: WithdrawalStatus.PROCESSING,
      date: '2024-01-24',
      transactionId: 'TXN987654321',
      accountInfo: '0300-*******',
      processingTime: '1-2 hours',
      fee: 45,
      notes: 'Processing with JazzCash'
    },
    {
      id: '3',
      amount: 2200.00,
      method: PaymentMethod.EASYPAISA,
      status: WithdrawalStatus.COMPLETED,
      date: '2024-01-23',
      transactionId: 'TXN456789123',
      accountInfo: '0321-*******',
      processingTime: '30 minutes',
      fee: 20.00,
      notes: 'Instant transfer completed'
    },
    {
      id: '4',
      amount: 500.00,
      method: PaymentMethod.BANK_TRANSFER,
      status: WithdrawalStatus.FAILED,
      date: '2024-01-22',
      transactionId: 'TXN789123456',
      accountInfo: 'UBL ****5678',
      processingTime: 'Failed',
      fee: 0.00,
      notes: 'Bank account verification failed. Please update your bank details.'
    },
    {
      id: '5',
      amount: 1200.00,
      method: PaymentMethod.JAZZCASH,
      status: WithdrawalStatus.COMPLETED,
      date: '2024-01-21',
      transactionId: 'TXN321654987',
      accountInfo: '0333-*******',
      processingTime: '45 minutes',
      fee: 18.00,
      notes: 'Successfully transferred'
    },
    {
      id: '6',
      amount: 800.00,
      method: PaymentMethod.BANK_TRANSFER,
      status: WithdrawalStatus.CANCELLED,
      date: '2024-01-20',
      transactionId: 'TXN654987321',
      accountInfo: 'MCB ****9876',
      processingTime: 'Cancelled',
      fee: 0.00,
      notes: 'Cancelled by user request'
    },
    {
      id: '7',
      amount: 950.00,
      method: PaymentMethod.EASYPAISA,
      status: WithdrawalStatus.COMPLETED,
      date: '2024-01-19',
      transactionId: 'TXN147258369',
      accountInfo: '0345-*******',
      processingTime: '25 minutes',
      fee: 16.00,
      notes: 'Quick transfer completed'
    },
    {
      id: '8',
      amount: 1800.00,
      method: PaymentMethod.BANK_TRANSFER,
      status: WithdrawalStatus.PROCESSING,
      date: '2024-01-18',
      transactionId: 'TXN963852741',
      accountInfo: 'ABL ****4567',
      processingTime: '1-2 business days',
      fee: 30.00,
      notes: 'Bank transfer in progress'
    }
  ];

  useEffect(() => {
    loadWithdrawalHistory();
  }, []);

  const loadWithdrawalHistory = async () => {
    try {
      await fetchWithdrawalHistory();
    } catch (error) {
      console.error('Error loading withdrawal history:', error);
    }
  };

  const onRefresh = async () => {
    setRefreshing(true);
    await loadWithdrawalHistory();
    setRefreshing(false);
  };

  const getStatusColor = (status: WithdrawalStatus) => {
    switch (status) {
      case WithdrawalStatus.COMPLETED:
        return { color: '#10b981', bg: '#d1fae5' };
      case WithdrawalStatus.PROCESSING:
        return { color: '#f59e0b', bg: '#fef3c7' };
      case WithdrawalStatus.FAILED:
        return { color: '#ef4444', bg: '#fee2e2' };
      case WithdrawalStatus.CANCELLED:
        return { color: '#6b7280', bg: '#f3f4f6' };
      default:
        return { color: '#6b7280', bg: '#f3f4f6' };
    }
  };

  const getMethodIcon = (method: PaymentMethod) => {
    switch (method) {
      case PaymentMethod.BANK_TRANSFER:
        return { icon: 'card', color: '#3b82f6', bg: '#dbeafe' };
      case PaymentMethod.JAZZCASH:
        return { icon: 'phone-portrait', color: '#dc2626', bg: '#fee2e2' };
      case PaymentMethod.EASYPAISA:
        return { icon: 'wallet', color: '#16a34a', bg: '#dcfce7' };
      default:
        return { icon: 'card', color: '#6b7280', bg: '#f3f4f6' };
    }
  };

  const getMethodBrandName = (method: PaymentMethod) => {
    switch (method) {
      case PaymentMethod.BANK_TRANSFER:
        return 'Bank Transfer';
      case PaymentMethod.JAZZCASH:
        return 'JazzCash';
      case PaymentMethod.EASYPAISA:
        return 'EasyPaisa';
      default:
        return 'Unknown';
    }
  };

  const filteredWithdrawals = mockWithdrawals.filter(withdrawal => {
    const matchesSearch = withdrawal.transactionId.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         withdrawal.accountInfo.toLowerCase().includes(searchQuery.toLowerCase());
    const matchesStatus = selectedStatus === 'all' || withdrawal.status === selectedStatus;
    const matchesMethod = selectedMethod === 'all' || withdrawal.method === selectedMethod;
    
    return matchesSearch && matchesStatus && matchesMethod;
  });

  const totalWithdrawn = mockWithdrawals
    .filter(w => w.status === WithdrawalStatus.COMPLETED)
    .reduce((sum, w) => sum + w.amount, 0);

  const totalFees = mockWithdrawals
    .filter(w => w.status === WithdrawalStatus.COMPLETED)
    .reduce((sum, w) => sum + (w.fee || 0), 0);

  const handleWithdrawalDetail = (withdrawal: WithdrawalRecord) => {
    setSelectedWithdrawal(withdrawal);
    setShowDetailModal(true);
  };

  const handleRetryWithdrawal = (withdrawal: WithdrawalRecord) => {
    Alert.alert(
      'Retry Withdrawal',
      `Do you want to retry the withdrawal of ${formatCurrency(withdrawal.amount)}?`,
      [
        { text: 'Cancel', style: 'cancel' },
        { 
          text: 'Retry', 
          onPress: () => {
            Alert.alert('Success', 'Withdrawal request has been resubmitted for processing.');
          }
        }
      ]
    );
  };

  const renderHeader = () => (
    <View style={{
      paddingHorizontal: 20,
      paddingVertical: 16,
      backgroundColor: '#ffffff',
      borderBottomWidth: 1,
      borderBottomColor: '#e5e7eb',
      flexDirection: 'row',
      alignItems: 'center',
    }}>
      <TouchableOpacity
        onPress={() => navigation.goBack()}
        style={{ marginRight: 16 }}
      >
        <Ionicons name="arrow-back" size={24} color="#111827" />
      </TouchableOpacity>
      <Text style={{ fontSize: 20, fontWeight: 'bold', color: '#111827', flex: 1 }}>
        Withdrawal History
      </Text>
      <TouchableOpacity
        onPress={() => setShowFilters(true)}
        style={{
          padding: 8,
          borderRadius: 8,
          backgroundColor: '#f3f4f6',
        }}
      >
        <Ionicons name="filter" size={20} color="#6b7280" />
      </TouchableOpacity>
    </View>
  );

  const renderSummaryCards = () => (
    <View style={{ padding: 20, gap: 12 }}>
      <View style={{ flexDirection: 'row', gap: 12 }}>
        <Card variant="elevated" style={{ flex: 1 }} padding="md">
          <Text style={{ fontSize: 24, fontWeight: 'bold', color: '#10b981' }}>
            {formatCurrency(totalWithdrawn)}
          </Text>
          <Text style={{ fontSize: 12, color: '#6b7280', marginTop: 4 }}>
            Total Withdrawn
          </Text>
        </Card>
        
        <Card variant="elevated" style={{ flex: 1 }} padding="md">
          <Text style={{ fontSize: 24, fontWeight: 'bold', color: '#f59e0b' }}>
            {mockWithdrawals.length}
          </Text>
          <Text style={{ fontSize: 12, color: '#6b7280', marginTop: 4 }}>
            Total Requests
          </Text>
        </Card>
      </View>
      
      <View style={{ flexDirection: 'row', gap: 12 }}>
        <Card variant="elevated" style={{ flex: 1 }} padding="md">
          <Text style={{ fontSize: 24, fontWeight: 'bold', color: '#ef4444' }}>
            {formatCurrency(totalFees)}
          </Text>
          <Text style={{ fontSize: 12, color: '#6b7280', marginTop: 4 }}>
            Total Fees
          </Text>
        </Card>
        
        <Card variant="elevated" style={{ flex: 1 }} padding="md">
          <Text style={{ fontSize: 24, fontWeight: 'bold', color: '#3b82f6' }}>
            {mockWithdrawals.filter(w => w.status === WithdrawalStatus.PROCESSING).length}
          </Text>
          <Text style={{ fontSize: 12, color: '#6b7280', marginTop: 4 }}>
            Processing
          </Text>
        </Card>
      </View>
    </View>
  );

  if (state.isLoading && !refreshing) {
    return (
      <SafeAreaView style={{ flex: 1, backgroundColor: '#f8fafc' }}>
        <LoadingSpinner message="Loading withdrawal history..." />
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={{ flex: 1, backgroundColor: '#f8fafc' }}>
      <StatusBar barStyle="dark-content" backgroundColor="#ffffff" />
      {renderHeader()}
      {renderSummaryCards()}
      
      {/* Search Bar */}
      <View style={{ paddingHorizontal: 20, marginBottom: 16 }}>
        <View style={{
          flexDirection: 'row',
          alignItems: 'center',
          backgroundColor: '#ffffff',
          borderRadius: 12,
          paddingHorizontal: 16,
          paddingVertical: 12,
          borderWidth: 1,
          borderColor: '#e5e7eb',
        }}>
          <Ionicons name="search" size={20} color="#6b7280" style={{ marginRight: 12 }} />
          <TextInput
            placeholder="Search by transaction ID or account..."
            value={searchQuery}
            onChangeText={setSearchQuery}
            style={{
              flex: 1,
              fontSize: 16,
              color: '#111827',
            }}
            placeholderTextColor="#9ca3af"
          />
        </View>
      </View>

      <ScrollView
        style={{ flex: 1 }}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
        showsVerticalScrollIndicator={false}
        contentContainerStyle={{ paddingBottom: 100 }}
      >
        {/* Withdrawal List */}
        <Card variant="elevated" margin="md" padding="lg">
          <Text style={{ fontSize: 18, fontWeight: 'bold', color: '#111827', marginBottom: 16 }}>
            Recent Withdrawals ({filteredWithdrawals.length})
          </Text>

          {filteredWithdrawals.length === 0 ? (
            <View style={{ alignItems: 'center', paddingVertical: 40 }}>
              <Ionicons name="document-text-outline" size={48} color="#d1d5db" />
              <Text style={{ fontSize: 16, color: '#6b7280', marginTop: 8 }}>
                No withdrawals found
              </Text>
              <Text style={{ fontSize: 14, color: '#9ca3af', textAlign: 'center', marginTop: 4 }}>
                Try adjusting your search or filter criteria
              </Text>
            </View>
          ) : (
            <View style={{ gap: 0 }}>
              {filteredWithdrawals.map((withdrawal, index) => {
                const methodStyle = getMethodIcon(withdrawal.method);
                const statusStyle = getStatusColor(withdrawal.status);

                return (
                  <TouchableOpacity
                    key={withdrawal.id}
                    onPress={() => handleWithdrawalDetail(withdrawal)}
                    style={{
                      flexDirection: 'row',
                      alignItems: 'center',
                      paddingVertical: 16,
                      borderBottomWidth: index < filteredWithdrawals.length - 1 ? 1 : 0,
                      borderBottomColor: '#f3f4f6',
                    }}
                  >
                    {/* Payment Method Icon */}
                    <View style={{
                      width: 48,
                      height: 48,
                      borderRadius: 24,
                      backgroundColor: methodStyle.bg,
                      alignItems: 'center',
                      justifyContent: 'center',
                      marginRight: 12,
                    }}>
                      <Ionicons name={methodStyle.icon as any} size={24} color={methodStyle.color} />
                    </View>

                    {/* Transaction Details */}
                    <View style={{ flex: 1 }}>
                      <View style={{
                        flexDirection: 'row',
                        alignItems: 'center',
                        marginBottom: 4,
                      }}>
                        <Text style={{
                          fontSize: 16,
                          fontWeight: '600',
                          color: '#111827',
                          marginRight: 8,
                        }}>
                          {getMethodBrandName(withdrawal.method)}
                        </Text>
                        <Badge
                          text={withdrawal.status}
                          style={{
                            backgroundColor: statusStyle.bg,
                            color: statusStyle.color,
                          }}
                        />
                      </View>

                      <Text style={{
                        fontSize: 14,
                        color: '#6b7280',
                        marginBottom: 2,
                      }}>
                        {withdrawal.accountInfo}
                      </Text>

                      <Text style={{
                        fontSize: 12,
                        color: '#9ca3af',
                      }}>
                        {formatDate(withdrawal.date)} • {withdrawal.transactionId}
                      </Text>
                    </View>

                    {/* Amount and Action */}
                    <View style={{ alignItems: 'flex-end' }}>
                      <Text style={{
                        fontSize: 18,
                        fontWeight: 'bold',
                        color: withdrawal.status === WithdrawalStatus.FAILED ? '#ef4444' : '#111827',
                        marginBottom: 4,
                      }}>
                        {withdrawal.status === WithdrawalStatus.FAILED ? '-' : ''}{formatCurrency(withdrawal.amount)}
                      </Text>
                      
                      {withdrawal.status === WithdrawalStatus.FAILED && (
                        <TouchableOpacity
                          onPress={() => handleRetryWithdrawal(withdrawal)}
                          style={{
                            paddingHorizontal: 8,
                            paddingVertical: 4,
                            backgroundColor: '#f97316',
                            borderRadius: 6,
                          }}
                        >
                          <Text style={{ fontSize: 12, color: 'white', fontWeight: '600' }}>
                            Retry
                          </Text>
                        </TouchableOpacity>
                      )}
                    </View>
                  </TouchableOpacity>
                );
              })}
            </View>
          )}
        </Card>
      </ScrollView>

      {/* Filter Modal */}
      <Modal
        visible={showFilters}
        animationType="slide"
        presentationStyle="pageSheet"
        onRequestClose={() => setShowFilters(false)}
      >
        <SafeAreaView style={{ flex: 1, backgroundColor: '#f8fafc' }}>
          <View style={{
            paddingHorizontal: 20,
            paddingVertical: 16,
            backgroundColor: '#ffffff',
            borderBottomWidth: 1,
            borderBottomColor: '#e5e7eb',
            flexDirection: 'row',
            alignItems: 'center',
            justifyContent: 'space-between',
          }}>
            <Text style={{ fontSize: 20, fontWeight: 'bold', color: '#111827' }}>
              Filter Withdrawals
            </Text>
            <TouchableOpacity onPress={() => setShowFilters(false)}>
              <Ionicons name="close" size={24} color="#111827" />
            </TouchableOpacity>
          </View>

          <ScrollView style={{ flex: 1, padding: 20 }}>
            {/* Status Filter */}
            <Card variant="elevated" padding="lg" style={{ marginBottom: 20 }}>
              <Text style={{ fontSize: 16, fontWeight: '600', color: '#111827', marginBottom: 12 }}>
                Status
              </Text>
              <View style={{ gap: 8 }}>
                {['all', WithdrawalStatus.COMPLETED, WithdrawalStatus.PROCESSING, WithdrawalStatus.FAILED, WithdrawalStatus.CANCELLED].map((status) => (
                  <TouchableOpacity
                    key={status}
                    onPress={() => setSelectedStatus(status as WithdrawalStatus | 'all')}
                    style={{
                      flexDirection: 'row',
                      alignItems: 'center',
                      padding: 12,
                      borderRadius: 8,
                      backgroundColor: selectedStatus === status ? '#fef3e2' : '#f9fafb',
                      borderWidth: 1,
                      borderColor: selectedStatus === status ? '#f97316' : '#e5e7eb',
                    }}
                  >
                    <View style={{
                      width: 20,
                      height: 20,
                      borderRadius: 10,
                      borderWidth: 2,
                      borderColor: selectedStatus === status ? '#f97316' : '#d1d5db',
                      backgroundColor: selectedStatus === status ? '#f97316' : 'transparent',
                      alignItems: 'center',
                      justifyContent: 'center',
                      marginRight: 12,
                    }}>
                      {selectedStatus === status && (
                        <Ionicons name="checkmark" size={12} color="white" />
                      )}
                    </View>
                    <Text style={{
                      fontSize: 16,
                      color: '#111827',
                      textTransform: 'capitalize',
                    }}>
                      {status === 'all' ? 'All Status' : status}
                    </Text>
                  </TouchableOpacity>
                ))}
              </View>
            </Card>

            {/* Payment Method Filter */}
            <Card variant="elevated" padding="lg" style={{ marginBottom: 20 }}>
              <Text style={{ fontSize: 16, fontWeight: '600', color: '#111827', marginBottom: 12 }}>
                Payment Method
              </Text>
              <View style={{ gap: 8 }}>
                {['all', PaymentMethod.BANK_TRANSFER, PaymentMethod.JAZZCASH, PaymentMethod.EASYPAISA].map((method) => (
                  <TouchableOpacity
                    key={method}
                    onPress={() => setSelectedMethod(method as PaymentMethod | 'all')}
                    style={{
                      flexDirection: 'row',
                      alignItems: 'center',
                      padding: 12,
                      borderRadius: 8,
                      backgroundColor: selectedMethod === method ? '#fef3e2' : '#f9fafb',
                      borderWidth: 1,
                      borderColor: selectedMethod === method ? '#f97316' : '#e5e7eb',
                    }}
                  >
                    <View style={{
                      width: 20,
                      height: 20,
                      borderRadius: 10,
                      borderWidth: 2,
                      borderColor: selectedMethod === method ? '#f97316' : '#d1d5db',
                      backgroundColor: selectedMethod === method ? '#f97316' : 'transparent',
                      alignItems: 'center',
                      justifyContent: 'center',
                      marginRight: 12,
                    }}>
                      {selectedMethod === method && (
                        <Ionicons name="checkmark" size={12} color="white" />
                      )}
                    </View>
                    <Text style={{
                      fontSize: 16,
                      color: '#111827',
                    }}>
                      {method === 'all' ? 'All Methods' : getMethodBrandName(method as PaymentMethod)}
                    </Text>
                  </TouchableOpacity>
                ))}
              </View>
            </Card>

            <View style={{ flexDirection: 'row', gap: 12 }}>
              <Button
                title="Clear Filters"
                onPress={() => {
                  setSelectedStatus('all');
                  setSelectedMethod('all');
                  setSearchQuery('');
                }}
                variant="outline"
                style={{ flex: 1 }}
              />
              <Button
                title="Apply Filters"
                onPress={() => setShowFilters(false)}
                style={{ flex: 1 }}
              />
            </View>
          </ScrollView>
        </SafeAreaView>
      </Modal>

      {/* Detail Modal */}
      <Modal
        visible={showDetailModal}
        animationType="slide"
        presentationStyle="pageSheet"
        onRequestClose={() => setShowDetailModal(false)}
      >
        <SafeAreaView style={{ flex: 1, backgroundColor: '#f8fafc' }}>
          <View style={{
            paddingHorizontal: 20,
            paddingVertical: 16,
            backgroundColor: '#ffffff',
            borderBottomWidth: 1,
            borderBottomColor: '#e5e7eb',
            flexDirection: 'row',
            alignItems: 'center',
            justifyContent: 'space-between',
          }}>
            <Text style={{ fontSize: 20, fontWeight: 'bold', color: '#111827' }}>
              Withdrawal Details
            </Text>
            <TouchableOpacity onPress={() => setShowDetailModal(false)}>
              <Ionicons name="close" size={24} color="#111827" />
            </TouchableOpacity>
          </View>

          {selectedWithdrawal && (
            <ScrollView style={{ flex: 1, padding: 20 }}>
              {/* Amount Card */}
              <Card variant="elevated" padding="lg" style={{ marginBottom: 20 }}>
                <View style={{ alignItems: 'center', paddingVertical: 20 }}>
                  <Text style={{ fontSize: 32, fontWeight: 'bold', color: '#111827', marginBottom: 8 }}>
                    {formatCurrency(selectedWithdrawal.amount)}
                  </Text>
                  <Badge
                    text={selectedWithdrawal.status}
                    style={{
                      backgroundColor: getStatusColor(selectedWithdrawal.status).bg,
                      color: getStatusColor(selectedWithdrawal.status).color,
                    }}
                  />
                </View>
              </Card>

              {/* Transaction Details */}
              <Card variant="elevated" padding="lg" style={{ marginBottom: 20 }}>
                <Text style={{ fontSize: 18, fontWeight: 'bold', color: '#111827', marginBottom: 16 }}>
                  Transaction Details
                </Text>

                <View style={{ gap: 12 }}>
                  <View style={{ flexDirection: 'row', justifyContent: 'space-between' }}>
                    <Text style={{ fontSize: 14, color: '#6b7280' }}>Transaction ID</Text>
                    <Text style={{ fontSize: 14, fontWeight: '600', color: '#111827' }}>
                      {selectedWithdrawal.transactionId}
                    </Text>
                  </View>

                  <View style={{ flexDirection: 'row', justifyContent: 'space-between' }}>
                    <Text style={{ fontSize: 14, color: '#6b7280' }}>Date & Time</Text>
                    <Text style={{ fontSize: 14, fontWeight: '600', color: '#111827' }}>
                      {formatDate(selectedWithdrawal.date)}
                    </Text>
                  </View>

                  <View style={{ flexDirection: 'row', justifyContent: 'space-between' }}>
                    <Text style={{ fontSize: 14, color: '#6b7280' }}>Payment Method</Text>
                    <Text style={{ fontSize: 14, fontWeight: '600', color: '#111827' }}>
                      {getMethodBrandName(selectedWithdrawal.method)}
                    </Text>
                  </View>

                  <View style={{ flexDirection: 'row', justifyContent: 'space-between' }}>
                    <Text style={{ fontSize: 14, color: '#6b7280' }}>Account Info</Text>
                    <Text style={{ fontSize: 14, fontWeight: '600', color: '#111827' }}>
                      {selectedWithdrawal.accountInfo}
                    </Text>
                  </View>

                  <View style={{ flexDirection: 'row', justifyContent: 'space-between' }}>
                    <Text style={{ fontSize: 14, color: '#6b7280' }}>Processing Time</Text>
                    <Text style={{ fontSize: 14, fontWeight: '600', color: '#111827' }}>
                      {selectedWithdrawal.processingTime}
                    </Text>
                  </View>

                  {selectedWithdrawal.fee && selectedWithdrawal.fee > 0 && (
                    <View style={{ flexDirection: 'row', justifyContent: 'space-between' }}>
                      <Text style={{ fontSize: 14, color: '#6b7280' }}>Transaction Fee</Text>
                      <Text style={{ fontSize: 14, fontWeight: '600', color: '#ef4444' }}>
                        {formatCurrency(selectedWithdrawal.fee)}
                      </Text>
                    </View>
                  )}
                </View>
              </Card>

              {/* Notes */}
              {selectedWithdrawal.notes && (
                <Card variant="elevated" padding="lg" style={{ marginBottom: 20 }}>
                  <Text style={{ fontSize: 18, fontWeight: 'bold', color: '#111827', marginBottom: 12 }}>
                    Notes
                  </Text>
                  <Text style={{ fontSize: 14, color: '#6b7280', lineHeight: 20 }}>
                    {selectedWithdrawal.notes}
                  </Text>
                </Card>
              )}

              {/* Action Buttons */}
              <View style={{ gap: 12, marginBottom: 20 }}>
                {selectedWithdrawal.status === WithdrawalStatus.FAILED && (
                  <Button
                    title="Retry Withdrawal"
                    onPress={() => {
                      setShowDetailModal(false);
                      handleRetryWithdrawal(selectedWithdrawal);
                    }}
                    fullWidth
                  />
                )}

                <Button
                  title="Contact Support"
                  variant="outline"
                  onPress={() => {
                    Alert.alert(
                      'Contact Support',
                      'Our support team will help you with your withdrawal inquiry.',
                      [
                        { text: 'Cancel', style: 'cancel' },
                        { text: 'Call Support', onPress: () => {} },
                        { text: 'Live Chat', onPress: () => {} }
                      ]
                    );
                  }}
                  fullWidth
                />
              </View>
            </ScrollView>
          )}
        </SafeAreaView>
      </Modal>
    </SafeAreaView>
  );
};

export default WithdrawalHistoryScreen;
