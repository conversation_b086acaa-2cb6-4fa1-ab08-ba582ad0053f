import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  ScrollView,
  TextInput,
  Alert,
  Image,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import { useNavigation } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import * as ImagePicker from 'expo-image-picker';
import * as Location from 'expo-location';
import { SafetyStackParamList } from '../../types';
import {
  IncidentType,
  IncidentSeverity,
  IncidentReport,
} from '../../types/safety';

type IncidentReportScreenNavigationProp = StackNavigationProp<SafetyStackParamList, 'IncidentReport'>;

const IncidentReportScreen: React.FC = () => {
  const navigation = useNavigation<IncidentReportScreenNavigationProp>();
  const [selectedType, setSelectedType] = useState<IncidentType | null>(null);
  const [selectedSeverity, setSeverity] = useState<IncidentSeverity>(IncidentSeverity.MEDIUM);
  const [title, setTitle] = useState('');
  const [description, setDescription] = useState('');
  const [currentLocation, setCurrentLocation] = useState<any>(null);
  const [photos, setPhotos] = useState<string[]>([]);
  const [damages, setDamages] = useState({
    vehicleDamage: false,
    personalInjury: false,
    propertyDamage: false,
    foodDamage: false,
  });
  const [policeReport, setPoliceReport] = useState({
    filed: false,
    reportNumber: '',
    officerName: '',
    stationName: '',
  });
  const [loading, setLoading] = useState(false);

  const incidentTypes = [
    {
      type: IncidentType.TRAFFIC_ACCIDENT,
      title: 'Traffic Accident',
      description: 'Vehicle collision or traffic-related incident',
      icon: 'car-sport',
      color: '#dc2626',
    },
    {
      type: IncidentType.VEHICLE_BREAKDOWN,
      title: 'Vehicle Breakdown',
      description: 'Mechanical failure or vehicle malfunction',
      icon: 'construct',
      color: '#f59e0b',
    },
    {
      type: IncidentType.THEFT_ROBBERY,
      title: 'Theft/Robbery',
      description: 'Theft of vehicle, food, or personal items',
      icon: 'shield-outline',
      color: '#dc2626',
    },
    {
      type: IncidentType.CUSTOMER_INCIDENT,
      title: 'Customer Incident',
      description: 'Issues with customer behavior or interaction',
      icon: 'person-outline',
      color: '#dc2626',
    },
    {
      type: IncidentType.FOOD_SPILLAGE,
      title: 'Food Spillage',
      description: 'Food spilled or damaged during delivery',
      icon: 'fast-food-outline',
      color: '#f59e0b',
    },
    {
      type: IncidentType.HARASSMENT,
      title: 'Harassment',
      description: 'Harassment or inappropriate behavior',
      icon: 'warning',
      color: '#dc2626',
    },
    {
      type: IncidentType.PROPERTY_DAMAGE,
      title: 'Property Damage',
      description: 'Damage to property during delivery',
      icon: 'home-outline',
      color: '#dc2626',
    },
    {
      type: IncidentType.INJURY,
      title: 'Personal Injury',
      description: 'Injury sustained during delivery',
      icon: 'medical',
      color: '#dc2626',
    },
    {
      type: IncidentType.WEATHER_RELATED,
      title: 'Weather Related',
      description: 'Weather-related delivery issues',
      icon: 'rainy',
      color: '#0891b2',
    },
    {
      type: IncidentType.OTHER,
      title: 'Other',
      description: 'Other incident not listed above',
      icon: 'ellipsis-horizontal',
      color: '#6b7280',
    },
  ];

  const severityLevels = [
    {
      level: IncidentSeverity.LOW,
      title: 'Low',
      description: 'Minor issue, no immediate action required',
      color: '#10b981',
    },
    {
      level: IncidentSeverity.MEDIUM,
      title: 'Medium',
      description: 'Moderate issue, requires attention',
      color: '#f59e0b',
    },
    {
      level: IncidentSeverity.HIGH,
      title: 'High',
      description: 'Serious issue, immediate attention needed',
      color: '#ea580c',
    },
    {
      level: IncidentSeverity.CRITICAL,
      title: 'Critical',
      description: 'Emergency situation, urgent response required',
      color: '#dc2626',
    },
  ];

  useEffect(() => {
    getCurrentLocation();
  }, []);

  const getCurrentLocation = async () => {
    try {
      const { status } = await Location.requestForegroundPermissionsAsync();
      if (status !== 'granted') {
        Alert.alert('Permission Denied', 'Location permission is required for incident reporting.');
        return;
      }

      const location = await Location.getCurrentPositionAsync({});
      const address = await Location.reverseGeocodeAsync({
        latitude: location.coords.latitude,
        longitude: location.coords.longitude,
      });

      setCurrentLocation({
        latitude: location.coords.latitude,
        longitude: location.coords.longitude,
        address: address[0] ? `${address[0].street}, ${address[0].city}` : 'Unknown location',
      });
    } catch (error) {
      console.error('Error getting location:', error);
    }
  };

  const pickImage = async () => {
    try {
      const { status } = await ImagePicker.requestMediaLibraryPermissionsAsync();
      if (status !== 'granted') {
        Alert.alert('Permission Denied', 'Camera roll permission is required to add photos.');
        return;
      }

      const result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: ImagePicker.MediaTypeOptions.Images,
        allowsEditing: true,
        aspect: [4, 3],
        quality: 0.8,
      });

      if (!result.canceled && result.assets[0]) {
        setPhotos([...photos, result.assets[0].uri]);
      }
    } catch (error) {
      console.error('Error picking image:', error);
      Alert.alert('Error', 'Failed to pick image');
    }
  };

  const takePhoto = async () => {
    try {
      const { status } = await ImagePicker.requestCameraPermissionsAsync();
      if (status !== 'granted') {
        Alert.alert('Permission Denied', 'Camera permission is required to take photos.');
        return;
      }

      const result = await ImagePicker.launchCameraAsync({
        allowsEditing: true,
        aspect: [4, 3],
        quality: 0.8,
      });

      if (!result.canceled && result.assets[0]) {
        setPhotos([...photos, result.assets[0].uri]);
      }
    } catch (error) {
      console.error('Error taking photo:', error);
      Alert.alert('Error', 'Failed to take photo');
    }
  };

  const removePhoto = (index: number) => {
    const newPhotos = photos.filter((_, i) => i !== index);
    setPhotos(newPhotos);
  };

  const submitReport = async () => {
    if (!selectedType) {
      Alert.alert('Missing Information', 'Please select an incident type.');
      return;
    }

    if (!title.trim()) {
      Alert.alert('Missing Information', 'Please provide a title for the incident.');
      return;
    }

    if (!description.trim()) {
      Alert.alert('Missing Information', 'Please provide a description of the incident.');
      return;
    }

    setLoading(true);

    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 2000));

      const report: Omit<IncidentReport, 'id' | 'reportedAt' | 'updatedAt' | 'status'> = {
        riderId: 'rider-1',
        type: selectedType,
        severity: selectedSeverity,
        title: title.trim(),
        description: description.trim(),
        location: currentLocation || {
          latitude: 0,
          longitude: 0,
          address: 'Unknown location',
        },
        dateTime: new Date().toISOString(),
        evidence: {
          photos,
        },
        involvedParties: {},
        damages,
        policeReport,
        insurance: {
          claimFiled: false,
        },
        followUpRequired: selectedSeverity === IncidentSeverity.HIGH || selectedSeverity === IncidentSeverity.CRITICAL,
      };

      Alert.alert(
        'Report Submitted',
        'Your incident report has been submitted successfully. Our team will review it and contact you if needed.',
        [
          {
            text: 'OK',
            onPress: () => navigation.goBack(),
          },
        ]
      );
    } catch (error) {
      console.error('Error submitting report:', error);
      Alert.alert('Error', 'Failed to submit incident report. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const renderIncidentType = (incidentType: typeof incidentTypes[0]) => (
    <TouchableOpacity
      key={incidentType.type}
      onPress={() => setSelectedType(incidentType.type)}
      style={{
        backgroundColor: selectedType === incidentType.type ? `${incidentType.color}15` : 'white',
        marginHorizontal: 16,
        marginVertical: 6,
        borderRadius: 12,
        padding: 16,
        flexDirection: 'row',
        alignItems: 'center',
        borderWidth: 2,
        borderColor: selectedType === incidentType.type ? incidentType.color : '#e5e7eb',
      }}
    >
      <View style={{
        width: 40,
        height: 40,
        backgroundColor: `${incidentType.color}15`,
        borderRadius: 20,
        alignItems: 'center',
        justifyContent: 'center',
        marginRight: 12,
      }}>
        <Ionicons
          name={incidentType.icon as any}
          size={20}
          color={incidentType.color}
        />
      </View>
      
      <View style={{ flex: 1 }}>
        <Text style={{
          fontSize: 16,
          fontWeight: '600',
          color: '#1f2937',
          marginBottom: 2,
        }}>
          {incidentType.title}
        </Text>
        <Text style={{
          fontSize: 14,
          color: '#6b7280',
          lineHeight: 18,
        }}>
          {incidentType.description}
        </Text>
      </View>
      
      {selectedType === incidentType.type && (
        <Ionicons
          name="checkmark-circle"
          size={24}
          color={incidentType.color}
        />
      )}
    </TouchableOpacity>
  );

  const renderSeverityLevel = (severity: typeof severityLevels[0]) => (
    <TouchableOpacity
      key={severity.level}
      onPress={() => setSeverity(severity.level)}
      style={{
        backgroundColor: selectedSeverity === severity.level ? `${severity.color}15` : 'white',
        marginRight: 12,
        borderRadius: 8,
        padding: 12,
        borderWidth: 2,
        borderColor: selectedSeverity === severity.level ? severity.color : '#e5e7eb',
        minWidth: 100,
        alignItems: 'center',
      }}
    >
      <Text style={{
        fontSize: 14,
        fontWeight: '600',
        color: selectedSeverity === severity.level ? severity.color : '#1f2937',
        marginBottom: 4,
      }}>
        {severity.title}
      </Text>
      <Text style={{
        fontSize: 12,
        color: '#6b7280',
        textAlign: 'center',
        lineHeight: 16,
      }}>
        {severity.description}
      </Text>
    </TouchableOpacity>
  );

  const renderDamageOption = (key: keyof typeof damages, label: string) => (
    <TouchableOpacity
      key={key}
      onPress={() => setDamages({ ...damages, [key]: !damages[key] })}
      style={{
        flexDirection: 'row',
        alignItems: 'center',
        backgroundColor: 'white',
        marginHorizontal: 16,
        marginVertical: 4,
        borderRadius: 8,
        padding: 12,
        borderWidth: 1,
        borderColor: '#e5e7eb',
      }}
    >
      <View style={{
        width: 20,
        height: 20,
        borderRadius: 4,
        borderWidth: 2,
        borderColor: damages[key] ? '#10b981' : '#d1d5db',
        backgroundColor: damages[key] ? '#10b981' : 'transparent',
        alignItems: 'center',
        justifyContent: 'center',
        marginRight: 12,
      }}>
        {damages[key] && (
          <Ionicons name="checkmark" size={12} color="white" />
        )}
      </View>
      <Text style={{
        fontSize: 14,
        color: '#1f2937',
        flex: 1,
      }}>
        {label}
      </Text>
    </TouchableOpacity>
  );

  return (
    <SafeAreaView style={{ flex: 1, backgroundColor: '#f9fafb' }}>
      {/* Header */}
      <View style={{
        flexDirection: 'row',
        alignItems: 'center',
        paddingHorizontal: 16,
        paddingVertical: 12,
        backgroundColor: 'white',
        borderBottomWidth: 1,
        borderBottomColor: '#e5e7eb',
      }}>
        <TouchableOpacity
          onPress={() => navigation.goBack()}
          style={{
            width: 40,
            height: 40,
            alignItems: 'center',
            justifyContent: 'center',
            marginRight: 8,
          }}
        >
          <Ionicons name="arrow-back" size={24} color="#374151" />
        </TouchableOpacity>
        
        <Text style={{
          fontSize: 18,
          fontWeight: '600',
          color: '#1f2937',
          flex: 1,
        }}>
          Report Incident
        </Text>
        
        <TouchableOpacity
          onPress={submitReport}
          disabled={loading || !selectedType || !title.trim() || !description.trim()}
          style={{
            backgroundColor: (!selectedType || !title.trim() || !description.trim()) ? '#d1d5db' : '#10b981',
            paddingHorizontal: 16,
            paddingVertical: 8,
            borderRadius: 8,
          }}
        >
          <Text style={{
            color: 'white',
            fontWeight: '600',
            fontSize: 14,
          }}>
            {loading ? 'Submitting...' : 'Submit'}
          </Text>
        </TouchableOpacity>
      </View>

      <ScrollView style={{ flex: 1 }}>
        {/* Current Location */}
        {currentLocation && (
          <View style={{
            backgroundColor: 'white',
            marginHorizontal: 16,
            marginTop: 16,
            borderRadius: 12,
            padding: 16,
            borderWidth: 1,
            borderColor: '#e5e7eb',
          }}>
            <View style={{
              flexDirection: 'row',
              alignItems: 'center',
              marginBottom: 8,
            }}>
              <Ionicons name="location" size={20} color="#10b981" />
              <Text style={{
                fontSize: 16,
                fontWeight: '600',
                color: '#1f2937',
                marginLeft: 8,
              }}>
                Incident Location
              </Text>
            </View>
            <Text style={{
              fontSize: 14,
              color: '#6b7280',
              lineHeight: 20,
            }}>
              {currentLocation.address}
            </Text>
          </View>
        )}

        {/* Incident Type Selection */}
        <View style={{ marginTop: 24 }}>
          <Text style={{
            fontSize: 18,
            fontWeight: '600',
            color: '#1f2937',
            paddingHorizontal: 16,
            marginBottom: 12,
          }}>
            Incident Type *
          </Text>
          
          {incidentTypes.map(incidentType => renderIncidentType(incidentType))}
        </View>

        {/* Severity Level */}
        <View style={{ marginTop: 24 }}>
          <Text style={{
            fontSize: 18,
            fontWeight: '600',
            color: '#1f2937',
            paddingHorizontal: 16,
            marginBottom: 12,
          }}>
            Severity Level
          </Text>
          
          <ScrollView
            horizontal
            showsHorizontalScrollIndicator={false}
            style={{ paddingLeft: 16 }}
          >
            {severityLevels.map(severity => renderSeverityLevel(severity))}
          </ScrollView>
        </View>

        {/* Title */}
        <View style={{ marginTop: 24, paddingHorizontal: 16 }}>
          <Text style={{
            fontSize: 16,
            fontWeight: '600',
            color: '#1f2937',
            marginBottom: 8,
          }}>
            Incident Title *
          </Text>
          <TextInput
            value={title}
            onChangeText={setTitle}
            placeholder="Brief title describing the incident"
            style={{
              backgroundColor: 'white',
              borderWidth: 1,
              borderColor: '#e5e7eb',
              borderRadius: 8,
              padding: 12,
              fontSize: 14,
              color: '#1f2937',
            }}
            maxLength={100}
          />
        </View>

        {/* Description */}
        <View style={{ marginTop: 16, paddingHorizontal: 16 }}>
          <Text style={{
            fontSize: 16,
            fontWeight: '600',
            color: '#1f2937',
            marginBottom: 8,
          }}>
            Detailed Description *
          </Text>
          <TextInput
            value={description}
            onChangeText={setDescription}
            placeholder="Provide detailed information about what happened, when it occurred, and any other relevant details..."
            multiline
            numberOfLines={6}
            style={{
              backgroundColor: 'white',
              borderWidth: 1,
              borderColor: '#e5e7eb',
              borderRadius: 8,
              padding: 12,
              fontSize: 14,
              color: '#1f2937',
              textAlignVertical: 'top',
              minHeight: 120,
            }}
            maxLength={1000}
          />
          <Text style={{
            fontSize: 12,
            color: '#6b7280',
            textAlign: 'right',
            marginTop: 4,
          }}>
            {description.length}/1000
          </Text>
        </View>

        {/* Photo Evidence */}
        <View style={{ marginTop: 24 }}>
          <Text style={{
            fontSize: 16,
            fontWeight: '600',
            color: '#1f2937',
            paddingHorizontal: 16,
            marginBottom: 12,
          }}>
            Photo Evidence
          </Text>
          
          <View style={{
            flexDirection: 'row',
            paddingHorizontal: 16,
            marginBottom: 12,
          }}>
            <TouchableOpacity
              onPress={takePhoto}
              style={{
                backgroundColor: '#3b82f6',
                paddingHorizontal: 16,
                paddingVertical: 8,
                borderRadius: 8,
                marginRight: 12,
                flexDirection: 'row',
                alignItems: 'center',
              }}
            >
              <Ionicons name="camera" size={16} color="white" />
              <Text style={{
                color: 'white',
                fontWeight: '500',
                fontSize: 14,
                marginLeft: 6,
              }}>
                Take Photo
              </Text>
            </TouchableOpacity>
            
            <TouchableOpacity
              onPress={pickImage}
              style={{
                backgroundColor: '#10b981',
                paddingHorizontal: 16,
                paddingVertical: 8,
                borderRadius: 8,
                flexDirection: 'row',
                alignItems: 'center',
              }}
            >
              <Ionicons name="images" size={16} color="white" />
              <Text style={{
                color: 'white',
                fontWeight: '500',
                fontSize: 14,
                marginLeft: 6,
              }}>
                Choose Photo
              </Text>
            </TouchableOpacity>
          </View>
          
          {photos.length > 0 && (
            <ScrollView
              horizontal
              showsHorizontalScrollIndicator={false}
              style={{ paddingLeft: 16 }}
            >
              {photos.map((photo, index) => (
                <View key={index} style={{ marginRight: 12, position: 'relative' }}>
                  <Image
                    source={{ uri: photo }}
                    style={{
                      width: 100,
                      height: 100,
                      borderRadius: 8,
                    }}
                  />
                  <TouchableOpacity
                    onPress={() => removePhoto(index)}
                    style={{
                      position: 'absolute',
                      top: -8,
                      right: -8,
                      backgroundColor: '#dc2626',
                      borderRadius: 12,
                      width: 24,
                      height: 24,
                      alignItems: 'center',
                      justifyContent: 'center',
                    }}
                  >
                    <Ionicons name="close" size={16} color="white" />
                  </TouchableOpacity>
                </View>
              ))}
            </ScrollView>
          )}
        </View>

        {/* Damages */}
        <View style={{ marginTop: 24 }}>
          <Text style={{
            fontSize: 16,
            fontWeight: '600',
            color: '#1f2937',
            paddingHorizontal: 16,
            marginBottom: 12,
          }}>
            Damages (if any)
          </Text>
          
          {renderDamageOption('vehicleDamage', 'Vehicle Damage')}
          {renderDamageOption('personalInjury', 'Personal Injury')}
          {renderDamageOption('propertyDamage', 'Property Damage')}
          {renderDamageOption('foodDamage', 'Food/Order Damage')}
        </View>

        {/* Police Report */}
        <View style={{ marginTop: 24, paddingHorizontal: 16 }}>
          <Text style={{
            fontSize: 16,
            fontWeight: '600',
            color: '#1f2937',
            marginBottom: 12,
          }}>
            Police Report
          </Text>
          
          <TouchableOpacity
            onPress={() => setPoliceReport({ ...policeReport, filed: !policeReport.filed })}
            style={{
              flexDirection: 'row',
              alignItems: 'center',
              backgroundColor: 'white',
              borderRadius: 8,
              padding: 12,
              borderWidth: 1,
              borderColor: '#e5e7eb',
              marginBottom: 12,
            }}
          >
            <View style={{
              width: 20,
              height: 20,
              borderRadius: 4,
              borderWidth: 2,
              borderColor: policeReport.filed ? '#10b981' : '#d1d5db',
              backgroundColor: policeReport.filed ? '#10b981' : 'transparent',
              alignItems: 'center',
              justifyContent: 'center',
              marginRight: 12,
            }}>
              {policeReport.filed && (
                <Ionicons name="checkmark" size={12} color="white" />
              )}
            </View>
            <Text style={{
              fontSize: 14,
              color: '#1f2937',
              flex: 1,
            }}>
              Police report filed
            </Text>
          </TouchableOpacity>
          
          {policeReport.filed && (
            <>
              <TextInput
                value={policeReport.reportNumber}
                onChangeText={(text) => setPoliceReport({ ...policeReport, reportNumber: text })}
                placeholder="Police report number"
                style={{
                  backgroundColor: 'white',
                  borderWidth: 1,
                  borderColor: '#e5e7eb',
                  borderRadius: 8,
                  padding: 12,
                  fontSize: 14,
                  color: '#1f2937',
                  marginBottom: 8,
                }}
              />
              <TextInput
                value={policeReport.officerName}
                onChangeText={(text) => setPoliceReport({ ...policeReport, officerName: text })}
                placeholder="Officer name"
                style={{
                  backgroundColor: 'white',
                  borderWidth: 1,
                  borderColor: '#e5e7eb',
                  borderRadius: 8,
                  padding: 12,
                  fontSize: 14,
                  color: '#1f2937',
                  marginBottom: 8,
                }}
              />
              <TextInput
                value={policeReport.stationName}
                onChangeText={(text) => setPoliceReport({ ...policeReport, stationName: text })}
                placeholder="Police station name"
                style={{
                  backgroundColor: 'white',
                  borderWidth: 1,
                  borderColor: '#e5e7eb',
                  borderRadius: 8,
                  padding: 12,
                  fontSize: 14,
                  color: '#1f2937',
                }}
              />
            </>
          )}
        </View>

        {/* Safety Notice */}
        <View style={{
          backgroundColor: '#fef3c7',
          marginHorizontal: 16,
          marginTop: 24,
          marginBottom: 32,
          borderRadius: 12,
          padding: 16,
          borderWidth: 1,
          borderColor: '#fcd34d',
        }}>
          <View style={{
            flexDirection: 'row',
            alignItems: 'center',
            marginBottom: 8,
          }}>
            <Ionicons name="warning" size={20} color="#d97706" />
            <Text style={{
              fontSize: 16,
              fontWeight: '600',
              color: '#d97706',
              marginLeft: 8,
            }}>
              Important Notice
            </Text>
          </View>
          <Text style={{
            fontSize: 14,
            color: '#92400e',
            lineHeight: 20,
          }}>
            If this is an emergency situation requiring immediate assistance, please call emergency services (15 for police, 1122 for ambulance) before filing this report.
          </Text>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

export default IncidentReportScreen;
