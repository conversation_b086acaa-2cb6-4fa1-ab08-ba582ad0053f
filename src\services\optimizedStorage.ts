import AsyncStorage from '@react-native-async-storage/async-storage';
import * as SecureStore from 'expo-secure-store';
import { PERFORMANCE_CONFIG, FEATURE_FLAGS } from '../config/performanceConfig';
import { performanceMonitor } from './performanceMonitoring';

/**
 * Optimized storage service with caching, compression, and performance monitoring
 */

interface StorageOptions {
  secure?: boolean;
  compress?: boolean;
  cache?: boolean;
  ttl?: number; // Time to live in milliseconds
}

interface CacheItem<T> {
  data: T;
  timestamp: number;
  ttl: number;
}

class OptimizedStorageService {
  private memoryCache = new Map<string, CacheItem<any>>();
  private maxCacheSize = 100;
  private compressionThreshold = 1024; // 1KB

  constructor() {
    this.setupCacheCleanup();
  }

  // Setup automatic cache cleanup
  private setupCacheCleanup(): void {
    setInterval(() => {
      this.cleanupExpiredCache();
    }, PERFORMANCE_CONFIG.STORAGE.AUTO_CLEANUP_INTERVAL);
  }

  // Clean up expired cache items
  private cleanupExpiredCache(): void {
    const now = Date.now();
    const expiredKeys: string[] = [];

    this.memoryCache.forEach((item, key) => {
      if (now > item.timestamp + item.ttl) {
        expiredKeys.push(key);
      }
    });

    expiredKeys.forEach(key => {
      this.memoryCache.delete(key);
    });

    if (FEATURE_FLAGS.PERFORMANCE_MONITORING && expiredKeys.length > 0) {
      performanceMonitor.trackMetric('cache_cleanup', expiredKeys.length);
    }
  }

  // Compress data if needed
  private compressData(data: string): string {
    if (!PERFORMANCE_CONFIG.STORAGE.COMPRESSION || data.length < this.compressionThreshold) {
      return data;
    }

    try {
      // Simple compression using JSON.stringify optimization
      // In production, you might want to use a proper compression library
      return JSON.stringify(JSON.parse(data));
    } catch {
      return data;
    }
  }

  // Decompress data if needed
  private decompressData(data: string): string {
    try {
      return data;
    } catch {
      return data;
    }
  }

  // Get from memory cache
  private getFromCache<T>(key: string): T | null {
    const cached = this.memoryCache.get(key);
    if (!cached) return null;

    const now = Date.now();
    if (now > cached.timestamp + cached.ttl) {
      this.memoryCache.delete(key);
      return null;
    }

    return cached.data;
  }

  // Set to memory cache
  private setToCache<T>(key: string, data: T, ttl: number): void {
    // Limit cache size
    if (this.memoryCache.size >= this.maxCacheSize) {
      const firstKey = this.memoryCache.keys().next().value;
      this.memoryCache.delete(firstKey);
    }

    this.memoryCache.set(key, {
      data,
      timestamp: Date.now(),
      ttl,
    });
  }

  // Store data with optimizations
  async setItem<T>(
    key: string,
    value: T,
    options: StorageOptions = {}
  ): Promise<void> {
    const {
      secure = false,
      compress = PERFORMANCE_CONFIG.STORAGE.COMPRESSION,
      cache = true,
      ttl = 24 * 60 * 60 * 1000, // 24 hours default
    } = options;

    const startTime = Date.now();

    try {
      const serializedValue = JSON.stringify(value);
      const processedValue = compress ? this.compressData(serializedValue) : serializedValue;

      // Store in memory cache if enabled
      if (cache) {
        this.setToCache(key, value, ttl);
      }

      // Store in persistent storage
      if (secure) {
        await SecureStore.setItemAsync(key, processedValue);
      } else {
        await AsyncStorage.setItem(key, processedValue);
      }

      if (FEATURE_FLAGS.PERFORMANCE_MONITORING) {
        const duration = Date.now() - startTime;
        performanceMonitor.trackMetric('storage_write', duration, {
          key,
          size: processedValue.length,
          secure,
          compressed: compress,
        });
      }
    } catch (error) {
      console.error('Storage setItem error:', error);
      throw error;
    }
  }

  // Retrieve data with optimizations
  async getItem<T>(
    key: string,
    options: StorageOptions = {}
  ): Promise<T | null> {
    const {
      secure = false,
      cache = true,
    } = options;

    const startTime = Date.now();

    try {
      // Check memory cache first
      if (cache) {
        const cached = this.getFromCache<T>(key);
        if (cached !== null) {
          if (FEATURE_FLAGS.PERFORMANCE_MONITORING) {
            performanceMonitor.trackMetric('storage_cache_hit', 1, { key });
          }
          return cached;
        }
      }

      // Retrieve from persistent storage
      let storedValue: string | null;
      if (secure) {
        storedValue = await SecureStore.getItemAsync(key);
      } else {
        storedValue = await AsyncStorage.getItem(key);
      }

      if (storedValue === null) {
        return null;
      }

      const processedValue = this.decompressData(storedValue);
      const parsedValue = JSON.parse(processedValue) as T;

      // Update memory cache
      if (cache) {
        this.setToCache(key, parsedValue, 24 * 60 * 60 * 1000); // 24 hours
      }

      if (FEATURE_FLAGS.PERFORMANCE_MONITORING) {
        const duration = Date.now() - startTime;
        performanceMonitor.trackMetric('storage_read', duration, {
          key,
          size: storedValue.length,
          secure,
          fromCache: false,
        });
      }

      return parsedValue;
    } catch (error) {
      console.error('Storage getItem error:', error);
      return null;
    }
  }

  // Remove item
  async removeItem(key: string, secure: boolean = false): Promise<void> {
    const startTime = Date.now();

    try {
      // Remove from memory cache
      this.memoryCache.delete(key);

      // Remove from persistent storage
      if (secure) {
        await SecureStore.deleteItemAsync(key);
      } else {
        await AsyncStorage.removeItem(key);
      }

      if (FEATURE_FLAGS.PERFORMANCE_MONITORING) {
        const duration = Date.now() - startTime;
        performanceMonitor.trackMetric('storage_delete', duration, { key, secure });
      }
    } catch (error) {
      console.error('Storage removeItem error:', error);
      throw error;
    }
  }

  // Clear all data
  async clear(includeSecure: boolean = false): Promise<void> {
    const startTime = Date.now();

    try {
      // Clear memory cache
      this.memoryCache.clear();

      // Clear AsyncStorage
      await AsyncStorage.clear();

      // Clear SecureStore if requested (note: this clears ALL secure items)
      if (includeSecure) {
        // SecureStore doesn't have a clear all method, so we'd need to track keys
        console.warn('SecureStore clear all not implemented - clear items individually');
      }

      if (FEATURE_FLAGS.PERFORMANCE_MONITORING) {
        const duration = Date.now() - startTime;
        performanceMonitor.trackMetric('storage_clear', duration, { includeSecure });
      }
    } catch (error) {
      console.error('Storage clear error:', error);
      throw error;
    }
  }

  // Get multiple items efficiently
  async getMultipleItems<T>(
    keys: string[],
    options: StorageOptions = {}
  ): Promise<Record<string, T | null>> {
    const startTime = Date.now();
    const result: Record<string, T | null> = {};

    try {
      // Check cache first
      const uncachedKeys: string[] = [];
      
      if (options.cache !== false) {
        keys.forEach(key => {
          const cached = this.getFromCache<T>(key);
          if (cached !== null) {
            result[key] = cached;
          } else {
            uncachedKeys.push(key);
          }
        });
      } else {
        uncachedKeys.push(...keys);
      }

      // Fetch uncached items
      if (uncachedKeys.length > 0) {
        if (options.secure) {
          // SecureStore doesn't have multiGet, so fetch individually
          await Promise.all(
            uncachedKeys.map(async key => {
              result[key] = await this.getItem<T>(key, options);
            })
          );
        } else {
          const pairs = await AsyncStorage.multiGet(uncachedKeys);
          pairs.forEach(([key, value]) => {
            if (value) {
              try {
                const processedValue = this.decompressData(value);
                const parsedValue = JSON.parse(processedValue) as T;
                result[key] = parsedValue;
                
                // Update cache
                if (options.cache !== false) {
                  this.setToCache(key, parsedValue, 24 * 60 * 60 * 1000);
                }
              } catch {
                result[key] = null;
              }
            } else {
              result[key] = null;
            }
          });
        }
      }

      if (FEATURE_FLAGS.PERFORMANCE_MONITORING) {
        const duration = Date.now() - startTime;
        performanceMonitor.trackMetric('storage_multi_read', duration, {
          keyCount: keys.length,
          cacheHits: keys.length - uncachedKeys.length,
        });
      }

      return result;
    } catch (error) {
      console.error('Storage getMultipleItems error:', error);
      throw error;
    }
  }

  // Set multiple items efficiently
  async setMultipleItems<T>(
    items: Record<string, T>,
    options: StorageOptions = {}
  ): Promise<void> {
    const startTime = Date.now();

    try {
      const pairs: [string, string][] = [];
      
      Object.entries(items).forEach(([key, value]) => {
        const serializedValue = JSON.stringify(value);
        const processedValue = options.compress ? this.compressData(serializedValue) : serializedValue;
        pairs.push([key, processedValue]);

        // Update cache
        if (options.cache !== false) {
          this.setToCache(key, value, options.ttl || 24 * 60 * 60 * 1000);
        }
      });

      if (options.secure) {
        // SecureStore doesn't have multiSet, so set individually
        await Promise.all(
          pairs.map(([key, value]) => SecureStore.setItemAsync(key, value))
        );
      } else {
        await AsyncStorage.multiSet(pairs);
      }

      if (FEATURE_FLAGS.PERFORMANCE_MONITORING) {
        const duration = Date.now() - startTime;
        performanceMonitor.trackMetric('storage_multi_write', duration, {
          itemCount: pairs.length,
        });
      }
    } catch (error) {
      console.error('Storage setMultipleItems error:', error);
      throw error;
    }
  }

  // Get storage usage statistics
  async getStorageStats(): Promise<{
    cacheSize: number;
    totalKeys: number;
    estimatedSize: number;
  }> {
    try {
      const keys = await AsyncStorage.getAllKeys();
      let estimatedSize = 0;

      // Estimate size by sampling some keys
      const sampleSize = Math.min(10, keys.length);
      const sampleKeys = keys.slice(0, sampleSize);
      
      for (const key of sampleKeys) {
        const value = await AsyncStorage.getItem(key);
        if (value) {
          estimatedSize += value.length;
        }
      }

      // Extrapolate total size
      const avgItemSize = estimatedSize / sampleSize;
      const totalEstimatedSize = avgItemSize * keys.length;

      return {
        cacheSize: this.memoryCache.size,
        totalKeys: keys.length,
        estimatedSize: totalEstimatedSize,
      };
    } catch (error) {
      console.error('Storage stats error:', error);
      return {
        cacheSize: this.memoryCache.size,
        totalKeys: 0,
        estimatedSize: 0,
      };
    }
  }
}

// Create singleton instance
export const optimizedStorage = new OptimizedStorageService();

export default optimizedStorage;
