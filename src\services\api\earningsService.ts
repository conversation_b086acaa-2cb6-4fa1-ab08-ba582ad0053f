import { apiClient } from './apiClient';
import {
  EarningsData,
  EarningsSummary,
  DailyEarnings,
  WeeklyEarnings,
  MonthlyEarnings,
  PerformanceMetrics,
  PayoutInfo,
  EarningsFilter,
  WalletBalance,
  WithdrawalMethod,
  WithdrawalRequest,
  PayoutSchedule,
  PayoutSettings,
  Deduction,
  Bonus,
} from '../../types/earnings';

class EarningsService {
  // Get earnings summary for different periods
  async getEarningsSummary(): Promise<EarningsSummary> {
    try {
      const response = await apiClient.get('/rider/earnings/summary');
      return response.data as EarningsSummary;
    } catch (error: any) {
      console.error('Error fetching earnings summary:', error);
      throw new Error(error.response?.data?.message || 'Failed to fetch earnings summary');
    }
  }

  // Get recent earnings with optional limit
  async getRecentEarnings(limit: number = 20): Promise<EarningsData[]> {
    try {
      const response = await apiClient.get(`/rider/earnings/recent?limit=${limit}`);
      return response.data as EarningsData[];
    } catch (error: any) {
      console.error('Error fetching recent earnings:', error);
      throw new Error(error.response?.data?.message || 'Failed to fetch recent earnings');
    }
  }

  // Get earnings with filters
  async getEarnings(filter: EarningsFilter): Promise<EarningsData[]> {
    try {
      const params = new URLSearchParams();
      
      if (filter.startDate) params.append('startDate', filter.startDate);
      if (filter.endDate) params.append('endDate', filter.endDate);
      if (filter.status) params.append('status', filter.status);
      if (filter.paymentMethod) params.append('paymentMethod', filter.paymentMethod);
      if (filter.minAmount) params.append('minAmount', filter.minAmount.toString());
      if (filter.maxAmount) params.append('maxAmount', filter.maxAmount.toString());

      const response = await apiClient.get(`/rider/earnings?${params.toString()}`);
      return response.data as EarningsData[];
    } catch (error: any) {
      console.error('Error fetching earnings:', error);
      throw new Error(error.response?.data?.message || 'Failed to fetch earnings');
    }
  }

  // Get daily earnings breakdown
  async getDailyEarnings(startDate: string, endDate: string): Promise<DailyEarnings[]> {
    try {
      const response = await apiClient.get(
        `/rider/earnings/daily?startDate=${startDate}&endDate=${endDate}`
      );
      return response.data as DailyEarnings[];
    } catch (error: any) {
      console.error('Error fetching daily earnings:', error);
      throw new Error(error.response?.data?.message || 'Failed to fetch daily earnings');
    }
  }

  // Get weekly earnings breakdown
  async getWeeklyEarnings(startDate: string, endDate: string): Promise<WeeklyEarnings[]> {
    try {
      const response = await apiClient.get(
        `/rider/earnings/weekly?startDate=${startDate}&endDate=${endDate}`
      );
      return response.data as WeeklyEarnings[];
    } catch (error: any) {
      console.error('Error fetching weekly earnings:', error);
      throw new Error(error.response?.data?.message || 'Failed to fetch weekly earnings');
    }
  }

  // Get monthly earnings breakdown
  async getMonthlyEarnings(year: number): Promise<MonthlyEarnings[]> {
    try {
      const response = await apiClient.get(`/rider/earnings/monthly?year=${year}`);
      return response.data as MonthlyEarnings[];
    } catch (error: any) {
      console.error('Error fetching monthly earnings:', error);
      throw new Error(error.response?.data?.message || 'Failed to fetch monthly earnings');
    }
  }

  // Get performance metrics
  async getPerformanceMetrics(): Promise<PerformanceMetrics> {
    try {
      const response = await apiClient.get('/rider/performance/metrics');
      return response.data as PerformanceMetrics;
    } catch (error: any) {
      console.error('Error fetching performance metrics:', error);
      throw new Error(error.response?.data?.message || 'Failed to fetch performance metrics');
    }
  }

  // Get payout information
  async getPayouts(): Promise<PayoutInfo[]> {
    try {
      const response = await apiClient.get('/rider/payouts');
      return response.data as PayoutInfo[];
    } catch (error: any) {
      console.error('Error fetching payouts:', error);
      throw new Error(error.response?.data?.message || 'Failed to fetch payouts');
    }
  }

  // Request instant payout (if available)
  async requestInstantPayout(amount: number): Promise<PayoutInfo> {
    try {
      const response = await apiClient.post('/rider/payouts/instant', { amount });
      return response.data as PayoutInfo;
    } catch (error: any) {
      console.error('Error requesting instant payout:', error);
      throw new Error(error.response?.data?.message || 'Failed to request instant payout');
    }
  }

  // Get earnings by order ID
  async getEarningsByOrderId(orderId: string): Promise<EarningsData> {
    try {
      const response = await apiClient.get(`/rider/earnings/order/${orderId}`);
      return response.data as EarningsData;
    } catch (error: any) {
      console.error('Error fetching earnings by order ID:', error);
      throw new Error(error.response?.data?.message || 'Failed to fetch earnings for order');
    }
  }

  // Export earnings data
  async exportEarnings(filter: EarningsFilter, format: 'csv' | 'pdf' = 'csv'): Promise<Blob> {
    try {
      const params = new URLSearchParams();
      
      if (filter.startDate) params.append('startDate', filter.startDate);
      if (filter.endDate) params.append('endDate', filter.endDate);
      if (filter.status) params.append('status', filter.status);
      if (filter.paymentMethod) params.append('paymentMethod', filter.paymentMethod);
      params.append('format', format);

      const response = await apiClient.get(`/rider/earnings/export?${params.toString()}`, {
        responseType: 'blob',
      });
      
      return response.data as Blob;
    } catch (error: any) {
      console.error('Error exporting earnings:', error);
      throw new Error(error.response?.data?.message || 'Failed to export earnings');
    }
  }

  // Wallet operations
  async getWalletBalance(): Promise<WalletBalance> {
    try {
      const response = await apiClient.get('/rider/wallet/balance');
      return response.data as WalletBalance;
    } catch (error: any) {
      console.error('Error fetching wallet balance:', error);
      throw new Error(error.response?.data?.message || 'Failed to fetch wallet balance');
    }
  }

  async getWithdrawalMethods(): Promise<WithdrawalMethod[]> {
    try {
      const response = await apiClient.get('/rider/wallet/withdrawal-methods');
      return response.data as WithdrawalMethod[];
    } catch (error: any) {
      console.error('Error fetching withdrawal methods:', error);
      throw new Error(error.response?.data?.message || 'Failed to fetch withdrawal methods');
    }
  }

  async addWithdrawalMethod(method: Omit<WithdrawalMethod, 'id' | 'createdAt'>): Promise<WithdrawalMethod> {
    try {
      const response = await apiClient.post('/rider/wallet/withdrawal-methods', method);
      return response.data as WithdrawalMethod;
    } catch (error: any) {
      console.error('Error adding withdrawal method:', error);
      throw new Error(error.response?.data?.message || 'Failed to add withdrawal method');
    }
  }

  async updateWithdrawalMethod(id: string, updates: Partial<WithdrawalMethod>): Promise<WithdrawalMethod> {
    try {
      const response = await apiClient.put(`/rider/wallet/withdrawal-methods/${id}`, updates);
      return response.data as WithdrawalMethod;
    } catch (error: any) {
      console.error('Error updating withdrawal method:', error);
      throw new Error(error.response?.data?.message || 'Failed to update withdrawal method');
    }
  }

  async deleteWithdrawalMethod(id: string): Promise<void> {
    try {
      await apiClient.delete(`/rider/wallet/withdrawal-methods/${id}`);
    } catch (error: any) {
      console.error('Error deleting withdrawal method:', error);
      throw new Error(error.response?.data?.message || 'Failed to delete withdrawal method');
    }
  }

  async requestWithdrawal(amount: number, methodId: string): Promise<WithdrawalRequest> {
    try {
      const response = await apiClient.post('/rider/wallet/withdraw', {
        amount,
        methodId,
      });
      return response.data as WithdrawalRequest;
    } catch (error: any) {
      console.error('Error requesting withdrawal:', error);
      throw new Error(error.response?.data?.message || 'Failed to request withdrawal');
    }
  }

  async getWithdrawalHistory(): Promise<WithdrawalRequest[]> {
    try {
      const response = await apiClient.get('/rider/wallet/withdrawals');
      return response.data as WithdrawalRequest[];
    } catch (error: any) {
      console.error('Error fetching withdrawal history:', error);
      throw new Error(error.response?.data?.message || 'Failed to fetch withdrawal history');
    }
  }

  async cancelWithdrawal(id: string): Promise<void> {
    try {
      await apiClient.post(`/rider/wallet/withdrawals/${id}/cancel`);
    } catch (error: any) {
      console.error('Error cancelling withdrawal:', error);
      throw new Error(error.response?.data?.message || 'Failed to cancel withdrawal');
    }
  }

  // Payout operations
  async getPayoutSchedule(): Promise<PayoutSchedule[]> {
    try {
      const response = await apiClient.get('/rider/payouts/schedule');
      return response.data as PayoutSchedule[];
    } catch (error: any) {
      console.error('Error fetching payout schedule:', error);
      throw new Error(error.response?.data?.message || 'Failed to fetch payout schedule');
    }
  }

  async getPayoutSettings(): Promise<PayoutSettings> {
    try {
      const response = await apiClient.get('/rider/payouts/settings');
      return response.data as PayoutSettings;
    } catch (error: any) {
      console.error('Error fetching payout settings:', error);
      throw new Error(error.response?.data?.message || 'Failed to fetch payout settings');
    }
  }

  async updatePayoutSettings(settings: Partial<PayoutSettings>): Promise<PayoutSettings> {
    try {
      const response = await apiClient.put('/rider/payouts/settings', settings);
      return response.data as PayoutSettings;
    } catch (error: any) {
      console.error('Error updating payout settings:', error);
      throw new Error(error.response?.data?.message || 'Failed to update payout settings');
    }
  }

  // Deductions and bonuses
  async getDeductions(startDate?: string, endDate?: string): Promise<Deduction[]> {
    try {
      const params = new URLSearchParams();
      if (startDate) params.append('startDate', startDate);
      if (endDate) params.append('endDate', endDate);

      const response = await apiClient.get(`/rider/earnings/deductions?${params.toString()}`);
      return response.data as Deduction[];
    } catch (error: any) {
      console.error('Error fetching deductions:', error);
      throw new Error(error.response?.data?.message || 'Failed to fetch deductions');
    }
  }

  async getBonuses(startDate?: string, endDate?: string): Promise<Bonus[]> {
    try {
      const params = new URLSearchParams();
      if (startDate) params.append('startDate', startDate);
      if (endDate) params.append('endDate', endDate);

      const response = await apiClient.get(`/rider/earnings/bonuses?${params.toString()}`);
      return response.data as Bonus[];
    } catch (error: any) {
      console.error('Error fetching bonuses:', error);
      throw new Error(error.response?.data?.message || 'Failed to fetch bonuses');
    }
  }
}

export const earningsService = new EarningsService();
