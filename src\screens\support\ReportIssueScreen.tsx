import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  ScrollView,
  Alert,
  Image,
  Modal,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import * as ImagePicker from 'expo-image-picker';
import { useNavigation } from '@react-navigation/native';
import { IssueType, IssueSeverity, IssueReport } from '../../types/support';

interface OrderInfo {
  id: string;
  customerName: string;
  restaurantName: string;
  deliveryAddress: string;
  orderTime: string;
}

const ReportIssueScreen: React.FC = () => {
  const navigation = useNavigation();
  
  // State
  const [selectedIssueType, setSelectedIssueType] = useState<IssueType | null>(null);
  const [selectedSeverity, setSelectedSeverity] = useState<IssueSeverity>(IssueSeverity.MEDIUM);
  const [selectedOrderId, setSelectedOrderId] = useState<string>('');
  const [title, setTitle] = useState('');
  const [description, setDescription] = useState('');
  const [evidence, setEvidence] = useState<{ id: string; type: 'photo'; url: string; description?: string }[]>([]);
  const [recentOrders, setRecentOrders] = useState<OrderInfo[]>([]);
  const [showOrderModal, setShowOrderModal] = useState(false);
  const [loading, setLoading] = useState(false);

  // Issue types with descriptions
  const issueTypes = [
    {
      type: IssueType.CUSTOMER_MISBEHAVIOR,
      title: 'Customer Misbehavior',
      description: 'Inappropriate behavior, harassment, or threats from customer',
      icon: 'person-outline',
      color: '#ef4444',
    },
    {
      type: IssueType.NON_PAYMENT,
      title: 'Non-Payment',
      description: 'Customer refused to pay or payment issues',
      icon: 'card-outline',
      color: '#f59e0b',
    },
    {
      type: IssueType.DELIVERY_DELAY,
      title: 'Delivery Delay',
      description: 'Unexpected delays due to external factors',
      icon: 'time-outline',
      color: '#3b82f6',
    },
    {
      type: IssueType.WRONG_ADDRESS,
      title: 'Wrong Address',
      description: 'Incorrect or incomplete delivery address provided',
      icon: 'location-outline',
      color: '#8b5cf6',
    },
    {
      type: IssueType.RESTAURANT_ISSUE,
      title: 'Restaurant Issue',
      description: 'Problems with restaurant staff or order preparation',
      icon: 'restaurant-outline',
      color: '#06b6d4',
    },
    {
      type: IssueType.APP_MALFUNCTION,
      title: 'App Malfunction',
      description: 'Technical issues with the rider app',
      icon: 'phone-portrait-outline',
      color: '#84cc16',
    },
    {
      type: IssueType.SAFETY_CONCERN,
      title: 'Safety Concern',
      description: 'Safety issues or dangerous situations',
      icon: 'shield-outline',
      color: '#dc2626',
    },
    {
      type: IssueType.OTHER,
      title: 'Other',
      description: 'Other issues not listed above',
      icon: 'ellipsis-horizontal-outline',
      color: '#6b7280',
    },
  ];

  const severityLevels = [
    { level: IssueSeverity.LOW, title: 'Low', description: 'Minor issue, not urgent', color: '#10b981' },
    { level: IssueSeverity.MEDIUM, title: 'Medium', description: 'Moderate issue, needs attention', color: '#f59e0b' },
    { level: IssueSeverity.HIGH, title: 'High', description: 'Important issue, requires quick response', color: '#ef4444' },
    { level: IssueSeverity.CRITICAL, title: 'Critical', description: 'Urgent issue, immediate attention needed', color: '#dc2626' },
  ];

  // Mock recent orders
  useEffect(() => {
    const mockOrders: OrderInfo[] = [
      {
        id: 'ORD-2024-001',
        customerName: 'Ahmed Khan',
        restaurantName: 'Pizza Palace',
        deliveryAddress: 'Block A, Gulshan-e-Iqbal, Karachi',
        orderTime: '2 hours ago',
      },
      {
        id: 'ORD-2024-002',
        customerName: 'Fatima Ali',
        restaurantName: 'Burger House',
        deliveryAddress: 'DHA Phase 2, Karachi',
        orderTime: '4 hours ago',
      },
      {
        id: 'ORD-2024-003',
        customerName: 'Hassan Sheikh',
        restaurantName: 'Desi Delights',
        deliveryAddress: 'Clifton Block 5, Karachi',
        orderTime: '6 hours ago',
      },
    ];
    setRecentOrders(mockOrders);
  }, []);

  const addPhoto = async () => {
    const { status } = await ImagePicker.requestMediaLibraryPermissionsAsync();
    if (status !== 'granted') {
      Alert.alert('Permission Required', 'Please grant camera roll permissions to add photos.');
      return;
    }

    Alert.alert(
      'Add Photo Evidence',
      'Choose an option',
      [
        { text: 'Camera', onPress: () => openCamera() },
        { text: 'Gallery', onPress: () => openGallery() },
        { text: 'Cancel', style: 'cancel' },
      ]
    );
  };

  const openCamera = async () => {
    const result = await ImagePicker.launchCameraAsync({
      mediaTypes: ImagePicker.MediaTypeOptions.Images,
      allowsEditing: true,
      aspect: [4, 3],
      quality: 0.8,
    });

    if (!result.canceled && result.assets[0]) {
      const newEvidence = {
        id: Date.now().toString(),
        type: 'photo' as const,
        url: result.assets[0].uri,
      };
      setEvidence(prev => [...prev, newEvidence]);
    }
  };

  const openGallery = async () => {
    const result = await ImagePicker.launchImageLibraryAsync({
      mediaTypes: ImagePicker.MediaTypeOptions.Images,
      allowsEditing: true,
      aspect: [4, 3],
      quality: 0.8,
    });

    if (!result.canceled && result.assets[0]) {
      const newEvidence = {
        id: Date.now().toString(),
        type: 'photo' as const,
        url: result.assets[0].uri,
      };
      setEvidence(prev => [...prev, newEvidence]);
    }
  };

  const removePhoto = (photoId: string) => {
    setEvidence(prev => prev.filter(item => item.id !== photoId));
  };

  const submitReport = async () => {
    if (!selectedIssueType) {
      Alert.alert('Error', 'Please select an issue type.');
      return;
    }

    if (!title.trim()) {
      Alert.alert('Error', 'Please provide a title for the issue.');
      return;
    }

    if (!description.trim()) {
      Alert.alert('Error', 'Please provide a description of the issue.');
      return;
    }

    setLoading(true);

    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 2000));

      const report: Omit<IssueReport, 'id' | 'createdAt' | 'updatedAt'> = {
        riderId: 'rider-1',
        orderId: selectedOrderId || undefined,
        type: selectedIssueType,
        severity: selectedSeverity,
        title: title.trim(),
        description: description.trim(),
        evidence: evidence.map(item => ({
          ...item,
          timestamp: new Date().toISOString(),
        })),
        status: 'open',
        tags: [],
      };

      Alert.alert(
        'Report Submitted',
        'Your issue report has been submitted successfully. Our support team will review it and get back to you soon.',
        [
          {
            text: 'OK',
            onPress: () => navigation.goBack(),
          },
        ]
      );
    } catch (error) {
      Alert.alert('Error', 'Failed to submit report. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const renderOrderModal = () => (
    <Modal
      visible={showOrderModal}
      animationType="slide"
      presentationStyle="pageSheet"
    >
      <SafeAreaView style={{ flex: 1, backgroundColor: 'white' }}>
        <View style={{
          flexDirection: 'row',
          alignItems: 'center',
          paddingHorizontal: 16,
          paddingVertical: 12,
          borderBottomWidth: 1,
          borderBottomColor: '#e5e7eb',
        }}>
          <TouchableOpacity
            onPress={() => setShowOrderModal(false)}
            style={{
              width: 40,
              height: 40,
              alignItems: 'center',
              justifyContent: 'center',
              marginRight: 8,
            }}
          >
            <Ionicons name="close" size={24} color="#374151" />
          </TouchableOpacity>
          
          <Text style={{
            fontSize: 18,
            fontWeight: '600',
            color: '#1f2937',
            flex: 1,
          }}>
            Select Order
          </Text>
        </View>

        <ScrollView style={{ flex: 1 }}>
          <TouchableOpacity
            onPress={() => {
              setSelectedOrderId('');
              setShowOrderModal(false);
            }}
            style={{
              flexDirection: 'row',
              alignItems: 'center',
              paddingHorizontal: 16,
              paddingVertical: 16,
              borderBottomWidth: 1,
              borderBottomColor: '#f3f4f6',
            }}
          >
            <View style={{
              width: 40,
              height: 40,
              backgroundColor: '#f3f4f6',
              borderRadius: 20,
              alignItems: 'center',
              justifyContent: 'center',
              marginRight: 12,
            }}>
              <Ionicons name="close-circle-outline" size={20} color="#6b7280" />
            </View>
            
            <View style={{ flex: 1 }}>
              <Text style={{
                fontSize: 16,
                fontWeight: '600',
                color: '#1f2937',
              }}>
                No specific order
              </Text>
              <Text style={{
                fontSize: 14,
                color: '#6b7280',
              }}>
                General issue not related to a specific order
              </Text>
            </View>
            
            {selectedOrderId === '' && (
              <Ionicons name="checkmark-circle" size={24} color="#10b981" />
            )}
          </TouchableOpacity>

          {recentOrders.map(order => (
            <TouchableOpacity
              key={order.id}
              onPress={() => {
                setSelectedOrderId(order.id);
                setShowOrderModal(false);
              }}
              style={{
                flexDirection: 'row',
                alignItems: 'center',
                paddingHorizontal: 16,
                paddingVertical: 16,
                borderBottomWidth: 1,
                borderBottomColor: '#f3f4f6',
              }}
            >
              <View style={{
                width: 40,
                height: 40,
                backgroundColor: '#f0f9ff',
                borderRadius: 20,
                alignItems: 'center',
                justifyContent: 'center',
                marginRight: 12,
              }}>
                <Ionicons name="receipt-outline" size={20} color="#3b82f6" />
              </View>
              
              <View style={{ flex: 1 }}>
                <Text style={{
                  fontSize: 16,
                  fontWeight: '600',
                  color: '#1f2937',
                }}>
                  {order.id}
                </Text>
                <Text style={{
                  fontSize: 14,
                  color: '#6b7280',
                }}>
                  {order.customerName} • {order.restaurantName}
                </Text>
                <Text style={{
                  fontSize: 12,
                  color: '#9ca3af',
                }}>
                  {order.orderTime}
                </Text>
              </View>
              
              {selectedOrderId === order.id && (
                <Ionicons name="checkmark-circle" size={24} color="#10b981" />
              )}
            </TouchableOpacity>
          ))}
        </ScrollView>
      </SafeAreaView>
    </Modal>
  );

  return (
    <SafeAreaView style={{ flex: 1, backgroundColor: '#f9fafb' }}>
      {/* Header */}
      <View style={{
        flexDirection: 'row',
        alignItems: 'center',
        paddingHorizontal: 16,
        paddingVertical: 12,
        backgroundColor: 'white',
        borderBottomWidth: 1,
        borderBottomColor: '#e5e7eb',
      }}>
        <TouchableOpacity
          onPress={() => navigation.goBack()}
          style={{
            width: 40,
            height: 40,
            alignItems: 'center',
            justifyContent: 'center',
            marginRight: 8,
          }}
        >
          <Ionicons name="arrow-back" size={24} color="#374151" />
        </TouchableOpacity>

        <Text style={{
          fontSize: 18,
          fontWeight: '600',
          color: '#1f2937',
          flex: 1,
        }}>
          Report Issue
        </Text>
      </View>

      <ScrollView style={{ flex: 1 }}>
        {/* Issue Type Selection */}
        <View style={{
          backgroundColor: 'white',
          marginTop: 16,
          paddingVertical: 16,
        }}>
          <Text style={{
            fontSize: 16,
            fontWeight: '600',
            color: '#1f2937',
            paddingHorizontal: 16,
            marginBottom: 12,
          }}>
            What type of issue are you reporting?
          </Text>

          <View style={{
            flexDirection: 'row',
            flexWrap: 'wrap',
            paddingHorizontal: 8,
          }}>
            {issueTypes.map(issueType => (
              <TouchableOpacity
                key={issueType.type}
                onPress={() => setSelectedIssueType(issueType.type)}
                style={{
                  width: '50%',
                  padding: 8,
                }}
              >
                <View style={{
                  backgroundColor: selectedIssueType === issueType.type ? '#f0f9ff' : '#f9fafb',
                  borderWidth: 2,
                  borderColor: selectedIssueType === issueType.type ? issueType.color : '#e5e7eb',
                  borderRadius: 12,
                  padding: 12,
                  minHeight: 100,
                }}>
                  <View style={{
                    width: 32,
                    height: 32,
                    backgroundColor: selectedIssueType === issueType.type ? issueType.color : '#f3f4f6',
                    borderRadius: 16,
                    alignItems: 'center',
                    justifyContent: 'center',
                    marginBottom: 8,
                  }}>
                    <Ionicons
                      name={issueType.icon as any}
                      size={16}
                      color={selectedIssueType === issueType.type ? 'white' : '#6b7280'}
                    />
                  </View>

                  <Text style={{
                    fontSize: 14,
                    fontWeight: '600',
                    color: selectedIssueType === issueType.type ? issueType.color : '#1f2937',
                    marginBottom: 4,
                  }}>
                    {issueType.title}
                  </Text>

                  <Text style={{
                    fontSize: 12,
                    color: '#6b7280',
                    lineHeight: 16,
                  }}>
                    {issueType.description}
                  </Text>
                </View>
              </TouchableOpacity>
            ))}
          </View>
        </View>

        {/* Severity Selection */}
        <View style={{
          backgroundColor: 'white',
          marginTop: 16,
          paddingVertical: 16,
        }}>
          <Text style={{
            fontSize: 16,
            fontWeight: '600',
            color: '#1f2937',
            paddingHorizontal: 16,
            marginBottom: 12,
          }}>
            How urgent is this issue?
          </Text>

          <View style={{ paddingHorizontal: 16 }}>
            {severityLevels.map(severity => (
              <TouchableOpacity
                key={severity.level}
                onPress={() => setSelectedSeverity(severity.level)}
                style={{
                  flexDirection: 'row',
                  alignItems: 'center',
                  paddingVertical: 12,
                  paddingHorizontal: 16,
                  marginVertical: 4,
                  backgroundColor: selectedSeverity === severity.level ? '#f0f9ff' : '#f9fafb',
                  borderWidth: 1,
                  borderColor: selectedSeverity === severity.level ? severity.color : '#e5e7eb',
                  borderRadius: 8,
                }}
              >
                <View style={{
                  width: 20,
                  height: 20,
                  borderRadius: 10,
                  borderWidth: 2,
                  borderColor: severity.color,
                  backgroundColor: selectedSeverity === severity.level ? severity.color : 'transparent',
                  marginRight: 12,
                  alignItems: 'center',
                  justifyContent: 'center',
                }}>
                  {selectedSeverity === severity.level && (
                    <Ionicons name="checkmark" size={12} color="white" />
                  )}
                </View>

                <View style={{ flex: 1 }}>
                  <Text style={{
                    fontSize: 14,
                    fontWeight: '600',
                    color: selectedSeverity === severity.level ? severity.color : '#1f2937',
                  }}>
                    {severity.title}
                  </Text>
                  <Text style={{
                    fontSize: 12,
                    color: '#6b7280',
                  }}>
                    {severity.description}
                  </Text>
                </View>
              </TouchableOpacity>
            ))}
          </View>
        </View>

        {/* Order Selection */}
        <View style={{
          backgroundColor: 'white',
          marginTop: 16,
          paddingVertical: 16,
        }}>
          <Text style={{
            fontSize: 16,
            fontWeight: '600',
            color: '#1f2937',
            paddingHorizontal: 16,
            marginBottom: 12,
          }}>
            Related Order (Optional)
          </Text>

          <TouchableOpacity
            onPress={() => setShowOrderModal(true)}
            style={{
              flexDirection: 'row',
              alignItems: 'center',
              paddingHorizontal: 16,
              paddingVertical: 12,
              marginHorizontal: 16,
              backgroundColor: '#f9fafb',
              borderWidth: 1,
              borderColor: '#e5e7eb',
              borderRadius: 8,
            }}
          >
            <Ionicons name="receipt-outline" size={20} color="#6b7280" />
            <Text style={{
              flex: 1,
              fontSize: 14,
              color: selectedOrderId ? '#1f2937' : '#9ca3af',
              marginLeft: 12,
            }}>
              {selectedOrderId || 'Select an order (if applicable)'}
            </Text>
            <Ionicons name="chevron-forward" size={20} color="#6b7280" />
          </TouchableOpacity>
        </View>

        {/* Issue Details */}
        <View style={{
          backgroundColor: 'white',
          marginTop: 16,
          paddingVertical: 16,
        }}>
          <Text style={{
            fontSize: 16,
            fontWeight: '600',
            color: '#1f2937',
            paddingHorizontal: 16,
            marginBottom: 12,
          }}>
            Issue Details
          </Text>

          <View style={{ paddingHorizontal: 16 }}>
            <Text style={{
              fontSize: 14,
              color: '#374151',
              marginBottom: 8,
            }}>
              Title *
            </Text>
            <TextInput
              value={title}
              onChangeText={setTitle}
              placeholder="Brief summary of the issue"
              placeholderTextColor="#9ca3af"
              style={{
                backgroundColor: '#f9fafb',
                borderWidth: 1,
                borderColor: '#e5e7eb',
                borderRadius: 8,
                paddingHorizontal: 12,
                paddingVertical: 12,
                fontSize: 16,
                color: '#1f2937',
                marginBottom: 16,
              }}
            />

            <Text style={{
              fontSize: 14,
              color: '#374151',
              marginBottom: 8,
            }}>
              Description *
            </Text>
            <TextInput
              value={description}
              onChangeText={setDescription}
              placeholder="Provide detailed information about the issue..."
              placeholderTextColor="#9ca3af"
              multiline
              numberOfLines={4}
              style={{
                backgroundColor: '#f9fafb',
                borderWidth: 1,
                borderColor: '#e5e7eb',
                borderRadius: 8,
                paddingHorizontal: 12,
                paddingVertical: 12,
                fontSize: 16,
                color: '#1f2937',
                textAlignVertical: 'top',
                minHeight: 100,
              }}
            />
          </View>
        </View>

        {/* Evidence Photos */}
        <View style={{
          backgroundColor: 'white',
          marginTop: 16,
          paddingVertical: 16,
        }}>
          <View style={{
            flexDirection: 'row',
            alignItems: 'center',
            justifyContent: 'space-between',
            paddingHorizontal: 16,
            marginBottom: 12,
          }}>
            <Text style={{
              fontSize: 16,
              fontWeight: '600',
              color: '#1f2937',
            }}>
              Photo Evidence
            </Text>

            <TouchableOpacity
              onPress={addPhoto}
              style={{
                flexDirection: 'row',
                alignItems: 'center',
                backgroundColor: '#3b82f6',
                paddingHorizontal: 12,
                paddingVertical: 6,
                borderRadius: 6,
              }}
            >
              <Ionicons name="camera" size={16} color="white" />
              <Text style={{
                color: 'white',
                fontSize: 12,
                fontWeight: '600',
                marginLeft: 4,
              }}>
                Add Photo
              </Text>
            </TouchableOpacity>
          </View>

          {evidence.length > 0 ? (
            <ScrollView
              horizontal
              showsHorizontalScrollIndicator={false}
              style={{ paddingLeft: 16 }}
            >
              {evidence.map(item => (
                <View
                  key={item.id}
                  style={{
                    marginRight: 12,
                    position: 'relative',
                  }}
                >
                  <Image
                    source={{ uri: item.url }}
                    style={{
                      width: 100,
                      height: 100,
                      borderRadius: 8,
                    }}
                  />
                  <TouchableOpacity
                    onPress={() => removePhoto(item.id)}
                    style={{
                      position: 'absolute',
                      top: -8,
                      right: -8,
                      width: 24,
                      height: 24,
                      backgroundColor: '#ef4444',
                      borderRadius: 12,
                      alignItems: 'center',
                      justifyContent: 'center',
                    }}
                  >
                    <Ionicons name="close" size={16} color="white" />
                  </TouchableOpacity>
                </View>
              ))}
            </ScrollView>
          ) : (
            <View style={{
              alignItems: 'center',
              paddingVertical: 32,
              paddingHorizontal: 16,
            }}>
              <Ionicons name="camera-outline" size={48} color="#d1d5db" />
              <Text style={{
                fontSize: 14,
                color: '#6b7280',
                textAlign: 'center',
                marginTop: 8,
              }}>
                Add photos to help us understand the issue better
              </Text>
            </View>
          )}
        </View>

        {/* Submit Button */}
        <View style={{
          paddingHorizontal: 16,
          paddingVertical: 24,
        }}>
          <TouchableOpacity
            onPress={submitReport}
            disabled={loading || !selectedIssueType || !title.trim() || !description.trim()}
            style={{
              backgroundColor: (!selectedIssueType || !title.trim() || !description.trim()) ? '#e5e7eb' : '#3b82f6',
              borderRadius: 12,
              paddingVertical: 16,
              alignItems: 'center',
            }}
          >
            <Text style={{
              color: (!selectedIssueType || !title.trim() || !description.trim()) ? '#9ca3af' : 'white',
              fontSize: 16,
              fontWeight: '600',
            }}>
              {loading ? 'Submitting...' : 'Submit Report'}
            </Text>
          </TouchableOpacity>
        </View>
      </ScrollView>

      {renderOrderModal()}
    </SafeAreaView>
  );
};

export default ReportIssueScreen;
