import React from 'react';
import { View, Text, ViewStyle, TextStyle } from 'react-native';

interface BadgeProps {
  text: string;
  variant?: 'default' | 'success' | 'warning' | 'error' | 'info';
  size?: 'sm' | 'md' | 'lg';
  style?: ViewStyle;
}

const Badge: React.FC<BadgeProps> = ({
  text,
  variant = 'default',
  size = 'md',
  style,
}) => {
  const getBadgeStyles = (): ViewStyle => {
    const baseStyles: ViewStyle = {
      borderRadius: 12,
      alignSelf: 'flex-start',
      justifyContent: 'center',
      alignItems: 'center',
    };

    // Size styles
    const sizeStyles: Record<string, ViewStyle> = {
      sm: { paddingHorizontal: 8, paddingVertical: 2, minHeight: 20 },
      md: { paddingHorizontal: 12, paddingVertical: 4, minHeight: 24 },
      lg: { paddingHorizontal: 16, paddingVertical: 6, minHeight: 28 },
    };

    // Variant styles
    const variantStyles: Record<string, ViewStyle> = {
      default: { backgroundColor: '#f3f4f6' },
      success: { backgroundColor: '#dcfce7' },
      warning: { backgroundColor: '#fef3c7' },
      error: { backgroundColor: '#fee2e2' },
      info: { backgroundColor: '#dbeafe' },
    };

    return {
      ...baseStyles,
      ...sizeStyles[size],
      ...variantStyles[variant],
    };
  };

  const getTextStyles = (): TextStyle => {
    const sizeStyles: Record<string, TextStyle> = {
      sm: { fontSize: 11 },
      md: { fontSize: 12 },
      lg: { fontSize: 14 },
    };

    const variantStyles: Record<string, TextStyle> = {
      default: { color: '#374151' },
      success: { color: '#166534' },
      warning: { color: '#92400e' },
      error: { color: '#991b1b' },
      info: { color: '#1e40af' },
    };

    return {
      fontWeight: '500',
      ...sizeStyles[size],
      ...variantStyles[variant],
    };
  };

  return (
    <View style={[getBadgeStyles(), style]}>
      <Text style={getTextStyles()}>{text}</Text>
    </View>
  );
};

export default Badge;
