import React from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  ViewStyle,
  TextStyle,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';

interface HeaderProps {
  title: string;
  subtitle?: string;
  leftIcon?: keyof typeof Ionicons.glyphMap;
  rightIcon?: keyof typeof Ionicons.glyphMap;
  onLeftPress?: () => void;
  onRightPress?: () => void;
  backgroundColor?: string;
  titleColor?: string;
  iconColor?: string;
  style?: ViewStyle;
}

const Header: React.FC<HeaderProps> = ({
  title,
  subtitle,
  leftIcon,
  rightIcon,
  onLeftPress,
  onRightPress,
  backgroundColor = 'white',
  titleColor = '#111827',
  iconColor = '#dc2626',
  style,
}) => {
  const headerStyle: ViewStyle = {
    paddingHorizontal: 20,
    paddingVertical: 16,
    backgroundColor,
    borderBottomWidth: 1,
    borderBottomColor: '#e5e7eb',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  };

  const titleContainerStyle: ViewStyle = {
    flex: 1,
    alignItems: 'center',
  };

  const titleStyle: TextStyle = {
    fontSize: 20,
    fontWeight: 'bold',
    color: titleColor,
  };

  const subtitleStyle: TextStyle = {
    fontSize: 14,
    color: '#6b7280',
    marginTop: 2,
  };

  const iconButtonStyle: ViewStyle = {
    padding: 8,
    borderRadius: 8,
    backgroundColor: '#f3f4f6',
    width: 40,
    height: 40,
    alignItems: 'center',
    justifyContent: 'center',
  };

  return (
    <View style={[headerStyle, style]}>
      {/* Left Icon */}
      <View style={{ width: 40 }}>
        {leftIcon && (
          <TouchableOpacity
            onPress={onLeftPress}
            style={iconButtonStyle}
          >
            <Ionicons name={leftIcon} size={20} color={iconColor} />
          </TouchableOpacity>
        )}
      </View>

      {/* Title */}
      <View style={titleContainerStyle}>
        <Text style={titleStyle}>{title}</Text>
        {subtitle && <Text style={subtitleStyle}>{subtitle}</Text>}
      </View>

      {/* Right Icon */}
      <View style={{ width: 40, alignItems: 'flex-end' }}>
        {rightIcon && (
          <TouchableOpacity
            onPress={onRightPress}
            style={iconButtonStyle}
          >
            <Ionicons name={rightIcon} size={20} color={iconColor} />
          </TouchableOpacity>
        )}
      </View>
    </View>
  );
};

export default Header;
