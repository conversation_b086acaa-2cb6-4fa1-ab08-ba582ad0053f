import React, { createContext, useContext, useReducer, ReactNode } from 'react';
import {
  DeliveryHistoryState,
  DeliveryHistoryContextType,
  DeliveryOrder,
  OrderHistoryFilter,
  PerformanceMetrics,
  DailyPerformance,
  WeeklyPerformance,
  MonthlyPerformance,
  RatingTrend,
  DeliveryTimeAnalysis,
  LocationAnalysis,
  OrderStatus,
  OrderType,
} from '../types/deliveryHistory';

// Initial state
const initialState: DeliveryHistoryState = {
  orders: [],
  filteredOrders: [],
  currentFilter: {
    sortBy: 'date',
    sortOrder: 'desc',
  },
  selectedOrder: null,
  performanceMetrics: null,
  dailyPerformance: [],
  weeklyPerformance: [],
  monthlyPerformance: [],
  ratingTrends: [],
  deliveryTimeAnalysis: [],
  locationAnalysis: [],
  isLoading: false,
  error: null,
  selectedPeriod: 'month',
};

// Action types
type DeliveryHistoryAction =
  | { type: 'SET_LOADING'; payload: boolean }
  | { type: 'SET_ERROR'; payload: string | null }
  | { type: 'SET_ORDERS'; payload: DeliveryOrder[] }
  | { type: 'SET_FILTERED_ORDERS'; payload: DeliveryOrder[] }
  | { type: 'SET_CURRENT_FILTER'; payload: OrderHistoryFilter }
  | { type: 'SET_SELECTED_ORDER'; payload: DeliveryOrder | null }
  | { type: 'SET_PERFORMANCE_METRICS'; payload: PerformanceMetrics }
  | { type: 'SET_DAILY_PERFORMANCE'; payload: DailyPerformance[] }
  | { type: 'SET_WEEKLY_PERFORMANCE'; payload: WeeklyPerformance[] }
  | { type: 'SET_MONTHLY_PERFORMANCE'; payload: MonthlyPerformance[] }
  | { type: 'SET_RATING_TRENDS'; payload: RatingTrend[] }
  | { type: 'SET_DELIVERY_TIME_ANALYSIS'; payload: DeliveryTimeAnalysis[] }
  | { type: 'SET_LOCATION_ANALYSIS'; payload: LocationAnalysis[] }
  | { type: 'SET_SELECTED_PERIOD'; payload: 'today' | 'week' | 'month' | 'year' | 'all' }
  | { type: 'CLEAR_ERROR' };

// Reducer
const deliveryHistoryReducer = (
  state: DeliveryHistoryState,
  action: DeliveryHistoryAction
): DeliveryHistoryState => {
  switch (action.type) {
    case 'SET_LOADING':
      return { ...state, isLoading: action.payload };
    case 'SET_ERROR':
      return { ...state, error: action.payload, isLoading: false };
    case 'SET_ORDERS':
      return { ...state, orders: action.payload };
    case 'SET_FILTERED_ORDERS':
      return { ...state, filteredOrders: action.payload };
    case 'SET_CURRENT_FILTER':
      return { ...state, currentFilter: action.payload };
    case 'SET_SELECTED_ORDER':
      return { ...state, selectedOrder: action.payload };
    case 'SET_PERFORMANCE_METRICS':
      return { ...state, performanceMetrics: action.payload };
    case 'SET_DAILY_PERFORMANCE':
      return { ...state, dailyPerformance: action.payload };
    case 'SET_WEEKLY_PERFORMANCE':
      return { ...state, weeklyPerformance: action.payload };
    case 'SET_MONTHLY_PERFORMANCE':
      return { ...state, monthlyPerformance: action.payload };
    case 'SET_RATING_TRENDS':
      return { ...state, ratingTrends: action.payload };
    case 'SET_DELIVERY_TIME_ANALYSIS':
      return { ...state, deliveryTimeAnalysis: action.payload };
    case 'SET_LOCATION_ANALYSIS':
      return { ...state, locationAnalysis: action.payload };
    case 'SET_SELECTED_PERIOD':
      return { ...state, selectedPeriod: action.payload };
    case 'CLEAR_ERROR':
      return { ...state, error: null };
    default:
      return state;
  }
};

// Context
const DeliveryHistoryContext = createContext<DeliveryHistoryContextType | undefined>(undefined);

// Provider component
interface DeliveryHistoryProviderProps {
  children: ReactNode;
}

export const DeliveryHistoryProvider: React.FC<DeliveryHistoryProviderProps> = ({ children }) => {
  const [state, dispatch] = useReducer(deliveryHistoryReducer, initialState);

  // Order history functions
  const fetchOrderHistory = async (filter?: OrderHistoryFilter) => {
    dispatch({ type: 'SET_LOADING', payload: true });
    try {
      // Mock API call - replace with actual API
      const mockOrders: DeliveryOrder[] = [
        {
          id: '1',
          orderNumber: 'ORD-2024-001',
          status: OrderStatus.DELIVERED,
          type: OrderType.FOOD,
          customer: {
            id: 'c1',
            name: 'Ahmed Khan',
            phone: '+92 300 1234567',
            rating: 4.8,
          },
          restaurant: {
            id: 'r1',
            name: 'KFC DHA',
            address: 'DHA Phase 2, Lahore',
            phone: '+92 42 1234567',
            category: 'Fast Food',
          },
          pickupLocation: {
            latitude: 31.4697,
            longitude: 74.4142,
            address: 'DHA Phase 2, Lahore',
            area: 'DHA',
            city: 'Lahore',
          },
          deliveryLocation: {
            latitude: 31.4504,
            longitude: 74.3588,
            address: 'Gulberg III, Lahore',
            area: 'Gulberg',
            city: 'Lahore',
          },
          items: [
            { id: '1', name: 'Zinger Burger', quantity: 2, price: 800 },
            { id: '2', name: 'Fries', quantity: 1, price: 300 },
          ],
          totalAmount: 1100,
          deliveryFee: 150,
          tip: 100,
          timeline: {
            orderPlaced: '2024-01-20T12:00:00Z',
            orderAccepted: '2024-01-20T12:02:00Z',
            arrivedAtRestaurant: '2024-01-20T12:15:00Z',
            orderPickedUp: '2024-01-20T12:25:00Z',
            deliveryStarted: '2024-01-20T12:26:00Z',
            orderDelivered: '2024-01-20T12:45:00Z',
          },
          metrics: {
            totalDistance: 5.2,
            deliveryTime: 19,
            pickupTime: 10,
            waitingTime: 8,
            averageSpeed: 16.4,
          },
          rating: {
            overall: 5,
            delivery: 5,
            communication: 4,
            timeliness: 5,
            comment: 'Great service, very fast delivery!',
            ratedAt: '2024-01-20T13:00:00Z',
          },
          paymentMethod: 'Cash',
          createdAt: '2024-01-20T12:00:00Z',
          updatedAt: '2024-01-20T12:45:00Z',
        },
        // Add more mock orders as needed
      ];

      await new Promise(resolve => setTimeout(resolve, 1000)); // Simulate network delay
      dispatch({ type: 'SET_ORDERS', payload: mockOrders });
      dispatch({ type: 'SET_FILTERED_ORDERS', payload: mockOrders });
      
      if (filter) {
        dispatch({ type: 'SET_CURRENT_FILTER', payload: filter });
      }
    } catch (error: any) {
      dispatch({ type: 'SET_ERROR', payload: error.message || 'Failed to fetch order history' });
    } finally {
      dispatch({ type: 'SET_LOADING', payload: false });
    }
  };

  const fetchOrderDetails = async (orderId: string) => {
    dispatch({ type: 'SET_LOADING', payload: true });
    try {
      // Mock API call - replace with actual API
      const mockOrder = state.orders.find(order => order.id === orderId);
      if (mockOrder) {
        dispatch({ type: 'SET_SELECTED_ORDER', payload: mockOrder });
      } else {
        throw new Error('Order not found');
      }
    } catch (error: any) {
      dispatch({ type: 'SET_ERROR', payload: error.message || 'Failed to fetch order details' });
    } finally {
      dispatch({ type: 'SET_LOADING', payload: false });
    }
  };

  const applyFilter = (filter: OrderHistoryFilter) => {
    dispatch({ type: 'SET_CURRENT_FILTER', payload: filter });
    
    let filtered = [...state.orders];

    // Apply filters
    if (filter.status && filter.status.length > 0) {
      filtered = filtered.filter(order => filter.status!.includes(order.status));
    }

    if (filter.type && filter.type.length > 0) {
      filtered = filtered.filter(order => filter.type!.includes(order.type));
    }

    if (filter.startDate) {
      filtered = filtered.filter(order => new Date(order.createdAt) >= new Date(filter.startDate!));
    }

    if (filter.endDate) {
      filtered = filtered.filter(order => new Date(order.createdAt) <= new Date(filter.endDate!));
    }

    if (filter.area) {
      filtered = filtered.filter(order => 
        order.pickupLocation.area.toLowerCase().includes(filter.area!.toLowerCase()) ||
        order.deliveryLocation.area.toLowerCase().includes(filter.area!.toLowerCase())
      );
    }

    if (filter.minAmount) {
      filtered = filtered.filter(order => order.totalAmount >= filter.minAmount!);
    }

    if (filter.maxAmount) {
      filtered = filtered.filter(order => order.totalAmount <= filter.maxAmount!);
    }

    if (filter.rating) {
      filtered = filtered.filter(order => order.rating && order.rating.overall >= filter.rating!);
    }

    // Apply sorting
    filtered.sort((a, b) => {
      let aValue: any, bValue: any;
      
      switch (filter.sortBy) {
        case 'date':
          aValue = new Date(a.createdAt);
          bValue = new Date(b.createdAt);
          break;
        case 'amount':
          aValue = a.totalAmount;
          bValue = b.totalAmount;
          break;
        case 'rating':
          aValue = a.rating?.overall || 0;
          bValue = b.rating?.overall || 0;
          break;
        case 'distance':
          aValue = a.metrics?.totalDistance || 0;
          bValue = b.metrics?.totalDistance || 0;
          break;
        default:
          aValue = new Date(a.createdAt);
          bValue = new Date(b.createdAt);
      }

      if (filter.sortOrder === 'asc') {
        return aValue > bValue ? 1 : -1;
      } else {
        return aValue < bValue ? 1 : -1;
      }
    });

    dispatch({ type: 'SET_FILTERED_ORDERS', payload: filtered });
  };

  const clearFilter = () => {
    const defaultFilter: OrderHistoryFilter = {
      sortBy: 'date',
      sortOrder: 'desc',
    };
    dispatch({ type: 'SET_CURRENT_FILTER', payload: defaultFilter });
    dispatch({ type: 'SET_FILTERED_ORDERS', payload: state.orders });
  };

  const setSelectedOrder = (order: DeliveryOrder | null) => {
    dispatch({ type: 'SET_SELECTED_ORDER', payload: order });
  };

  // Performance functions
  const fetchPerformanceMetrics = async (period?: string) => {
    dispatch({ type: 'SET_LOADING', payload: true });
    try {
      // Mock API call - replace with actual API
      const mockMetrics: PerformanceMetrics = {
        averageDeliveryTime: 22.5,
        averagePickupTime: 8.3,
        averageWaitingTime: 5.2,
        averageDistance: 4.8,
        averageSpeed: 18.5,
        totalOrders: 156,
        completedOrders: 142,
        cancelledOrders: 8,
        rejectedOrders: 6,
        acceptanceRate: 96.2,
        completionRate: 91.0,
        cancellationRate: 5.1,
        onTimeDeliveryRate: 88.7,
        averageRating: 4.6,
        totalRatings: 128,
        ratingDistribution: {
          1: 2,
          2: 3,
          3: 8,
          4: 35,
          5: 80,
        },
        totalEarnings: 45750.50,
        averageEarningsPerOrder: 293.27,
        totalTips: 8420.00,
        averageTipPerOrder: 53.97,
        period: (period as any) || 'month',
        startDate: '2024-01-01',
        endDate: '2024-01-31',
      };

      await new Promise(resolve => setTimeout(resolve, 1000));
      dispatch({ type: 'SET_PERFORMANCE_METRICS', payload: mockMetrics });
    } catch (error: any) {
      dispatch({ type: 'SET_ERROR', payload: error.message || 'Failed to fetch performance metrics' });
    } finally {
      dispatch({ type: 'SET_LOADING', payload: false });
    }
  };

  const fetchDailyPerformance = async (startDate: string, endDate: string) => {
    dispatch({ type: 'SET_LOADING', payload: true });
    try {
      // Mock API call - replace with actual API
      const mockDailyPerformance: DailyPerformance[] = [
        { date: '2024-01-15', ordersCompleted: 8, totalEarnings: 2340, averageRating: 4.7, averageDeliveryTime: 21, totalDistance: 38.5, hoursWorked: 6.5 },
        { date: '2024-01-16', ordersCompleted: 12, totalEarnings: 3520, averageRating: 4.5, averageDeliveryTime: 24, totalDistance: 52.3, hoursWorked: 8.0 },
        // Add more mock data as needed
      ];

      await new Promise(resolve => setTimeout(resolve, 1000));
      dispatch({ type: 'SET_DAILY_PERFORMANCE', payload: mockDailyPerformance });
    } catch (error: any) {
      dispatch({ type: 'SET_ERROR', payload: error.message || 'Failed to fetch daily performance' });
    } finally {
      dispatch({ type: 'SET_LOADING', payload: false });
    }
  };

  const fetchWeeklyPerformance = async (year: number) => {
    // Implementation for weekly performance
    console.log('Fetching weekly performance for year:', year);
  };

  const fetchMonthlyPerformance = async (year: number) => {
    // Implementation for monthly performance
    console.log('Fetching monthly performance for year:', year);
  };

  // Analytics functions
  const fetchRatingTrends = async (period: string) => {
    // Implementation for rating trends
    console.log('Fetching rating trends for period:', period);
  };

  const fetchDeliveryTimeAnalysis = async (period: string) => {
    // Implementation for delivery time analysis
    console.log('Fetching delivery time analysis for period:', period);
  };

  const fetchLocationAnalysis = async (period: string) => {
    // Implementation for location analysis
    console.log('Fetching location analysis for period:', period);
  };

  // Utility functions
  const setSelectedPeriod = (period: 'today' | 'week' | 'month' | 'year' | 'all') => {
    dispatch({ type: 'SET_SELECTED_PERIOD', payload: period });
  };

  const exportOrderHistory = async (format: 'csv' | 'pdf'): Promise<Blob> => {
    // Implementation for export functionality
    console.log('Exporting order history in format:', format);
    return new Blob();
  };

  const clearError = () => {
    dispatch({ type: 'CLEAR_ERROR' });
  };

  const contextValue: DeliveryHistoryContextType = {
    state,
    fetchOrderHistory,
    fetchOrderDetails,
    applyFilter,
    clearFilter,
    setSelectedOrder,
    fetchPerformanceMetrics,
    fetchDailyPerformance,
    fetchWeeklyPerformance,
    fetchMonthlyPerformance,
    fetchRatingTrends,
    fetchDeliveryTimeAnalysis,
    fetchLocationAnalysis,
    setSelectedPeriod,
    exportOrderHistory,
    clearError,
  };

  return (
    <DeliveryHistoryContext.Provider value={contextValue}>
      {children}
    </DeliveryHistoryContext.Provider>
  );
};

// Hook to use the context
export const useDeliveryHistory = (): DeliveryHistoryContextType => {
  const context = useContext(DeliveryHistoryContext);
  if (!context) {
    throw new Error('useDeliveryHistory must be used within a DeliveryHistoryProvider');
  }
  return context;
};

export default DeliveryHistoryContext;
