import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StatusBar,
  Animated,
  Alert,
  Linking,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import { LinearGradient } from 'expo-linear-gradient';

interface MaintenanceModeScreenProps {
  estimatedDuration?: string;
  maintenanceType?: 'scheduled' | 'emergency' | 'update';
  onRetry?: () => void;
  contactSupport?: () => void;
}

const MaintenanceModeScreen: React.FC<MaintenanceModeScreenProps> = ({
  estimatedDuration = '30 minutes',
  maintenanceType = 'scheduled',
  onRetry,
  contactSupport,
}) => {
  const [fadeAnimation] = useState(new Animated.Value(0));
  const [pulseAnimation] = useState(new Animated.Value(1));
  const [currentTime, setCurrentTime] = useState(new Date());

  useEffect(() => {
    // Start animations
    Animated.timing(fadeAnimation, {
      toValue: 1,
      duration: 1000,
      useNativeDriver: true,
    }).start();

    // Pulse animation for the maintenance icon
    Animated.loop(
      Animated.sequence([
        Animated.timing(pulseAnimation, {
          toValue: 1.2,
          duration: 2000,
          useNativeDriver: true,
        }),
        Animated.timing(pulseAnimation, {
          toValue: 1,
          duration: 2000,
          useNativeDriver: true,
        }),
      ])
    ).start();

    // Update time every minute
    const timeInterval = setInterval(() => {
      setCurrentTime(new Date());
    }, 60000);

    return () => clearInterval(timeInterval);
  }, []);

  const getMaintenanceConfig = () => {
    switch (maintenanceType) {
      case 'emergency':
        return {
          icon: 'warning',
          title: 'Emergency Maintenance',
          subtitle: 'Unexpected maintenance in progress',
          description: 'We\'re experiencing technical issues and are working to resolve them as quickly as possible. We apologize for any inconvenience.',
          color: '#dc2626',
          backgroundColor: '#fef2f2',
        };
      case 'update':
        return {
          icon: 'download',
          title: 'System Update',
          subtitle: 'Upgrading to serve you better',
          description: 'We\'re installing new features and improvements to enhance your delivery experience. Thank you for your patience!',
          color: '#10b981',
          backgroundColor: '#f0fdf4',
        };
      default: // scheduled
        return {
          icon: 'construct',
          title: 'Scheduled Maintenance',
          subtitle: 'Routine system maintenance',
          description: 'We\'re performing scheduled maintenance to keep our systems running smoothly and securely.',
          color: '#3b82f6',
          backgroundColor: '#eff6ff',
        };
    }
  };

  const handleCheckStatus = () => {
    Alert.alert(
      'Check Status',
      'Would you like to check our status page for real-time updates?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Open Status Page',
          onPress: () => Linking.openURL('https://status.foodway.com'),
        },
      ]
    );
  };

  const handleContactSupport = () => {
    Alert.alert(
      'Contact Support',
      'How would you like to contact our support team?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'WhatsApp',
          onPress: () => Linking.openURL('whatsapp://send?phone=+923001234567'),
        },
        {
          text: 'Email',
          onPress: () => Linking.openURL('mailto:<EMAIL>'),
        },
      ]
    );
  };

  const config = getMaintenanceConfig();

  return (
    <View style={{ flex: 1, backgroundColor: config.backgroundColor }}>
      <SafeAreaView style={{ flex: 1 }}>
        <StatusBar barStyle="light-content" backgroundColor={config.color} />
        
        {/* Enhanced Header */}
        <LinearGradient
          colors={[config.color, `${config.color}CC`]}
          style={{
            paddingHorizontal: 20,
            paddingTop: 40,
            paddingBottom: 60,
            borderBottomLeftRadius: 40,
            borderBottomRightRadius: 40,
            shadowColor: config.color,
            shadowOffset: { width: 0, height: 12 },
            shadowOpacity: 0.3,
            shadowRadius: 20,
            elevation: 16,
          }}
        >
          <Animated.View style={{
            alignItems: 'center',
            opacity: fadeAnimation,
          }}>
            <Animated.View style={{
              transform: [{ scale: pulseAnimation }],
              marginBottom: 24,
            }}>
              <View style={{
                width: 120,
                height: 120,
                backgroundColor: 'rgba(255,255,255,0.2)',
                borderRadius: 60,
                alignItems: 'center',
                justifyContent: 'center',
                borderWidth: 4,
                borderColor: 'rgba(255,255,255,0.3)',
              }}>
                <Ionicons name={config.icon as any} size={60} color="white" />
              </View>
            </Animated.View>
            
            <Text style={{
              fontSize: 28,
              fontWeight: 'bold',
              color: 'white',
              marginBottom: 8,
              textAlign: 'center',
            }}>
              {config.title}
            </Text>
            
            <Text style={{
              fontSize: 16,
              color: 'rgba(255,255,255,0.9)',
              textAlign: 'center',
              fontWeight: '500',
            }}>
              {config.subtitle}
            </Text>
          </Animated.View>
        </LinearGradient>

        <Animated.View style={{
          flex: 1,
          opacity: fadeAnimation,
          paddingHorizontal: 20,
          paddingTop: 40,
        }}>
          {/* Main Content Card */}
          <View style={{
            backgroundColor: 'white',
            borderRadius: 24,
            padding: 32,
            shadowColor: config.color,
            shadowOffset: { width: 0, height: 8 },
            shadowOpacity: 0.1,
            shadowRadius: 16,
            elevation: 8,
            borderWidth: 1,
            borderColor: `${config.color}20`,
            marginBottom: 24,
          }}>
            <Text style={{
              fontSize: 18,
              color: '#374151',
              lineHeight: 28,
              textAlign: 'center',
              marginBottom: 32,
            }}>
              {config.description}
            </Text>

            {/* Estimated Duration */}
            <View style={{
              backgroundColor: `${config.color}10`,
              borderRadius: 16,
              padding: 20,
              marginBottom: 24,
              borderWidth: 1,
              borderColor: `${config.color}30`,
            }}>
              <View style={{
                flexDirection: 'row',
                alignItems: 'center',
                justifyContent: 'center',
                marginBottom: 8,
              }}>
                <Ionicons name="time" size={20} color={config.color} />
                <Text style={{
                  fontSize: 16,
                  fontWeight: '600',
                  color: config.color,
                  marginLeft: 8,
                }}>
                  Estimated Duration
                </Text>
              </View>
              <Text style={{
                fontSize: 24,
                fontWeight: 'bold',
                color: config.color,
                textAlign: 'center',
              }}>
                {estimatedDuration}
              </Text>
            </View>

            {/* Current Time */}
            <View style={{
              alignItems: 'center',
              marginBottom: 24,
            }}>
              <Text style={{
                fontSize: 14,
                color: '#6b7280',
                marginBottom: 4,
              }}>
                Current Time
              </Text>
              <Text style={{
                fontSize: 18,
                fontWeight: '600',
                color: '#111827',
              }}>
                {currentTime.toLocaleTimeString([], { 
                  hour: '2-digit', 
                  minute: '2-digit',
                  hour12: true 
                })}
              </Text>
            </View>

            {/* Action Buttons */}
            <View style={{ gap: 12 }}>
              <TouchableOpacity
                onPress={onRetry}
                style={{
                  backgroundColor: config.color,
                  borderRadius: 16,
                  paddingVertical: 16,
                  paddingHorizontal: 24,
                  alignItems: 'center',
                  flexDirection: 'row',
                  justifyContent: 'center',
                }}
              >
                <Ionicons name="refresh" size={20} color="white" />
                <Text style={{
                  fontSize: 16,
                  fontWeight: 'bold',
                  color: 'white',
                  marginLeft: 8,
                }}>
                  Check Again
                </Text>
              </TouchableOpacity>

              <TouchableOpacity
                onPress={handleCheckStatus}
                style={{
                  backgroundColor: 'transparent',
                  borderWidth: 2,
                  borderColor: config.color,
                  borderRadius: 16,
                  paddingVertical: 16,
                  paddingHorizontal: 24,
                  alignItems: 'center',
                  flexDirection: 'row',
                  justifyContent: 'center',
                }}
              >
                <Ionicons name="information-circle" size={20} color={config.color} />
                <Text style={{
                  fontSize: 16,
                  fontWeight: 'bold',
                  color: config.color,
                  marginLeft: 8,
                }}>
                  Status Updates
                </Text>
              </TouchableOpacity>
            </View>
          </View>

          {/* Support Section */}
          <View style={{
            backgroundColor: 'white',
            borderRadius: 20,
            padding: 24,
            shadowColor: '#000',
            shadowOffset: { width: 0, height: 4 },
            shadowOpacity: 0.1,
            shadowRadius: 8,
            elevation: 4,
          }}>
            <View style={{
              flexDirection: 'row',
              alignItems: 'center',
              marginBottom: 16,
            }}>
              <Ionicons name="headset" size={24} color="#6b7280" />
              <Text style={{
                fontSize: 18,
                fontWeight: '600',
                color: '#111827',
                marginLeft: 12,
              }}>
                Need Immediate Help?
              </Text>
            </View>
            
            <Text style={{
              fontSize: 14,
              color: '#6b7280',
              lineHeight: 20,
              marginBottom: 16,
            }}>
              If you have urgent delivery-related questions, our support team is still available to assist you.
            </Text>

            <TouchableOpacity
              onPress={handleContactSupport}
              style={{
                backgroundColor: '#10b981',
                borderRadius: 12,
                paddingVertical: 12,
                paddingHorizontal: 16,
                alignItems: 'center',
                flexDirection: 'row',
                justifyContent: 'center',
              }}
            >
              <Ionicons name="chatbubble" size={16} color="white" />
              <Text style={{
                fontSize: 14,
                fontWeight: '600',
                color: 'white',
                marginLeft: 8,
              }}>
                Contact Support
              </Text>
            </TouchableOpacity>
          </View>

          {/* Footer */}
          <View style={{
            alignItems: 'center',
            paddingVertical: 24,
          }}>
            <Text style={{
              fontSize: 12,
              color: '#9ca3af',
              textAlign: 'center',
            }}>
              Thank you for your patience while we improve FoodWay
            </Text>
          </View>
        </Animated.View>
      </SafeAreaView>
    </View>
  );
};

export default MaintenanceModeScreen;
