import React, { useState, useEffect } from 'react';
import { createStackNavigator } from '@react-navigation/stack';
import AsyncStorage from '@react-native-async-storage/async-storage';

// Screens
import SplashScreen from '../screens/auth/SplashScreen';
import OnboardingScreen from '../screens/auth/OnboardingScreen';
import LoginScreen from '../screens/auth/LoginScreen';
import RegistrationScreen from '../screens/auth/RegistrationScreen';
import RegistrationInfoScreen from '../screens/auth/RegistrationInfoScreen';
import DocumentStatusScreen from '../screens/auth/DocumentStatusScreen';
import DocumentUploadScreen from '../screens/auth/DocumentUploadScreen';

// Types
import { RegistrationData, DocumentStatus, DocumentVerificationStatus } from '../types/auth';

export type AuthStackParamList = {
  Splash: undefined;
  Onboarding: undefined;
  Login: undefined;
  Registration: undefined;
  RegistrationInfo: undefined;
  DocumentStatus: undefined;
  DocumentUpload: {
    documentType: string;
    documentTitle: string;
    existingImage?: string;
  };
};

const Stack = createStackNavigator<AuthStackParamList>();

const AuthNavigator: React.FC = () => {
  const [initialRoute, setInitialRoute] = useState<keyof AuthStackParamList>('Splash');
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    checkInitialRoute();
  }, []);

  const checkInitialRoute = async () => {
    try {
      const hasSeenOnboarding = await AsyncStorage.getItem('hasSeenOnboarding');
      const isLoggedIn = await AsyncStorage.getItem('userToken');

      if (!hasSeenOnboarding) {
        setInitialRoute('Splash');
      } else if (!isLoggedIn) {
        setInitialRoute('Login');
      } else {
        setInitialRoute('Login'); // Will be handled by main navigator
      }
    } catch (error) {
      console.error('Error checking initial route:', error);
      setInitialRoute('Splash');
    } finally {
      setIsLoading(false);
    }
  };

  if (isLoading) {
    return null; // Or a loading component
  }

  return (
    <Stack.Navigator
      id={"AuthStack" as any}
      initialRouteName={initialRoute}
      screenOptions={{
        headerShown: false,
        gestureEnabled: false,
      }}
    >
      <Stack.Screen
        name="Splash"
        component={SplashScreenWrapper}
      />
      <Stack.Screen
        name="Onboarding"
        component={OnboardingScreenWrapper}
      />
      <Stack.Screen
        name="Login"
        component={LoginScreen}
      />
      <Stack.Screen
        name="Registration"
        component={RegistrationScreenWrapper}
      />
      <Stack.Screen
        name="RegistrationInfo"
        component={RegistrationInfoScreen}
      />
      <Stack.Screen
        name="DocumentStatus"
        component={DocumentStatusScreenWrapper}
      />
      <Stack.Screen
        name="DocumentUpload"
        component={DocumentUploadScreenWrapper}
      />
    </Stack.Navigator>
  );
};

// Screen Wrappers with Navigation Logic

const SplashScreenWrapper: React.FC<any> = ({ navigation }) => {
  const handleSplashFinish = async () => {
    try {
      const hasSeenOnboarding = await AsyncStorage.getItem('hasSeenOnboarding');
      if (hasSeenOnboarding) {
        navigation.replace('Login');
      } else {
        navigation.replace('Onboarding');
      }
    } catch (error) {
      navigation.replace('Onboarding');
    }
  };

  return <SplashScreen onFinish={handleSplashFinish} />;
};

const OnboardingScreenWrapper: React.FC<any> = ({ navigation }) => {
  const handleOnboardingComplete = async () => {
    try {
      await AsyncStorage.setItem('hasSeenOnboarding', 'true');
      navigation.replace('Login');
    } catch (error) {
      console.error('Error saving onboarding status:', error);
      navigation.replace('Login');
    }
  };

  const handleSkip = async () => {
    await handleOnboardingComplete();
  };

  return (
    <OnboardingScreen
      onComplete={handleOnboardingComplete}
      onSkip={handleSkip}
    />
  );
};

const RegistrationScreenWrapper: React.FC<any> = ({ navigation }) => {
  const handleRegistrationComplete = async (data: RegistrationData) => {
    try {
      console.log('Registration data:', data);
      // Here you would send registration data to your backend

      // Navigate to document status screen
      navigation.replace('DocumentStatus');
    } catch (error) {
      console.error('Registration failed:', error);
    }
  };

  const handleBack = () => {
    navigation.goBack();
  };

  return (
    <RegistrationScreen
      onComplete={handleRegistrationComplete}
      onBack={handleBack}
    />
  );
};

const DocumentStatusScreenWrapper: React.FC<any> = ({ navigation }) => {
  // Mock document status - in real app, this would come from API
  const [documentStatus] = useState<DocumentStatus>({
    cnic: DocumentVerificationStatus.PENDING,
    passport: DocumentVerificationStatus.NOT_SUBMITTED,
    drivingLicense: DocumentVerificationStatus.APPROVED,
    vehicleRegistration: DocumentVerificationStatus.REJECTED,
    profilePhoto: DocumentVerificationStatus.PENDING,
    overall: DocumentVerificationStatus.PENDING,
    rejectionReasons: ['Vehicle registration document is not clear', 'Please upload a clearer image'],
    lastUpdated: new Date().toISOString(),
  });

  const handleUploadDocument = (documentType: string) => {
    const documentTitles: Record<string, string> = {
      cnic: 'CNIC',
      passport: 'Passport',
      drivingLicense: 'Driving License',
      vehicleRegistration: 'Vehicle Registration',
      profilePhoto: 'Profile Photo',
    };

    navigation.navigate('DocumentUpload', {
      documentType,
      documentTitle: documentTitles[documentType] || 'Document',
    });
  };

  const handleRefresh = async () => {
    console.log('Refreshing document status...');
    // Implement refresh logic
  };

  const handleContinue = () => {
    // When all documents are approved, continue to main app
    console.log('All documents approved, continuing to main app...');
  };

  return (
    <DocumentStatusScreen
      documentStatus={documentStatus}
      onUploadDocument={handleUploadDocument}
      onRefresh={handleRefresh}
      onContinue={handleContinue}
    />
  );
};

const DocumentUploadScreenWrapper: React.FC<any> = ({ navigation, route }) => {
  const { documentType, documentTitle, existingImage } = route.params;

  const handleUpload = async (uri: string) => {
    try {
      console.log('Uploading document:', documentType, uri);
      // Here you would upload the document to your backend

      // Navigate back to document status
      navigation.goBack();
    } catch (error) {
      console.error('Document upload failed:', error);
    }
  };

  const handleBack = () => {
    navigation.goBack();
  };

  return (
    <DocumentUploadScreen
      documentType={documentType}
      documentTitle={documentTitle}
      onUpload={handleUpload}
      onBack={handleBack}
      existingImage={existingImage}
    />
  );
};

export default AuthNavigator;
