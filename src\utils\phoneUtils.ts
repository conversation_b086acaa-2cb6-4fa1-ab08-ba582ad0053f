import { Linking, Alert, Platform } from 'react-native';

export interface PhoneCallOptions {
  phoneNumber: string;
  contactName?: string;
  showConfirmation?: boolean;
  fallbackMessage?: string;
}

/**
 * Utility class for handling phone calls with proper error handling
 */
export class PhoneUtils {
  /**
   * Make a phone call with proper error handling and confirmation
   */
  static async makeCall(options: PhoneCallOptions): Promise<boolean> {
    const {
      phoneNumber,
      contactName = 'Contact',
      showConfirmation = true,
      fallbackMessage = 'Unable to make phone call. Please dial manually.'
    } = options;

    // Clean the phone number
    const cleanedNumber = this.cleanPhoneNumber(phoneNumber);
    
    if (!this.isValidPhoneNumber(cleanedNumber)) {
      Alert.alert('Invalid Number', 'The phone number is not valid.');
      return false;
    }

    const makeCallAction = async () => {
      try {
        const phoneUrl = `tel:${cleanedNumber}`;
        
        // Check if the device can handle phone calls
        const canMakeCall = await Linking.canOpenURL(phoneUrl);
        
        if (canMakeCall) {
          await Linking.openURL(phoneUrl);
          return true;
        } else {
          // Fallback for devices without phone capability (like tablets)
          Alert.alert(
            'Phone Not Available',
            `${fallbackMessage}\n\nNumber: ${this.formatPhoneNumber(cleanedNumber)}`,
            [
              { text: 'Copy Number', onPress: () => this.copyToClipboard(cleanedNumber) },
              { text: 'OK' }
            ]
          );
          return false;
        }
      } catch (error) {
        console.error('Error making phone call:', error);
        Alert.alert(
          'Call Failed',
          `${fallbackMessage}\n\nNumber: ${this.formatPhoneNumber(cleanedNumber)}`,
          [
            { text: 'Copy Number', onPress: () => this.copyToClipboard(cleanedNumber) },
            { text: 'OK' }
          ]
        );
        return false;
      }
    };

    if (showConfirmation) {
      return new Promise((resolve) => {
        Alert.alert(
          `Call ${contactName}`,
          `Do you want to call ${this.formatPhoneNumber(cleanedNumber)}?`,
          [
            { 
              text: 'Cancel', 
              style: 'cancel',
              onPress: () => resolve(false)
            },
            {
              text: 'Call',
              onPress: async () => {
                const success = await makeCallAction();
                resolve(success);
              }
            }
          ]
        );
      });
    } else {
      return makeCallAction();
    }
  }

  /**
   * Make an emergency call (no confirmation required)
   */
  static async makeEmergencyCall(phoneNumber: string, serviceName: string = 'Emergency Services'): Promise<boolean> {
    return this.makeCall({
      phoneNumber,
      contactName: serviceName,
      showConfirmation: false,
      fallbackMessage: `Unable to call ${serviceName}. Please dial manually.`
    });
  }

  /**
   * Open WhatsApp with a phone number
   */
  static async openWhatsApp(phoneNumber: string, message?: string): Promise<boolean> {
    try {
      const cleanedNumber = this.cleanPhoneNumber(phoneNumber);
      let whatsappUrl = `whatsapp://send?phone=${cleanedNumber}`;
      
      if (message) {
        whatsappUrl += `&text=${encodeURIComponent(message)}`;
      }

      const canOpenWhatsApp = await Linking.canOpenURL(whatsappUrl);
      
      if (canOpenWhatsApp) {
        await Linking.openURL(whatsappUrl);
        return true;
      } else {
        Alert.alert(
          'WhatsApp Not Available',
          'WhatsApp is not installed on your device.',
          [
            { text: 'OK' },
            {
              text: 'Call Instead',
              onPress: () => this.makeCall({ phoneNumber, showConfirmation: false })
            }
          ]
        );
        return false;
      }
    } catch (error) {
      console.error('Error opening WhatsApp:', error);
      Alert.alert('Error', 'Unable to open WhatsApp.');
      return false;
    }
  }

  /**
   * Clean phone number by removing non-numeric characters except +
   */
  private static cleanPhoneNumber(phoneNumber: string): string {
    return phoneNumber.replace(/[^\d+]/g, '');
  }

  /**
   * Validate phone number format
   */
  private static isValidPhoneNumber(phoneNumber: string): boolean {
    const cleaned = phoneNumber.replace(/[^\d]/g, '');
    return cleaned.length >= 7 && cleaned.length <= 15;
  }

  /**
   * Format phone number for display
   */
  private static formatPhoneNumber(phoneNumber: string): string {
    const cleaned = phoneNumber.replace(/[^\d+]/g, '');
    
    // Pakistan format
    if (cleaned.startsWith('+92') || cleaned.startsWith('92')) {
      const number = cleaned.replace(/^\+?92/, '');
      if (number.length === 10) {
        return `+92 ${number.slice(0, 3)}-${number.slice(3, 7)}-${number.slice(7)}`;
      }
    }
    
    // US format
    if (cleaned.startsWith('+1') || (cleaned.length === 10 && !cleaned.startsWith('+'))) {
      const number = cleaned.replace(/^\+?1/, '');
      if (number.length === 10) {
        return `+1 (${number.slice(0, 3)}) ${number.slice(3, 6)}-${number.slice(6)}`;
      }
    }
    
    // International format
    if (cleaned.startsWith('+')) {
      return cleaned;
    }
    
    // Default format
    return phoneNumber;
  }

  /**
   * Copy text to clipboard (placeholder - would need clipboard library)
   */
  private static copyToClipboard(text: string): void {
    // This would require @react-native-clipboard/clipboard
    // For now, just show the number
    Alert.alert('Phone Number', text);
  }

  /**
   * Get emergency numbers for different countries
   */
  static getEmergencyNumbers(country: string = 'PK'): { [key: string]: string } {
    const emergencyNumbers: { [key: string]: { [key: string]: string } } = {
      PK: {
        police: '15',
        ambulance: '1122',
        fire: '16',
        rescue: '1122'
      },
      US: {
        emergency: '911',
        police: '911',
        ambulance: '911',
        fire: '911'
      },
      UK: {
        emergency: '999',
        police: '999',
        ambulance: '999',
        fire: '999'
      }
    };

    return emergencyNumbers[country] || emergencyNumbers['PK'];
  }

  /**
   * Check if device supports phone calls
   */
  static async canMakePhoneCalls(): Promise<boolean> {
    try {
      return await Linking.canOpenURL('tel:');
    } catch (error) {
      return false;
    }
  }
}

// Export convenience functions
export const makeCall = PhoneUtils.makeCall;
export const makeEmergencyCall = PhoneUtils.makeEmergencyCall;
export const openWhatsApp = PhoneUtils.openWhatsApp;
export const canMakePhoneCalls = PhoneUtils.canMakePhoneCalls;
export const getEmergencyNumbers = PhoneUtils.getEmergencyNumbers;
