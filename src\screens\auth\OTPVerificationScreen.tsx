import React, { useState, useRef, useEffect } from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  Alert,
  Keyboard,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import { But<PERSON>, LoadingSpinner } from '../../components/ui';

interface OTPVerificationScreenProps {
  phoneNumber: string;
  onVerificationSuccess: (code: string) => void;
  onResendCode: () => void;
  onBack: () => void;
  isLoading?: boolean;
}

const OTPVerificationScreen: React.FC<OTPVerificationScreenProps> = ({
  phoneNumber,
  onVerificationSuccess,
  onResendCode,
  onBack,
  isLoading = false,
}) => {
  const [otp, setOtp] = useState(['', '', '', '', '', '']);
  const [timer, setTimer] = useState(60);
  const [canResend, setCanResend] = useState(false);
  const inputRefs = useRef<any[]>([]);

  useEffect(() => {
    if (timer > 0) {
      const interval = setInterval(() => {
        setTimer((prev) => prev - 1);
      }, 1000);
      return () => clearInterval(interval);
    } else {
      setCanResend(true);
    }
  }, [timer]);

  const handleOtpChange = (value: string, index: number) => {
    if (value.length > 1) return; // Prevent multiple characters

    const newOtp = [...otp];
    newOtp[index] = value;
    setOtp(newOtp);

    // Auto-focus next input
    if (value && index < 5) {
      inputRefs.current[index + 1]?.focus();
    }

    // Auto-submit when all fields are filled
    if (newOtp.every(digit => digit !== '') && newOtp.join('').length === 6) {
      Keyboard.dismiss();
      handleVerify(newOtp.join(''));
    }
  };

  const handleKeyPress = (key: string, index: number) => {
    if (key === 'Backspace' && !otp[index] && index > 0) {
      inputRefs.current[index - 1]?.focus();
    }
  };

  const handleVerify = (code?: string) => {
    const otpCode = code || otp.join('');
    if (otpCode.length !== 6) {
      Alert.alert('Invalid Code', 'Please enter a 6-digit verification code.');
      return;
    }
    onVerificationSuccess(otpCode);
  };

  const handleResend = () => {
    if (!canResend) return;
    
    setTimer(60);
    setCanResend(false);
    setOtp(['', '', '', '', '', '']);
    inputRefs.current[0]?.focus();
    onResendCode();
  };

  const formatPhoneNumber = (phone: string) => {
    // Format phone number for display
    if (phone.startsWith('+92')) {
      return phone.replace('+92', '+92 ').replace(/(\d{3})(\d{7})/, '$1 $2');
    }
    return phone;
  };

  return (
    <View style={{ flex: 1, backgroundColor: '#f1f5f9' }}>
      <SafeAreaView style={{ flex: 1 }}>
        {/* Enhanced Header */}
        <View style={{
          backgroundColor: '#dc2626',
          paddingHorizontal: 20,
          paddingTop: 20,
          paddingBottom: 28,
          borderBottomLeftRadius: 28,
          borderBottomRightRadius: 28,
          shadowColor: '#dc2626',
          shadowOffset: { width: 0, height: 12 },
          shadowOpacity: 0.3,
          shadowRadius: 20,
          elevation: 16,
        }}>
          <View style={{
            flexDirection: 'row',
            alignItems: 'center',
            marginBottom: 20,
          }}>
            <TouchableOpacity
              onPress={onBack}
              style={{
                width: 48,
                height: 48,
                borderRadius: 24,
                backgroundColor: 'rgba(255,255,255,0.2)',
                justifyContent: 'center',
                alignItems: 'center',
                marginRight: 16,
                borderWidth: 2,
                borderColor: 'rgba(255,255,255,0.3)',
              }}
            >
              <Ionicons name="arrow-back" size={24} color="white" />
            </TouchableOpacity>
            <View style={{ flex: 1 }}>
              <Text style={{
                fontSize: 24,
                fontWeight: 'bold',
                color: 'white',
                marginBottom: 4,
              }}>
                Verify Phone Number
              </Text>
              <Text style={{
                fontSize: 14,
                color: 'rgba(255,255,255,0.8)',
              }}>
                Enter the code we sent you
              </Text>
            </View>
          </View>
        </View>

        <View style={{ flex: 1, paddingHorizontal: 24, paddingVertical: 32 }}>
          {/* Enhanced Icon Section */}
          <View style={{ alignItems: 'center', marginBottom: 40 }}>
            <View style={{
              width: 100,
              height: 100,
              backgroundColor: 'white',
              borderRadius: 50,
              justifyContent: 'center',
              alignItems: 'center',
              marginBottom: 24,
              shadowColor: '#dc2626',
              shadowOffset: { width: 0, height: 8 },
              shadowOpacity: 0.15,
              shadowRadius: 20,
              elevation: 12,
              borderWidth: 4,
              borderColor: '#fef2f2',
            }}>
              <Ionicons name="phone-portrait" size={48} color="#dc2626" />
            </View>
            <Text style={{
              fontSize: 28,
              fontWeight: 'bold',
              color: '#111827',
              textAlign: 'center',
              marginBottom: 12,
            }}>
              Enter Verification Code
            </Text>
            <Text style={{
              fontSize: 16,
              color: '#6b7280',
              textAlign: 'center',
              lineHeight: 24,
            }}>
              We've sent a 6-digit code to{'\n'}
              <Text style={{ fontWeight: '600', color: '#111827' }}>
                {formatPhoneNumber(phoneNumber)}
              </Text>
            </Text>
          </View>

          {/* Enhanced OTP Input */}
          <View style={{
            flexDirection: 'row',
            justifyContent: 'center',
            marginBottom: 40,
            paddingHorizontal: 20,
          }}>
            {otp.map((digit, index) => (
              <TextInput
                key={index}
                ref={(ref) => (inputRefs.current[index] = ref)}
                style={{
                  width: 56,
                  height: 56,
                  borderWidth: 2,
                  borderColor: digit ? '#dc2626' : '#e2e8f0',
                  borderRadius: 16,
                  textAlign: 'center',
                  fontSize: 20,
                  fontWeight: 'bold',
                  marginHorizontal: 6,
                  backgroundColor: digit ? '#fef2f2' : 'white',
                  shadowColor: digit ? '#dc2626' : '#000',
                  shadowOffset: { width: 0, height: digit ? 4 : 2 },
                  shadowOpacity: digit ? 0.2 : 0.05,
                  shadowRadius: digit ? 8 : 4,
                  elevation: digit ? 6 : 2,
                  color: '#111827',
                  textAlignVertical: 'center',
                }}
                value={digit}
                onChangeText={(value) => handleOtpChange(value, index)}
                onKeyPress={({ nativeEvent }) => handleKeyPress(nativeEvent.key, index)}
                keyboardType="numeric"
                maxLength={1}
                selectTextOnFocus
              />
            ))}
          </View>

          {/* Enhanced Timer and Resend */}
          <View style={{ alignItems: 'center', marginBottom: 32 }}>
            {!canResend ? (
              <View style={{
                backgroundColor: 'white',
                paddingHorizontal: 20,
                paddingVertical: 12,
                borderRadius: 20,
                shadowColor: '#000',
                shadowOffset: { width: 0, height: 4 },
                shadowOpacity: 0.1,
                shadowRadius: 8,
                elevation: 4,
                borderWidth: 1,
                borderColor: '#f1f5f9',
              }}>
                <Text style={{ fontSize: 16, color: '#6b7280', textAlign: 'center' }}>
                  Resend code in{' '}
                  <Text style={{ fontWeight: 'bold', color: '#dc2626' }}>{timer}s</Text>
                </Text>
              </View>
            ) : (
              <TouchableOpacity
                onPress={handleResend}
                style={{
                  flexDirection: 'row',
                  alignItems: 'center',
                  backgroundColor: '#dc2626',
                  paddingHorizontal: 20,
                  paddingVertical: 12,
                  borderRadius: 20,
                  shadowColor: '#dc2626',
                  shadowOffset: { width: 0, height: 4 },
                  shadowOpacity: 0.3,
                  shadowRadius: 8,
                  elevation: 6,
                }}
              >
                <Ionicons name="refresh" size={18} color="white" />
                <Text style={{
                  color: 'white',
                  fontWeight: 'bold',
                  marginLeft: 8,
                  fontSize: 16,
                }}>
                  Resend Code
                </Text>
              </TouchableOpacity>
            )}
          </View>

          {/* Enhanced Verify Button */}
          <Button
            title="Verify & Continue"
            onPress={() => handleVerify()}
            disabled={otp.join('').length !== 6 || isLoading}
            loading={isLoading}
            variant="primary"
            size="lg"
            fullWidth
            style={{ marginBottom: 24 }}
          />

          {/* Enhanced Help Text */}
          <View style={{
            backgroundColor: '#f0f9ff',
            borderWidth: 2,
            borderColor: '#bae6fd',
            borderRadius: 16,
            padding: 20,
          }}>
            <View style={{ flexDirection: 'row', alignItems: 'flex-start' }}>
              <Ionicons name="information-circle" size={24} color="#0284c7" />
              <View style={{ flex: 1, marginLeft: 12 }}>
                <Text style={{
                  fontSize: 16,
                  fontWeight: 'bold',
                  color: '#0c4a6e',
                  marginBottom: 8,
                }}>
                  Didn't receive the code?
                </Text>
                <Text style={{
                  fontSize: 14,
                  color: '#075985',
                  lineHeight: 20,
                }}>
                  • Check your SMS messages{'\n'}
                  • Ensure you have network coverage{'\n'}
                  • Try resending the code{'\n'}
                  • Contact support if issues persist
                </Text>
              </View>
            </View>
          </View>
        </View>
      </SafeAreaView>
    </View>
  );
};

export default OTPVerificationScreen;
