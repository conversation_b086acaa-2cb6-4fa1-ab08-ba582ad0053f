// Support system types and interfaces

// Chat message types
export enum MessageType {
  TEXT = 'text',
  IMAGE = 'image',
  FILE = 'file',
  SYSTEM = 'system',
}

export enum MessageStatus {
  SENDING = 'sending',
  SENT = 'sent',
  DELIVERED = 'delivered',
  READ = 'read',
  FAILED = 'failed',
}

export interface ChatMessage {
  id: string;
  chatId: string;
  senderId: string;
  senderName: string;
  senderRole: 'rider' | 'admin' | 'system';
  type: MessageType;
  content: string;
  attachments?: {
    id: string;
    name: string;
    url: string;
    type: string;
    size: number;
  }[];
  status: MessageStatus;
  timestamp: string;
  readAt?: string;
}

// Chat session types
export enum ChatStatus {
  ACTIVE = 'active',
  WAITING = 'waiting',
  CLOSED = 'closed',
  RESOLVED = 'resolved',
}

export enum ChatPriority {
  LOW = 'low',
  NORMAL = 'normal',
  HIGH = 'high',
  URGENT = 'urgent',
}

export interface ChatSession {
  id: string;
  riderId: string;
  adminId?: string;
  adminName?: string;
  subject: string;
  status: ChatStatus;
  priority: ChatPriority;
  messages: ChatMessage[];
  createdAt: string;
  updatedAt: string;
  closedAt?: string;
  rating?: number;
  feedback?: string;
  tags: string[];
  isTyping?: boolean;
  lastMessageAt: string;
  unreadCount: number;
}

// FAQ types
export enum FAQCategory {
  GETTING_STARTED = 'getting_started',
  ORDERS = 'orders',
  PAYMENTS = 'payments',
  VEHICLE = 'vehicle',
  ACCOUNT = 'account',
  TECHNICAL = 'technical',
  POLICIES = 'policies',
}

export interface FAQItem {
  id: string;
  category: FAQCategory;
  question: string;
  answer: string;
  tags: string[];
  isPopular: boolean;
  helpfulCount: number;
  notHelpfulCount: number;
  createdAt: string;
  updatedAt: string;
}

export interface FAQSection {
  category: FAQCategory;
  title: string;
  icon: string;
  items: FAQItem[];
}

// Support ticket types
export enum TicketType {
  ORDER_ISSUE = 'order_issue',
  PAYMENT_ISSUE = 'payment_issue',
  TECHNICAL_ISSUE = 'technical_issue',
  ACCOUNT_ISSUE = 'account_issue',
  VEHICLE_ISSUE = 'vehicle_issue',
  GENERAL_INQUIRY = 'general_inquiry',
  FEATURE_REQUEST = 'feature_request',
  BUG_REPORT = 'bug_report',
}

export enum TicketPriority {
  LOW = 'low',
  NORMAL = 'normal',
  HIGH = 'high',
  URGENT = 'urgent',
}

export enum TicketStatus {
  OPEN = 'open',
  IN_PROGRESS = 'in_progress',
  WAITING_FOR_RESPONSE = 'waiting_for_response',
  RESOLVED = 'resolved',
  CLOSED = 'closed',
}

export interface SupportTicket {
  id: string;
  riderId: string;
  type: TicketType;
  priority: TicketPriority;
  status: TicketStatus;
  subject: string;
  description: string;
  orderId?: string;
  attachments: {
    id: string;
    name: string;
    url: string;
    type: string;
    size: number;
  }[];
  assignedTo?: string;
  assignedToName?: string;
  createdAt: string;
  updatedAt: string;
  resolvedAt?: string;
  closedAt?: string;
  rating?: number;
  feedback?: string;
  tags: string[];
  responses: TicketResponse[];
}

export interface TicketResponse {
  id: string;
  ticketId: string;
  responderId: string;
  responderName: string;
  responderRole: 'rider' | 'admin';
  content: string;
  attachments?: {
    id: string;
    name: string;
    url: string;
    type: string;
    size: number;
  }[];
  timestamp: string;
  isInternal: boolean;
}

// Issue reporting types
export enum IssueType {
  CUSTOMER_MISBEHAVIOR = 'customer_misbehavior',
  NON_PAYMENT = 'non_payment',
  DELIVERY_DELAY = 'delivery_delay',
  WRONG_ADDRESS = 'wrong_address',
  RESTAURANT_ISSUE = 'restaurant_issue',
  APP_MALFUNCTION = 'app_malfunction',
  SAFETY_CONCERN = 'safety_concern',
  OTHER = 'other',
}

export enum IssueSeverity {
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high',
  CRITICAL = 'critical',
}

export interface IssueReport {
  id: string;
  riderId: string;
  orderId?: string;
  type: IssueType;
  severity: IssueSeverity;
  title: string;
  description: string;
  location?: {
    latitude: number;
    longitude: number;
    address: string;
  };
  evidence: {
    id: string;
    type: 'photo' | 'video' | 'audio';
    url: string;
    description?: string;
    timestamp: string;
  }[];
  customerInfo?: {
    name: string;
    phone: string;
    address: string;
  };
  restaurantInfo?: {
    name: string;
    address: string;
    phone: string;
  };
  status: TicketStatus;
  assignedTo?: string;
  createdAt: string;
  updatedAt: string;
  resolvedAt?: string;
  resolution?: string;
  compensationAmount?: number;
  tags: string[];
}

// Feedback types
export enum FeedbackType {
  FEATURE_REQUEST = 'feature_request',
  BUG_REPORT = 'bug_report',
  IMPROVEMENT_SUGGESTION = 'improvement_suggestion',
  GENERAL_FEEDBACK = 'general_feedback',
  COMPLAINT = 'complaint',
  COMPLIMENT = 'compliment',
}

export enum FeedbackCategory {
  APP_PERFORMANCE = 'app_performance',
  USER_INTERFACE = 'user_interface',
  NAVIGATION = 'navigation',
  EARNINGS = 'earnings',
  ORDERS = 'orders',
  SUPPORT = 'support',
  GENERAL = 'general',
}

export interface AppFeedback {
  id: string;
  riderId: string;
  type: FeedbackType;
  category: FeedbackCategory;
  title: string;
  description: string;
  rating: number; // 1-5 stars
  screenshots: {
    id: string;
    url: string;
    description?: string;
  }[];
  deviceInfo: {
    platform: string;
    version: string;
    model: string;
    appVersion: string;
  };
  status: 'submitted' | 'under_review' | 'in_progress' | 'completed' | 'rejected';
  priority: 'low' | 'medium' | 'high';
  assignedTo?: string;
  createdAt: string;
  updatedAt: string;
  response?: string;
  responseAt?: string;
  tags: string[];
}

// Support contact types
export interface SupportContact {
  id: string;
  type: 'phone' | 'email' | 'whatsapp';
  label: string;
  value: string;
  isEmergency: boolean;
  isActive: boolean;
  availableHours: {
    start: string;
    end: string;
    timezone: string;
  };
  description?: string;
}

// Support statistics
export interface SupportStats {
  totalTickets: number;
  openTickets: number;
  resolvedTickets: number;
  averageResponseTime: number; // in minutes
  averageResolutionTime: number; // in hours
  satisfactionRating: number; // 1-5 stars
  popularIssues: {
    type: string;
    count: number;
  }[];
}

// Context types
export interface SupportContextType {
  // Chat state
  activeChat: ChatSession | null;
  chatHistory: ChatSession[];
  isTyping: boolean;
  
  // FAQ state
  faqs: FAQSection[];
  searchResults: FAQItem[];
  
  // Tickets state
  tickets: SupportTicket[];
  issueReports: IssueReport[];
  feedback: AppFeedback[];
  
  // Support contacts
  supportContacts: SupportContact[];
  
  // Loading states
  loading: boolean;
  error: string | null;
  
  // Chat actions
  startChat: (subject: string, priority?: ChatPriority) => Promise<ChatSession>;
  sendMessage: (chatId: string, content: string, type?: MessageType, attachments?: File[]) => Promise<void>;
  endChat: (chatId: string, rating?: number, feedback?: string) => Promise<void>;
  markAsRead: (chatId: string, messageId: string) => Promise<void>;
  
  // FAQ actions
  searchFAQs: (query: string) => Promise<FAQItem[]>;
  markFAQHelpful: (faqId: string, helpful: boolean) => Promise<void>;
  
  // Ticket actions
  createTicket: (ticket: Omit<SupportTicket, 'id' | 'createdAt' | 'updatedAt' | 'responses'>) => Promise<SupportTicket>;
  updateTicket: (ticketId: string, updates: Partial<SupportTicket>) => Promise<void>;
  addTicketResponse: (ticketId: string, content: string, attachments?: File[]) => Promise<void>;
  
  // Issue reporting actions
  reportIssue: (issue: Omit<IssueReport, 'id' | 'createdAt' | 'updatedAt'>) => Promise<IssueReport>;
  updateIssueReport: (issueId: string, updates: Partial<IssueReport>) => Promise<void>;
  
  // Feedback actions
  submitFeedback: (feedback: Omit<AppFeedback, 'id' | 'createdAt' | 'updatedAt' | 'deviceInfo'>) => Promise<AppFeedback>;
  
  // Support actions
  callSupport: (contactId: string) => Promise<void>;
  getSupportStats: () => Promise<SupportStats>;
}
