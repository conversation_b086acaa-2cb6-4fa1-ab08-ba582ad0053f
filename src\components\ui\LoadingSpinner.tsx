import React from 'react';
import { View, ActivityIndicator, Text, ViewStyle } from 'react-native';

interface LoadingSpinnerProps {
  size?: 'small' | 'large';
  color?: string;
  message?: string;
  overlay?: boolean;
  style?: ViewStyle;
}

const LoadingSpinner: React.FC<LoadingSpinnerProps> = ({
  size = 'large',
  color = '#ef4444',
  message,
  overlay = false,
  style,
}) => {
  const containerStyle: ViewStyle = {
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
    ...(overlay && {
      position: 'absolute',
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
      backgroundColor: 'rgba(255, 255, 255, 0.9)',
      zIndex: 1000,
    }),
  };

  return (
    <View style={[containerStyle, style]}>
      <ActivityIndicator size={size} color={color} />
      {message && (
        <Text style={{
          marginTop: 12,
          fontSize: 16,
          color: '#6b7280',
          textAlign: 'center',
        }}>
          {message}
        </Text>
      )}
    </View>
  );
};

export default LoadingSpinner;
