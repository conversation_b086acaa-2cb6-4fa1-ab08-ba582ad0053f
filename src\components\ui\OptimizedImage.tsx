import React, { useState, useCallback, useRef, useEffect } from 'react';
import {
  Image,
  ImageProps,
  ImageStyle,
  View,
  ActivityIndicator,
  Text,
  StyleSheet,
  Dimensions,
} from 'react-native';
import { optimizeImageUri, useMemoryCleanup } from '../../utils/performanceUtils';

interface OptimizedImageProps extends Omit<ImageProps, 'source'> {
  source: { uri: string } | number;
  width?: number;
  height?: number;
  placeholder?: React.ReactNode;
  errorComponent?: React.ReactNode;
  lazy?: boolean;
  cachePolicy?: 'memory' | 'disk' | 'memory-disk';
  quality?: number;
  onLoadStart?: () => void;
  onLoadEnd?: () => void;
  onError?: (error: any) => void;
}

const { width: screenWidth } = Dimensions.get('window');

export const OptimizedImage: React.FC<OptimizedImageProps> = ({
  source,
  width,
  height,
  style,
  placeholder,
  errorComponent,
  lazy = false,
  cachePolicy = 'memory-disk',
  quality = 80,
  onLoadStart,
  onLoadEnd,
  onError,
  ...props
}) => {
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(false);
  const [shouldLoad, setShouldLoad] = useState(!lazy);
  const imageRef = useRef<Image>(null);
  const mountedRef = useRef(true);

  // Cleanup on unmount
  useMemoryCleanup(() => {
    mountedRef.current = false;
  });

  useEffect(() => {
    return () => {
      mountedRef.current = false;
    };
  }, []);

  // Optimize image source
  const optimizedSource = React.useMemo(() => {
    if (typeof source === 'number') {
      return source;
    }

    const imageWidth = width || (style as ImageStyle)?.width || screenWidth;
    const imageHeight = height || (style as ImageStyle)?.height;

    return {
      uri: optimizeImageUri(
        source.uri,
        typeof imageWidth === 'number' ? imageWidth : undefined,
        typeof imageHeight === 'number' ? imageHeight : undefined
      ),
    };
  }, [source, width, height, style]);

  const handleLoadStart = useCallback(() => {
    if (!mountedRef.current) return;
    setLoading(true);
    setError(false);
    onLoadStart?.();
  }, [onLoadStart]);

  const handleLoadEnd = useCallback(() => {
    if (!mountedRef.current) return;
    setLoading(false);
    onLoadEnd?.();
  }, [onLoadEnd]);

  const handleError = useCallback((errorEvent: any) => {
    if (!mountedRef.current) return;
    setLoading(false);
    setError(true);
    onError?.(errorEvent);
  }, [onError]);

  const handleLayout = useCallback(() => {
    if (lazy && !shouldLoad) {
      setShouldLoad(true);
    }
  }, [lazy, shouldLoad]);

  // Default placeholder
  const defaultPlaceholder = (
    <View style={[styles.placeholder, style]}>
      <ActivityIndicator size="small" color="#666" />
    </View>
  );

  // Default error component
  const defaultErrorComponent = (
    <View style={[styles.placeholder, style]}>
      <Text style={styles.errorText}>Failed to load image</Text>
    </View>
  );

  // Show placeholder while lazy loading
  if (lazy && !shouldLoad) {
    return (
      <View style={style} onLayout={handleLayout}>
        {placeholder || defaultPlaceholder}
      </View>
    );
  }

  // Show error component if image failed to load
  if (error) {
    return errorComponent || defaultErrorComponent;
  }

  return (
    <View style={style}>
      <Image
        ref={imageRef}
        source={optimizedSource}
        style={[style, loading && styles.loading]}
        onLoadStart={handleLoadStart}
        onLoadEnd={handleLoadEnd}
        onError={handleError}
        {...props}
      />
      {loading && (placeholder || defaultPlaceholder)}
    </View>
  );
};

// Optimized Image List Component for better performance in lists
interface OptimizedImageListProps {
  images: Array<{ uri: string; id: string }>;
  itemWidth: number;
  itemHeight: number;
  numColumns?: number;
  onImagePress?: (image: { uri: string; id: string }) => void;
}

export const OptimizedImageList: React.FC<OptimizedImageListProps> = ({
  images,
  itemWidth,
  itemHeight,
  numColumns = 2,
  onImagePress,
}) => {
  const renderImage = useCallback(
    (image: { uri: string; id: string }, index: number) => (
      <OptimizedImage
        key={image.id}
        source={{ uri: image.uri }}
        width={itemWidth}
        height={itemHeight}
        style={styles.listImage}
        lazy={index > 10} // Lazy load images beyond the first 10
        onPress={() => onImagePress?.(image)}
      />
    ),
    [itemWidth, itemHeight, onImagePress]
  );

  return (
    <View style={styles.imageList}>
      {images.map(renderImage)}
    </View>
  );
};

const styles = StyleSheet.create({
  placeholder: {
    backgroundColor: '#f0f0f0',
    justifyContent: 'center',
    alignItems: 'center',
    minHeight: 100,
  },
  loading: {
    opacity: 0,
  },
  errorText: {
    color: '#666',
    fontSize: 12,
    textAlign: 'center',
  },
  imageList: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  listImage: {
    marginBottom: 10,
  },
});

export default OptimizedImage;
