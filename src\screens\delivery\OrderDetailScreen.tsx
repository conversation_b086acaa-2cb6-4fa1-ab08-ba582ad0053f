import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  ScrollView,
  RefreshControl,
  TouchableOpacity,
  Image,
  Linking,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import { useNavigation, useRoute } from '@react-navigation/native';
import { Card, Button, Badge, LoadingSpinner } from '../../components/ui';
import { formatCurrency, formatDate, formatTime } from '../../utils/helpers';
import { getStatusColor, getStatusText, getOrderTypeIcon } from '../../utils/orderHelpers';
import {
  DeliveryOrder,
  OrderStatus,
  OrderType,
  CancellationReason,
} from '../../types/deliveryHistory';

// Mock order data - will be replaced with context
const mockOrder: DeliveryOrder = {
  id: '1',
  orderNumber: 'ORD-2024-001',
  status: OrderStatus.DELIVERED,
  type: OrderType.FOOD,
  customer: {
    id: 'c1',
    name: '<PERSON>',
    phone: '+92 300 1234567',
    rating: 4.8,
    totalOrders: 45,
  },
  restaurant: {
    id: 'r1',
    name: 'KFC DHA',
    address: 'DHA Phase 2, Main Boulevard, Lahore',
    phone: '+92 42 1234567',
    category: 'Fast Food',
    rating: 4.5,
    image: 'https://example.com/kfc-logo.png',
  },
  pickupLocation: {
    latitude: 31.4697,
    longitude: 74.4142,
    address: 'DHA Phase 2, Main Boulevard, Lahore',
    area: 'DHA Phase 2',
    city: 'Lahore',
    landmark: 'Near Packages Mall',
  },
  deliveryLocation: {
    latitude: 31.4504,
    longitude: 74.3588,
    address: 'House 123, Block C, Gulberg III, Lahore',
    area: 'Gulberg III',
    city: 'Lahore',
    landmark: 'Near Liberty Market',
  },
  items: [
    { id: '1', name: 'Zinger Burger', quantity: 2, price: 800, specialInstructions: 'Extra spicy' },
    { id: '2', name: 'Regular Fries', quantity: 1, price: 300 },
    { id: '3', name: 'Pepsi 500ml', quantity: 2, price: 200 },
  ],
  totalAmount: 1300,
  deliveryFee: 150,
  tip: 100,
  timeline: {
    orderPlaced: '2024-01-20T12:00:00Z',
    orderAccepted: '2024-01-20T12:02:00Z',
    arrivedAtRestaurant: '2024-01-20T12:15:00Z',
    orderPickedUp: '2024-01-20T12:25:00Z',
    deliveryStarted: '2024-01-20T12:26:00Z',
    orderDelivered: '2024-01-20T12:45:00Z',
  },
  metrics: {
    totalDistance: 5.2,
    deliveryTime: 19,
    pickupTime: 10,
    waitingTime: 8,
    averageSpeed: 16.4,
  },
  rating: {
    overall: 5,
    delivery: 5,
    communication: 4,
    timeliness: 5,
    comment: 'Great service, very fast delivery! The food was still hot when it arrived.',
    ratedAt: '2024-01-20T13:00:00Z',
  },
  specialInstructions: 'Please call when you arrive. Ring the doorbell twice.',
  paymentMethod: 'Cash on Delivery',
  proofOfDelivery: 'https://example.com/proof-image.jpg',
  createdAt: '2024-01-20T12:00:00Z',
  updatedAt: '2024-01-20T12:45:00Z',
};

const OrderDetailScreen: React.FC = () => {
  const navigation = useNavigation();
  const route = useRoute();
  const { orderId } = route.params as { orderId: string };
  
  const [order, setOrder] = useState<DeliveryOrder | null>(mockOrder);
  const [refreshing, setRefreshing] = useState(false);
  const [isLoading, setIsLoading] = useState(false);

  useEffect(() => {
    loadOrderDetails();
  }, [orderId]);

  const loadOrderDetails = async () => {
    setIsLoading(true);
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      setOrder(mockOrder);
    } catch (error) {
      console.error('Error loading order details:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const onRefresh = async () => {
    setRefreshing(true);
    await loadOrderDetails();
    setRefreshing(false);
  };

  const handleCall = (phoneNumber: string) => {
    Linking.openURL(`tel:${phoneNumber}`);
  };

  const handleOpenMap = (latitude: number, longitude: number, address: string) => {
    const url = `https://maps.google.com/?q=${latitude},${longitude}`;
    Linking.openURL(url);
  };



  const renderOrderHeader = () => {
    if (!order) return null;

    return (
      <Card variant="elevated" margin="md" padding="lg">
        <View style={{ flexDirection: 'row', justifyContent: 'space-between', alignItems: 'flex-start', marginBottom: 16 }}>
          <View style={{ flex: 1 }}>
            <Text style={{ fontSize: 20, fontWeight: 'bold', color: '#111827' }}>
              {order.orderNumber}
            </Text>
            <Text style={{ fontSize: 14, color: '#6b7280', marginTop: 4 }}>
              {formatDate(order.createdAt)} • {formatTime(order.createdAt)}
            </Text>
          </View>
          <Badge
            text={getStatusText(order.status)}
            style={{ backgroundColor: getStatusColor(order.status) }}
          />
        </View>

        <View style={{ flexDirection: 'row', alignItems: 'center', marginBottom: 16 }}>
          <View style={{
            width: 40,
            height: 40,
            borderRadius: 20,
            backgroundColor: '#f97316',
            alignItems: 'center',
            justifyContent: 'center',
            marginRight: 12,
          }}>
            <Ionicons name={getOrderTypeIcon(order.type)} size={20} color="white" />
          </View>
          <View style={{ flex: 1 }}>
            <Text style={{ fontSize: 16, fontWeight: '600', color: '#111827' }}>
              {order.type.charAt(0).toUpperCase() + order.type.slice(1)} Order
            </Text>
            <Text style={{ fontSize: 14, color: '#6b7280' }}>
              Total: {formatCurrency(order.totalAmount + order.deliveryFee + order.tip)}
            </Text>
          </View>
        </View>

        {order.metrics && (
          <View style={{ flexDirection: 'row', justifyContent: 'space-between', paddingTop: 16, borderTopWidth: 1, borderTopColor: '#f3f4f6' }}>
            <View style={{ alignItems: 'center', flex: 1 }}>
              <Text style={{ fontSize: 16, fontWeight: 'bold', color: '#111827' }}>
                {order.metrics.totalDistance.toFixed(1)} km
              </Text>
              <Text style={{ fontSize: 12, color: '#6b7280' }}>Distance</Text>
            </View>
            <View style={{ alignItems: 'center', flex: 1 }}>
              <Text style={{ fontSize: 16, fontWeight: 'bold', color: '#111827' }}>
                {order.metrics.deliveryTime} min
              </Text>
              <Text style={{ fontSize: 12, color: '#6b7280' }}>Delivery Time</Text>
            </View>
            <View style={{ alignItems: 'center', flex: 1 }}>
              <Text style={{ fontSize: 16, fontWeight: 'bold', color: '#111827' }}>
                {order.metrics.averageSpeed.toFixed(1)} km/h
              </Text>
              <Text style={{ fontSize: 12, color: '#6b7280' }}>Avg Speed</Text>
            </View>
            {order.rating && (
              <View style={{ alignItems: 'center', flex: 1 }}>
                <View style={{ flexDirection: 'row', alignItems: 'center' }}>
                  <Ionicons name="star" size={16} color="#f59e0b" />
                  <Text style={{ fontSize: 16, fontWeight: 'bold', color: '#111827', marginLeft: 4 }}>
                    {order.rating.overall.toFixed(1)}
                  </Text>
                </View>
                <Text style={{ fontSize: 12, color: '#6b7280' }}>Rating</Text>
              </View>
            )}
          </View>
        )}
      </Card>
    );
  };

  const renderCustomerInfo = () => {
    if (!order) return null;

    return (
      <Card variant="elevated" margin="md" padding="lg">
        <Text style={{ fontSize: 18, fontWeight: 'bold', color: '#111827', marginBottom: 16 }}>
          Customer Information
        </Text>

        <View style={{ flexDirection: 'row', alignItems: 'center', justifyContent: 'space-between', marginBottom: 12 }}>
          <View style={{ flex: 1 }}>
            <Text style={{ fontSize: 16, fontWeight: '600', color: '#111827' }}>
              {order.customer.name}
            </Text>
            <Text style={{ fontSize: 14, color: '#6b7280' }}>
              {order.customer.phone}
            </Text>
            {order.customer.totalOrders && (
              <Text style={{ fontSize: 12, color: '#9ca3af', marginTop: 2 }}>
                {order.customer.totalOrders} total orders
              </Text>
            )}
          </View>
          
          <View style={{ flexDirection: 'row', alignItems: 'center', gap: 8 }}>
            {order.customer.rating && (
              <View style={{ flexDirection: 'row', alignItems: 'center', backgroundColor: '#f9fafb', paddingHorizontal: 8, paddingVertical: 4, borderRadius: 12 }}>
                <Ionicons name="star" size={12} color="#f59e0b" />
                <Text style={{ fontSize: 12, fontWeight: '600', color: '#111827', marginLeft: 2 }}>
                  {order.customer.rating.toFixed(1)}
                </Text>
              </View>
            )}
            
            <TouchableOpacity
              onPress={() => handleCall(order.customer.phone)}
              style={{
                width: 36,
                height: 36,
                borderRadius: 18,
                backgroundColor: '#10b981',
                alignItems: 'center',
                justifyContent: 'center',
              }}
            >
              <Ionicons name="call" size={16} color="white" />
            </TouchableOpacity>
          </View>
        </View>

        <View style={{ backgroundColor: '#f9fafb', padding: 12, borderRadius: 8 }}>
          <View style={{ flexDirection: 'row', alignItems: 'flex-start', marginBottom: 8 }}>
            <Ionicons name="location" size={16} color="#f97316" style={{ marginTop: 2, marginRight: 8 }} />
            <View style={{ flex: 1 }}>
              <Text style={{ fontSize: 14, fontWeight: '600', color: '#111827' }}>
                Delivery Address
              </Text>
              <Text style={{ fontSize: 14, color: '#6b7280', marginTop: 2 }}>
                {order.deliveryLocation.address}
              </Text>
              {order.deliveryLocation.landmark && (
                <Text style={{ fontSize: 12, color: '#9ca3af', marginTop: 1 }}>
                  {order.deliveryLocation.landmark}
                </Text>
              )}
            </View>
            <TouchableOpacity
              onPress={() => handleOpenMap(
                order.deliveryLocation.latitude,
                order.deliveryLocation.longitude,
                order.deliveryLocation.address
              )}
              style={{
                padding: 4,
                borderRadius: 4,
                backgroundColor: '#ffffff',
              }}
            >
              <Ionicons name="map" size={16} color="#f97316" />
            </TouchableOpacity>
          </View>
        </View>
      </Card>
    );
  };

  const renderRestaurantInfo = () => {
    if (!order) return null;

    return (
      <Card variant="elevated" margin="md" padding="lg">
        <Text style={{ fontSize: 18, fontWeight: 'bold', color: '#111827', marginBottom: 16 }}>
          Restaurant Information
        </Text>

        <View style={{ flexDirection: 'row', alignItems: 'center', justifyContent: 'space-between', marginBottom: 12 }}>
          <View style={{ flexDirection: 'row', alignItems: 'center', flex: 1 }}>
            {order.restaurant.image ? (
              <Image
                source={{ uri: order.restaurant.image }}
                style={{ width: 40, height: 40, borderRadius: 20, marginRight: 12 }}
              />
            ) : (
              <View style={{
                width: 40,
                height: 40,
                borderRadius: 20,
                backgroundColor: '#f97316',
                alignItems: 'center',
                justifyContent: 'center',
                marginRight: 12,
              }}>
                <Ionicons name="restaurant" size={20} color="white" />
              </View>
            )}
            
            <View style={{ flex: 1 }}>
              <Text style={{ fontSize: 16, fontWeight: '600', color: '#111827' }}>
                {order.restaurant.name}
              </Text>
              <Text style={{ fontSize: 14, color: '#6b7280' }}>
                {order.restaurant.category}
              </Text>
              {order.restaurant.rating && (
                <View style={{ flexDirection: 'row', alignItems: 'center', marginTop: 2 }}>
                  <Ionicons name="star" size={12} color="#f59e0b" />
                  <Text style={{ fontSize: 12, color: '#6b7280', marginLeft: 2 }}>
                    {order.restaurant.rating.toFixed(1)}
                  </Text>
                </View>
              )}
            </View>
          </View>
          
          <TouchableOpacity
            onPress={() => handleCall(order.restaurant.phone)}
            style={{
              width: 36,
              height: 36,
              borderRadius: 18,
              backgroundColor: '#3b82f6',
              alignItems: 'center',
              justifyContent: 'center',
            }}
          >
            <Ionicons name="call" size={16} color="white" />
          </TouchableOpacity>
        </View>

        <View style={{ backgroundColor: '#f9fafb', padding: 12, borderRadius: 8 }}>
          <View style={{ flexDirection: 'row', alignItems: 'flex-start' }}>
            <Ionicons name="location" size={16} color="#f97316" style={{ marginTop: 2, marginRight: 8 }} />
            <View style={{ flex: 1 }}>
              <Text style={{ fontSize: 14, fontWeight: '600', color: '#111827' }}>
                Pickup Address
              </Text>
              <Text style={{ fontSize: 14, color: '#6b7280', marginTop: 2 }}>
                {order.restaurant.address}
              </Text>
              {order.pickupLocation.landmark && (
                <Text style={{ fontSize: 12, color: '#9ca3af', marginTop: 1 }}>
                  {order.pickupLocation.landmark}
                </Text>
              )}
            </View>
            <TouchableOpacity
              onPress={() => handleOpenMap(
                order.pickupLocation.latitude,
                order.pickupLocation.longitude,
                order.pickupLocation.address
              )}
              style={{
                padding: 4,
                borderRadius: 4,
                backgroundColor: '#ffffff',
              }}
            >
              <Ionicons name="map" size={16} color="#f97316" />
            </TouchableOpacity>
          </View>
        </View>
      </Card>
    );
  };

  const renderOrderItems = () => {
    if (!order) return null;

    return (
      <Card variant="elevated" margin="md" padding="lg">
        <Text style={{ fontSize: 18, fontWeight: 'bold', color: '#111827', marginBottom: 16 }}>
          Order Items
        </Text>

        {order.items.map((item, index) => (
          <View key={item.id} style={{
            flexDirection: 'row',
            justifyContent: 'space-between',
            alignItems: 'flex-start',
            paddingVertical: 12,
            borderBottomWidth: index < order.items.length - 1 ? 1 : 0,
            borderBottomColor: '#f3f4f6',
          }}>
            <View style={{ flex: 1 }}>
              <Text style={{ fontSize: 16, fontWeight: '600', color: '#111827' }}>
                {item.name}
              </Text>
              {item.specialInstructions && (
                <Text style={{ fontSize: 12, color: '#f97316', marginTop: 2 }}>
                  Note: {item.specialInstructions}
                </Text>
              )}
              <Text style={{ fontSize: 14, color: '#6b7280', marginTop: 2 }}>
                {formatCurrency(item.price)} each
              </Text>
            </View>

            <View style={{ alignItems: 'flex-end' }}>
              <Text style={{ fontSize: 16, fontWeight: 'bold', color: '#111827' }}>
                {item.quantity}x
              </Text>
              <Text style={{ fontSize: 16, fontWeight: 'bold', color: '#f97316' }}>
                {formatCurrency(item.price * item.quantity)}
              </Text>
            </View>
          </View>
        ))}

        <View style={{
          flexDirection: 'row',
          justifyContent: 'space-between',
          alignItems: 'center',
          marginTop: 16,
          paddingTop: 16,
          borderTopWidth: 1,
          borderTopColor: '#e5e7eb',
        }}>
          <Text style={{ fontSize: 16, fontWeight: 'bold', color: '#111827' }}>
            Total Items
          </Text>
          <Text style={{ fontSize: 18, fontWeight: 'bold', color: '#f97316' }}>
            {formatCurrency(order.totalAmount)}
          </Text>
        </View>
      </Card>
    );
  };

  const renderTimeline = () => {
    if (!order) return null;

    const timelineEvents = [
      { key: 'orderPlaced', label: 'Order Placed', time: order.timeline.orderPlaced, icon: 'receipt-outline' },
      { key: 'orderAccepted', label: 'Order Accepted', time: order.timeline.orderAccepted, icon: 'checkmark-circle-outline' },
      { key: 'pickedUp', label: 'Picked Up', time: order.timeline.pickedUp, icon: 'bag-outline' },
      { key: 'delivered', label: 'Delivered', time: order.timeline.delivered, icon: 'home-outline' },
    ].filter(event => event.time);

    return (
      <Card variant="elevated" margin="md" padding="lg">
        <Text style={{ fontSize: 18, fontWeight: 'bold', color: '#111827', marginBottom: 16 }}>
          Order Timeline
        </Text>

        {timelineEvents.map((event, index) => (
          <View key={event.key} style={{
            flexDirection: 'row',
            alignItems: 'flex-start',
            marginBottom: index < timelineEvents.length - 1 ? 16 : 0,
          }}>
            <View style={{
              width: 32,
              height: 32,
              borderRadius: 16,
              backgroundColor: '#f97316',
              alignItems: 'center',
              justifyContent: 'center',
              marginRight: 12,
            }}>
              <Ionicons name={event.icon as any} size={16} color="white" />
            </View>

            <View style={{ flex: 1 }}>
              <Text style={{ fontSize: 16, fontWeight: '600', color: '#111827' }}>
                {event.label}
              </Text>
              <Text style={{ fontSize: 14, color: '#6b7280', marginTop: 2 }}>
                {formatDate(event.time)} at {formatTime(event.time)}
              </Text>
            </View>
          </View>
        ))}
      </Card>
    );
  };

  const renderPaymentInfo = () => {
    if (!order) return null;

    return (
      <Card variant="elevated" margin="md" padding="lg">
        <Text style={{ fontSize: 18, fontWeight: 'bold', color: '#111827', marginBottom: 16 }}>
          Payment Information
        </Text>

        <View style={{ marginBottom: 16 }}>
          <View style={{ flexDirection: 'row', justifyContent: 'space-between', marginBottom: 8 }}>
            <Text style={{ fontSize: 14, color: '#6b7280' }}>Subtotal</Text>
            <Text style={{ fontSize: 14, color: '#111827' }}>{formatCurrency(order.totalAmount)}</Text>
          </View>
          <View style={{ flexDirection: 'row', justifyContent: 'space-between', marginBottom: 8 }}>
            <Text style={{ fontSize: 14, color: '#6b7280' }}>Delivery Fee</Text>
            <Text style={{ fontSize: 14, color: '#111827' }}>{formatCurrency(order.deliveryFee)}</Text>
          </View>
          {order.tip > 0 && (
            <View style={{ flexDirection: 'row', justifyContent: 'space-between', marginBottom: 8 }}>
              <Text style={{ fontSize: 14, color: '#6b7280' }}>Tip</Text>
              <Text style={{ fontSize: 14, color: '#10b981' }}>{formatCurrency(order.tip)}</Text>
            </View>
          )}
          <View style={{
            flexDirection: 'row',
            justifyContent: 'space-between',
            paddingTop: 8,
            borderTopWidth: 1,
            borderTopColor: '#e5e7eb',
          }}>
            <Text style={{ fontSize: 16, fontWeight: 'bold', color: '#111827' }}>Total</Text>
            <Text style={{ fontSize: 16, fontWeight: 'bold', color: '#f97316' }}>
              {formatCurrency(order.totalAmount + order.deliveryFee + order.tip)}
            </Text>
          </View>
        </View>

        <View style={{
          flexDirection: 'row',
          alignItems: 'center',
          padding: 12,
          backgroundColor: '#f8fafc',
          borderRadius: 8,
        }}>
          <Ionicons name="card-outline" size={20} color="#f97316" />
          <Text style={{ fontSize: 14, color: '#111827', marginLeft: 8 }}>
            Payment Method: {order.paymentMethod}
          </Text>
        </View>

        {order.paymentStatus && (
          <View style={{
            flexDirection: 'row',
            alignItems: 'center',
            marginTop: 8,
            padding: 8,
            backgroundColor: order.paymentStatus === 'paid' ? '#f0fdf4' : '#fef3e2',
            borderRadius: 6,
          }}>
            <Ionicons
              name={order.paymentStatus === 'paid' ? 'checkmark-circle' : 'time'}
              size={16}
              color={order.paymentStatus === 'paid' ? '#10b981' : '#f97316'}
            />
            <Text style={{
              fontSize: 12,
              color: order.paymentStatus === 'paid' ? '#10b981' : '#f97316',
              marginLeft: 4,
              fontWeight: '600',
            }}>
              {order.paymentStatus === 'paid' ? 'Payment Completed' : 'Payment Pending'}
            </Text>
          </View>
        )}
      </Card>
    );
  };

  const renderRating = () => {
    if (!order?.rating) return null;

    return (
      <Card variant="elevated" margin="md" padding="lg">
        <Text style={{ fontSize: 18, fontWeight: 'bold', color: '#111827', marginBottom: 16 }}>
          Customer Rating
        </Text>

        <View style={{ alignItems: 'center', marginBottom: 16 }}>
          <View style={{ flexDirection: 'row', alignItems: 'center', marginBottom: 8 }}>
            {[1, 2, 3, 4, 5].map((star) => (
              <Ionicons
                key={star}
                name={star <= order.rating!.overall ? 'star' : 'star-outline'}
                size={24}
                color="#f59e0b"
                style={{ marginHorizontal: 2 }}
              />
            ))}
          </View>
          <Text style={{ fontSize: 24, fontWeight: 'bold', color: '#111827' }}>
            {order.rating.overall.toFixed(1)} / 5.0
          </Text>
          <Text style={{ fontSize: 14, color: '#6b7280', marginTop: 4 }}>
            Rated on {formatDate(order.rating.ratedAt)}
          </Text>
        </View>

        {order.rating.comment && (
          <View style={{
            padding: 12,
            backgroundColor: '#f8fafc',
            borderRadius: 8,
            borderLeftWidth: 4,
            borderLeftColor: '#f97316',
          }}>
            <Text style={{ fontSize: 14, color: '#111827', fontStyle: 'italic' }}>
              "{order.rating.comment}"
            </Text>
          </View>
        )}

        <View style={{ flexDirection: 'row', justifyContent: 'space-around', marginTop: 16 }}>
          <View style={{ alignItems: 'center' }}>
            <Text style={{ fontSize: 12, color: '#6b7280', marginBottom: 4 }}>Food Quality</Text>
            <Text style={{ fontSize: 16, fontWeight: 'bold', color: '#f97316' }}>
              {order.rating.foodQuality}/5
            </Text>
          </View>
          <View style={{ alignItems: 'center' }}>
            <Text style={{ fontSize: 12, color: '#6b7280', marginBottom: 4 }}>Delivery Time</Text>
            <Text style={{ fontSize: 16, fontWeight: 'bold', color: '#f97316' }}>
              {order.rating.deliveryTime}/5
            </Text>
          </View>
          <View style={{ alignItems: 'center' }}>
            <Text style={{ fontSize: 12, color: '#6b7280', marginBottom: 4 }}>Service</Text>
            <Text style={{ fontSize: 16, fontWeight: 'bold', color: '#f97316' }}>
              {order.rating.service}/5
            </Text>
          </View>
        </View>
      </Card>
    );
  };

  const renderSpecialInstructions = () => {
    if (!order?.specialInstructions) return null;

    return (
      <Card variant="elevated" margin="md" padding="lg">
        <Text style={{ fontSize: 18, fontWeight: 'bold', color: '#111827', marginBottom: 16 }}>
          Special Instructions
        </Text>
        <View style={{
          padding: 12,
          backgroundColor: '#fef3e2',
          borderRadius: 8,
          borderLeftWidth: 4,
          borderLeftColor: '#f97316',
        }}>
          <Text style={{ fontSize: 14, color: '#111827', lineHeight: 20 }}>
            {order.specialInstructions}
          </Text>
        </View>
      </Card>
    );
  };

  if (isLoading) {
    return (
      <SafeAreaView style={{ flex: 1, backgroundColor: '#f8fafc' }}>
        <LoadingSpinner message="Loading order details..." />
      </SafeAreaView>
    );
  }

  if (!order) {
    return (
      <SafeAreaView style={{ flex: 1, backgroundColor: '#f8fafc' }}>
        <View style={{ flex: 1, alignItems: 'center', justifyContent: 'center' }}>
          <Ionicons name="document-text-outline" size={64} color="#d1d5db" />
          <Text style={{ fontSize: 18, fontWeight: 'bold', color: '#6b7280', marginTop: 16 }}>
            Order not found
          </Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={{ flex: 1, backgroundColor: '#f8fafc' }}>
      <View style={{
        paddingHorizontal: 20,
        paddingVertical: 16,
        backgroundColor: '#ffffff',
        borderBottomWidth: 1,
        borderBottomColor: '#e5e7eb',
        flexDirection: 'row',
        alignItems: 'center',
      }}>
        <TouchableOpacity
          onPress={() => navigation.goBack()}
          style={{ marginRight: 16 }}
        >
          <Ionicons name="arrow-back" size={24} color="#111827" />
        </TouchableOpacity>
        <Text style={{ fontSize: 24, fontWeight: 'bold', color: '#111827' }}>
          Order Details
        </Text>
      </View>

      <ScrollView
        style={{ flex: 1 }}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
        showsVerticalScrollIndicator={false}
      >
        {renderOrderHeader()}
        {renderCustomerInfo()}
        {renderRestaurantInfo()}
        {renderOrderItems()}
        {renderTimeline()}
        {renderPaymentInfo()}
        {renderRating()}
        {renderSpecialInstructions()}

        {/* Bottom spacing */}
        <View style={{ height: 20 }} />
      </ScrollView>
    </SafeAreaView>
  );
};

export default OrderDetailScreen;
