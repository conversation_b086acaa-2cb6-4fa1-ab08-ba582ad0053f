import { useEffect, useRef, useCallback, useState } from 'react';
import { AppState, AppStateStatus, InteractionManager } from 'react-native';
import { performanceMonitor } from '../services/performanceMonitoring';
import { PERFORMANCE_CONFIG, FEATURE_FLAGS } from '../config/performanceConfig';
import { 
  useLazyInit, 
  useInteractionManager, 
  useMemoryCleanup,
  useBatchedUpdates,
  debounce,
  throttle 
} from '../utils/performanceUtils';

/**
 * Hook for comprehensive performance optimization
 */
export const usePerformanceOptimization = (componentName: string) => {
  const renderStartTime = useRef(Date.now());
  const interactionComplete = useInteractionManager();
  const batchUpdate = useBatchedUpdates();

  // Track component render performance
  useEffect(() => {
    if (FEATURE_FLAGS.PERFORMANCE_MONITORING) {
      const renderTime = Date.now() - renderStartTime.current;
      performanceMonitor.trackRender(componentName, renderTime);
    }
  }, [componentName]);

  // Memory cleanup on unmount
  useMemoryCleanup(() => {
    // Cleanup component-specific resources
    if (__DEV__) {
      console.log(`[Performance] Cleaning up ${componentName}`);
    }
  });

  return {
    interactionComplete,
    batchUpdate,
    isOptimizationEnabled: FEATURE_FLAGS.PERFORMANCE_MONITORING,
  };
};

/**
 * Hook for optimized list performance
 */
export const useOptimizedList = <T>(
  data: T[],
  options: {
    itemHeight?: number;
    containerHeight?: number;
    enableVirtualization?: boolean;
  } = {}
) => {
  const {
    itemHeight = 60,
    containerHeight = 600,
    enableVirtualization = FEATURE_FLAGS.LIST_VIRTUALIZATION,
  } = options;

  const [visibleRange, setVisibleRange] = useState({ start: 0, end: 10 });
  const scrollOffsetRef = useRef(0);

  const updateVisibleRange = useCallback(
    throttle((scrollOffset: number) => {
      if (!enableVirtualization) return;

      scrollOffsetRef.current = scrollOffset;
      const start = Math.max(0, Math.floor(scrollOffset / itemHeight) - 2);
      const visibleCount = Math.ceil(containerHeight / itemHeight) + 4;
      const end = Math.min(data.length, start + visibleCount);
      
      setVisibleRange({ start, end });
    }, PERFORMANCE_CONFIG.UI.DEBOUNCE_DELAYS.SCROLL),
    [data.length, itemHeight, containerHeight, enableVirtualization]
  );

  const visibleData = enableVirtualization 
    ? data.slice(visibleRange.start, visibleRange.end)
    : data;

  const getItemLayout = useCallback(
    (data: any, index: number) => ({
      length: itemHeight,
      offset: itemHeight * index,
      index,
    }),
    [itemHeight]
  );

  return {
    visibleData,
    updateVisibleRange,
    visibleRange,
    getItemLayout,
    listConfig: PERFORMANCE_CONFIG.UI.LIST_OPTIMIZATION,
  };
};

/**
 * Hook for optimized image loading
 */
export const useOptimizedImage = (uri: string, dimensions?: { width: number; height: number }) => {
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(false);
  const [optimizedUri, setOptimizedUri] = useState(uri);

  useEffect(() => {
    if (FEATURE_FLAGS.OPTIMIZED_IMAGES && uri && dimensions) {
      const { width, height } = dimensions;
      const separator = uri.includes('?') ? '&' : '?';
      const quality = PERFORMANCE_CONFIG.UI.IMAGE_OPTIMIZATION.QUALITY;
      const format = PERFORMANCE_CONFIG.UI.IMAGE_OPTIMIZATION.FORMAT;
      
      const newUri = `${uri}${separator}w=${width}&h=${height}&quality=${quality}&format=${format}`;
      setOptimizedUri(newUri);
    }
  }, [uri, dimensions]);

  const handleLoadStart = useCallback(() => {
    setLoading(true);
    setError(false);
  }, []);

  const handleLoadEnd = useCallback(() => {
    setLoading(false);
  }, []);

  const handleError = useCallback(() => {
    setLoading(false);
    setError(true);
  }, []);

  return {
    optimizedUri,
    loading,
    error,
    handlers: {
      onLoadStart: handleLoadStart,
      onLoadEnd: handleLoadEnd,
      onError: handleError,
    },
  };
};

/**
 * Hook for network request optimization
 */
export const useOptimizedNetwork = () => {
  const requestCache = useRef(new Map());
  const pendingRequests = useRef(new Map());

  const makeRequest = useCallback(async (
    url: string,
    options: {
      method?: string;
      data?: any;
      cache?: boolean;
      cacheTTL?: number;
    } = {}
  ) => {
    const { method = 'GET', data, cache = true, cacheTTL = 5 * 60 * 1000 } = options;
    const cacheKey = `${method}-${url}-${JSON.stringify(data || {})}`;

    // Check cache first
    if (cache && method === 'GET') {
      const cached = requestCache.current.get(cacheKey);
      if (cached && Date.now() - cached.timestamp < cacheTTL) {
        return cached.data;
      }
    }

    // Check for pending request
    if (pendingRequests.current.has(cacheKey)) {
      return pendingRequests.current.get(cacheKey);
    }

    // Make new request
    const requestPromise = performanceMonitor.measureAsync(
      `network_${method.toLowerCase()}_${url.split('/').pop()}`,
      async () => {
        // Simulate network request - replace with actual implementation
        const response = await fetch(url, {
          method,
          body: data ? JSON.stringify(data) : undefined,
          headers: {
            'Content-Type': 'application/json',
          },
        });
        return response.json();
      }
    );

    pendingRequests.current.set(cacheKey, requestPromise);

    try {
      const result = await requestPromise;
      
      // Cache successful GET requests
      if (cache && method === 'GET') {
        requestCache.current.set(cacheKey, {
          data: result,
          timestamp: Date.now(),
        });
      }

      return result;
    } finally {
      pendingRequests.current.delete(cacheKey);
    }
  }, []);

  const clearCache = useCallback(() => {
    requestCache.current.clear();
  }, []);

  return {
    makeRequest,
    clearCache,
    cacheSize: requestCache.current.size,
  };
};

/**
 * Hook for app state optimization
 */
export const useAppStateOptimization = () => {
  const [appState, setAppState] = useState<AppStateStatus>(AppState.currentState);
  const backgroundTime = useRef<number>(0);

  useEffect(() => {
    const handleAppStateChange = (nextAppState: AppStateStatus) => {
      if (appState === 'background' && nextAppState === 'active') {
        const timeInBackground = Date.now() - backgroundTime.current;
        
        if (FEATURE_FLAGS.PERFORMANCE_MONITORING) {
          performanceMonitor.trackMetric('app_background_time', timeInBackground);
        }

        // Clear caches if app was in background for too long
        if (timeInBackground > 30 * 60 * 1000) { // 30 minutes
          // Trigger cache cleanup
          if (__DEV__) {
            console.log('[Performance] App was in background for long time, clearing caches');
          }
        }
      } else if (nextAppState === 'background') {
        backgroundTime.current = Date.now();
      }

      setAppState(nextAppState);
    };

    const subscription = AppState.addEventListener('change', handleAppStateChange);
    return () => subscription?.remove();
  }, [appState]);

  return {
    appState,
    isActive: appState === 'active',
    isBackground: appState === 'background',
    isInactive: appState === 'inactive',
  };
};

/**
 * Hook for memory optimization
 */
export const useMemoryOptimization = () => {
  const [memoryWarning, setMemoryWarning] = useState(false);
  const cleanupFunctions = useRef<(() => void)[]>([]);

  const addCleanupFunction = useCallback((fn: () => void) => {
    cleanupFunctions.current.push(fn);
  }, []);

  const triggerCleanup = useCallback(() => {
    cleanupFunctions.current.forEach(fn => {
      try {
        fn();
      } catch (error) {
        console.error('[Performance] Cleanup function error:', error);
      }
    });
    cleanupFunctions.current = [];
    setMemoryWarning(false);
  }, []);

  useEffect(() => {
    // Simulate memory monitoring - in real app, use native modules
    const checkMemory = () => {
      if (FEATURE_FLAGS.MEMORY_MANAGEMENT) {
        // Trigger cleanup if memory usage is high
        const shouldCleanup = Math.random() > 0.95; // Simulate 5% chance
        if (shouldCleanup) {
          setMemoryWarning(true);
          setTimeout(triggerCleanup, 1000);
        }
      }
    };

    const interval = setInterval(checkMemory, PERFORMANCE_CONFIG.MEMORY.CLEANUP_INTERVAL);
    return () => clearInterval(interval);
  }, [triggerCleanup]);

  return {
    memoryWarning,
    addCleanupFunction,
    triggerCleanup,
  };
};

/**
 * Hook for debounced and throttled functions
 */
export const useOptimizedCallbacks = () => {
  const debouncedSearch = useLazyInit(() => 
    debounce((query: string, callback: (query: string) => void) => {
      callback(query);
    }, PERFORMANCE_CONFIG.UI.DEBOUNCE_DELAYS.SEARCH)
  );

  const throttledScroll = useLazyInit(() =>
    throttle((offset: number, callback: (offset: number) => void) => {
      callback(offset);
    }, PERFORMANCE_CONFIG.UI.DEBOUNCE_DELAYS.SCROLL)
  );

  const debouncedInput = useLazyInit(() =>
    debounce((value: string, callback: (value: string) => void) => {
      callback(value);
    }, PERFORMANCE_CONFIG.UI.DEBOUNCE_DELAYS.INPUT)
  );

  return {
    debouncedSearch,
    throttledScroll,
    debouncedInput,
  };
};

export default usePerformanceOptimization;
