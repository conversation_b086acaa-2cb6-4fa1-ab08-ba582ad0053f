import { Animated, Easing, InteractionManager } from 'react-native';
import { PERFORMANCE_CONFIG, FEATURE_FLAGS } from '../config/performanceConfig';
import { performanceMonitor } from './performanceMonitoring';

/**
 * Optimized animation service for better performance and battery life
 */

interface AnimationConfig {
  duration?: number;
  easing?: (value: number) => number;
  useNativeDriver?: boolean;
  delay?: number;
  iterations?: number;
}

interface SpringConfig {
  tension?: number;
  friction?: number;
  useNativeDriver?: boolean;
  delay?: number;
}

class OptimizedAnimationService {
  private activeAnimations = new Set<Animated.CompositeAnimation>();
  private animationQueue: Array<() => void> = [];
  private isProcessingQueue = false;

  constructor() {
    this.setupAnimationCleanup();
  }

  // Setup automatic cleanup of completed animations
  private setupAnimationCleanup(): void {
    setInterval(() => {
      this.cleanupCompletedAnimations();
    }, 30000); // Clean up every 30 seconds
  }

  // Clean up completed animations
  private cleanupCompletedAnimations(): void {
    const completedAnimations: Animated.CompositeAnimation[] = [];
    
    this.activeAnimations.forEach(animation => {
      // Check if animation is still running (this is a simplified check)
      try {
        // In a real implementation, you'd need a way to check if animation is complete
        completedAnimations.push(animation);
      } catch (error) {
        completedAnimations.push(animation);
      }
    });

    completedAnimations.forEach(animation => {
      this.activeAnimations.delete(animation);
    });

    if (FEATURE_FLAGS.PERFORMANCE_MONITORING && completedAnimations.length > 0) {
      performanceMonitor.trackMetric('animation_cleanup', completedAnimations.length);
    }
  }

  // Create optimized timing animation
  createTiming(
    value: Animated.Value,
    toValue: number,
    config: AnimationConfig = {}
  ): Animated.CompositeAnimation {
    const {
      duration = PERFORMANCE_CONFIG.UI.ANIMATION.DURATION,
      easing = Easing.out(Easing.ease),
      useNativeDriver = PERFORMANCE_CONFIG.UI.ANIMATION.USE_NATIVE_DRIVER,
      delay = 0,
    } = config;

    const animation = Animated.timing(value, {
      toValue,
      duration,
      easing,
      useNativeDriver,
      delay,
    });

    this.activeAnimations.add(animation);
    return animation;
  }

  // Create optimized spring animation
  createSpring(
    value: Animated.Value,
    toValue: number,
    config: SpringConfig = {}
  ): Animated.CompositeAnimation {
    const {
      tension = 100,
      friction = 8,
      useNativeDriver = PERFORMANCE_CONFIG.UI.ANIMATION.USE_NATIVE_DRIVER,
      delay = 0,
    } = config;

    const animation = Animated.spring(value, {
      toValue,
      tension,
      friction,
      useNativeDriver,
      delay,
    });

    this.activeAnimations.add(animation);
    return animation;
  }

  // Create fade in animation
  createFadeIn(
    value: Animated.Value,
    duration: number = 300
  ): Animated.CompositeAnimation {
    return this.createTiming(value, 1, { duration });
  }

  // Create fade out animation
  createFadeOut(
    value: Animated.Value,
    duration: number = 300
  ): Animated.CompositeAnimation {
    return this.createTiming(value, 0, { duration });
  }

  // Create slide in animation
  createSlideIn(
    value: Animated.Value,
    fromValue: number = 100,
    duration: number = 300
  ): Animated.CompositeAnimation {
    value.setValue(fromValue);
    return this.createTiming(value, 0, { duration });
  }

  // Create scale animation
  createScale(
    value: Animated.Value,
    toValue: number = 1.1,
    duration: number = 200
  ): Animated.CompositeAnimation {
    return this.createSpring(value, toValue, { tension: 150, friction: 8 });
  }

  // Create pulse animation
  createPulse(
    value: Animated.Value,
    minScale: number = 1,
    maxScale: number = 1.1,
    duration: number = 1000
  ): Animated.CompositeAnimation {
    const animation = Animated.loop(
      Animated.sequence([
        this.createTiming(value, maxScale, { duration: duration / 2 }),
        this.createTiming(value, minScale, { duration: duration / 2 }),
      ])
    );

    this.activeAnimations.add(animation);
    return animation;
  }

  // Create rotation animation
  createRotation(
    value: Animated.Value,
    duration: number = 2000
  ): Animated.CompositeAnimation {
    const animation = Animated.loop(
      this.createTiming(value, 1, {
        duration,
        easing: Easing.linear,
      })
    );

    this.activeAnimations.add(animation);
    return animation;
  }

  // Create stagger animation for multiple elements
  createStagger(
    animations: Animated.CompositeAnimation[],
    staggerDelay: number = 100
  ): Animated.CompositeAnimation {
    const staggeredAnimation = Animated.stagger(staggerDelay, animations);
    this.activeAnimations.add(staggeredAnimation);
    return staggeredAnimation;
  }

  // Create parallel animations
  createParallel(
    animations: Animated.CompositeAnimation[]
  ): Animated.CompositeAnimation {
    const parallelAnimation = Animated.parallel(animations);
    this.activeAnimations.add(parallelAnimation);
    return parallelAnimation;
  }

  // Create sequence animations
  createSequence(
    animations: Animated.CompositeAnimation[]
  ): Animated.CompositeAnimation {
    const sequenceAnimation = Animated.sequence(animations);
    this.activeAnimations.add(sequenceAnimation);
    return sequenceAnimation;
  }

  // Start animation with performance tracking
  startAnimation(
    animation: Animated.CompositeAnimation,
    callback?: (finished?: boolean) => void
  ): void {
    const startTime = Date.now();

    // Queue animation if too many are running
    if (this.activeAnimations.size > 10) {
      this.queueAnimation(() => this.startAnimation(animation, callback));
      return;
    }

    InteractionManager.runAfterInteractions(() => {
      animation.start((finished) => {
        this.activeAnimations.delete(animation);
        
        if (FEATURE_FLAGS.PERFORMANCE_MONITORING) {
          const duration = Date.now() - startTime;
          performanceMonitor.trackMetric('animation_duration', duration, {
            finished: finished ?? false,
          });
        }

        callback?.(finished);
        this.processAnimationQueue();
      });
    });
  }

  // Queue animation for later execution
  private queueAnimation(animationFn: () => void): void {
    this.animationQueue.push(animationFn);
  }

  // Process queued animations
  private processAnimationQueue(): void {
    if (this.isProcessingQueue || this.animationQueue.length === 0) {
      return;
    }

    this.isProcessingQueue = true;
    
    const nextAnimation = this.animationQueue.shift();
    if (nextAnimation) {
      nextAnimation();
    }

    this.isProcessingQueue = false;
  }

  // Stop all animations
  stopAllAnimations(): void {
    this.activeAnimations.forEach(animation => {
      try {
        animation.stop();
      } catch (error) {
        console.error('Error stopping animation:', error);
      }
    });
    
    this.activeAnimations.clear();
    this.animationQueue = [];

    if (FEATURE_FLAGS.PERFORMANCE_MONITORING) {
      performanceMonitor.trackMetric('animations_stopped', 1);
    }
  }

  // Stop specific animation
  stopAnimation(animation: Animated.CompositeAnimation): void {
    try {
      animation.stop();
      this.activeAnimations.delete(animation);
    } catch (error) {
      console.error('Error stopping specific animation:', error);
    }
  }

  // Get animation statistics
  getAnimationStats(): {
    activeCount: number;
    queuedCount: number;
    totalCreated: number;
  } {
    return {
      activeCount: this.activeAnimations.size,
      queuedCount: this.animationQueue.length,
      totalCreated: this.activeAnimations.size + this.animationQueue.length,
    };
  }

  // Create optimized list item animation
  createListItemAnimation(
    value: Animated.Value,
    index: number,
    isVisible: boolean
  ): Animated.CompositeAnimation {
    const delay = index * 50; // Stagger delay
    const toValue = isVisible ? 1 : 0;
    
    return this.createTiming(value, toValue, {
      duration: 200,
      delay,
      useNativeDriver: true,
    });
  }

  // Create optimized modal animation
  createModalAnimation(
    opacity: Animated.Value,
    scale: Animated.Value,
    isVisible: boolean
  ): Animated.CompositeAnimation {
    if (isVisible) {
      opacity.setValue(0);
      scale.setValue(0.8);
      
      return this.createParallel([
        this.createTiming(opacity, 1, { duration: 200 }),
        this.createSpring(scale, 1, { tension: 150, friction: 8 }),
      ]);
    } else {
      return this.createParallel([
        this.createTiming(opacity, 0, { duration: 150 }),
        this.createTiming(scale, 0.8, { duration: 150 }),
      ]);
    }
  }

  // Create optimized loading animation
  createLoadingAnimation(value: Animated.Value): Animated.CompositeAnimation {
    return this.createRotation(value, 1000);
  }

  // Cleanup all resources
  cleanup(): void {
    this.stopAllAnimations();
    this.animationQueue = [];
  }
}

// Create singleton instance
export const optimizedAnimations = new OptimizedAnimationService();

// React hook for optimized animations
export const useOptimizedAnimation = () => {
  const createFadeAnimation = React.useCallback((initialValue: number = 0) => {
    return new Animated.Value(initialValue);
  }, []);

  const createScaleAnimation = React.useCallback((initialValue: number = 1) => {
    return new Animated.Value(initialValue);
  }, []);

  const createSlideAnimation = React.useCallback((initialValue: number = 0) => {
    return new Animated.Value(initialValue);
  }, []);

  React.useEffect(() => {
    return () => {
      // Cleanup animations on unmount
      optimizedAnimations.stopAllAnimations();
    };
  }, []);

  return {
    createFadeAnimation,
    createScaleAnimation,
    createSlideAnimation,
    startAnimation: optimizedAnimations.startAnimation.bind(optimizedAnimations),
    stopAnimation: optimizedAnimations.stopAnimation.bind(optimizedAnimations),
    createTiming: optimizedAnimations.createTiming.bind(optimizedAnimations),
    createSpring: optimizedAnimations.createSpring.bind(optimizedAnimations),
    createFadeIn: optimizedAnimations.createFadeIn.bind(optimizedAnimations),
    createFadeOut: optimizedAnimations.createFadeOut.bind(optimizedAnimations),
    createScale: optimizedAnimations.createScale.bind(optimizedAnimations),
    createPulse: optimizedAnimations.createPulse.bind(optimizedAnimations),
  };
};

export default optimizedAnimations;
