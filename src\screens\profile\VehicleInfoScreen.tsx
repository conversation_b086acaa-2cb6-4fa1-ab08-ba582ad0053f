import React, { useState } from 'react';
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  Alert,
  Modal,
  TextInput,
  Image,
  RefreshControl,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import { useNavigation } from '@react-navigation/native';
import * as ImagePicker from 'expo-image-picker';
import { Card, Button, Badge, LoadingSpinner } from '../../components/ui';
import { VehicleType, DocumentStatus, Vehicle, VehicleForm } from '../../types/profile';

// Utility function
const formatDate = (dateString: string) => {
  const date = new Date(dateString);
  return date.toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
  });
};

// Mock vehicle data
const mockVehicles: Vehicle[] = [
  {
    id: '1',
    type: VehicleType.MOTORCYCLE,
    make: 'Honda',
    model: 'CD 70',
    year: 2022,
    color: 'Red',
    plateNumber: 'LES-1234',
    engineNumber: 'CD70-123456',
    chassisNumber: 'CH-789012',
    registrationDate: '2022-03-15',
    insuranceExpiry: '2025-03-15',
    fitnessExpiry: '2024-12-31',
    documents: [
      { id: '1', type: 'vehicle_registration', name: 'Registration Certificate', status: DocumentStatus.VERIFIED, uploadedAt: '2023-01-16', verifiedAt: '2023-01-17' },
      { id: '2', type: 'insurance', name: 'Insurance Certificate', status: DocumentStatus.VERIFIED, uploadedAt: '2023-01-16', verifiedAt: '2023-01-18', expiryDate: '2025-03-15' },
    ],
    isActive: true,
    createdAt: '2023-01-16',
    updatedAt: '2023-01-16',
  },
  {
    id: '2',
    type: VehicleType.MOTORCYCLE,
    make: 'Yamaha',
    model: 'YBR 125',
    year: 2021,
    color: 'Blue',
    plateNumber: 'LES-5678',
    engineNumber: 'YBR-654321',
    chassisNumber: 'CH-345678',
    registrationDate: '2021-08-20',
    insuranceExpiry: '2024-08-20',
    fitnessExpiry: '2024-08-20',
    documents: [
      { id: '3', type: 'vehicle_registration', name: 'Registration Certificate', status: DocumentStatus.VERIFIED, uploadedAt: '2023-02-01', verifiedAt: '2023-02-02' },
      { id: '4', type: 'insurance', name: 'Insurance Certificate', status: DocumentStatus.EXPIRED, uploadedAt: '2023-02-01', expiryDate: '2024-08-20' },
    ],
    isActive: false,
    createdAt: '2023-02-01',
    updatedAt: '2023-02-01',
  },
];

const VehicleInfoScreen: React.FC = () => {
  const navigation = useNavigation();
  const [vehicles, setVehicles] = useState(mockVehicles);
  const [showAddModal, setShowAddModal] = useState(false);
  const [uploading, setUploading] = useState(false);
  const [editingVehicleId, setEditingVehicleId] = useState<string | null>(null);
  const [newVehicle, setNewVehicle] = useState<VehicleForm>({
    type: VehicleType.MOTORCYCLE,
    make: '',
    model: '',
    year: '',
    color: '',
    plateNumber: '',
    engineNumber: '',
    chassisNumber: '',
  });

  const getVehicleTypeIcon = (type: VehicleType) => {
    switch (type) {
      case VehicleType.MOTORCYCLE:
        return 'bicycle-outline';
      case VehicleType.CAR:
        return 'car-outline';
      case VehicleType.BICYCLE:
        return 'bicycle-outline';
      case VehicleType.SCOOTER:
        return 'bicycle-outline';
      default:
        return 'car-outline';
    }
  };

  const getDocumentStatusColor = (status: DocumentStatus) => {
    switch (status) {
      case DocumentStatus.VERIFIED:
        return '#10b981';
      case DocumentStatus.PENDING:
        return '#f59e0b';
      case DocumentStatus.REJECTED:
        return '#ef4444';
      case DocumentStatus.EXPIRED:
        return '#ef4444';
      case DocumentStatus.NOT_UPLOADED:
        return '#6b7280';
      default:
        return '#6b7280';
    }
  };

  const getDocumentStatusText = (status: DocumentStatus) => {
    switch (status) {
      case DocumentStatus.VERIFIED:
        return 'Verified';
      case DocumentStatus.PENDING:
        return 'Pending';
      case DocumentStatus.REJECTED:
        return 'Rejected';
      case DocumentStatus.EXPIRED:
        return 'Expired';
      case DocumentStatus.NOT_UPLOADED:
        return 'Not Uploaded';
      default:
        return 'Unknown';
    }
  };

  const handleSetActiveVehicle = (vehicleId: string) => {
    Alert.alert(
      'Set Active Vehicle',
      'Are you sure you want to set this vehicle as active?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Confirm',
          onPress: () => {
            setVehicles(prev => prev.map(v => ({
              ...v,
              isActive: v.id === vehicleId,
            })));
            Alert.alert('Success', 'Active vehicle updated successfully!');
          },
        },
      ]
    );
  };

  const handleDeleteVehicle = (vehicleId: string) => {
    const vehicle = vehicles.find(v => v.id === vehicleId);
    if (vehicle?.isActive) {
      Alert.alert('Error', 'Cannot delete active vehicle. Please set another vehicle as active first.');
      return;
    }

    Alert.alert(
      'Delete Vehicle',
      'Are you sure you want to delete this vehicle? This action cannot be undone.',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Delete',
          style: 'destructive',
          onPress: () => {
            setVehicles(prev => prev.filter(v => v.id !== vehicleId));
            Alert.alert('Success', 'Vehicle deleted successfully!');
          },
        },
      ]
    );
  };

  const handleUploadDocument = async (vehicleId: string, documentType: string) => {
    const { status } = await ImagePicker.requestMediaLibraryPermissionsAsync();
    if (status !== 'granted') {
      Alert.alert('Permission Required', 'Please grant camera roll permissions to upload documents.');
      return;
    }

    Alert.alert(
      'Upload Document',
      'Choose an option',
      [
        { text: 'Camera', onPress: () => openCamera(vehicleId, documentType) },
        { text: 'Gallery', onPress: () => openGallery(vehicleId, documentType) },
        { text: 'Cancel', style: 'cancel' },
      ]
    );
  };

  const openCamera = async (vehicleId: string, documentType: string) => {
    const { status } = await ImagePicker.requestCameraPermissionsAsync();
    if (status !== 'granted') {
      Alert.alert('Permission Required', 'Please grant camera permissions to take a photo.');
      return;
    }

    const result = await ImagePicker.launchCameraAsync({
      mediaTypes: ImagePicker.MediaTypeOptions.Images,
      allowsEditing: true,
      aspect: [4, 3],
      quality: 0.8,
    });

    if (!result.canceled) {
      uploadDocument(vehicleId, documentType, result.assets[0].uri);
    }
  };

  const openGallery = async (vehicleId: string, documentType: string) => {
    const result = await ImagePicker.launchImageLibraryAsync({
      mediaTypes: ImagePicker.MediaTypeOptions.Images,
      allowsEditing: true,
      aspect: [4, 3],
      quality: 0.8,
    });

    if (!result.canceled) {
      uploadDocument(vehicleId, documentType, result.assets[0].uri);
    }
  };

  const uploadDocument = async (vehicleId: string, documentType: string, uri: string) => {
    setUploading(true);
    try {
      // Simulate upload
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      // Update vehicle documents
      setVehicles(prev => prev.map(vehicle => {
        if (vehicle.id === vehicleId) {
          const existingDocIndex = vehicle.documents.findIndex(doc => doc.type === documentType);
          const newDoc = {
            id: Date.now().toString(),
            type: documentType as any,
            name: documentType.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase()),
            status: DocumentStatus.PENDING,
            uploadedAt: new Date().toISOString(),
            url: uri,
          };

          if (existingDocIndex >= 0) {
            // Update existing document
            const updatedDocs = [...vehicle.documents];
            updatedDocs[existingDocIndex] = newDoc;
            return { ...vehicle, documents: updatedDocs };
          } else {
            // Add new document
            return { ...vehicle, documents: [...vehicle.documents, newDoc] };
          }
        }
        return vehicle;
      }));

      Alert.alert('Success', 'Document uploaded successfully! It will be reviewed within 24 hours.');
    } catch (error) {
      Alert.alert('Error', 'Failed to upload document. Please try again.');
    } finally {
      setUploading(false);
    }
  };

  const handleAddVehicle = async () => {
    if (!newVehicle.make || !newVehicle.model || !newVehicle.year || !newVehicle.plateNumber) {
      Alert.alert('Error', 'Please fill in all required fields.');
      return;
    }

    try {
      const vehicle: Vehicle = {
        id: Date.now().toString(),
        type: newVehicle.type,
        make: newVehicle.make,
        model: newVehicle.model,
        year: parseInt(newVehicle.year),
        color: newVehicle.color,
        plateNumber: newVehicle.plateNumber.toUpperCase(),
        engineNumber: newVehicle.engineNumber,
        chassisNumber: newVehicle.chassisNumber,
        documents: [],
        isActive: vehicles.length === 0, // Set as active if it's the first vehicle
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      };

      setVehicles(prev => [...prev, vehicle]);
      setShowAddModal(false);
      setNewVehicle({
        type: VehicleType.MOTORCYCLE,
        make: '',
        model: '',
        year: '',
        color: '',
        plateNumber: '',
        engineNumber: '',
        chassisNumber: '',
      });
      Alert.alert('Success', 'Vehicle added successfully!');
    } catch (error) {
      Alert.alert('Error', 'Failed to add vehicle. Please try again.');
    }
  };

  const handleEditVehicle = (vehicle: Vehicle) => {
    setNewVehicle({
      type: vehicle.type,
      make: vehicle.make,
      model: vehicle.model,
      year: vehicle.year.toString(),
      color: vehicle.color,
      plateNumber: vehicle.plateNumber,
      engineNumber: vehicle.engineNumber || '',
      chassisNumber: vehicle.chassisNumber || '',
    });
    setEditingVehicleId(vehicle.id);
    setShowAddModal(true);
  };

  const renderVehicleCard = (vehicle: Vehicle) => (
    <View key={vehicle.id} style={{
      backgroundColor: 'white',
      marginHorizontal: 20,
      marginVertical: 8,
      borderRadius: 20,
      overflow: 'hidden',
      shadowColor: vehicle.isActive ? '#dc2626' : '#6b7280',
      shadowOffset: { width: 0, height: 8 },
      shadowOpacity: 0.15,
      shadowRadius: 16,
      elevation: 12,
      borderWidth: 1,
      borderColor: vehicle.isActive ? 'rgba(220, 38, 38, 0.1)' : 'rgba(107, 114, 128, 0.1)',
    }}>
        {/* Enhanced Header with gradient */}
        <View style={{
          backgroundColor: vehicle.isActive ? '#dc2626' : '#6b7280',
          paddingHorizontal: 20,
          paddingVertical: 20,
        }}>
          <View style={{ flexDirection: 'row', justifyContent: 'space-between', alignItems: 'center' }}>
            <View style={{ flexDirection: 'row', alignItems: 'center', flex: 1 }}>
              <View style={{
                width: 48,
                height: 48,
                backgroundColor: 'rgba(255,255,255,0.2)',
                borderRadius: 24,
                alignItems: 'center',
                justifyContent: 'center',
                marginRight: 12,
              }}>
                <Ionicons name={getVehicleTypeIcon(vehicle.type) as any} size={28} color="white" />
              </View>
              <View style={{ flex: 1 }}>
                <Text style={{ fontSize: 20, fontWeight: 'bold', color: 'white' }}>
                  {vehicle.make} {vehicle.model}
                </Text>
                <Text style={{ fontSize: 14, color: 'rgba(255,255,255,0.8)' }}>
                  {vehicle.plateNumber} • {vehicle.year}
                </Text>
              </View>
            </View>

            {vehicle.isActive && (
              <View style={{
                backgroundColor: 'rgba(255,255,255,0.2)',
                paddingHorizontal: 12,
                paddingVertical: 6,
                borderRadius: 20,
              }}>
                <Text style={{ fontSize: 12, fontWeight: 'bold', color: 'white' }}>
                  ACTIVE
                </Text>
              </View>
            )}
          </View>
        </View>

        {/* Content */}
        <View style={{ padding: 20 }}>
          {/* Vehicle Details */}
          <View style={{ marginBottom: 20 }}>
            <Text style={{ fontSize: 16, fontWeight: 'bold', color: '#111827', marginBottom: 12 }}>
              Vehicle Details
            </Text>
            <View style={{
              backgroundColor: '#f8fafc',
              borderRadius: 12,
              padding: 16,
              gap: 8,
            }}>
              <View style={{ flexDirection: 'row', justifyContent: 'space-between' }}>
                <Text style={{ fontSize: 14, color: '#6b7280', fontWeight: '500' }}>Color</Text>
                <Text style={{ fontSize: 14, color: '#111827', fontWeight: '600' }}>{vehicle.color}</Text>
              </View>
              {vehicle.engineNumber && (
                <View style={{ flexDirection: 'row', justifyContent: 'space-between' }}>
                  <Text style={{ fontSize: 14, color: '#6b7280', fontWeight: '500' }}>Engine No.</Text>
                  <Text style={{ fontSize: 14, color: '#111827', fontWeight: '600', fontFamily: 'monospace' }}>
                    {vehicle.engineNumber}
                  </Text>
                </View>
              )}
              {vehicle.chassisNumber && (
                <View style={{ flexDirection: 'row', justifyContent: 'space-between' }}>
                  <Text style={{ fontSize: 14, color: '#6b7280', fontWeight: '500' }}>Chassis No.</Text>
                  <Text style={{ fontSize: 14, color: '#111827', fontWeight: '600', fontFamily: 'monospace' }}>
                    {vehicle.chassisNumber}
                  </Text>
                </View>
              )}
              <View style={{ flexDirection: 'row', justifyContent: 'space-between' }}>
                <Text style={{ fontSize: 14, color: '#6b7280', fontWeight: '500' }}>Registered</Text>
                <Text style={{ fontSize: 14, color: '#111827', fontWeight: '600' }}>
                  {formatDate(vehicle.registrationDate)}
                </Text>
              </View>
            </View>
          </View>

          {/* Action Buttons */}
          <View style={{ flexDirection: 'row', gap: 12, marginBottom: 20 }}>
            <TouchableOpacity
              onPress={() => handleEditVehicle(vehicle)}
              style={{
                flex: 1,
                backgroundColor: '#3b82f6',
                paddingVertical: 12,
                borderRadius: 12,
                flexDirection: 'row',
                alignItems: 'center',
                justifyContent: 'center',
                shadowColor: '#3b82f6',
                shadowOffset: { width: 0, height: 4 },
                shadowOpacity: 0.3,
                shadowRadius: 8,
                elevation: 6,
              }}
            >
              <Ionicons name="pencil" size={16} color="white" />
              <Text style={{ fontSize: 14, fontWeight: 'bold', color: 'white', marginLeft: 6 }}>
                Edit
              </Text>
            </TouchableOpacity>

            {!vehicle.isActive ? (
              <TouchableOpacity
                onPress={() => handleSetActiveVehicle(vehicle.id)}
                style={{
                  flex: 1,
                  backgroundColor: '#10b981',
                  paddingVertical: 12,
                  borderRadius: 12,
                  flexDirection: 'row',
                  alignItems: 'center',
                  justifyContent: 'center',
                  shadowColor: '#10b981',
                  shadowOffset: { width: 0, height: 4 },
                  shadowOpacity: 0.3,
                  shadowRadius: 8,
                  elevation: 6,
                }}
              >
                <Ionicons name="checkmark-circle" size={16} color="white" />
                <Text style={{ fontSize: 14, fontWeight: 'bold', color: 'white', marginLeft: 6 }}>
                  Set Active
                </Text>
              </TouchableOpacity>
            ) : (
              <TouchableOpacity
                onPress={() => handleDeleteVehicle(vehicle.id)}
                style={{
                  backgroundColor: '#ef4444',
                  paddingHorizontal: 16,
                  paddingVertical: 12,
                  borderRadius: 12,
                  shadowColor: '#ef4444',
                  shadowOffset: { width: 0, height: 4 },
                  shadowOpacity: 0.3,
                  shadowRadius: 8,
                  elevation: 6,
                }}
              >
                <Ionicons name="trash" size={16} color="white" />
              </TouchableOpacity>
            )}
          </View>
        </View>
      </View>

      {/* Documents Section */}
      <View style={{ borderTopWidth: 1, borderTopColor: '#e5e7eb', paddingTop: 16 }}>
        <Text style={{ fontSize: 16, fontWeight: 'bold', color: '#111827', marginBottom: 12 }}>
          Documents
        </Text>
        
        <View style={{ gap: 8 }}>
          {['vehicle_registration', 'insurance'].map((docType) => {
            const doc = vehicle.documents.find(d => d.type === docType);
            const docName = docType.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase());
            
            return (
              <View key={docType} style={{ flexDirection: 'row', justifyContent: 'space-between', alignItems: 'center' }}>
                <Text style={{ fontSize: 14, color: '#111827', flex: 1 }}>
                  {docName}
                </Text>
                
                <View style={{ flexDirection: 'row', alignItems: 'center', gap: 8 }}>
                  {doc ? (
                    <Badge
                      text={getDocumentStatusText(doc.status)}
                      style={{ backgroundColor: getDocumentStatusColor(doc.status) }}
                    />
                  ) : (
                    <Badge
                      text="Not Uploaded"
                      style={{ backgroundColor: '#6b7280' }}
                    />
                  )}
                  
                  <TouchableOpacity
                    onPress={() => handleUploadDocument(vehicle.id, docType)}
                    disabled={uploading}
                    style={{
                      backgroundColor: '#ef4444',
                      paddingHorizontal: 8,
                      paddingVertical: 4,
                      borderRadius: 4,
                    }}
                  >
                    {uploading ? (
                      <LoadingSpinner size="small" color="white" />
                    ) : (
                      <Ionicons name={doc ? "refresh" : "cloud-upload"} size={12} color="white" />
                    )}
                  </TouchableOpacity>
                </View>
              </View>
            );
          })}
        </View>

        {vehicle.documents.some(doc => doc.status === DocumentStatus.EXPIRED) && (
          <View style={{ marginTop: 12, padding: 12, backgroundColor: '#fef2f2', borderRadius: 8, borderLeftWidth: 4, borderLeftColor: '#ef4444' }}>
            <Text style={{ fontSize: 12, color: '#92400e', fontWeight: '600' }}>
              ⚠️ Document Expired
            </Text>
            <Text style={{ fontSize: 12, color: '#92400e', marginTop: 2 }}>
              Please update expired documents to continue using this vehicle.
            </Text>
          </View>
        )}
      </View>
    </View>
  );

  const renderAddVehicleModal = () => (
    <Modal
      visible={showAddModal}
      animationType="slide"
      presentationStyle="pageSheet"
      onRequestClose={() => setShowAddModal(false)}
    >
      <SafeAreaView style={{ flex: 1, backgroundColor: '#f8fafc' }}>
        <View style={{
          paddingHorizontal: 20,
          paddingVertical: 16,
          backgroundColor: '#ffffff',
          borderBottomWidth: 1,
          borderBottomColor: '#e5e7eb',
          flexDirection: 'row',
          alignItems: 'center',
          justifyContent: 'space-between',
        }}>
          <TouchableOpacity onPress={() => setShowAddModal(false)}>
            <Ionicons name="close" size={24} color="#6b7280" />
          </TouchableOpacity>
          <Text style={{ fontSize: 18, fontWeight: 'bold', color: '#111827' }}>
            Add Vehicle
          </Text>
          <View style={{ width: 24 }} />
        </View>

        <ScrollView style={{ flex: 1 }} contentContainerStyle={{ padding: 20 }}>
          <Card variant="elevated" padding="lg">
            <Text style={{ fontSize: 16, fontWeight: 'bold', color: '#111827', marginBottom: 16 }}>
              Vehicle Information
            </Text>

            {/* Vehicle Type */}
            <View style={{ marginBottom: 16 }}>
              <Text style={{ fontSize: 14, fontWeight: '600', color: '#374151', marginBottom: 8 }}>
                Vehicle Type *
              </Text>
              <View style={{ flexDirection: 'row', gap: 8 }}>
                {Object.values(VehicleType).map((type) => (
                  <TouchableOpacity
                    key={type}
                    onPress={() => setNewVehicle(prev => ({ ...prev, type }))}
                    style={{
                      flex: 1,
                      backgroundColor: newVehicle.type === type ? '#fef2f2' : '#ffffff',
                      borderColor: newVehicle.type === type ? '#ef4444' : '#e5e7eb',
                      borderWidth: 1,
                      paddingVertical: 12,
                      paddingHorizontal: 16,
                      borderRadius: 8,
                      alignItems: 'center',
                    }}
                  >
                    <Ionicons
                      name={getVehicleTypeIcon(type) as any}
                      size={20}
                      color={newVehicle.type === type ? '#ef4444' : '#6b7280'}
                    />
                    <Text style={{
                      fontSize: 12,
                      fontWeight: '600',
                      color: newVehicle.type === type ? '#ef4444' : '#6b7280',
                      marginTop: 4,
                      textTransform: 'capitalize',
                    }}>
                      {type}
                    </Text>
                  </TouchableOpacity>
                ))}
              </View>
            </View>

            {/* Make */}
            <View style={{ marginBottom: 16 }}>
              <Text style={{ fontSize: 14, fontWeight: '600', color: '#374151', marginBottom: 8 }}>
                Make *
              </Text>
              <TextInput
                value={newVehicle.make}
                onChangeText={(text) => setNewVehicle(prev => ({ ...prev, make: text }))}
                placeholder="e.g., Honda, Yamaha, Toyota"
                style={{
                  backgroundColor: '#ffffff',
                  borderColor: '#e5e7eb',
                  borderWidth: 1,
                  paddingVertical: 12,
                  paddingHorizontal: 16,
                  borderRadius: 8,
                  fontSize: 16,
                  color: '#111827',
                }}
              />
            </View>

            {/* Model */}
            <View style={{ marginBottom: 16 }}>
              <Text style={{ fontSize: 14, fontWeight: '600', color: '#374151', marginBottom: 8 }}>
                Model *
              </Text>
              <TextInput
                value={newVehicle.model}
                onChangeText={(text) => setNewVehicle(prev => ({ ...prev, model: text }))}
                placeholder="e.g., CD 70, YBR 125, Corolla"
                style={{
                  backgroundColor: '#ffffff',
                  borderColor: '#e5e7eb',
                  borderWidth: 1,
                  paddingVertical: 12,
                  paddingHorizontal: 16,
                  borderRadius: 8,
                  fontSize: 16,
                  color: '#111827',
                }}
              />
            </View>

            {/* Year and Color */}
            <View style={{ flexDirection: 'row', gap: 12, marginBottom: 16 }}>
              <View style={{ flex: 1 }}>
                <Text style={{ fontSize: 14, fontWeight: '600', color: '#374151', marginBottom: 8 }}>
                  Year *
                </Text>
                <TextInput
                  value={newVehicle.year}
                  onChangeText={(text) => setNewVehicle(prev => ({ ...prev, year: text }))}
                  placeholder="2023"
                  keyboardType="numeric"
                  maxLength={4}
                  style={{
                    backgroundColor: '#ffffff',
                    borderColor: '#e5e7eb',
                    borderWidth: 1,
                    paddingVertical: 12,
                    paddingHorizontal: 16,
                    borderRadius: 8,
                    fontSize: 16,
                    color: '#111827',
                  }}
                />
              </View>

              <View style={{ flex: 1 }}>
                <Text style={{ fontSize: 14, fontWeight: '600', color: '#374151', marginBottom: 8 }}>
                  Color *
                </Text>
                <TextInput
                  value={newVehicle.color}
                  onChangeText={(text) => setNewVehicle(prev => ({ ...prev, color: text }))}
                  placeholder="Red"
                  style={{
                    backgroundColor: '#ffffff',
                    borderColor: '#e5e7eb',
                    borderWidth: 1,
                    paddingVertical: 12,
                    paddingHorizontal: 16,
                    borderRadius: 8,
                    fontSize: 16,
                    color: '#111827',
                  }}
                />
              </View>
            </View>

            {/* Plate Number */}
            <View style={{ marginBottom: 16 }}>
              <Text style={{ fontSize: 14, fontWeight: '600', color: '#374151', marginBottom: 8 }}>
                Plate Number *
              </Text>
              <TextInput
                value={newVehicle.plateNumber}
                onChangeText={(text) => setNewVehicle(prev => ({ ...prev, plateNumber: text.toUpperCase() }))}
                placeholder="LES-1234"
                autoCapitalize="characters"
                style={{
                  backgroundColor: '#ffffff',
                  borderColor: '#e5e7eb',
                  borderWidth: 1,
                  paddingVertical: 12,
                  paddingHorizontal: 16,
                  borderRadius: 8,
                  fontSize: 16,
                  color: '#111827',
                }}
              />
            </View>
            {/* Engine Number (Optional) */}
            <View style={{ marginBottom: 16 }}>
              <Text style={{ fontSize: 14, fontWeight: '600', color: '#374151', marginBottom: 8 }}>
                Engine Number (Optional)
              </Text>
              <TextInput
                value={newVehicle.engineNumber}
                onChangeText={(text) => setNewVehicle(prev => ({ ...prev, engineNumber: text }))}
                placeholder="Engine number"
                style={{
                  backgroundColor: '#ffffff',
                  borderColor: '#e5e7eb',
                  borderWidth: 1,
                  paddingVertical: 12,
                  paddingHorizontal: 16,
                  borderRadius: 8,
                  fontSize: 16,
                  color: '#111827',
                }}
              />
            </View>

            {/* Chassis Number (Optional) */}
            <View style={{ marginBottom: 24 }}>
              <Text style={{ fontSize: 14, fontWeight: '600', color: '#374151', marginBottom: 8 }}>
                Chassis Number (Optional)
              </Text>
              <TextInput
                value={newVehicle.chassisNumber}
                onChangeText={(text) => setNewVehicle(prev => ({ ...prev, chassisNumber: text }))}
                placeholder="Chassis number"
                style={{
                  backgroundColor: '#ffffff',
                  borderColor: '#e5e7eb',
                  borderWidth: 1,
                  paddingVertical: 12,
                  paddingHorizontal: 16,
                  borderRadius: 8,
                  fontSize: 16,
                  color: '#111827',
                }}
              />
            </View>

            <Button
              title="Add Vehicle"
              onPress={handleAddVehicle}
              style={{ backgroundColor: '#ef4444' }}
            />
          </Card>
        </ScrollView>
      </SafeAreaView>
    </Modal>
  );

  return (
    <View style={{ flex: 1, backgroundColor: '#f1f5f9' }}>
      <SafeAreaView style={{ flex: 1 }}>
        {/* Enhanced Header */}
        <View style={{
          backgroundColor: '#dc2626',
          paddingHorizontal: 20,
          paddingTop: 20,
          paddingBottom: 28,
          borderBottomLeftRadius: 28,
          borderBottomRightRadius: 28,
          shadowColor: '#dc2626',
          shadowOffset: { width: 0, height: 12 },
          shadowOpacity: 0.3,
          shadowRadius: 20,
          elevation: 16,
        }}>
          <View style={{
            flexDirection: 'row',
            alignItems: 'center',
            marginBottom: 20,
          }}>
            <TouchableOpacity
              onPress={() => navigation.goBack()}
              style={{
                width: 48,
                height: 48,
                borderRadius: 24,
                backgroundColor: 'rgba(255,255,255,0.2)',
                justifyContent: 'center',
                alignItems: 'center',
                marginRight: 16,
                borderWidth: 2,
                borderColor: 'rgba(255,255,255,0.3)',
              }}
            >
              <Ionicons name="arrow-back" size={24} color="white" />
            </TouchableOpacity>

            <View style={{ flex: 1 }}>
              <Text style={{
                fontSize: 24,
                fontWeight: 'bold',
                color: 'white',
                marginBottom: 4,
              }}>
                🚗 Vehicle Info
              </Text>
              <Text style={{
                fontSize: 14,
                color: 'rgba(255,255,255,0.8)',
                fontWeight: '500',
              }}>
                Manage your vehicles
              </Text>
            </View>

            <TouchableOpacity
              onPress={() => setShowAddModal(true)}
              style={{
                backgroundColor: 'rgba(255,255,255,0.2)',
                paddingHorizontal: 16,
                paddingVertical: 10,
                borderRadius: 16,
                borderWidth: 2,
                borderColor: 'rgba(255,255,255,0.3)',
                flexDirection: 'row',
                alignItems: 'center',
              }}
            >
              <Ionicons name="add" size={18} color="white" />
              <Text style={{ fontSize: 14, fontWeight: 'bold', color: 'white', marginLeft: 6 }}>
                Add
              </Text>
            </TouchableOpacity>
          </View>
        </View>

      <ScrollView style={{ flex: 1 }} showsVerticalScrollIndicator={false}>
        {vehicles.length === 0 ? (
          <View style={{ flex: 1, alignItems: 'center', justifyContent: 'center', padding: 40 }}>
            <Ionicons name="car-outline" size={64} color="#d1d5db" />
            <Text style={{ fontSize: 18, fontWeight: 'bold', color: '#6b7280', marginTop: 16, textAlign: 'center' }}>
              No Vehicles Added
            </Text>
            <Text style={{ fontSize: 14, color: '#9ca3af', marginTop: 8, textAlign: 'center' }}>
              Add your first vehicle to start receiving delivery orders
            </Text>
            <Button
              title="Add Vehicle"
              onPress={() => setShowAddModal(true)}
              style={{ backgroundColor: '#ef4444', marginTop: 24 }}
            />
          </View>
        ) : (
          <>
            {vehicles.map(renderVehicleCard)}

            {/* Bottom spacing */}
            <View style={{ height: 20 }} />
          </>
        )}
      </ScrollView>

      {renderAddVehicleModal()}
      </SafeAreaView>
    </View>
  );
};

export default VehicleInfoScreen;
