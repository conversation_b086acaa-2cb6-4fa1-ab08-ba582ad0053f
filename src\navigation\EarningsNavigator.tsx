import React from 'react';
import { createStackNavigator } from '@react-navigation/stack';

import EarningsScreen from '../screens/earnings/EarningsScreen';
import EarningsHistoryScreen from '../screens/earnings/EarningsHistoryScreen';
import WalletScreen from '../screens/earnings/WalletScreen';
import WithdrawalHistoryScreen from '../screens/earnings/WithdrawalHistoryScreen';
import PayoutScheduleScreen from '../screens/earnings/PayoutScheduleScreen';
import PerformanceAnalyticsScreen from '../screens/delivery/PerformanceAnalyticsScreen';

const Stack = createStackNavigator();

const EarningsNavigator: React.FC = () => {
  return (
    <Stack.Navigator
      id={"EarningsStack" as any}
      screenOptions={{
        headerShown: false,
      }}
    >
      <Stack.Screen name="EarningsMain" component={EarningsScreen} />
      <Stack.Screen name="EarningsHistory" component={EarningsHistoryScreen} />
      <Stack.Screen name="Wallet" component={WalletScreen} />
      <Stack.Screen name="WithdrawalHistory" component={WithdrawalHistoryScreen} />
      <Stack.Screen name="PayoutSchedule" component={PayoutScheduleScreen} />
      <Stack.Screen name="PerformanceAnalytics" component={PerformanceAnalyticsScreen} />
    </Stack.Navigator>
  );
};

export default EarningsNavigator;
