import AsyncStorage from '@react-native-async-storage/async-storage';
import {
  LoginCredentials,
  PhoneLoginCredentials,
  AuthTokens,
  AuthUser,
  LoginResponse,
  ApiError
} from '../../types/auth';
import { STORAGE_KEYS, API_CONFIG } from '../../utils/constants';
import { tokenManager } from './tokenManager';
import axios from 'axios';

class AuthService {
  constructor() {
    // Override tokenManager's refreshToken method to avoid circular dependency
    tokenManager.refreshToken = this.refreshToken.bind(this);
  }

  // Store tokens securely
  async storeTokens(tokens: AuthTokens): Promise<void> {
    return tokenManager.storeTokens(tokens);
  }

  // Retrieve tokens
  async getTokens(): Promise<AuthTokens | null> {
    return tokenManager.getTokens();
  }

  // Store user data
  async storeUserData(user: AuthUser): Promise<void> {
    try {
      await AsyncStorage.setItem(STORAGE_KEYS.USER_DATA, JSON.stringify(user));
    } catch (error) {
      console.error('Error storing user data:', error);
      throw new Error('Failed to store user data');
    }
  }

  // Retrieve user data
  async getUserData(): Promise<AuthUser | null> {
    try {
      const userData = await AsyncStorage.getItem(STORAGE_KEYS.USER_DATA);
      return userData ? JSON.parse(userData) : null;
    } catch (error) {
      console.error('Error retrieving user data:', error);
      return null;
    }
  }

  // Clear all auth data
  async clearAuthData(): Promise<void> {
    try {
      await tokenManager.clearAuthData();
      await AsyncStorage.removeItem(STORAGE_KEYS.USER_DATA);
    } catch (error) {
      console.error('Error clearing auth data:', error);
    }
  }

  // Check if token is expired
  isTokenExpired(expiresAt: number): boolean {
    return tokenManager.isTokenExpired(expiresAt);
  }

  // Login with email and password
  async login(credentials: LoginCredentials): Promise<LoginResponse> {
    try {
      const response = await axios.post<LoginResponse>(`${API_CONFIG.BASE_URL}/auth/login`, {
        email: credentials.email,
        password: credentials.password,
      });

      if (response.data) {
        await this.storeTokens(response.data.tokens);
        await this.storeUserData(response.data.user);
        return response.data;
      }

      throw new Error('Invalid response from server');
    } catch (error: any) {
      console.error('Login error:', error);

      if (error.response?.data?.message) {
        throw new Error(error.response.data.message);
      }

      throw new Error('Login failed. Please check your credentials and try again.');
    }
  }

  // Login with phone number
  async loginWithPhone(credentials: PhoneLoginCredentials): Promise<LoginResponse> {
    try {
      const response = await axios.post<LoginResponse>(`${API_CONFIG.BASE_URL}/auth/login-phone`, {
        phoneNumber: credentials.phoneNumber,
        verificationCode: credentials.verificationCode,
      });

      if (response.data) {
        await this.storeTokens(response.data.tokens);
        await this.storeUserData(response.data.user);
        return response.data;
      }

      throw new Error('Invalid response from server');
    } catch (error: any) {
      console.error('Phone login error:', error);

      if (error.response?.data?.message) {
        throw new Error(error.response.data.message);
      }

      throw new Error('Phone login failed. Please check your verification code and try again.');
    }
  }

  // Send verification code
  async sendVerificationCode(phoneNumber: string): Promise<void> {
    try {
      await axios.post(`${API_CONFIG.BASE_URL}/auth/send-verification`, { phoneNumber });
    } catch (error: any) {
      console.error('Send verification code error:', error);

      if (error.response?.data?.message) {
        throw new Error(error.response.data.message);
      }

      throw new Error('Failed to send verification code. Please try again.');
    }
  }

  // Refresh access token
  async refreshToken(): Promise<AuthTokens> {
    try {
      const tokens = await this.getTokens();

      if (!tokens?.refreshToken) {
        throw new Error('No refresh token available');
      }

      const response = await axios.post<{ tokens: AuthTokens }>(`${API_CONFIG.BASE_URL}/auth/refresh`, {
        refreshToken: tokens.refreshToken,
      });

      if (response.data?.tokens) {
        await this.storeTokens(response.data.tokens);
        return response.data.tokens;
      }

      throw new Error('Invalid response from server');
    } catch (error: any) {
      console.error('Refresh token error:', error);
      await this.clearAuthData();
      throw new Error('Session expired. Please log in again.');
    }
  }

  // Logout
  async logout(): Promise<void> {
    try {
      const tokens = await this.getTokens();

      if (tokens?.accessToken) {
        // Notify server about logout
        await axios.post(`${API_CONFIG.BASE_URL}/auth/logout`, {}, {
          headers: {
            Authorization: `Bearer ${tokens.accessToken}`,
          },
        });
      }
    } catch (error) {
      console.error('Logout API error:', error);
      // Continue with local logout even if API call fails
    } finally {
      await this.clearAuthData();
    }
  }

  // Check authentication status
  async isAuthenticated(): Promise<boolean> {
    try {
      const tokens = await this.getTokens();
      const userData = await this.getUserData();

      if (!tokens || !userData) {
        return false;
      }

      // Check if token is expired
      if (this.isTokenExpired(tokens.expiresAt)) {
        try {
          await this.refreshToken();
          return true;
        } catch {
          return false;
        }
      }

      return true;
    } catch (error) {
      console.error('Authentication check error:', error);
      return false;
    }
  }

  // Get current user
  async getCurrentUser(): Promise<AuthUser | null> {
    try {
      const isAuth = await this.isAuthenticated();
      if (!isAuth) {
        return null;
      }

      return await this.getUserData();
    } catch (error) {
      console.error('Get current user error:', error);
      return null;
    }
  }

  // Forgot password
  async forgotPassword(email: string): Promise<void> {
    try {
      await axios.post(`${API_CONFIG.BASE_URL}/auth/forgot-password`, { email });
    } catch (error: any) {
      console.error('Forgot password error:', error);

      if (error.response?.data?.message) {
        throw new Error(error.response.data.message);
      }

      throw new Error('Failed to send password reset email. Please try again.');
    }
  }

  // Reset password
  async resetPassword(token: string, newPassword: string): Promise<void> {
    try {
      await axios.post(`${API_CONFIG.BASE_URL}/auth/reset-password`, {
        token,
        password: newPassword,
      });
    } catch (error: any) {
      console.error('Reset password error:', error);

      if (error.response?.data?.message) {
        throw new Error(error.response.data.message);
      }

      throw new Error('Failed to reset password. Please try again.');
    }
  }
}

export const authService = new AuthService();
