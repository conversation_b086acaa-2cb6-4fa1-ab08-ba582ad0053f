import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  ScrollView,
  Modal,
  Animated,
  Dimensions,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import { useNavigation } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import { AdvancedToolsStackParamList } from '../../types';
import {
  PerformanceStats,
  Badge,
  RiderBadge,
  BadgeType,
  BadgeRarity,
  Leaderboard,
  LeaderboardEntry,
  Achievement,
} from '../../types/advancedTools';

type PerformanceScreenNavigationProp = StackNavigationProp<AdvancedToolsStackParamList, 'Performance'>;

const { width } = Dimensions.get('window');

const PerformanceScreen: React.FC = () => {
  const navigation = useNavigation<PerformanceScreenNavigationProp>();
  const [performanceStats, setPerformanceStats] = useState<PerformanceStats | null>(null);
  const [availableBadges, setAvailableBadges] = useState<Badge[]>([]);
  const [riderBadges, setRiderBadges] = useState<RiderBadge[]>([]);
  const [leaderboards, setLeaderboards] = useState<Leaderboard[]>([]);
  const [selectedBadge, setSelectedBadge] = useState<Badge | null>(null);
  const [showBadgeDetails, setShowBadgeDetails] = useState(false);
  const [activeTab, setActiveTab] = useState<'overview' | 'badges' | 'leaderboard'>('overview');
  const [selectedPeriod, setSelectedPeriod] = useState<'daily' | 'weekly' | 'monthly'>('weekly');
  const [loading, setLoading] = useState(true);

  // Mock performance data
  const mockPerformanceStats: PerformanceStats = {
    riderId: 'rider-1',
    period: 'weekly',
    totalDeliveries: 127,
    averageRating: 4.8,
    averageDeliveryTime: 22,
    onTimeDeliveryRate: 94,
    customerSatisfactionScore: 96,
    totalEarnings: 15420,
    totalDistance: 342,
    fuelEfficiency: 18.5,
    safetyScore: 98,
    rank: 3,
    totalRiders: 156,
    points: 2840,
    level: 12,
    nextLevelPoints: 3000,
    badges: [],
    achievements: [
      {
        id: 'ach-1',
        name: 'Speed Demon',
        description: 'Complete 50 deliveries under 20 minutes',
        icon: 'flash',
        unlockedAt: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000).toISOString(),
        category: 'Speed',
        points: 100,
      },
      {
        id: 'ach-2',
        name: 'Customer Favorite',
        description: 'Maintain 4.8+ rating for 30 days',
        icon: 'heart',
        unlockedAt: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000).toISOString(),
        category: 'Service',
        points: 150,
      },
    ],
  };

  const mockBadges: Badge[] = [
    {
      id: 'badge-1',
      name: 'Delivery Master',
      description: 'Complete 100 successful deliveries',
      type: BadgeType.DELIVERY_COUNT,
      rarity: BadgeRarity.COMMON,
      icon: 'checkmark-circle',
      color: '#10b981',
      criteria: {
        metric: 'total_deliveries',
        threshold: 100,
      },
      reward: {
        points: 200,
        bonus: 500,
        title: 'Delivery Expert',
      },
      isActive: true,
      createdAt: new Date().toISOString(),
    },
    {
      id: 'badge-2',
      name: 'Speed Lightning',
      description: 'Maintain average delivery time under 20 minutes',
      type: BadgeType.SPEED,
      rarity: BadgeRarity.RARE,
      icon: 'flash',
      color: '#f59e0b',
      criteria: {
        metric: 'average_delivery_time',
        threshold: 20,
        timeframe: 'weekly',
      },
      reward: {
        points: 500,
        bonus: 1000,
        title: 'Speed Demon',
      },
      isActive: true,
      createdAt: new Date().toISOString(),
    },
    {
      id: 'badge-3',
      name: 'Rating Champion',
      description: 'Achieve 4.9+ average rating',
      type: BadgeType.RATING,
      rarity: BadgeRarity.EPIC,
      icon: 'star',
      color: '#8b5cf6',
      criteria: {
        metric: 'average_rating',
        threshold: 4.9,
      },
      reward: {
        points: 1000,
        bonus: 2000,
        title: 'Service Excellence',
      },
      isActive: true,
      createdAt: new Date().toISOString(),
    },
    {
      id: 'badge-4',
      name: 'Safety Guardian',
      description: 'Maintain 100% safety score for 30 days',
      type: BadgeType.SAFETY,
      rarity: BadgeRarity.LEGENDARY,
      icon: 'shield-checkmark',
      color: '#dc2626',
      criteria: {
        metric: 'safety_score',
        threshold: 100,
        timeframe: 'monthly',
      },
      reward: {
        points: 2000,
        bonus: 5000,
        title: 'Safety Legend',
      },
      isActive: true,
      createdAt: new Date().toISOString(),
    },
  ];

  const mockRiderBadges: RiderBadge[] = [
    {
      id: 'rb-1',
      riderId: 'rider-1',
      badgeId: 'badge-1',
      badge: mockBadges[0],
      earnedAt: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString(),
      progress: 1.0,
      isCompleted: true,
      currentValue: 127,
      targetValue: 100,
    },
    {
      id: 'rb-2',
      riderId: 'rider-1',
      badgeId: 'badge-2',
      badge: mockBadges[1],
      earnedAt: '',
      progress: 0.9,
      isCompleted: false,
      currentValue: 22,
      targetValue: 20,
    },
    {
      id: 'rb-3',
      riderId: 'rider-1',
      badgeId: 'badge-3',
      badge: mockBadges[2],
      earnedAt: '',
      progress: 0.8,
      isCompleted: false,
      currentValue: 4.8,
      targetValue: 4.9,
    },
  ];

  const mockLeaderboard: Leaderboard = {
    period: 'weekly',
    category: 'deliveries',
    lastUpdated: new Date().toISOString(),
    riders: [
      {
        rank: 1,
        riderId: 'rider-top1',
        riderName: 'Ahmed Khan',
        riderPhoto: undefined,
        value: 145,
        change: 2,
        badge: 'crown',
        isCurrentUser: false,
      },
      {
        rank: 2,
        riderId: 'rider-top2',
        riderName: 'Muhammad Ali',
        riderPhoto: undefined,
        value: 138,
        change: -1,
        badge: 'medal',
        isCurrentUser: false,
      },
      {
        rank: 3,
        riderId: 'rider-1',
        riderName: 'You',
        riderPhoto: undefined,
        value: 127,
        change: 1,
        badge: 'trophy',
        isCurrentUser: true,
      },
      {
        rank: 4,
        riderId: 'rider-4',
        riderName: 'Hassan Sheikh',
        riderPhoto: undefined,
        value: 119,
        change: -2,
        badge: undefined,
        isCurrentUser: false,
      },
      {
        rank: 5,
        riderId: 'rider-5',
        riderName: 'Usman Ahmad',
        riderPhoto: undefined,
        value: 112,
        change: 0,
        badge: undefined,
        isCurrentUser: false,
      },
    ],
  };

  useEffect(() => {
    loadPerformanceData();
  }, [selectedPeriod]);

  const loadPerformanceData = async () => {
    setLoading(true);
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      setPerformanceStats(mockPerformanceStats);
      setAvailableBadges(mockBadges);
      setRiderBadges(mockRiderBadges);
      setLeaderboards([mockLeaderboard]);
    } catch (error) {
      console.error('Error loading performance data:', error);
    } finally {
      setLoading(false);
    }
  };

  const getRarityColor = (rarity: BadgeRarity): string => {
    switch (rarity) {
      case BadgeRarity.COMMON:
        return '#6b7280';
      case BadgeRarity.UNCOMMON:
        return '#10b981';
      case BadgeRarity.RARE:
        return '#3b82f6';
      case BadgeRarity.EPIC:
        return '#8b5cf6';
      case BadgeRarity.LEGENDARY:
        return '#f59e0b';
      default:
        return '#6b7280';
    }
  };

  const getRarityLabel = (rarity: BadgeRarity): string => {
    return rarity.charAt(0).toUpperCase() + rarity.slice(1);
  };

  const openBadgeDetails = (badge: Badge) => {
    setSelectedBadge(badge);
    setShowBadgeDetails(true);
  };

  const renderOverviewTab = () => {
    if (!performanceStats) return null;

    return (
      <ScrollView style={{ flex: 1 }}>
        {/* Level & Points */}
        <View style={{
          backgroundColor: 'white',
          marginHorizontal: 16,
          marginTop: 16,
          borderRadius: 12,
          padding: 16,
        }}>
          <View style={{
            flexDirection: 'row',
            alignItems: 'center',
            justifyContent: 'space-between',
            marginBottom: 16,
          }}>
            <View>
              <Text style={{
                fontSize: 24,
                fontWeight: 'bold',
                color: '#1f2937',
              }}>
                Level {performanceStats.level}
              </Text>
              <Text style={{
                fontSize: 14,
                color: '#6b7280',
              }}>
                {performanceStats.points} / {performanceStats.nextLevelPoints} XP
              </Text>
            </View>

            <View style={{
              alignItems: 'center',
            }}>
              <View style={{
                width: 60,
                height: 60,
                borderRadius: 30,
                backgroundColor: '#f97316',
                alignItems: 'center',
                justifyContent: 'center',
              }}>
                <Ionicons name="trophy" size={30} color="white" />
              </View>
              <Text style={{
                fontSize: 12,
                color: '#6b7280',
                marginTop: 4,
              }}>
                Rank #{performanceStats.rank}
              </Text>
            </View>
          </View>

          {/* Progress Bar */}
          <View style={{
            height: 8,
            backgroundColor: '#f3f4f6',
            borderRadius: 4,
            overflow: 'hidden',
          }}>
            <View style={{
              height: '100%',
              width: `${(performanceStats.points / performanceStats.nextLevelPoints) * 100}%`,
              backgroundColor: '#f97316',
            }} />
          </View>
        </View>

        {/* Performance Stats */}
        <View style={{
          backgroundColor: 'white',
          marginHorizontal: 16,
          marginTop: 16,
          borderRadius: 12,
          padding: 16,
        }}>
          <Text style={{
            fontSize: 16,
            fontWeight: '600',
            color: '#1f2937',
            marginBottom: 16,
          }}>
            This Week's Performance
          </Text>

          <View style={{
            flexDirection: 'row',
            flexWrap: 'wrap',
            justifyContent: 'space-between',
          }}>
            <View style={{
              width: '48%',
              backgroundColor: '#f0f9ff',
              padding: 12,
              borderRadius: 8,
              marginBottom: 12,
            }}>
              <Text style={{
                fontSize: 20,
                fontWeight: 'bold',
                color: '#0284c7',
              }}>
                {performanceStats.totalDeliveries}
              </Text>
              <Text style={{
                fontSize: 12,
                color: '#0369a1',
              }}>
                Total Deliveries
              </Text>
            </View>

            <View style={{
              width: '48%',
              backgroundColor: '#f0fdf4',
              padding: 12,
              borderRadius: 8,
              marginBottom: 12,
            }}>
              <Text style={{
                fontSize: 20,
                fontWeight: 'bold',
                color: '#16a34a',
              }}>
                {performanceStats.averageRating}⭐
              </Text>
              <Text style={{
                fontSize: 12,
                color: '#15803d',
              }}>
                Average Rating
              </Text>
            </View>

            <View style={{
              width: '48%',
              backgroundColor: '#fef3c7',
              padding: 12,
              borderRadius: 8,
              marginBottom: 12,
            }}>
              <Text style={{
                fontSize: 20,
                fontWeight: 'bold',
                color: '#d97706',
              }}>
                Rs.{performanceStats.totalEarnings.toLocaleString()}
              </Text>
              <Text style={{
                fontSize: 12,
                color: '#92400e',
              }}>
                Total Earnings
              </Text>
            </View>

            <View style={{
              width: '48%',
              backgroundColor: '#fdf2f8',
              padding: 12,
              borderRadius: 8,
              marginBottom: 12,
            }}>
              <Text style={{
                fontSize: 20,
                fontWeight: 'bold',
                color: '#be185d',
              }}>
                {performanceStats.onTimeDeliveryRate}%
              </Text>
              <Text style={{
                fontSize: 12,
                color: '#9d174d',
              }}>
                On-Time Rate
              </Text>
            </View>
          </View>
        </View>

        {/* Recent Achievements */}
        <View style={{
          backgroundColor: 'white',
          marginHorizontal: 16,
          marginTop: 16,
          marginBottom: 32,
          borderRadius: 12,
          padding: 16,
        }}>
          <Text style={{
            fontSize: 16,
            fontWeight: '600',
            color: '#1f2937',
            marginBottom: 16,
          }}>
            Recent Achievements
          </Text>

          {performanceStats.achievements.map((achievement) => (
            <View
              key={achievement.id}
              style={{
                flexDirection: 'row',
                alignItems: 'center',
                paddingVertical: 12,
                paddingHorizontal: 12,
                backgroundColor: '#f9fafb',
                borderRadius: 8,
                marginBottom: 8,
              }}
            >
              <View style={{
                width: 40,
                height: 40,
                borderRadius: 20,
                backgroundColor: '#f97316',
                alignItems: 'center',
                justifyContent: 'center',
                marginRight: 12,
              }}>
                <Ionicons name={achievement.icon as any} size={20} color="white" />
              </View>

              <View style={{ flex: 1 }}>
                <Text style={{
                  fontSize: 14,
                  fontWeight: '600',
                  color: '#1f2937',
                }}>
                  {achievement.name}
                </Text>
                <Text style={{
                  fontSize: 12,
                  color: '#6b7280',
                }}>
                  {achievement.description}
                </Text>
              </View>

              <View style={{
                backgroundColor: '#dcfce7',
                paddingHorizontal: 8,
                paddingVertical: 4,
                borderRadius: 12,
              }}>
                <Text style={{
                  fontSize: 12,
                  color: '#16a34a',
                  fontWeight: '600',
                }}>
                  +{achievement.points} XP
                </Text>
              </View>
            </View>
          ))}
        </View>
      </ScrollView>
    );
  };

  const renderBadgesTab = () => {
    return (
      <ScrollView style={{ flex: 1 }}>
        {/* Progress Summary */}
        <View style={{
          backgroundColor: 'white',
          marginHorizontal: 16,
          marginTop: 16,
          borderRadius: 12,
          padding: 16,
        }}>
          <Text style={{
            fontSize: 16,
            fontWeight: '600',
            color: '#1f2937',
            marginBottom: 12,
          }}>
            Badge Progress
          </Text>

          <View style={{
            flexDirection: 'row',
            justifyContent: 'space-between',
          }}>
            <View style={{ alignItems: 'center', flex: 1 }}>
              <Text style={{
                fontSize: 20,
                fontWeight: 'bold',
                color: '#10b981',
              }}>
                {riderBadges.filter(b => b.isCompleted).length}
              </Text>
              <Text style={{
                fontSize: 12,
                color: '#6b7280',
                textAlign: 'center',
              }}>
                Earned
              </Text>
            </View>

            <View style={{ alignItems: 'center', flex: 1 }}>
              <Text style={{
                fontSize: 20,
                fontWeight: 'bold',
                color: '#f59e0b',
              }}>
                {riderBadges.filter(b => !b.isCompleted && b.progress > 0).length}
              </Text>
              <Text style={{
                fontSize: 12,
                color: '#6b7280',
                textAlign: 'center',
              }}>
                In Progress
              </Text>
            </View>

            <View style={{ alignItems: 'center', flex: 1 }}>
              <Text style={{
                fontSize: 20,
                fontWeight: 'bold',
                color: '#6b7280',
              }}>
                {availableBadges.length - riderBadges.length}
              </Text>
              <Text style={{
                fontSize: 12,
                color: '#6b7280',
                textAlign: 'center',
              }}>
                Available
              </Text>
            </View>
          </View>
        </View>

        {/* Badges Grid */}
        <View style={{
          marginHorizontal: 16,
          marginTop: 16,
          marginBottom: 32,
        }}>
          <Text style={{
            fontSize: 16,
            fontWeight: '600',
            color: '#1f2937',
            marginBottom: 16,
          }}>
            All Badges
          </Text>

          <View style={{
            flexDirection: 'row',
            flexWrap: 'wrap',
            justifyContent: 'space-between',
          }}>
            {availableBadges.map((badge) => {
              const riderBadge = riderBadges.find(rb => rb.badgeId === badge.id);
              const isEarned = riderBadge?.isCompleted || false;
              const progress = riderBadge?.progress || 0;

              return (
                <TouchableOpacity
                  key={badge.id}
                  onPress={() => openBadgeDetails(badge)}
                  style={{
                    width: '48%',
                    backgroundColor: 'white',
                    borderRadius: 12,
                    padding: 16,
                    marginBottom: 16,
                    borderWidth: 2,
                    borderColor: isEarned ? badge.color : '#e5e7eb',
                    opacity: isEarned ? 1 : progress > 0 ? 0.8 : 0.6,
                  }}
                >
                  <View style={{
                    alignItems: 'center',
                    marginBottom: 12,
                  }}>
                    <View style={{
                      width: 60,
                      height: 60,
                      borderRadius: 30,
                      backgroundColor: isEarned ? badge.color : '#f3f4f6',
                      alignItems: 'center',
                      justifyContent: 'center',
                      marginBottom: 8,
                    }}>
                      <Ionicons
                        name={badge.icon as any}
                        size={30}
                        color={isEarned ? 'white' : '#9ca3af'}
                      />
                    </View>

                    <View style={{
                      backgroundColor: getRarityColor(badge.rarity) + '20',
                      paddingHorizontal: 8,
                      paddingVertical: 2,
                      borderRadius: 8,
                    }}>
                      <Text style={{
                        fontSize: 10,
                        color: getRarityColor(badge.rarity),
                        fontWeight: '600',
                        textTransform: 'uppercase',
                      }}>
                        {getRarityLabel(badge.rarity)}
                      </Text>
                    </View>
                  </View>

                  <Text style={{
                    fontSize: 14,
                    fontWeight: '600',
                    color: '#1f2937',
                    textAlign: 'center',
                    marginBottom: 4,
                  }}>
                    {badge.name}
                  </Text>

                  <Text style={{
                    fontSize: 12,
                    color: '#6b7280',
                    textAlign: 'center',
                    marginBottom: 8,
                  }}>
                    {badge.description}
                  </Text>

                  {isEarned ? (
                    <View style={{
                      backgroundColor: '#dcfce7',
                      paddingVertical: 4,
                      borderRadius: 6,
                    }}>
                      <Text style={{
                        fontSize: 12,
                        color: '#16a34a',
                        fontWeight: '600',
                        textAlign: 'center',
                      }}>
                        ✓ EARNED
                      </Text>
                    </View>
                  ) : progress > 0 ? (
                    <View>
                      <View style={{
                        height: 4,
                        backgroundColor: '#f3f4f6',
                        borderRadius: 2,
                        overflow: 'hidden',
                        marginBottom: 4,
                      }}>
                        <View style={{
                          height: '100%',
                          width: `${progress * 100}%`,
                          backgroundColor: badge.color,
                        }} />
                      </View>
                      <Text style={{
                        fontSize: 10,
                        color: '#6b7280',
                        textAlign: 'center',
                      }}>
                        {Math.round(progress * 100)}% Complete
                      </Text>
                    </View>
                  ) : (
                    <View style={{
                      backgroundColor: '#f3f4f6',
                      paddingVertical: 4,
                      borderRadius: 6,
                    }}>
                      <Text style={{
                        fontSize: 12,
                        color: '#6b7280',
                        textAlign: 'center',
                      }}>
                        Not Started
                      </Text>
                    </View>
                  )}
                </TouchableOpacity>
              );
            })}
          </View>
        </View>
      </ScrollView>
    );
  };

  const renderLeaderboardTab = () => {
    const currentLeaderboard = leaderboards.find(l => l.period === selectedPeriod);

    return (
      <ScrollView style={{ flex: 1 }}>
        {/* Period Selector */}
        <View style={{
          flexDirection: 'row',
          paddingHorizontal: 16,
          paddingVertical: 12,
          backgroundColor: 'white',
          borderBottomWidth: 1,
          borderBottomColor: '#e5e7eb',
        }}>
          {(['daily', 'weekly', 'monthly'] as const).map((period) => (
            <TouchableOpacity
              key={period}
              onPress={() => setSelectedPeriod(period)}
              style={{
                flex: 1,
                paddingVertical: 8,
                paddingHorizontal: 12,
                marginHorizontal: 4,
                borderRadius: 8,
                backgroundColor: selectedPeriod === period ? '#f97316' : '#f3f4f6',
              }}
            >
              <Text style={{
                textAlign: 'center',
                fontSize: 14,
                fontWeight: '600',
                color: selectedPeriod === period ? 'white' : '#6b7280',
                textTransform: 'capitalize',
              }}>
                {period}
              </Text>
            </TouchableOpacity>
          ))}
        </View>

        {currentLeaderboard && (
          <View style={{
            backgroundColor: 'white',
            marginHorizontal: 16,
            marginTop: 16,
            borderRadius: 12,
            overflow: 'hidden',
          }}>
            <View style={{
              backgroundColor: '#f97316',
              padding: 16,
            }}>
              <Text style={{
                fontSize: 18,
                fontWeight: '600',
                color: 'white',
                textAlign: 'center',
              }}>
                🏆 Top Performers - {selectedPeriod.charAt(0).toUpperCase() + selectedPeriod.slice(1)}
              </Text>
              <Text style={{
                fontSize: 12,
                color: 'rgba(255,255,255,0.8)',
                textAlign: 'center',
                marginTop: 4,
              }}>
                Updated {new Date(currentLeaderboard.lastUpdated).toLocaleTimeString()}
              </Text>
            </View>

            {currentLeaderboard.riders.map((rider, index) => (
              <View
                key={rider.riderId}
                style={{
                  flexDirection: 'row',
                  alignItems: 'center',
                  paddingHorizontal: 16,
                  paddingVertical: 12,
                  backgroundColor: rider.isCurrentUser ? '#fef3c7' : 'white',
                  borderBottomWidth: index < currentLeaderboard.riders.length - 1 ? 1 : 0,
                  borderBottomColor: '#f3f4f6',
                }}
              >
                {/* Rank */}
                <View style={{
                  width: 40,
                  alignItems: 'center',
                  marginRight: 12,
                }}>
                  {rider.rank <= 3 ? (
                    <View style={{
                      width: 32,
                      height: 32,
                      borderRadius: 16,
                      backgroundColor: rider.rank === 1 ? '#fbbf24' : rider.rank === 2 ? '#9ca3af' : '#cd7c2f',
                      alignItems: 'center',
                      justifyContent: 'center',
                    }}>
                      <Text style={{
                        fontSize: 14,
                        fontWeight: 'bold',
                        color: 'white',
                      }}>
                        {rider.rank}
                      </Text>
                    </View>
                  ) : (
                    <Text style={{
                      fontSize: 16,
                      fontWeight: '600',
                      color: '#6b7280',
                    }}>
                      {rider.rank}
                    </Text>
                  )}
                </View>

                {/* Rider Info */}
                <View style={{ flex: 1 }}>
                  <View style={{
                    flexDirection: 'row',
                    alignItems: 'center',
                  }}>
                    <Text style={{
                      fontSize: 16,
                      fontWeight: rider.isCurrentUser ? '600' : '500',
                      color: rider.isCurrentUser ? '#d97706' : '#1f2937',
                    }}>
                      {rider.riderName}
                    </Text>
                    {rider.isCurrentUser && (
                      <View style={{
                        backgroundColor: '#f97316',
                        paddingHorizontal: 6,
                        paddingVertical: 2,
                        borderRadius: 8,
                        marginLeft: 8,
                      }}>
                        <Text style={{
                          fontSize: 10,
                          color: 'white',
                          fontWeight: '600',
                        }}>
                          YOU
                        </Text>
                      </View>
                    )}
                  </View>

                  <View style={{
                    flexDirection: 'row',
                    alignItems: 'center',
                    marginTop: 2,
                  }}>
                    <Text style={{
                      fontSize: 14,
                      color: '#6b7280',
                    }}>
                      {rider.value} deliveries
                    </Text>

                    {rider.change !== 0 && (
                      <View style={{
                        flexDirection: 'row',
                        alignItems: 'center',
                        marginLeft: 8,
                      }}>
                        <Ionicons
                          name={rider.change > 0 ? 'arrow-up' : 'arrow-down'}
                          size={12}
                          color={rider.change > 0 ? '#10b981' : '#ef4444'}
                        />
                        <Text style={{
                          fontSize: 12,
                          color: rider.change > 0 ? '#10b981' : '#ef4444',
                          marginLeft: 2,
                        }}>
                          {Math.abs(rider.change)}
                        </Text>
                      </View>
                    )}
                  </View>
                </View>

                {/* Badge */}
                {rider.badge && (
                  <View style={{
                    marginLeft: 12,
                  }}>
                    <Ionicons
                      name={rider.badge as any}
                      size={24}
                      color={rider.rank === 1 ? '#fbbf24' : rider.rank === 2 ? '#9ca3af' : '#cd7c2f'}
                    />
                  </View>
                )}
              </View>
            ))}
          </View>
        )}

        <View style={{ height: 32 }} />
      </ScrollView>
    );
  };

  return (
    <SafeAreaView style={{ flex: 1, backgroundColor: '#f9fafb' }}>
      {/* Header */}
      <View style={{
        flexDirection: 'row',
        alignItems: 'center',
        paddingHorizontal: 16,
        paddingVertical: 12,
        backgroundColor: 'white',
        borderBottomWidth: 1,
        borderBottomColor: '#e5e7eb',
      }}>
        <TouchableOpacity
          onPress={() => navigation.goBack()}
          style={{
            width: 40,
            height: 40,
            alignItems: 'center',
            justifyContent: 'center',
            marginRight: 8,
          }}
        >
          <Ionicons name="arrow-back" size={24} color="#374151" />
        </TouchableOpacity>

        <Text style={{
          fontSize: 18,
          fontWeight: '600',
          color: '#1f2937',
          flex: 1,
        }}>
          Performance & Badges
        </Text>

        <TouchableOpacity
          onPress={loadPerformanceData}
          style={{
            width: 40,
            height: 40,
            alignItems: 'center',
            justifyContent: 'center',
          }}
        >
          <Ionicons name="refresh" size={24} color="#374151" />
        </TouchableOpacity>
      </View>

      {/* Tab Navigation */}
      <View style={{
        flexDirection: 'row',
        backgroundColor: 'white',
        borderBottomWidth: 1,
        borderBottomColor: '#e5e7eb',
      }}>
        {(['overview', 'badges', 'leaderboard'] as const).map((tab) => (
          <TouchableOpacity
            key={tab}
            onPress={() => setActiveTab(tab)}
            style={{
              flex: 1,
              paddingVertical: 12,
              borderBottomWidth: 2,
              borderBottomColor: activeTab === tab ? '#f97316' : 'transparent',
            }}
          >
            <Text style={{
              textAlign: 'center',
              fontSize: 14,
              fontWeight: '600',
              color: activeTab === tab ? '#f97316' : '#6b7280',
              textTransform: 'capitalize',
            }}>
              {tab}
            </Text>
          </TouchableOpacity>
        ))}
      </View>

      {/* Tab Content */}
      {loading ? (
        <View style={{
          flex: 1,
          alignItems: 'center',
          justifyContent: 'center',
        }}>
          <Text style={{
            fontSize: 16,
            color: '#6b7280',
          }}>
            Loading performance data...
          </Text>
        </View>
      ) : (
        <>
          {activeTab === 'overview' && renderOverviewTab()}
          {activeTab === 'badges' && renderBadgesTab()}
          {activeTab === 'leaderboard' && renderLeaderboardTab()}
        </>
      )}

      {/* Badge Details Modal */}
      <Modal
        visible={showBadgeDetails}
        animationType="slide"
        presentationStyle="pageSheet"
      >
        {selectedBadge && (
          <SafeAreaView style={{ flex: 1, backgroundColor: '#f9fafb' }}>
            {/* Modal Header */}
            <View style={{
              flexDirection: 'row',
              alignItems: 'center',
              paddingHorizontal: 16,
              paddingVertical: 12,
              backgroundColor: 'white',
              borderBottomWidth: 1,
              borderBottomColor: '#e5e7eb',
            }}>
              <TouchableOpacity
                onPress={() => setShowBadgeDetails(false)}
                style={{
                  width: 40,
                  height: 40,
                  alignItems: 'center',
                  justifyContent: 'center',
                  marginRight: 8,
                }}
              >
                <Ionicons name="close" size={24} color="#374151" />
              </TouchableOpacity>

              <Text style={{
                fontSize: 18,
                fontWeight: '600',
                color: '#1f2937',
                flex: 1,
              }}>
                Badge Details
              </Text>
            </View>

            <ScrollView style={{ flex: 1, padding: 16 }}>
              {/* Badge Info */}
              <View style={{
                backgroundColor: 'white',
                borderRadius: 12,
                padding: 24,
                alignItems: 'center',
                marginBottom: 16,
              }}>
                <View style={{
                  width: 100,
                  height: 100,
                  borderRadius: 50,
                  backgroundColor: selectedBadge.color,
                  alignItems: 'center',
                  justifyContent: 'center',
                  marginBottom: 16,
                }}>
                  <Ionicons
                    name={selectedBadge.icon as any}
                    size={50}
                    color="white"
                  />
                </View>

                <View style={{
                  backgroundColor: getRarityColor(selectedBadge.rarity) + '20',
                  paddingHorizontal: 12,
                  paddingVertical: 4,
                  borderRadius: 12,
                  marginBottom: 8,
                }}>
                  <Text style={{
                    fontSize: 12,
                    color: getRarityColor(selectedBadge.rarity),
                    fontWeight: '600',
                    textTransform: 'uppercase',
                  }}>
                    {getRarityLabel(selectedBadge.rarity)}
                  </Text>
                </View>

                <Text style={{
                  fontSize: 24,
                  fontWeight: 'bold',
                  color: '#1f2937',
                  textAlign: 'center',
                  marginBottom: 8,
                }}>
                  {selectedBadge.name}
                </Text>

                <Text style={{
                  fontSize: 16,
                  color: '#6b7280',
                  textAlign: 'center',
                  lineHeight: 24,
                }}>
                  {selectedBadge.description}
                </Text>
              </View>

              {/* Requirements */}
              <View style={{
                backgroundColor: 'white',
                borderRadius: 12,
                padding: 16,
                marginBottom: 16,
              }}>
                <Text style={{
                  fontSize: 16,
                  fontWeight: '600',
                  color: '#1f2937',
                  marginBottom: 12,
                }}>
                  Requirements
                </Text>

                <Text style={{
                  fontSize: 14,
                  color: '#6b7280',
                  lineHeight: 20,
                }}>
                  {selectedBadge.criteria.metric.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase())}: {selectedBadge.criteria.threshold}
                  {selectedBadge.criteria.timeframe && ` (${selectedBadge.criteria.timeframe})`}
                </Text>
              </View>

              {/* Rewards */}
              <View style={{
                backgroundColor: 'white',
                borderRadius: 12,
                padding: 16,
                marginBottom: 32,
              }}>
                <Text style={{
                  fontSize: 16,
                  fontWeight: '600',
                  color: '#1f2937',
                  marginBottom: 12,
                }}>
                  Rewards
                </Text>

                <View style={{
                  flexDirection: 'row',
                  justifyContent: 'space-between',
                }}>
                  <View style={{ alignItems: 'center', flex: 1 }}>
                    <Text style={{
                      fontSize: 18,
                      fontWeight: 'bold',
                      color: '#f97316',
                    }}>
                      {selectedBadge.reward.points}
                    </Text>
                    <Text style={{
                      fontSize: 12,
                      color: '#6b7280',
                      textAlign: 'center',
                    }}>
                      XP Points
                    </Text>
                  </View>

                  {selectedBadge.reward.bonus && (
                    <View style={{ alignItems: 'center', flex: 1 }}>
                      <Text style={{
                        fontSize: 18,
                        fontWeight: 'bold',
                        color: '#10b981',
                      }}>
                        Rs.{selectedBadge.reward.bonus}
                      </Text>
                      <Text style={{
                        fontSize: 12,
                        color: '#6b7280',
                        textAlign: 'center',
                      }}>
                        Bonus
                      </Text>
                    </View>
                  )}

                  {selectedBadge.reward.title && (
                    <View style={{ alignItems: 'center', flex: 1 }}>
                      <Text style={{
                        fontSize: 14,
                        fontWeight: 'bold',
                        color: '#8b5cf6',
                        textAlign: 'center',
                      }}>
                        {selectedBadge.reward.title}
                      </Text>
                      <Text style={{
                        fontSize: 12,
                        color: '#6b7280',
                        textAlign: 'center',
                      }}>
                        Title
                      </Text>
                    </View>
                  )}
                </View>
              </View>
            </ScrollView>
          </SafeAreaView>
        )}
      </Modal>
    </SafeAreaView>
  );
};

export default PerformanceScreen;
