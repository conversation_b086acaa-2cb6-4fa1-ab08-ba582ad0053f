import { apiClient, ApiResponse, retryRequest } from './apiConfig';

// Order types
export interface Order {
  id: string;
  orderNumber: string;
  status: 'pending' | 'accepted' | 'picked_up' | 'in_transit' | 'delivered' | 'cancelled';
  customer: {
    id: string;
    name: string;
    phone: string;
    address: string;
    location: {
      latitude: number;
      longitude: number;
    };
  };
  restaurant: {
    id: string;
    name: string;
    address: string;
    phone: string;
    location: {
      latitude: number;
      longitude: number;
    };
  };
  items: {
    id: string;
    name: string;
    quantity: number;
    price: number;
    specialInstructions?: string;
  }[];
  payment: {
    method: 'cash' | 'card' | 'wallet';
    amount: number;
    tip?: number;
    deliveryFee: number;
  };
  timing: {
    orderTime: string;
    estimatedPickupTime: string;
    estimatedDeliveryTime: string;
    actualPickupTime?: string;
    actualDeliveryTime?: string;
  };
  distance: {
    toRestaurant: number;
    toCustomer: number;
    total: number;
  };
  earnings: {
    baseFee: number;
    distanceFee: number;
    tip: number;
    bonus?: number;
    total: number;
  };
  specialInstructions?: string;
  createdAt: string;
  updatedAt: string;
}

export interface OrderRequest {
  id: string;
  orderId: string;
  expiresAt: string;
  autoDeclineIn: number;
}

export interface OrderStatusUpdate {
  status: Order['status'];
  location?: {
    latitude: number;
    longitude: number;
  };
  notes?: string;
  proofPhoto?: string;
  customerSignature?: string;
  otp?: string;
}

export interface DeliveryProof {
  photo?: string;
  signature?: string;
  otp?: string;
  notes?: string;
}

// Order Service
export class OrderService {
  // Get available order requests
  static async getOrderRequests(): Promise<ApiResponse<OrderRequest[]>> {
    return retryRequest(async () => {
      const response = await apiClient.get<ApiResponse<OrderRequest[]>>('/orders/requests');
      return response.data;
    });
  }

  // Get order details
  static async getOrderDetails(orderId: string): Promise<ApiResponse<Order>> {
    return retryRequest(async () => {
      const response = await apiClient.get<ApiResponse<Order>>(`/orders/${orderId}`);
      return response.data;
    });
  }

  // Accept order
  static async acceptOrder(orderId: string): Promise<ApiResponse<{ message: string; order: Order }>> {
    return retryRequest(async () => {
      const response = await apiClient.post<ApiResponse<{ message: string; order: Order }>>(
        `/orders/${orderId}/accept`
      );
      return response.data;
    });
  }

  // Decline order
  static async declineOrder(orderId: string, reason?: string): Promise<ApiResponse<{ message: string }>> {
    return retryRequest(async () => {
      const response = await apiClient.post<ApiResponse<{ message: string }>>(
        `/orders/${orderId}/decline`,
        { reason }
      );
      return response.data;
    });
  }

  // Update order status
  static async updateOrderStatus(
    orderId: string, 
    statusUpdate: OrderStatusUpdate
  ): Promise<ApiResponse<{ message: string; order: Order }>> {
    return retryRequest(async () => {
      const response = await apiClient.put<ApiResponse<{ message: string; order: Order }>>(
        `/orders/${orderId}/status`,
        statusUpdate
      );
      return response.data;
    });
  }

  // Mark order as picked up
  static async markPickedUp(
    orderId: string, 
    location: { latitude: number; longitude: number },
    photo?: string
  ): Promise<ApiResponse<{ message: string; order: Order }>> {
    return retryRequest(async () => {
      const response = await apiClient.post<ApiResponse<{ message: string; order: Order }>>(
        `/orders/${orderId}/pickup`,
        { location, photo }
      );
      return response.data;
    });
  }

  // Mark order as delivered
  static async markDelivered(
    orderId: string,
    deliveryProof: DeliveryProof
  ): Promise<ApiResponse<{ message: string; order: Order; earnings: any }>> {
    return retryRequest(async () => {
      const response = await apiClient.post<ApiResponse<{ message: string; order: Order; earnings: any }>>(
        `/orders/${orderId}/deliver`,
        deliveryProof
      );
      return response.data;
    });
  }

  // Cancel order
  static async cancelOrder(
    orderId: string, 
    reason: string
  ): Promise<ApiResponse<{ message: string }>> {
    return retryRequest(async () => {
      const response = await apiClient.post<ApiResponse<{ message: string }>>(
        `/orders/${orderId}/cancel`,
        { reason }
      );
      return response.data;
    });
  }

  // Get active orders
  static async getActiveOrders(): Promise<ApiResponse<Order[]>> {
    return retryRequest(async () => {
      const response = await apiClient.get<ApiResponse<Order[]>>('/orders/active');
      return response.data;
    });
  }

  // Get order history
  static async getOrderHistory(params?: {
    page?: number;
    limit?: number;
    status?: string;
    dateFrom?: string;
    dateTo?: string;
  }): Promise<ApiResponse<{
    orders: Order[];
    pagination: {
      page: number;
      limit: number;
      total: number;
      totalPages: number;
    };
  }>> {
    return retryRequest(async () => {
      const response = await apiClient.get<ApiResponse<{
        orders: Order[];
        pagination: any;
      }>>('/orders/history', { params });
      return response.data;
    });
  }

  // Get navigation route
  static async getNavigationRoute(
    orderId: string,
    destination: 'restaurant' | 'customer'
  ): Promise<ApiResponse<{
    route: {
      distance: number;
      duration: number;
      coordinates: { latitude: number; longitude: number }[];
    };
  }>> {
    return retryRequest(async () => {
      const response = await apiClient.get<ApiResponse<{
        route: any;
      }>>(`/orders/${orderId}/route/${destination}`);
      return response.data;
    });
  }

  // Report order issue
  static async reportOrderIssue(
    orderId: string,
    issue: {
      type: 'customer_not_available' | 'wrong_address' | 'payment_issue' | 'restaurant_delay' | 'other';
      description: string;
      photo?: string;
    }
  ): Promise<ApiResponse<{ message: string; ticketId: string }>> {
    return retryRequest(async () => {
      const response = await apiClient.post<ApiResponse<{ message: string; ticketId: string }>>(
        `/orders/${orderId}/report-issue`,
        issue
      );
      return response.data;
    });
  }

  // Get order earnings breakdown
  static async getOrderEarnings(orderId: string): Promise<ApiResponse<{
    baseFee: number;
    distanceFee: number;
    tip: number;
    bonus: number;
    surge: number;
    total: number;
    breakdown: {
      [key: string]: number;
    };
  }>> {
    return retryRequest(async () => {
      const response = await apiClient.get<ApiResponse<any>>(`/orders/${orderId}/earnings`);
      return response.data;
    });
  }

  // Update rider location during delivery
  static async updateRiderLocation(
    orderId: string,
    location: { latitude: number; longitude: number }
  ): Promise<ApiResponse<{ message: string }>> {
    return retryRequest(async () => {
      const response = await apiClient.put<ApiResponse<{ message: string }>>(
        `/orders/${orderId}/location`,
        { location }
      );
      return response.data;
    });
  }

  // Get order tracking info for customer
  static async getOrderTracking(orderId: string): Promise<ApiResponse<{
    status: string;
    riderLocation?: { latitude: number; longitude: number };
    estimatedArrival?: string;
    timeline: {
      status: string;
      timestamp: string;
      description: string;
    }[];
  }>> {
    return retryRequest(async () => {
      const response = await apiClient.get<ApiResponse<any>>(`/orders/${orderId}/tracking`);
      return response.data;
    });
  }
}

export default OrderService;
