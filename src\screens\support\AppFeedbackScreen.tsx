import React, { useState } from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  ScrollView,
  Alert,
  Image,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import * as ImagePicker from 'expo-image-picker';
import { useNavigation } from '@react-navigation/native';
import { FeedbackType, FeedbackCategory, AppFeedback } from '../../types/support';

const AppFeedbackScreen: React.FC = () => {
  const navigation = useNavigation();
  
  // State
  const [selectedType, setSelectedType] = useState<FeedbackType | null>(null);
  const [selectedCategory, setSelectedCategory] = useState<FeedbackCategory | null>(null);
  const [title, setTitle] = useState('');
  const [description, setDescription] = useState('');
  const [rating, setRating] = useState(0);
  const [screenshots, setScreenshots] = useState<{ id: string; url: string; description?: string }[]>([]);
  const [loading, setLoading] = useState(false);

  // Feedback types
  const feedbackTypes = [
    {
      type: FeedbackType.FEATURE_REQUEST,
      title: 'Feature Request',
      description: 'Suggest new features or improvements',
      icon: 'bulb-outline',
      color: '#3b82f6',
    },
    {
      type: FeedbackType.BUG_REPORT,
      title: 'Bug Report',
      description: 'Report technical issues or bugs',
      icon: 'bug-outline',
      color: '#ef4444',
    },
    {
      type: FeedbackType.IMPROVEMENT_SUGGESTION,
      title: 'Improvement',
      description: 'Suggest improvements to existing features',
      icon: 'trending-up-outline',
      color: '#10b981',
    },
    {
      type: FeedbackType.GENERAL_FEEDBACK,
      title: 'General Feedback',
      description: 'Share your thoughts about the app',
      icon: 'chatbubble-outline',
      color: '#8b5cf6',
    },
    {
      type: FeedbackType.COMPLAINT,
      title: 'Complaint',
      description: 'Report issues or concerns',
      icon: 'warning-outline',
      color: '#f59e0b',
    },
    {
      type: FeedbackType.COMPLIMENT,
      title: 'Compliment',
      description: 'Share positive feedback',
      icon: 'heart-outline',
      color: '#ec4899',
    },
  ];

  // Categories
  const categories = [
    {
      category: FeedbackCategory.APP_PERFORMANCE,
      title: 'App Performance',
      description: 'Speed, crashes, loading times',
      icon: 'speedometer-outline',
    },
    {
      category: FeedbackCategory.USER_INTERFACE,
      title: 'User Interface',
      description: 'Design, layout, usability',
      icon: 'phone-portrait-outline',
    },
    {
      category: FeedbackCategory.NAVIGATION,
      title: 'Navigation',
      description: 'Maps, GPS, routing',
      icon: 'navigate-outline',
    },
    {
      category: FeedbackCategory.EARNINGS,
      title: 'Earnings',
      description: 'Payment, wallet, earnings tracking',
      icon: 'wallet-outline',
    },
    {
      category: FeedbackCategory.ORDERS,
      title: 'Orders',
      description: 'Order management, delivery flow',
      icon: 'receipt-outline',
    },
    {
      category: FeedbackCategory.SUPPORT,
      title: 'Support',
      description: 'Help center, customer service',
      icon: 'help-circle-outline',
    },
    {
      category: FeedbackCategory.GENERAL,
      title: 'General',
      description: 'Overall app experience',
      icon: 'apps-outline',
    },
  ];

  const addScreenshot = async () => {
    const { status } = await ImagePicker.requestMediaLibraryPermissionsAsync();
    if (status !== 'granted') {
      Alert.alert('Permission Required', 'Please grant camera roll permissions to add screenshots.');
      return;
    }

    Alert.alert(
      'Add Screenshot',
      'Choose an option',
      [
        { text: 'Camera', onPress: () => openCamera() },
        { text: 'Gallery', onPress: () => openGallery() },
        { text: 'Cancel', style: 'cancel' },
      ]
    );
  };

  const openCamera = async () => {
    const result = await ImagePicker.launchCameraAsync({
      mediaTypes: ImagePicker.MediaTypeOptions.Images,
      allowsEditing: true,
      aspect: [9, 16], // Phone screenshot aspect ratio
      quality: 0.8,
    });

    if (!result.canceled && result.assets[0]) {
      const newScreenshot = {
        id: Date.now().toString(),
        url: result.assets[0].uri,
      };
      setScreenshots(prev => [...prev, newScreenshot]);
    }
  };

  const openGallery = async () => {
    const result = await ImagePicker.launchImageLibraryAsync({
      mediaTypes: ImagePicker.MediaTypeOptions.Images,
      allowsEditing: true,
      aspect: [9, 16],
      quality: 0.8,
    });

    if (!result.canceled && result.assets[0]) {
      const newScreenshot = {
        id: Date.now().toString(),
        url: result.assets[0].uri,
      };
      setScreenshots(prev => [...prev, newScreenshot]);
    }
  };

  const removeScreenshot = (screenshotId: string) => {
    setScreenshots(prev => prev.filter(item => item.id !== screenshotId));
  };

  const submitFeedback = async () => {
    if (!selectedType) {
      Alert.alert('Error', 'Please select a feedback type.');
      return;
    }

    if (!selectedCategory) {
      Alert.alert('Error', 'Please select a category.');
      return;
    }

    if (!title.trim()) {
      Alert.alert('Error', 'Please provide a title for your feedback.');
      return;
    }

    if (!description.trim()) {
      Alert.alert('Error', 'Please provide a description.');
      return;
    }

    if (rating === 0) {
      Alert.alert('Error', 'Please provide a rating.');
      return;
    }

    setLoading(true);

    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 2000));

      const feedback: Omit<AppFeedback, 'id' | 'createdAt' | 'updatedAt' | 'deviceInfo'> = {
        riderId: 'rider-1',
        type: selectedType,
        category: selectedCategory,
        title: title.trim(),
        description: description.trim(),
        rating,
        screenshots,
        status: 'submitted',
        priority: 'medium',
        tags: [],
      };

      Alert.alert(
        'Feedback Submitted',
        'Thank you for your feedback! We appreciate your input and will review it carefully.',
        [
          {
            text: 'OK',
            onPress: () => navigation.goBack(),
          },
        ]
      );
    } catch (error) {
      Alert.alert('Error', 'Failed to submit feedback. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const renderStarRating = () => (
    <View style={{
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'center',
      paddingVertical: 20,
      backgroundColor: '#f8fafc',
      borderRadius: 16,
      marginHorizontal: 20,
    }}>
      {[1, 2, 3, 4, 5].map(star => (
        <TouchableOpacity
          key={star}
          onPress={() => setRating(star)}
          style={{
            marginHorizontal: 8,
            padding: 8,
            borderRadius: 20,
            backgroundColor: star <= rating ? '#dc262620' : 'transparent',
          }}
        >
          <Ionicons
            name={star <= rating ? 'star' : 'star-outline'}
            size={36}
            color={star <= rating ? '#dc2626' : '#d1d5db'}
          />
        </TouchableOpacity>
      ))}
    </View>
  );

  return (
    <View style={{ flex: 1, backgroundColor: '#f1f5f9' }}>
      <SafeAreaView style={{ flex: 1 }}>
        {/* Enhanced Header */}
        <View style={{
          backgroundColor: '#dc2626',
          paddingHorizontal: 20,
          paddingTop: 20,
          paddingBottom: 28,
          borderBottomLeftRadius: 28,
          borderBottomRightRadius: 28,
          shadowColor: '#dc2626',
          shadowOffset: { width: 0, height: 12 },
          shadowOpacity: 0.3,
          shadowRadius: 20,
          elevation: 16,
        }}>
          <View style={{
            flexDirection: 'row',
            alignItems: 'center',
            marginBottom: 20,
          }}>
            <TouchableOpacity
              onPress={() => navigation.goBack()}
              style={{
                width: 48,
                height: 48,
                borderRadius: 24,
                backgroundColor: 'rgba(255,255,255,0.2)',
                justifyContent: 'center',
                alignItems: 'center',
                marginRight: 16,
                borderWidth: 2,
                borderColor: 'rgba(255,255,255,0.3)',
              }}
            >
              <Ionicons name="arrow-back" size={24} color="white" />
            </TouchableOpacity>

            <View style={{ flex: 1 }}>
              <Text style={{
                fontSize: 24,
                fontWeight: 'bold',
                color: 'white',
                marginBottom: 4,
              }}>
                💬 App Feedback
              </Text>
              <Text style={{
                fontSize: 14,
                color: 'rgba(255,255,255,0.8)',
                fontWeight: '500',
              }}>
                Help us improve your experience
              </Text>
            </View>
          </View>
        </View>

      <ScrollView style={{ flex: 1 }}>
        {/* Feedback Type Selection */}
        <View style={{
          backgroundColor: 'white',
          marginHorizontal: 20,
          marginTop: -12,
          borderRadius: 20,
          paddingVertical: 20,
          shadowColor: '#dc2626',
          shadowOffset: { width: 0, height: 8 },
          shadowOpacity: 0.1,
          shadowRadius: 16,
          elevation: 8,
          borderWidth: 1,
          borderColor: 'rgba(220, 38, 38, 0.1)',
        }}>
          <Text style={{
            fontSize: 18,
            fontWeight: 'bold',
            color: '#111827',
            paddingHorizontal: 20,
            marginBottom: 16,
          }}>
            🎯 What type of feedback do you have?
          </Text>
          
          <View style={{
            flexDirection: 'row',
            flexWrap: 'wrap',
            paddingHorizontal: 8,
          }}>
            {feedbackTypes.map(feedbackType => (
              <TouchableOpacity
                key={feedbackType.type}
                onPress={() => setSelectedType(feedbackType.type)}
                style={{
                  width: '50%',
                  padding: 8,
                }}
              >
                <View style={{
                  backgroundColor: selectedType === feedbackType.type ? '#f0f9ff' : '#f9fafb',
                  borderWidth: 2,
                  borderColor: selectedType === feedbackType.type ? feedbackType.color : '#e5e7eb',
                  borderRadius: 12,
                  padding: 12,
                  minHeight: 100,
                }}>
                  <View style={{
                    width: 32,
                    height: 32,
                    backgroundColor: selectedType === feedbackType.type ? feedbackType.color : '#f3f4f6',
                    borderRadius: 16,
                    alignItems: 'center',
                    justifyContent: 'center',
                    marginBottom: 8,
                  }}>
                    <Ionicons
                      name={feedbackType.icon as any}
                      size={16}
                      color={selectedType === feedbackType.type ? 'white' : '#6b7280'}
                    />
                  </View>
                  
                  <Text style={{
                    fontSize: 14,
                    fontWeight: '600',
                    color: selectedType === feedbackType.type ? feedbackType.color : '#1f2937',
                    marginBottom: 4,
                  }}>
                    {feedbackType.title}
                  </Text>
                  
                  <Text style={{
                    fontSize: 12,
                    color: '#6b7280',
                    lineHeight: 16,
                  }}>
                    {feedbackType.description}
                  </Text>
                </View>
              </TouchableOpacity>
            ))}
          </View>
        </View>

        {/* Category Selection */}
        <View style={{
          backgroundColor: 'white',
          marginTop: 16,
          paddingVertical: 16,
        }}>
          <Text style={{
            fontSize: 16,
            fontWeight: '600',
            color: '#1f2937',
            paddingHorizontal: 16,
            marginBottom: 12,
          }}>
            Which area of the app does this relate to?
          </Text>
          
          <View style={{ paddingHorizontal: 16 }}>
            {categories.map(category => (
              <TouchableOpacity
                key={category.category}
                onPress={() => setSelectedCategory(category.category)}
                style={{
                  flexDirection: 'row',
                  alignItems: 'center',
                  paddingVertical: 12,
                  paddingHorizontal: 16,
                  marginVertical: 4,
                  backgroundColor: selectedCategory === category.category ? '#f0f9ff' : '#f9fafb',
                  borderWidth: 1,
                  borderColor: selectedCategory === category.category ? '#3b82f6' : '#e5e7eb',
                  borderRadius: 8,
                }}
              >
                <View style={{
                  width: 32,
                  height: 32,
                  backgroundColor: selectedCategory === category.category ? '#3b82f6' : '#f3f4f6',
                  borderRadius: 16,
                  alignItems: 'center',
                  justifyContent: 'center',
                  marginRight: 12,
                }}>
                  <Ionicons
                    name={category.icon as any}
                    size={16}
                    color={selectedCategory === category.category ? 'white' : '#6b7280'}
                  />
                </View>
                
                <View style={{ flex: 1 }}>
                  <Text style={{
                    fontSize: 14,
                    fontWeight: '600',
                    color: selectedCategory === category.category ? '#3b82f6' : '#1f2937',
                  }}>
                    {category.title}
                  </Text>
                  <Text style={{
                    fontSize: 12,
                    color: '#6b7280',
                  }}>
                    {category.description}
                  </Text>
                </View>
                
                {selectedCategory === category.category && (
                  <Ionicons name="checkmark-circle" size={20} color="#3b82f6" />
                )}
              </TouchableOpacity>
            ))}
          </View>
        </View>

        {/* Rating */}
        <View style={{
          backgroundColor: 'white',
          marginHorizontal: 20,
          marginTop: 16,
          borderRadius: 20,
          paddingVertical: 20,
          shadowColor: '#dc2626',
          shadowOffset: { width: 0, height: 8 },
          shadowOpacity: 0.1,
          shadowRadius: 16,
          elevation: 8,
          borderWidth: 1,
          borderColor: 'rgba(220, 38, 38, 0.1)',
        }}>
          <Text style={{
            fontSize: 18,
            fontWeight: 'bold',
            color: '#111827',
            paddingHorizontal: 20,
            marginBottom: 8,
          }}>
            ⭐ How would you rate your overall experience?
          </Text>

          <Text style={{
            fontSize: 14,
            color: '#6b7280',
            paddingHorizontal: 20,
            marginBottom: 16,
            textAlign: 'center',
          }}>
            Tap the stars to rate (1 = Poor, 5 = Excellent)
          </Text>

          {renderStarRating()}

          {rating > 0 && (
            <View style={{
              backgroundColor: '#dc262610',
              marginHorizontal: 20,
              marginTop: 16,
              padding: 12,
              borderRadius: 12,
              alignItems: 'center',
            }}>
              <Text style={{
                fontSize: 16,
                color: '#dc2626',
                textAlign: 'center',
                fontWeight: 'bold',
              }}>
                {rating === 1 ? '😞 Poor' :
                 rating === 2 ? '😐 Fair' :
                 rating === 3 ? '🙂 Good' :
                 rating === 4 ? '😊 Very Good' :
                 '🤩 Excellent'}
              </Text>
            </View>
          )}
        </View>

        {/* Feedback Details */}
        <View style={{
          backgroundColor: 'white',
          marginTop: 16,
          paddingVertical: 16,
        }}>
          <Text style={{
            fontSize: 16,
            fontWeight: '600',
            color: '#1f2937',
            paddingHorizontal: 16,
            marginBottom: 12,
          }}>
            Feedback Details
          </Text>

          <View style={{ paddingHorizontal: 16 }}>
            <Text style={{
              fontSize: 14,
              color: '#374151',
              marginBottom: 8,
            }}>
              Title *
            </Text>
            <TextInput
              value={title}
              onChangeText={setTitle}
              placeholder="Brief summary of your feedback"
              placeholderTextColor="#9ca3af"
              style={{
                backgroundColor: '#f9fafb',
                borderWidth: 1,
                borderColor: '#e5e7eb',
                borderRadius: 8,
                paddingHorizontal: 12,
                paddingVertical: 12,
                fontSize: 16,
                color: '#1f2937',
                marginBottom: 16,
              }}
            />

            <Text style={{
              fontSize: 14,
              color: '#374151',
              marginBottom: 8,
            }}>
              Description *
            </Text>
            <TextInput
              value={description}
              onChangeText={setDescription}
              placeholder="Please provide detailed feedback..."
              placeholderTextColor="#9ca3af"
              multiline
              numberOfLines={4}
              style={{
                backgroundColor: '#f9fafb',
                borderWidth: 1,
                borderColor: '#e5e7eb',
                borderRadius: 8,
                paddingHorizontal: 12,
                paddingVertical: 12,
                fontSize: 16,
                color: '#1f2937',
                textAlignVertical: 'top',
                minHeight: 100,
              }}
            />
          </View>
        </View>

        {/* Screenshots */}
        <View style={{
          backgroundColor: 'white',
          marginTop: 16,
          paddingVertical: 16,
        }}>
          <View style={{
            flexDirection: 'row',
            alignItems: 'center',
            justifyContent: 'space-between',
            paddingHorizontal: 16,
            marginBottom: 12,
          }}>
            <Text style={{
              fontSize: 16,
              fontWeight: '600',
              color: '#1f2937',
            }}>
              Screenshots (Optional)
            </Text>

            <TouchableOpacity
              onPress={addScreenshot}
              style={{
                flexDirection: 'row',
                alignItems: 'center',
                backgroundColor: '#3b82f6',
                paddingHorizontal: 12,
                paddingVertical: 6,
                borderRadius: 6,
              }}
            >
              <Ionicons name="camera" size={16} color="white" />
              <Text style={{
                color: 'white',
                fontSize: 12,
                fontWeight: '600',
                marginLeft: 4,
              }}>
                Add Screenshot
              </Text>
            </TouchableOpacity>
          </View>

          <Text style={{
            fontSize: 12,
            color: '#6b7280',
            paddingHorizontal: 16,
            marginBottom: 16,
          }}>
            Screenshots help us understand your feedback better
          </Text>

          {screenshots.length > 0 ? (
            <ScrollView
              horizontal
              showsHorizontalScrollIndicator={false}
              style={{ paddingLeft: 16 }}
            >
              {screenshots.map(item => (
                <View
                  key={item.id}
                  style={{
                    marginRight: 12,
                    position: 'relative',
                  }}
                >
                  <Image
                    source={{ uri: item.url }}
                    style={{
                      width: 60,
                      height: 120,
                      borderRadius: 8,
                    }}
                    resizeMode="cover"
                  />
                  <TouchableOpacity
                    onPress={() => removeScreenshot(item.id)}
                    style={{
                      position: 'absolute',
                      top: -8,
                      right: -8,
                      width: 24,
                      height: 24,
                      backgroundColor: '#ef4444',
                      borderRadius: 12,
                      alignItems: 'center',
                      justifyContent: 'center',
                    }}
                  >
                    <Ionicons name="close" size={16} color="white" />
                  </TouchableOpacity>
                </View>
              ))}
            </ScrollView>
          ) : (
            <View style={{
              alignItems: 'center',
              paddingVertical: 32,
              paddingHorizontal: 16,
            }}>
              <Ionicons name="image-outline" size={48} color="#d1d5db" />
              <Text style={{
                fontSize: 14,
                color: '#6b7280',
                textAlign: 'center',
                marginTop: 8,
              }}>
                No screenshots added yet
              </Text>
            </View>
          )}
        </View>

        {/* Submit Button */}
        <View style={{
          paddingHorizontal: 16,
          paddingVertical: 24,
        }}>
          <TouchableOpacity
            onPress={submitFeedback}
            disabled={loading || !selectedType || !selectedCategory || !title.trim() || !description.trim() || rating === 0}
            style={{
              backgroundColor: (!selectedType || !selectedCategory || !title.trim() || !description.trim() || rating === 0) ? '#e5e7eb' : '#3b82f6',
              borderRadius: 12,
              paddingVertical: 16,
              alignItems: 'center',
            }}
          >
            <Text style={{
              color: (!selectedType || !selectedCategory || !title.trim() || !description.trim() || rating === 0) ? '#9ca3af' : 'white',
              fontSize: 16,
              fontWeight: '600',
            }}>
              {loading ? 'Submitting...' : 'Submit Feedback'}
            </Text>
          </TouchableOpacity>

          <Text style={{
            fontSize: 12,
            color: '#6b7280',
            textAlign: 'center',
            marginTop: 12,
            lineHeight: 16,
          }}>
            Your feedback helps us improve the FoodWay rider experience. Thank you for taking the time to share your thoughts!
          </Text>
        </View>
      </ScrollView>
      </SafeAreaView>
    </View>
  );
};

export default AppFeedbackScreen;
