import React from 'react';
import { createBottomTabNavigator } from '@react-navigation/bottom-tabs';
import { Ionicons } from '@expo/vector-icons';
import { MainTabParamList } from '../types';
import CustomTabBar from '../components/navigation/CustomTabBar';

// Navigators
import DashboardNavigator from './DashboardNavigator';
import OrderNavigator from './OrderNavigator';
import EarningsNavigator from './EarningsNavigator';
import DeliveryHistoryNavigator from './DeliveryHistoryNavigator';
import ProfileNavigator from './ProfileNavigator';
import SupportNavigator from './SupportNavigator';
import SafetyNavigator from './SafetyNavigator';
import AdvancedToolsNavigator from './AdvancedToolsNavigator';

const Tab = createBottomTabNavigator<MainTabParamList>();

const MainNavigator: React.FC = () => {
  return (
    <Tab.Navigator
      id={"MainTab" as any}
      tabBar={(props) => <CustomTabBar {...props} />}
      screenOptions={{
        headerShown: false,
      }}
    >
      <Tab.Screen name="Dashboard" component={DashboardNavigator} />
      <Tab.Screen name="Orders" component={OrderNavigator} />
      <Tab.Screen name="Earnings" component={EarningsNavigator} />
      <Tab.Screen name="Profile" component={ProfileNavigator} />
      <Tab.Screen name="Safety" component={SafetyNavigator} />

      {/* Hidden tabs - accessible via navigation but not shown in tab bar */}
      <Tab.Screen
        name="History"
        component={DeliveryHistoryNavigator}
        options={{
          tabBarButton: () => null, // Hide from tab bar
        }}
      />
      <Tab.Screen
        name="Support"
        component={SupportNavigator}
        options={{
          tabBarButton: () => null, // Hide from tab bar
        }}
      />
      <Tab.Screen
        name="AdvancedTools"
        component={AdvancedToolsNavigator}
        options={{
          tabBarButton: () => null, // Hide from tab bar
        }}
      />
    </Tab.Navigator>
  );
};

export default MainNavigator;
