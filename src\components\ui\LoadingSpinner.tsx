import React from 'react';
import { View, ActivityIndicator, Text, ViewStyle, Animated } from 'react-native';
import { Ionicons } from '@expo/vector-icons';

interface LoadingSpinnerProps {
  size?: 'small' | 'medium' | 'large';
  color?: string;
  message?: string;
  overlay?: boolean;
  variant?: 'spinner' | 'dots' | 'skeleton';
  style?: ViewStyle;
}

const LoadingSpinner: React.FC<LoadingSpinnerProps> = ({
  size = 'large',
  color = '#dc2626',
  message,
  overlay = false,
  variant = 'spinner',
  style,
}) => {
  const getSizeValue = () => {
    switch (size) {
      case 'small': return 'small' as const;
      case 'medium': return 'large' as const;
      case 'large': return 'large' as const;
      default: return 'large' as const;
    }
  };

  const getContainerStyle = (): ViewStyle => {
    const baseStyle: ViewStyle = {
      justifyContent: 'center',
      alignItems: 'center',
      padding: 24,
    };

    if (overlay) {
      return {
        ...baseStyle,
        position: 'absolute',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        backgroundColor: 'rgba(255, 255, 255, 0.95)',
        zIndex: 1000,
        backdropFilter: 'blur(4px)',
      };
    }

    return baseStyle;
  };

  const renderSpinner = () => {
    switch (variant) {
      case 'spinner':
        return (
          <View style={{
            backgroundColor: 'white',
            borderRadius: 20,
            padding: 20,
            shadowColor: color,
            shadowOffset: { width: 0, height: 8 },
            shadowOpacity: 0.1,
            shadowRadius: 16,
            elevation: 8,
            alignItems: 'center',
          }}>
            <ActivityIndicator size={getSizeValue()} color={color} />
            {message && (
              <Text style={{
                marginTop: 16,
                fontSize: 16,
                color: '#6b7280',
                textAlign: 'center',
                fontWeight: '500',
              }}>
                {message}
              </Text>
            )}
          </View>
        );

      case 'dots':
        return (
          <View style={{ flexDirection: 'row', alignItems: 'center' }}>
            {[0, 1, 2].map((index) => (
              <View
                key={index}
                style={{
                  width: 12,
                  height: 12,
                  borderRadius: 6,
                  backgroundColor: color,
                  marginHorizontal: 4,
                  opacity: 0.7,
                }}
              />
            ))}
            {message && (
              <Text style={{
                marginLeft: 16,
                fontSize: 16,
                color: '#6b7280',
                fontWeight: '500',
              }}>
                {message}
              </Text>
            )}
          </View>
        );

      case 'skeleton':
        return (
          <View style={{ width: '100%' }}>
            {[1, 2, 3].map((index) => (
              <View
                key={index}
                style={{
                  height: 20,
                  backgroundColor: '#f1f5f9',
                  borderRadius: 10,
                  marginBottom: 12,
                  width: `${100 - index * 10}%`,
                }}
              />
            ))}
          </View>
        );

      default:
        return <ActivityIndicator size={getSizeValue()} color={color} />;
    }
  };

  return (
    <View style={[getContainerStyle(), style]}>
      {renderSpinner()}
    </View>
  );
};

export default LoadingSpinner;
