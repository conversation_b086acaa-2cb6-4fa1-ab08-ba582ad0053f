#!/usr/bin/env node

/**
 * Image optimization script for React Native Food Delivery Rider App
 * This script optimizes images for better performance and smaller bundle size
 */

const fs = require('fs');
const path = require('path');

// Supported image formats
const SUPPORTED_FORMATS = ['.png', '.jpg', '.jpeg', '.gif', '.bmp'];

// Optimization recommendations
const OPTIMIZATION_RULES = {
  // App icons
  'icon.png': { maxWidth: 1024, maxHeight: 1024, quality: 90 },
  'adaptive-icon.png': { maxWidth: 1024, maxHeight: 1024, quality: 90 },
  'splash-icon.png': { maxWidth: 1024, maxHeight: 1024, quality: 85 },
  'favicon.png': { maxWidth: 256, maxHeight: 256, quality: 85 },
  
  // General rules by directory
  'assets': { maxWidth: 1024, maxHeight: 1024, quality: 80 },
  'images': { maxWidth: 800, maxHeight: 600, quality: 75 },
  'icons': { maxWidth: 256, maxHeight: 256, quality: 85 },
  'backgrounds': { maxWidth: 1200, maxHeight: 800, quality: 70 },
};

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m',
};

function colorize(text, color) {
  return `${colors[color]}${text}${colors.reset}`;
}

function logHeader(text) {
  console.log('\n' + colorize('='.repeat(60), 'cyan'));
  console.log(colorize(`  ${text}`, 'bright'));
  console.log(colorize('='.repeat(60), 'cyan') + '\n');
}

function logSuccess(text) {
  console.log(colorize(`✓ ${text}`, 'green'));
}

function logWarning(text) {
  console.log(colorize(`⚠ ${text}`, 'yellow'));
}

function logError(text) {
  console.log(colorize(`✗ ${text}`, 'red'));
}

function logInfo(text) {
  console.log(colorize(`ℹ ${text}`, 'blue'));
}

// Get file size in KB
function getFileSizeKB(filePath) {
  const stats = fs.statSync(filePath);
  return (stats.size / 1024).toFixed(2);
}

// Check if file is an image
function isImageFile(filePath) {
  const ext = path.extname(filePath).toLowerCase();
  return SUPPORTED_FORMATS.includes(ext);
}

// Get optimization rules for a file
function getOptimizationRules(filePath) {
  const fileName = path.basename(filePath);
  const dirName = path.basename(path.dirname(filePath));
  
  // Check specific file rules first
  if (OPTIMIZATION_RULES[fileName]) {
    return OPTIMIZATION_RULES[fileName];
  }
  
  // Check directory rules
  if (OPTIMIZATION_RULES[dirName]) {
    return OPTIMIZATION_RULES[dirName];
  }
  
  // Default rules
  return { maxWidth: 800, maxHeight: 600, quality: 75 };
}

// Scan directory for images
function scanForImages(dirPath, images = []) {
  if (!fs.existsSync(dirPath)) {
    return images;
  }

  const items = fs.readdirSync(dirPath);
  
  items.forEach(item => {
    const itemPath = path.join(dirPath, item);
    const stat = fs.statSync(itemPath);
    
    if (stat.isDirectory()) {
      // Skip node_modules and other build directories
      if (!['node_modules', '.git', 'build', 'dist', '.expo'].includes(item)) {
        scanForImages(itemPath, images);
      }
    } else if (isImageFile(itemPath)) {
      images.push(itemPath);
    }
  });
  
  return images;
}

// Analyze image
function analyzeImage(imagePath) {
  const fileName = path.basename(imagePath);
  const fileSize = getFileSizeKB(imagePath);
  const rules = getOptimizationRules(imagePath);
  
  const analysis = {
    path: imagePath,
    fileName,
    fileSize: parseFloat(fileSize),
    rules,
    issues: [],
    recommendations: [],
  };

  // Check file size
  if (analysis.fileSize > 500) {
    analysis.issues.push('Large file size');
    analysis.recommendations.push(`Reduce file size (current: ${fileSize}KB)`);
  }

  // Check if WebP conversion would be beneficial
  if (path.extname(imagePath).toLowerCase() !== '.webp' && analysis.fileSize > 50) {
    analysis.recommendations.push('Consider converting to WebP format');
  }

  // Check if image might need resizing
  analysis.recommendations.push(`Ensure dimensions don't exceed ${rules.maxWidth}x${rules.maxHeight}`);
  analysis.recommendations.push(`Optimize with quality ${rules.quality}%`);

  return analysis;
}

// Generate optimization report
function generateOptimizationReport() {
  logHeader('IMAGE OPTIMIZATION ANALYSIS');

  const projectRoot = process.cwd();
  const imagePaths = scanForImages(projectRoot);
  
  logInfo(`Found ${imagePaths.length} images to analyze`);
  
  const report = {
    timestamp: new Date().toISOString(),
    totalImages: imagePaths.length,
    totalSize: 0,
    issues: [],
    recommendations: [],
    images: [],
  };

  imagePaths.forEach(imagePath => {
    const analysis = analyzeImage(imagePath);
    report.images.push(analysis);
    report.totalSize += analysis.fileSize;
    
    if (analysis.issues.length > 0) {
      report.issues.push({
        file: analysis.fileName,
        issues: analysis.issues,
      });
    }
    
    report.recommendations.push(...analysis.recommendations);
  });

  // Remove duplicate recommendations
  report.recommendations = [...new Set(report.recommendations)];

  return report;
}

// Display optimization suggestions
function displayOptimizationSuggestions() {
  logHeader('IMAGE OPTIMIZATION SUGGESTIONS');

  const suggestions = [
    {
      title: 'Use WebP Format',
      description: 'Convert PNG/JPEG images to WebP for 25-35% smaller file sizes',
      implementation: 'Use OptimizedImage component with format="webp"',
    },
    {
      title: 'Implement Lazy Loading',
      description: 'Load images only when they become visible',
      implementation: 'Use OptimizedImage component with lazy={true}',
    },
    {
      title: 'Optimize Image Dimensions',
      description: 'Resize images to match their display size',
      implementation: 'Provide width and height props to OptimizedImage',
    },
    {
      title: 'Use Progressive JPEG',
      description: 'For large photos, use progressive JPEG for better perceived performance',
      implementation: 'Save images with progressive encoding',
    },
    {
      title: 'Implement Image Caching',
      description: 'Cache frequently used images in memory',
      implementation: 'Use OptimizedImage component with cachePolicy="memory-disk"',
    },
    {
      title: 'Compress App Icons',
      description: 'Optimize app icons and splash screens',
      implementation: 'Use tools like ImageOptim or TinyPNG',
    },
  ];

  suggestions.forEach((suggestion, index) => {
    console.log(colorize(`${index + 1}. ${suggestion.title}`, 'bright'));
    console.log(`   ${suggestion.description}`);
    console.log(colorize(`   Implementation: ${suggestion.implementation}`, 'blue'));
    console.log('');
  });
}

// Display optimization commands
function displayOptimizationCommands() {
  logHeader('OPTIMIZATION COMMANDS');

  const commands = [
    {
      tool: 'ImageOptim (macOS)',
      command: 'imageoptim assets/',
      description: 'Lossless compression for all images',
    },
    {
      tool: 'TinyPNG CLI',
      command: 'tinypng assets/*.png',
      description: 'Online compression service',
    },
    {
      tool: 'Sharp (Node.js)',
      command: 'npm install sharp && node optimize-with-sharp.js',
      description: 'Programmatic image optimization',
    },
    {
      tool: 'WebP Conversion',
      command: 'cwebp -q 80 input.png -o output.webp',
      description: 'Convert images to WebP format',
    },
  ];

  commands.forEach(cmd => {
    console.log(colorize(cmd.tool, 'bright'));
    console.log(`   Command: ${colorize(cmd.command, 'green')}`);
    console.log(`   Description: ${cmd.description}`);
    console.log('');
  });
}

// Check for optimization tools
function checkOptimizationTools() {
  logHeader('OPTIMIZATION TOOLS CHECK');

  const tools = [
    {
      name: 'OptimizedImage Component',
      path: 'src/components/ui/OptimizedImage.tsx',
      required: true,
    },
    {
      name: 'Performance Config',
      path: 'src/config/performanceConfig.ts',
      required: true,
    },
    {
      name: 'Image Optimization Utils',
      path: 'src/utils/performanceUtils.ts',
      required: true,
    },
  ];

  let allToolsAvailable = true;

  tools.forEach(tool => {
    const toolPath = path.join(process.cwd(), tool.path);
    if (fs.existsSync(toolPath)) {
      logSuccess(`${tool.name} available`);
    } else {
      if (tool.required) {
        logError(`${tool.name} missing (required)`);
        allToolsAvailable = false;
      } else {
        logWarning(`${tool.name} missing (optional)`);
      }
    }
  });

  return allToolsAvailable;
}

// Main function
function main() {
  const args = process.argv.slice(2);
  const command = args[0] || 'analyze';

  switch (command) {
    case 'analyze':
      const report = generateOptimizationReport();
      
      console.log(`\nTotal images: ${report.totalImages}`);
      console.log(`Total size: ${report.totalSize.toFixed(2)}KB`);
      console.log(`Issues found: ${report.issues.length}`);
      
      if (report.issues.length > 0) {
        logHeader('ISSUES FOUND');
        report.issues.forEach(issue => {
          logWarning(`${issue.file}: ${issue.issues.join(', ')}`);
        });
      }

      // Save detailed report
      const reportPath = path.join(process.cwd(), 'image-optimization-report.json');
      fs.writeFileSync(reportPath, JSON.stringify(report, null, 2));
      logInfo(`Detailed report saved to: image-optimization-report.json`);
      break;

    case 'suggestions':
      displayOptimizationSuggestions();
      break;

    case 'commands':
      displayOptimizationCommands();
      break;

    case 'check-tools':
      checkOptimizationTools();
      break;

    case 'help':
    default:
      logHeader('IMAGE OPTIMIZATION HELP');
      console.log('Usage: node scripts/optimize-images.js [command]');
      console.log('\nCommands:');
      console.log('  analyze        Analyze images and generate report (default)');
      console.log('  suggestions    Show optimization suggestions');
      console.log('  commands       Show optimization commands');
      console.log('  check-tools    Check if optimization tools are available');
      console.log('  help           Show this help message');
      break;
  }
}

// Run the script
if (require.main === module) {
  main();
}

module.exports = {
  scanForImages,
  analyzeImage,
  generateOptimizationReport,
  OPTIMIZATION_RULES,
};
