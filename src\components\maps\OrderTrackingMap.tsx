import React, { useEffect, useRef, useState } from 'react';
import { View, Text, TouchableOpacity, Alert } from 'react-native';
// Temporarily disabled due to React Native 0.79.x compatibility issues
// import MapView, { Marker, Polyline, Region, PROVIDER_GOOGLE } from 'react-native-maps';
import { Ionicons } from '@expo/vector-icons';
import { Order } from '../../types/orders';
import { LocationData } from '../../types/location';
import { useLocation } from '../../context/LocationContext';
import { Button } from '../ui';
import { MAP_CONFIG } from '../../utils/constants';

interface OrderTrackingMapProps {
  order: Order;
  onNavigateToPickup?: () => void;
  onNavigateToDelivery?: () => void;
  showNavigationButtons?: boolean;
  onClose?: () => void;
}

const OrderTrackingMap: React.FC<OrderTrackingMapProps> = ({
  order,
  onNavigateToPickup,
  onNavigateToDelivery,
  showNavigationButtons = true,
  onClose,
}) => {
  // const mapRef = useRef<MapView>(null);
  const { state: locationState, getCurrentLocation, startLocationTracking, stopLocationTracking } = useLocation();
  const [region, setRegion] = useState<any>(null);
  const [routeCoordinates, setRouteCoordinates] = useState<{ latitude: number; longitude: number }[]>([]);

  useEffect(() => {
    initializeMap();
    startTracking();

    return () => {
      stopLocationTracking();
    };
  }, []);

  useEffect(() => {
    if (locationState.currentLocation) {
      updateRoute();
    }
  }, [locationState.currentLocation]);

  const initializeMap = async () => {
    try {
      // Get current location or use order pickup location as fallback
      let currentLocation: LocationData;
      
      try {
        currentLocation = await getCurrentLocation();
      } catch (error) {
        // Fallback to pickup location if current location fails
        currentLocation = {
          coords: {
            latitude: order.pickupAddress.latitude,
            longitude: order.pickupAddress.longitude,
          },
          accuracy: 0,
          altitude: 0,
          heading: 0,
          speed: 0,
          timestamp: Date.now(),
        };
      }

      // Calculate region to show all relevant points
      const coordinates = [
        { latitude: currentLocation.coords.latitude, longitude: currentLocation.coords.longitude },
        { latitude: order.pickupAddress.latitude, longitude: order.pickupAddress.longitude },
        { latitude: order.deliveryAddress.latitude, longitude: order.deliveryAddress.longitude },
      ];

      const { minLat, maxLat, minLng, maxLng } = coordinates.reduce(
        (bounds, coord) => ({
          minLat: Math.min(bounds.minLat, coord.latitude),
          maxLat: Math.max(bounds.maxLat, coord.latitude),
          minLng: Math.min(bounds.minLng, coord.longitude),
          maxLng: Math.max(bounds.maxLng, coord.longitude),
        }),
        {
          minLat: coordinates[0].latitude,
          maxLat: coordinates[0].latitude,
          minLng: coordinates[0].longitude,
          maxLng: coordinates[0].longitude,
        }
      );

      const latDelta = (maxLat - minLat) * 1.2; // Add 20% padding
      const lngDelta = (maxLng - minLng) * 1.2;

      setRegion({
        latitude: (minLat + maxLat) / 2,
        longitude: (minLng + maxLng) / 2,
        latitudeDelta: Math.max(latDelta, 0.01), // Minimum zoom level
        longitudeDelta: Math.max(lngDelta, 0.01),
      });
    } catch (error) {
      console.error('Error initializing map:', error);
    }
  };

  const startTracking = async () => {
    try {
      await startLocationTracking();
    } catch (error) {
      console.error('Error starting location tracking:', error);
      Alert.alert('Location Error', 'Unable to start location tracking. Please check your location permissions.');
    }
  };

  const updateRoute = () => {
    if (!locationState.currentLocation) return;

    // Simple route - in a real app, you'd use a routing service
    const newRoute = [
      {
        latitude: locationState.currentLocation.coords.latitude,
        longitude: locationState.currentLocation.coords.longitude,
      },
      {
        latitude: order.pickupAddress.latitude,
        longitude: order.pickupAddress.longitude,
      },
      {
        latitude: order.deliveryAddress.latitude,
        longitude: order.deliveryAddress.longitude,
      },
    ];

    setRouteCoordinates(newRoute);
  };

  const centerOnCurrentLocation = () => {
    // Temporarily disabled due to React Native Maps compatibility
    // if (locationState.currentLocation && mapRef.current) {
    //   mapRef.current.animateToRegion({
    //     latitude: locationState.currentLocation.coords.latitude,
    //     longitude: locationState.currentLocation.coords.longitude,
    //     latitudeDelta: 0.01,
    //     longitudeDelta: 0.01,
    //   });
    // }
  };

  const fitToMarkers = () => {
    // Temporarily disabled due to React Native Maps compatibility
    if (false) { // mapRef.current
      const coordinates = [
        { latitude: order.pickupAddress.latitude, longitude: order.pickupAddress.longitude },
        { latitude: order.deliveryAddress.latitude, longitude: order.deliveryAddress.longitude },
      ];

      if (locationState.currentLocation) {
        coordinates.unshift({
          latitude: locationState.currentLocation.coords.latitude,
          longitude: locationState.currentLocation.coords.longitude,
        });
      }

      // mapRef.current.fitToCoordinates(coordinates, {
        // edgePadding: { top: 50, right: 50, bottom: 50, left: 50 },
        // animated: true,
      // });
    }
  };

  if (!region) {
    return (
      <View style={{
        flex: 1,
        justifyContent: 'center',
        alignItems: 'center',
        backgroundColor: '#f3f4f6',
      }}>
        <Text style={{ color: '#6b7280' }}>Loading map...</Text>
      </View>
    );
  }

  return (
    <View style={{ flex: 1 }}>
      {/* Close button for modal */}
      {onClose && (
        <View style={{
          position: 'absolute',
          top: 50,
          left: 16,
          zIndex: 1000,
        }}>
          <TouchableOpacity
            onPress={onClose}
            style={{
              width: 44,
              height: 44,
              backgroundColor: 'white',
              borderRadius: 22,
              justifyContent: 'center',
              alignItems: 'center',
              shadowColor: '#000',
              shadowOffset: { width: 0, height: 2 },
              shadowOpacity: 0.25,
              shadowRadius: 4,
              elevation: 5,
            }}
          >
            <Ionicons name="close" size={24} color="#374151" />
          </TouchableOpacity>
        </View>
      )}

      {/* Temporarily disabled due to React Native Maps compatibility issues */}
      <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center', backgroundColor: '#f0f0f0' }}>
        <Text style={{ fontSize: 16, color: '#666' }}>Map temporarily disabled</Text>
        <Text style={{ fontSize: 14, color: '#999', marginTop: 8 }}>
          React Native Maps compatibility with RN 0.79.x
        </Text>
      </View>
      {/* MapView temporarily disabled due to React Native Maps compatibility issues with RN 0.79.x */}

      {/* Navigation buttons */}
      <View style={{
        position: 'absolute',
        top: 16,
        right: 16,
        flexDirection: 'column',
        gap: 8,
      }}>
        <TouchableOpacity
          onPress={centerOnCurrentLocation}
          style={{
            width: 44,
            height: 44,
            backgroundColor: 'white',
            borderRadius: 22,
            justifyContent: 'center',
            alignItems: 'center',
            shadowColor: '#000',
            shadowOffset: { width: 0, height: 2 },
            shadowOpacity: 0.25,
            shadowRadius: 4,
            elevation: 5,
          }}
        >
          <Ionicons name="locate" size={24} color="#374151" />
        </TouchableOpacity>

        <TouchableOpacity
          onPress={fitToMarkers}
          style={{
            width: 44,
            height: 44,
            backgroundColor: 'white',
            borderRadius: 22,
            justifyContent: 'center',
            alignItems: 'center',
            shadowColor: '#000',
            shadowOffset: { width: 0, height: 2 },
            shadowOpacity: 0.25,
            shadowRadius: 4,
            elevation: 5,
          }}
        >
          <Ionicons name="resize" size={24} color="#374151" />
        </TouchableOpacity>
      </View>

      {/* Navigation buttons */}
      {showNavigationButtons && (
        <View style={{
          position: 'absolute',
          bottom: 16,
          left: 16,
          right: 16,
          flexDirection: 'row',
          gap: 12,
        }}>
          <Button
            title="Navigate to Pickup"
            leftIcon="navigate-outline"
            onPress={onNavigateToPickup}
            style={{ flex: 1 }}
            variant="outline"
          />
          <Button
            title="Navigate to Customer"
            leftIcon="navigate-outline"
            onPress={onNavigateToDelivery}
            style={{ flex: 1 }}
          />
        </View>
      )}
    </View>
  );
};

export default OrderTrackingMap;
