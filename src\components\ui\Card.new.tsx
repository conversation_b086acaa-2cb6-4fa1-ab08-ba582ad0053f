import React from 'react';
import { View, ViewStyle } from 'react-native';

interface CardProps {
  children: React.ReactNode;
  variant?: 'default' | 'elevated' | 'outlined';
  padding?: 'none' | 'sm' | 'md' | 'lg' | 'xl';
  margin?: 'none' | 'sm' | 'md' | 'lg' | 'xl';
  style?: ViewStyle;
}

const Card: React.FC<CardProps> = ({
  children,
  variant = 'default',
  padding = 'md',
  margin = 'md',
  style,
}) => {
  const getCardStyles = (): ViewStyle => {
    const baseStyles: ViewStyle = {
      backgroundColor: 'white',
      borderRadius: 12,
    };

    // Variant styles
    const variantStyles: Record<string, ViewStyle> = {
      default: {
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 1 },
        shadowOpacity: 0.05,
        shadowRadius: 2,
        elevation: 1,
      },
      elevated: {
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 4 },
        shadowOpacity: 0.1,
        shadowRadius: 8,
        elevation: 4,
      },
      outlined: {
        borderWidth: 2,
        borderColor: '#d1d5db',
        shadowColor: 'transparent',
        elevation: 0,
      },
    };

    // Padding styles
    const paddingStyles: Record<string, ViewStyle> = {
      none: { padding: 0 },
      sm: { padding: 12 },
      md: { padding: 16 },
      lg: { padding: 20 },
      xl: { padding: 24 },
    };

    // Margin styles
    const marginStyles: Record<string, ViewStyle> = {
      none: { margin: 0 },
      sm: { margin: 8 },
      md: { margin: 16 },
      lg: { margin: 20 },
      xl: { margin: 24 },
    };

    return {
      ...baseStyles,
      ...variantStyles[variant],
      ...paddingStyles[padding],
      ...marginStyles[margin],
    };
  };

  return (
    <View style={[getCardStyles(), style]}>
      {children}
    </View>
  );
};

export default Card;
