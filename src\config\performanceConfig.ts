import { Platform } from 'react-native';

/**
 * Performance optimization configuration for React Native Food Delivery Rider App
 */

export const PERFORMANCE_CONFIG = {
  // Memory Management
  MEMORY: {
    MAX_IMAGE_CACHE_SIZE: 50 * 1024 * 1024, // 50MB
    MAX_LOCATION_HISTORY: 100,
    MAX_ORDER_CACHE: 50,
    CLEANUP_INTERVAL: 5 * 60 * 1000, // 5 minutes
    LOW_MEMORY_THRESHOLD: 0.8, // 80% memory usage
  },

  // Network Optimization
  NETWORK: {
    REQUEST_TIMEOUT: 10000, // 10 seconds
    RETRY_ATTEMPTS: 3,
    RETRY_DELAY: 1000, // 1 second
    BATCH_SIZE: 5,
    CACHE_TTL: {
      USER_PROFILE: 30 * 60 * 1000, // 30 minutes
      RESTAURANT_DATA: 15 * 60 * 1000, // 15 minutes
      ORDER_HISTORY: 5 * 60 * 1000, // 5 minutes
      EARNINGS_DATA: 10 * 60 * 1000, // 10 minutes
    },
    COMPRESSION: true,
    DEDUPLICATION: true,
  },

  // Location Services
  LOCATION: {
    UPDATE_INTERVALS: {
      ACTIVE: 5000, // 5 seconds when app is active
      BACKGROUND: 30000, // 30 seconds in background
      INACTIVE: 60000, // 1 minute when inactive
    },
    DISTANCE_FILTERS: {
      ACTIVE: 10, // 10 meters
      BACKGROUND: 50, // 50 meters
      INACTIVE: 100, // 100 meters
    },
    ACCURACY: {
      ACTIVE: 'high',
      BACKGROUND: 'balanced',
      INACTIVE: 'low',
    },
    MAX_CACHE_AGE: 60000, // 1 minute
    GEOFENCE_RADIUS: 100, // 100 meters
  },

  // UI Performance
  UI: {
    LIST_OPTIMIZATION: {
      WINDOW_SIZE: 10,
      MAX_TO_RENDER_PER_BATCH: 10,
      UPDATE_CELLS_BATCHING_PERIOD: 50,
      INITIAL_NUM_TO_RENDER: 10,
      REMOVE_CLIPPED_SUBVIEWS: true,
    },
    IMAGE_OPTIMIZATION: {
      LAZY_LOADING_THRESHOLD: 10, // Load images after 10th item
      QUALITY: 80,
      FORMAT: 'webp',
      PLACEHOLDER_COLOR: '#f0f0f0',
    },
    ANIMATION: {
      USE_NATIVE_DRIVER: true,
      DURATION: 250,
      EASING: 'ease-out',
      REDUCE_MOTION: false,
    },
    DEBOUNCE_DELAYS: {
      SEARCH: 300,
      SCROLL: 100,
      RESIZE: 250,
      INPUT: 500,
    },
  },

  // Battery Optimization
  BATTERY: {
    BACKGROUND_TASK_INTERVAL: 15 * 60 * 1000, // 15 minutes
    WAKE_LOCK_TIMEOUT: 30000, // 30 seconds
    GPS_OPTIMIZATION: {
      POWER_SAVE_MODE: true,
      ADAPTIVE_ACCURACY: true,
      STOP_ON_STATIONARY: true,
    },
    NETWORK_BATCHING: true,
    REDUCE_ANIMATIONS: false,
  },

  // Storage Optimization
  STORAGE: {
    MAX_CACHE_SIZE: 100 * 1024 * 1024, // 100MB
    CLEANUP_THRESHOLD: 0.9, // 90% of max size
    COMPRESSION: true,
    ENCRYPTION: true,
    AUTO_CLEANUP_INTERVAL: 24 * 60 * 60 * 1000, // 24 hours
  },

  // Performance Monitoring
  MONITORING: {
    ENABLED: __DEV__,
    SAMPLE_RATE: 0.1, // 10% sampling in production
    MAX_METRICS: 1000,
    CRITICAL_THRESHOLDS: {
      JS_THREAD_DELAY: 100, // 100ms
      COMPONENT_RENDER: 100, // 100ms
      NETWORK_REQUEST: 5000, // 5 seconds
      MEMORY_USAGE: 100, // 100MB
      BATTERY_DRAIN: 5, // 5% per hour
    },
    EXPORT_INTERVAL: 60 * 60 * 1000, // 1 hour
  },

  // Platform-specific optimizations
  PLATFORM: Platform.select({
    ios: {
      MEMORY_WARNING_THRESHOLD: 0.85,
      BACKGROUND_PROCESSING: true,
      HARDWARE_ACCELERATION: true,
    },
    android: {
      MEMORY_WARNING_THRESHOLD: 0.8,
      BACKGROUND_PROCESSING: true,
      HARDWARE_ACCELERATION: true,
      PROGUARD_ENABLED: true,
      HERMES_ENABLED: true,
    },
  }),

  // Development vs Production
  ENVIRONMENT: __DEV__ ? {
    LOGGING: true,
    PERFORMANCE_OVERLAY: true,
    MEMORY_MONITORING: true,
    NETWORK_LOGGING: true,
    CRASH_REPORTING: false,
  } : {
    LOGGING: false,
    PERFORMANCE_OVERLAY: false,
    MEMORY_MONITORING: false,
    NETWORK_LOGGING: false,
    CRASH_REPORTING: true,
  },
};

// Feature flags for gradual rollout
export const FEATURE_FLAGS = {
  OPTIMIZED_IMAGES: true,
  NETWORK_CACHING: true,
  LOCATION_OPTIMIZATION: true,
  MEMORY_MANAGEMENT: true,
  PERFORMANCE_MONITORING: __DEV__,
  BATTERY_OPTIMIZATION: true,
  LIST_VIRTUALIZATION: true,
  LAZY_LOADING: true,
  BACKGROUND_SYNC: true,
  OFFLINE_SUPPORT: true,
};

// Performance budgets
export const PERFORMANCE_BUDGETS = {
  APP_STARTUP_TIME: 3000, // 3 seconds
  SCREEN_TRANSITION_TIME: 300, // 300ms
  LIST_SCROLL_FPS: 60,
  MEMORY_USAGE_LIMIT: 150 * 1024 * 1024, // 150MB
  NETWORK_REQUEST_TIME: 2000, // 2 seconds
  IMAGE_LOAD_TIME: 1000, // 1 second
  BATTERY_DRAIN_RATE: 3, // 3% per hour
};

// Optimization strategies based on device capabilities
export const getOptimizationStrategy = (deviceInfo: {
  totalMemory?: number;
  cpuCount?: number;
  isLowEndDevice?: boolean;
}) => {
  const { totalMemory = 2048, cpuCount = 4, isLowEndDevice = false } = deviceInfo;

  if (isLowEndDevice || totalMemory < 2048) {
    return {
      ...PERFORMANCE_CONFIG,
      MEMORY: {
        ...PERFORMANCE_CONFIG.MEMORY,
        MAX_IMAGE_CACHE_SIZE: 25 * 1024 * 1024, // 25MB
        MAX_LOCATION_HISTORY: 50,
        MAX_ORDER_CACHE: 25,
      },
      UI: {
        ...PERFORMANCE_CONFIG.UI,
        LIST_OPTIMIZATION: {
          ...PERFORMANCE_CONFIG.UI.LIST_OPTIMIZATION,
          WINDOW_SIZE: 5,
          MAX_TO_RENDER_PER_BATCH: 5,
          INITIAL_NUM_TO_RENDER: 5,
        },
        IMAGE_OPTIMIZATION: {
          ...PERFORMANCE_CONFIG.UI.IMAGE_OPTIMIZATION,
          QUALITY: 60,
          LAZY_LOADING_THRESHOLD: 5,
        },
      },
      LOCATION: {
        ...PERFORMANCE_CONFIG.LOCATION,
        UPDATE_INTERVALS: {
          ACTIVE: 10000, // 10 seconds
          BACKGROUND: 60000, // 1 minute
          INACTIVE: 120000, // 2 minutes
        },
      },
    };
  }

  return PERFORMANCE_CONFIG;
};

export default PERFORMANCE_CONFIG;
