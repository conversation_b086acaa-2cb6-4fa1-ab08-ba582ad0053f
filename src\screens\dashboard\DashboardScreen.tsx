import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  Alert,
  Modal,
  Animated,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { Card, Badge, Button, Screen } from '../../components/ui';
import { useAuth } from '../../context/AuthContext';
import { useOrders } from '../../context/OrderContext';
import { useEarnings } from '../../context/EarningsContext';
import { useNavigation } from '@react-navigation/native';
import { formatCurrency } from '../../utils/helpers';
import OrderRequestModal from '../../components/orders/OrderRequestModal';



interface DashboardStats {
  todayEarnings: number;
  todayOrders: number;
  todayDistance: number;
  weeklyEarnings: number;
  rating: number;
  completionRate: number;
  activeOrders: number;
  upcomingBonuses: {
    title: string;
    amount: number;
    progress: number;
    target: number;
  }[];
}

// Helper function to get initials
const getInitials = (firstName?: string, lastName?: string): string => {
  const first = firstName?.charAt(0)?.toUpperCase() || '';
  const last = lastName?.charAt(0)?.toUpperCase() || '';
  return first + last || 'DR';
};

const DashboardScreen: React.FC = () => {
  const navigation = useNavigation();
  const { state: authState } = useAuth();
  const { state: orderState, fetchAvailableOrders } = useOrders();
  const { state: earningsState, fetchEarningsSummary } = useEarnings();
  const [isOnline, setIsOnline] = useState(false);
  const [refreshing, setRefreshing] = useState(false);
  const [showOrderRequest, setShowOrderRequest] = useState(false);

  // Animation refs
  const toggleAnimation = useRef(new Animated.Value(0)).current;
  const [stats, setStats] = useState<DashboardStats>({
    todayEarnings: 3825,
    todayOrders: 8,
    todayDistance: 45.2,
    weeklyEarnings: 26770,
    rating: 4.8,
    completionRate: 96,
    activeOrders: 2,
    upcomingBonuses: [
      {
        title: 'Complete 10 orders',
        amount: 750,
        progress: 8,
        target: 10,
      },
      {
        title: 'Weekend Warrior',
        amount: 1500,
        progress: 3,
        target: 15,
      },
    ],
  });

  useEffect(() => {
    // Fetch initial data
    fetchAvailableOrders();
    fetchEarningsSummary();



    // Simulate real-time order requests when online
    let orderInterval: any;
    if (isOnline) {
      orderInterval = setInterval(() => {
        // Simulate incoming order request (30% chance every 10 seconds)
        if (Math.random() < 0.3 && orderState.availableOrders.length > 0) {
          setShowOrderRequest(true);
        }
      }, 10000);
    }

    return () => {
      if (orderInterval) {
        clearInterval(orderInterval);
      }
    };
  }, [isOnline]);

  const onRefresh = async () => {
    setRefreshing(true);
    try {
      await Promise.all([
        fetchAvailableOrders(),
        fetchEarningsSummary(),
      ]);

      // Update stats with real data
      setStats(prev => ({
        ...prev,
        activeOrders: orderState.acceptedOrders.length,
        todayEarnings: earningsState.summary?.today?.total || prev.todayEarnings,
      }));
    } catch (error) {
      console.error('Error refreshing dashboard:', error);
    } finally {
      setRefreshing(false);
    }
  };

  const toggleOnlineStatus = async () => {
    const { state, canGoOnline, checkVerificationStatus } = useAuth();

    // If trying to go online, check verification status first
    if (!isOnline) {
      // Check if user can go online
      if (!canGoOnline()) {
        // Refresh verification status
        await checkVerificationStatus();

        // Check again after refresh
        if (!canGoOnline()) {
          Alert.alert(
            '⚠️ Verification Required',
            'You need to complete document verification before going online. Please upload and verify all required documents.',
            [
              { text: 'Cancel', style: 'cancel' },
              {
                text: 'View Documents',
                onPress: () => (navigation as any).navigate('DocumentStatus')
              }
            ]
          );
          return;
        }
      }
    }

    const newStatus = !isOnline;
    setIsOnline(newStatus);

    // Animate toggle
    Animated.timing(toggleAnimation, {
      toValue: newStatus ? 1 : 0,
      duration: 300,
      useNativeDriver: false,
    }).start();

    if (newStatus) {
      Alert.alert(
        '🟢 Going Online',
        'You are now available to receive order requests and start earning! 💰',
        [{ text: 'Let\'s Go! 🚀' }]
      );
      // Start fetching available orders
      fetchAvailableOrders();
    } else {
      Alert.alert(
        '🔴 Going Offline',
        'You will no longer receive new order requests. Take a well-deserved break! 😊',
        [{ text: 'OK' }]
      );
    }
  };



  const renderOnlineToggle = () => (
    <View style={{
      backgroundColor: 'white',
      borderRadius: 20,
      padding: 24,
      marginBottom: 24,
      shadowColor: isOnline ? '#10b981' : '#dc2626',
      shadowOffset: { width: 0, height: 8 },
      shadowOpacity: 0.15,
      shadowRadius: 20,
      elevation: 12,
      borderWidth: 2,
      borderColor: isOnline ? 'rgba(16, 185, 129, 0.1)' : 'rgba(220, 38, 38, 0.1)',
    }}>
      <View style={{
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between',
      }}>
        <View style={{ flexDirection: 'row', alignItems: 'center', flex: 1 }}>
          {/* Animated Status Indicator */}
          <View style={{
            width: 16,
            height: 16,
            borderRadius: 8,
            backgroundColor: isOnline ? '#10b981' : '#dc2626',
            marginRight: 16,
            shadowColor: isOnline ? '#10b981' : '#dc2626',
            shadowOffset: { width: 0, height: 0 },
            shadowOpacity: 0.6,
            shadowRadius: 8,
            elevation: 4,
          }} />
          <View style={{ flex: 1 }}>
            <Text style={{
              fontSize: 20,
              fontWeight: 'bold',
              color: '#111827',
              marginBottom: 4,
            }}>
              {isOnline ? 'Online' : 'Offline'}
            </Text>
            <Text style={{
              fontSize: 15,
              color: '#6b7280',
              lineHeight: 20,
            }}>
              {isOnline ? 'Receiving orders' : 'Turn on to start receiving orders and earning 🚀'}
            </Text>
          </View>
        </View>

        {/* Enhanced Toggle Switch */}
        <TouchableOpacity
          onPress={toggleOnlineStatus}
          style={{
            width: 60,
            height: 32,
            borderRadius: 16,
            backgroundColor: isOnline ? '#10b981' : '#e5e7eb',
            justifyContent: 'center',
            paddingHorizontal: 2,
            shadowColor: '#000',
            shadowOffset: { width: 0, height: 2 },
            shadowOpacity: 0.1,
            shadowRadius: 4,
            elevation: 3,
          }}
        >
          <View style={{
            width: 28,
            height: 28,
            borderRadius: 14,
            backgroundColor: 'white',
            alignSelf: isOnline ? 'flex-end' : 'flex-start',
            shadowColor: '#000',
            shadowOffset: { width: 0, height: 2 },
            shadowOpacity: 0.2,
            shadowRadius: 4,
            elevation: 4,
            justifyContent: 'center',
            alignItems: 'center',
          }}>
            <Ionicons
              name={isOnline ? 'checkmark' : 'close'}
              size={16}
              color={isOnline ? '#10b981' : '#9ca3af'}
            />
          </View>
        </TouchableOpacity>
      </View>
    </View>
  );

  const renderStatsGrid = () => (
    <View style={{ marginBottom: 24 }}>
      <View style={{
        flexDirection: 'row',
        flexWrap: 'wrap',
        justifyContent: 'space-between',
        gap: 16,
      }}>
        {/* Today's Earnings */}
        <View style={{
          width: '48%',
          backgroundColor: 'white',
          borderRadius: 20,
          padding: 20,
          shadowColor: '#dc2626',
          shadowOffset: { width: 0, height: 6 },
          shadowOpacity: 0.1,
          shadowRadius: 16,
          elevation: 8,
          borderWidth: 1,
          borderColor: 'rgba(220, 38, 38, 0.05)',
        }}>
          <View style={{
            flexDirection: 'row',
            alignItems: 'center',
            marginBottom: 16,
          }}>
            <View style={{
              width: 48,
              height: 48,
              backgroundColor: '#dc2626',
              borderRadius: 24,
              justifyContent: 'center',
              alignItems: 'center',
              marginRight: 12,
              shadowColor: '#dc2626',
              shadowOffset: { width: 0, height: 4 },
              shadowOpacity: 0.3,
              shadowRadius: 8,
              elevation: 6,
            }}>
              <Ionicons name="wallet" size={24} color="white" />
            </View>
            <Text style={{ fontSize: 15, color: '#6b7280', fontWeight: '600' }}>
              Earnings
            </Text>
          </View>
          <Text style={{ fontSize: 28, fontWeight: 'bold', color: '#111827', marginBottom: 4 }}>
            Rs {stats.todayEarnings.toLocaleString()}
          </Text>
          <Text style={{ fontSize: 13, color: '#10b981', fontWeight: '600' }}>
            +12% from yesterday
          </Text>
        </View>

        {/* Today's Orders */}
        <View style={{
          width: '48%',
          backgroundColor: 'white',
          borderRadius: 20,
          padding: 20,
          shadowColor: '#dc2626',
          shadowOffset: { width: 0, height: 6 },
          shadowOpacity: 0.1,
          shadowRadius: 16,
          elevation: 8,
          borderWidth: 1,
          borderColor: 'rgba(220, 38, 38, 0.05)',
        }}>
          <View style={{
            flexDirection: 'row',
            alignItems: 'center',
            marginBottom: 16,
          }}>
            <View style={{
              width: 48,
              height: 48,
              backgroundColor: '#dc2626',
              borderRadius: 24,
              justifyContent: 'center',
              alignItems: 'center',
              marginRight: 12,
              shadowColor: '#dc2626',
              shadowOffset: { width: 0, height: 4 },
              shadowOpacity: 0.3,
              shadowRadius: 8,
              elevation: 6,
            }}>
              <Ionicons name="receipt" size={24} color="white" />
            </View>
            <Text style={{ fontSize: 15, color: '#6b7280', fontWeight: '600' }}>
              Orders
            </Text>
          </View>
          <Text style={{ fontSize: 28, fontWeight: 'bold', color: '#111827', marginBottom: 4 }}>
            {stats.todayOrders}
          </Text>
          <Text style={{ fontSize: 13, color: '#dc2626', fontWeight: '600' }}>
            {stats.activeOrders} active
          </Text>
        </View>

        {/* Rating */}
        <View style={{
          width: '48%',
          backgroundColor: 'white',
          borderRadius: 20,
          padding: 20,
          shadowColor: '#dc2626',
          shadowOffset: { width: 0, height: 6 },
          shadowOpacity: 0.1,
          shadowRadius: 16,
          elevation: 8,
          borderWidth: 1,
          borderColor: 'rgba(220, 38, 38, 0.05)',
        }}>
          <View style={{
            flexDirection: 'row',
            alignItems: 'center',
            marginBottom: 16,
          }}>
            <View style={{
              width: 48,
              height: 48,
              backgroundColor: '#dc2626',
              borderRadius: 24,
              justifyContent: 'center',
              alignItems: 'center',
              marginRight: 12,
              shadowColor: '#dc2626',
              shadowOffset: { width: 0, height: 4 },
              shadowOpacity: 0.3,
              shadowRadius: 8,
              elevation: 6,
            }}>
              <Ionicons name="star" size={24} color="white" />
            </View>
            <Text style={{ fontSize: 15, color: '#6b7280', fontWeight: '600' }}>
              Rating
            </Text>
          </View>
          <View style={{ flexDirection: 'row', alignItems: 'center', marginBottom: 4 }}>
            <Text style={{ fontSize: 28, fontWeight: 'bold', color: '#111827', marginRight: 8 }}>
              {stats.rating.toFixed(1)}
            </Text>
            <Ionicons name="star" size={20} color="#fbbf24" />
          </View>
          <Text style={{ fontSize: 13, color: '#dc2626', fontWeight: '600' }}>
            Excellent service!
          </Text>
        </View>

        {/* Bonuses */}
        <View style={{
          width: '48%',
          backgroundColor: 'white',
          borderRadius: 20,
          padding: 20,
          shadowColor: '#dc2626',
          shadowOffset: { width: 0, height: 6 },
          shadowOpacity: 0.1,
          shadowRadius: 16,
          elevation: 8,
          borderWidth: 1,
          borderColor: 'rgba(220, 38, 38, 0.05)',
        }}>
          <View style={{
            flexDirection: 'row',
            alignItems: 'center',
            marginBottom: 16,
          }}>
            <View style={{
              width: 48,
              height: 48,
              backgroundColor: '#dc2626',
              borderRadius: 24,
              justifyContent: 'center',
              alignItems: 'center',
              marginRight: 12,
              shadowColor: '#dc2626',
              shadowOffset: { width: 0, height: 4 },
              shadowOpacity: 0.3,
              shadowRadius: 8,
              elevation: 6,
            }}>
              <Ionicons name="gift" size={24} color="white" />
            </View>
            <Text style={{ fontSize: 15, color: '#6b7280', fontWeight: '600' }}>
              Bonuses
            </Text>
          </View>
          <Text style={{ fontSize: 28, fontWeight: 'bold', color: '#111827', marginBottom: 4 }}>
            Rs 2,250
          </Text>
          <Text style={{ fontSize: 13, color: '#dc2626', fontWeight: '600' }}>
            2 available
          </Text>
        </View>
      </View>
    </View>
  );


  const renderQuickActions = () => (
    <View style={{ marginBottom: 24 }}>
      {/* Go Online Button - Only show when offline */}
      {!isOnline && (
        <TouchableOpacity
          onPress={toggleOnlineStatus}
          style={{
            backgroundColor: '#dc2626',
            borderRadius: 20,
            padding: 20,
            marginBottom: 16,
            shadowColor: '#dc2626',
            shadowOffset: { width: 0, height: 8 },
            shadowOpacity: 0.3,
            shadowRadius: 16,
            elevation: 12,
            alignItems: 'center',
            justifyContent: 'center',
          }}
        >
          <View style={{ flexDirection: 'row', alignItems: 'center' }}>
            <Ionicons name="power" size={28} color="white" style={{ marginRight: 12 }} />
            <Text style={{
              fontSize: 20,
              fontWeight: 'bold',
              color: 'white',
              textAlign: 'center',
            }}>
              Go Online
            </Text>
          </View>
        </TouchableOpacity>
      )}

      {/* Quick Actions Grid */}
      <View style={{
        backgroundColor: 'white',
        borderRadius: 20,
        padding: 20,
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 6 },
        shadowOpacity: 0.1,
        shadowRadius: 16,
        elevation: 8,
      }}>
        <View style={{ flexDirection: 'row', justifyContent: 'space-between', marginBottom: 16 }}>
          <TouchableOpacity
            style={{
              flex: 1,
              backgroundColor: '#dc2626',
              padding: 16,
              borderRadius: 16,
              alignItems: 'center',
              marginRight: 8,
              shadowColor: '#dc2626',
              shadowOffset: { width: 0, height: 4 },
              shadowOpacity: 0.2,
              shadowRadius: 8,
              elevation: 6,
            }}
            onPress={() => (navigation as any).navigate('OrderRequests')}
          >
            <Ionicons name="notifications" size={28} color="white" style={{ marginBottom: 8 }} />
            <Text style={{ fontSize: 13, fontWeight: '600', color: 'white', textAlign: 'center' }}>
              Order Requests
            </Text>
            {orderState.availableOrders.length > 0 && (
              <View style={{
                position: 'absolute',
                top: 8,
                right: 8,
                backgroundColor: '#fbbf24',
                borderRadius: 12,
                minWidth: 24,
                height: 24,
                justifyContent: 'center',
                alignItems: 'center',
              }}>
                <Text style={{ fontSize: 12, color: 'white', fontWeight: 'bold' }}>
                  {orderState.availableOrders.length}
                </Text>
              </View>
            )}
          </TouchableOpacity>

          <TouchableOpacity
            style={{
              flex: 1,
              backgroundColor: '#f8fafc',
              padding: 16,
              borderRadius: 16,
              alignItems: 'center',
              marginLeft: 8,
              borderWidth: 2,
              borderColor: '#e2e8f0',
            }}
            onPress={() => (navigation as any).navigate('Orders')}
          >
            <Ionicons name="receipt" size={28} color="#dc2626" style={{ marginBottom: 8 }} />
            <Text style={{ fontSize: 13, fontWeight: '600', color: '#111827', textAlign: 'center' }}>
              My Orders
            </Text>
          </TouchableOpacity>
        </View>

        <View style={{ flexDirection: 'row', justifyContent: 'space-between' }}>
          <TouchableOpacity
            style={{
              flex: 1,
              backgroundColor: '#f8fafc',
              padding: 16,
              borderRadius: 16,
              alignItems: 'center',
              marginRight: 8,
              borderWidth: 2,
              borderColor: '#e2e8f0',
            }}
            onPress={() => (navigation as any).navigate('Earnings')}
          >
            <Ionicons name="wallet" size={28} color="#dc2626" style={{ marginBottom: 8 }} />
            <Text style={{ fontSize: 13, fontWeight: '600', color: '#111827', textAlign: 'center' }}>
              Earnings
            </Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={{
              flex: 1,
              backgroundColor: '#f8fafc',
              padding: 16,
              borderRadius: 16,
              alignItems: 'center',
              marginLeft: 8,
              borderWidth: 2,
              borderColor: '#e2e8f0',
            }}
            onPress={() => (navigation as any).navigate('Support')}
          >
            <Ionicons name="help-circle" size={28} color="#dc2626" style={{ marginBottom: 8 }} />
            <Text style={{ fontSize: 13, fontWeight: '600', color: '#111827', textAlign: 'center' }}>
              Support
            </Text>
          </TouchableOpacity>
        </View>
      </View>
    </View>
  );

  const renderActiveOrders = () => (
    <View style={{
      backgroundColor: 'white',
      borderRadius: 20,
      padding: 24,
      marginBottom: 24,
      shadowColor: '#000',
      shadowOffset: { width: 0, height: 6 },
      shadowOpacity: 0.1,
      shadowRadius: 16,
      elevation: 8,
      borderWidth: 1,
      borderColor: 'rgba(0, 0, 0, 0.05)',
    }}>
      <View style={{
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        marginBottom: 20,
      }}>
        <Text style={{ fontSize: 22, fontWeight: 'bold', color: '#111827' }}>
          Active Orders
        </Text>
        {stats.activeOrders > 0 && (
          <View style={{
            backgroundColor: '#dc2626',
            paddingHorizontal: 12,
            paddingVertical: 6,
            borderRadius: 20,
          }}>
            <Text style={{ color: 'white', fontSize: 12, fontWeight: 'bold' }}>
              {stats.activeOrders} active
            </Text>
          </View>
        )}
      </View>

      {stats.activeOrders > 0 ? (
        <TouchableOpacity style={{
          backgroundColor: '#fef3c7',
          padding: 20,
          borderRadius: 16,
          borderLeftWidth: 4,
          borderLeftColor: '#f59e0b',
          shadowColor: '#f59e0b',
          shadowOffset: { width: 0, height: 2 },
          shadowOpacity: 0.1,
          shadowRadius: 8,
          elevation: 4,
        }}>
          <View style={{ flexDirection: 'row', alignItems: 'center', marginBottom: 8 }}>
            <Ionicons name="time" size={20} color="#d97706" style={{ marginRight: 8 }} />
            <Text style={{ fontSize: 16, fontWeight: '600', color: '#92400e' }}>
              You have {stats.activeOrders} order{stats.activeOrders > 1 ? 's' : ''} in progress
            </Text>
          </View>
          <Text style={{ fontSize: 14, color: '#b45309', marginLeft: 28 }}>
            Tap to view details and continue delivery
          </Text>
        </TouchableOpacity>
      ) : (
        <View style={{
          backgroundColor: '#f8fafc',
          padding: 24,
          borderRadius: 16,
          alignItems: 'center',
          borderWidth: 2,
          borderColor: '#e2e8f0',
          borderStyle: 'dashed',
        }}>
          <View style={{
            width: 60,
            height: 60,
            backgroundColor: '#e2e8f0',
            borderRadius: 30,
            justifyContent: 'center',
            alignItems: 'center',
            marginBottom: 12,
          }}>
            <Ionicons name="checkmark-circle-outline" size={32} color="#64748b" />
          </View>
          <Text style={{ fontSize: 18, color: '#475569', marginBottom: 4, fontWeight: '600' }}>
            No active orders
          </Text>
          <Text style={{ fontSize: 14, color: '#94a3b8', textAlign: 'center', lineHeight: 20 }}>
            {isOnline ? 'Waiting for new requests...' : 'Go online to receive orders'}
          </Text>
        </View>
      )}
    </View>
  );

  const renderUpcomingBonuses = () => (
    <Card variant="elevated" style={{ marginBottom: 20 }}>
      <View style={{ padding: 20 }}>
      <Text style={{ fontSize: 20, fontWeight: 'bold', color: '#111827', marginBottom: 16 }}>
        Upcoming Bonuses
      </Text>

      {stats.upcomingBonuses.map((bonus, index) => (
        <View key={index} style={{
          backgroundColor: '#f0f9ff',
          padding: 16,
          borderRadius: 12,
          marginBottom: index < stats.upcomingBonuses.length - 1 ? 12 : 0,
          borderLeftWidth: 4,
          borderLeftColor: '#0ea5e9',
        }}>
          <View style={{
            flexDirection: 'row',
            justifyContent: 'space-between',
            alignItems: 'center',
            marginBottom: 8,
          }}>
            <Text style={{ fontSize: 16, fontWeight: '600', color: '#0c4a6e' }}>
              {bonus.title}
            </Text>
            <Text style={{ fontSize: 16, fontWeight: 'bold', color: '#059669' }}>
              +{formatCurrency(bonus.amount)}
            </Text>
          </View>

          <View style={{
            flexDirection: 'row',
            alignItems: 'center',
            marginBottom: 8,
          }}>
            <View style={{
              flex: 1,
              height: 6,
              backgroundColor: '#e0f2fe',
              borderRadius: 3,
              marginRight: 12,
            }}>
              <View style={{
                width: `${(bonus.progress / bonus.target) * 100}%`,
                height: '100%',
                backgroundColor: '#0ea5e9',
                borderRadius: 3,
              }} />
            </View>
            <Text style={{ fontSize: 12, color: '#0c4a6e', fontWeight: '500' }}>
              {bonus.progress}/{bonus.target}
            </Text>
          </View>

          <Text style={{ fontSize: 12, color: '#0369a1' }}>
            {bonus.target - bonus.progress} more to unlock bonus
          </Text>
        </View>
      ))}
      </View>
    </Card>
  );

  const renderWeeklyOverview = () => (
    <Card variant="elevated" style={{ marginBottom: 20 }}>
      <View style={{ padding: 20 }}>
      <Text style={{ fontSize: 20, fontWeight: 'bold', color: '#111827', marginBottom: 16 }}>
        This Week
      </Text>

      <View style={{
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
      }}>
        <View>
          <Text style={{ fontSize: 14, color: '#6b7280', marginBottom: 4 }}>
            Total Earnings
          </Text>
          <Text style={{ fontSize: 24, fontWeight: 'bold', color: '#111827' }}>
            {formatCurrency(stats.weeklyEarnings)}
          </Text>
        </View>

        <View style={{ alignItems: 'flex-end' }}>
          <Badge text={`${stats.completionRate}% completion`} variant="success" />
          <Text style={{ fontSize: 12, color: '#6b7280', marginTop: 4 }}>
            Great performance!
          </Text>
        </View>
      </View>
      </View>
    </Card>
  );



  return (
    <Screen scrollable backgroundColor="#f1f5f9" padding="none">
      {/* Enhanced Header with Gradient */}
      <View style={{
        background: 'linear-gradient(135deg, #dc2626 0%, #ef4444 100%)',
        backgroundColor: '#dc2626',
        paddingTop: 50,
        paddingBottom: 30,
        paddingHorizontal: 20,
        borderBottomLeftRadius: 24,
        borderBottomRightRadius: 24,
        shadowColor: '#dc2626',
        shadowOffset: { width: 0, height: 8 },
        shadowOpacity: 0.3,
        shadowRadius: 16,
        elevation: 12,
      }}>
        <View style={{
          flexDirection: 'row',
          justifyContent: 'space-between',
          alignItems: 'center',
          marginBottom: 20,
        }}>
          {/* Profile Avatar */}
          <View style={{
            width: 50,
            height: 50,
            backgroundColor: 'rgba(255,255,255,0.2)',
            borderRadius: 25,
            justifyContent: 'center',
            alignItems: 'center',
            borderWidth: 2,
            borderColor: 'rgba(255,255,255,0.3)',
          }}>
            <Text style={{ color: 'white', fontWeight: 'bold', fontSize: 18 }}>
              {authState.user ? getInitials(authState.user.firstName, authState.user.lastName) : 'DR'}
            </Text>
          </View>

          {/* Notification Bell */}
          <TouchableOpacity
            onPress={() => (navigation as any).navigate('Notifications')}
            style={{
              padding: 12,
              borderRadius: 12,
              backgroundColor: 'rgba(255,255,255,0.2)',
              position: 'relative',
            }}
          >
            <Ionicons name="notifications" size={24} color="white" />
            {orderState.availableOrders.length > 0 && (
              <View style={{
                position: 'absolute',
                top: 6,
                right: 6,
                backgroundColor: '#fbbf24',
                borderRadius: 10,
                minWidth: 20,
                height: 20,
                justifyContent: 'center',
                alignItems: 'center',
              }}>
                <Text style={{ fontSize: 12, color: 'white', fontWeight: 'bold' }}>
                  {orderState.availableOrders.length}
                </Text>
              </View>
            )}
          </TouchableOpacity>
        </View>

        {/* Welcome Text */}
        <View>
          <Text style={{ fontSize: 28, fontWeight: 'bold', color: 'white', marginBottom: 4 }}>
            Hello, {authState.user?.firstName || 'Demo'}! 👋
          </Text>
          <Text style={{ fontSize: 16, color: 'rgba(255,255,255,0.9)' }}>
            Ready to start delivering?
          </Text>
        </View>
      </View>

      <View style={{ padding: 20, marginTop: -12 }}>
        {renderOnlineToggle()}
        {renderStatsGrid()}
        {renderActiveOrders()}
        {renderQuickActions()}
        {renderUpcomingBonuses()}
        {renderWeeklyOverview()}

        {/* Bottom spacing */}
        <View style={{ height: 20 }} />
      </View>

      {/* Order Request Modal */}
      <Modal
        visible={showOrderRequest}
        animationType="slide"
        presentationStyle="pageSheet"
      >
        <OrderRequestModal
          visible={showOrderRequest}
          onClose={() => setShowOrderRequest(false)}
          orders={orderState.availableOrders}
        />
      </Modal>
    </Screen>
  );
};

export default DashboardScreen;
