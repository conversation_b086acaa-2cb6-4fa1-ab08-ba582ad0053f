import React from 'react';
import { createStackNavigator } from '@react-navigation/stack';
import { SafetyStackParamList } from '../types';

// Safety Screens
import SafetyMainScreen from '../screens/safety/SafetyMainScreen';
import EmergencySOSScreen from '../screens/safety/EmergencySOSScreen';
import EmergencySOSMainScreen from '../screens/emergency/EmergencySOSMainScreen';
import IncidentReportScreen from '../screens/safety/IncidentReportScreen';
import TermsPoliciesScreen from '../screens/safety/TermsPoliciesScreen';

const Stack = createStackNavigator<SafetyStackParamList>();

const SafetyNavigator: React.FC = () => {
  return (
    <Stack.Navigator
      screenOptions={{
        headerShown: false,
        cardStyle: { backgroundColor: '#f9fafb' },
      }}
    >
      <Stack.Screen
        name="SafetyMain"
        component={SafetyMainScreen}
      />
      <Stack.Screen
        name="EmergencySOS"
        component={EmergencySOSMainScreen}
      />
      <Stack.Screen
        name="EmergencySOSLegacy"
        component={EmergencySOSScreen}
      />
      <Stack.Screen
        name="IncidentReport"
        component={IncidentReportScreen}
      />
      <Stack.Screen
        name="TermsPolicies"
        component={TermsPoliciesScreen}
      />
    </Stack.Navigator>
  );
};

export default SafetyNavigator;
