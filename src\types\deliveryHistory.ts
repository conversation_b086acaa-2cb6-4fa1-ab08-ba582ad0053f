// Delivery History Types and Interfaces

export enum OrderStatus {
  PENDING = 'pending',
  ACCEPTED = 'accepted',
  PICKED_UP = 'picked_up',
  IN_TRANSIT = 'in_transit',
  DELIVERED = 'delivered',
  CANCELLED = 'cancelled',
  REJECTED = 'rejected',
}

export enum OrderType {
  FOOD = 'food',
  GROCERY = 'grocery',
  PHARMACY = 'pharmacy',
  ELECTRONICS = 'electronics',
  CLOTHING = 'clothing',
  OTHER = 'other',
}

export enum CancellationReason {
  CUSTOMER_CANCELLED = 'customer_cancelled',
  RESTAURANT_CANCELLED = 'restaurant_cancelled',
  RIDER_CANCELLED = 'rider_cancelled',
  SYSTEM_CANCELLED = 'system_cancelled',
  PAYMENT_FAILED = 'payment_failed',
  ADDRESS_ISSUE = 'address_issue',
  WEATHER_CONDITIONS = 'weather_conditions',
  VEHICLE_BREAKDOWN = 'vehicle_breakdown',
  OTHER = 'other',
}

export interface Location {
  latitude: number;
  longitude: number;
  address: string;
  area: string;
  city: string;
  landmark?: string;
}

export interface Customer {
  id: string;
  name: string;
  phone: string;
  rating?: number;
  totalOrders?: number;
}

export interface Restaurant {
  id: string;
  name: string;
  address: string;
  phone: string;
  rating?: number;
  category: string;
  image?: string;
}

export interface OrderItem {
  id: string;
  name: string;
  quantity: number;
  price: number;
  specialInstructions?: string;
}

export interface DeliveryTimeline {
  orderPlaced: string;
  orderAccepted?: string;
  arrivedAtRestaurant?: string;
  orderPickedUp?: string;
  deliveryStarted?: string;
  orderDelivered?: string;
  orderCancelled?: string;
}

export interface DeliveryMetrics {
  totalDistance: number; // in kilometers
  deliveryTime: number; // in minutes
  pickupTime: number; // in minutes
  waitingTime: number; // in minutes
  averageSpeed: number; // km/h
}

export interface OrderRating {
  overall: number;
  delivery: number;
  communication: number;
  timeliness: number;
  comment?: string;
  ratedAt: string;
}

export interface DeliveryOrder {
  id: string;
  orderNumber: string;
  status: OrderStatus;
  type: OrderType;
  
  // Customer and Restaurant info
  customer: Customer;
  restaurant: Restaurant;
  
  // Location details
  pickupLocation: Location;
  deliveryLocation: Location;
  
  // Order details
  items: OrderItem[];
  totalAmount: number;
  deliveryFee: number;
  tip: number;
  
  // Timeline and metrics
  timeline: DeliveryTimeline;
  metrics?: DeliveryMetrics;
  
  // Rating and feedback
  rating?: OrderRating;
  
  // Cancellation info
  cancellationReason?: CancellationReason;
  cancellationNote?: string;
  
  // Additional info
  specialInstructions?: string;
  paymentMethod: string;
  proofOfDelivery?: string; // image URL
  
  createdAt: string;
  updatedAt: string;
}

export interface OrderHistoryFilter {
  status?: OrderStatus[];
  type?: OrderType[];
  startDate?: string;
  endDate?: string;
  area?: string;
  city?: string;
  minAmount?: number;
  maxAmount?: number;
  rating?: number;
  sortBy?: 'date' | 'amount' | 'rating' | 'distance';
  sortOrder?: 'asc' | 'desc';
  limit?: number;
  offset?: number;
}

export interface PerformanceMetrics {
  // Delivery performance
  averageDeliveryTime: number; // in minutes
  averagePickupTime: number; // in minutes
  averageWaitingTime: number; // in minutes
  averageDistance: number; // in kilometers
  averageSpeed: number; // km/h
  
  // Order statistics
  totalOrders: number;
  completedOrders: number;
  cancelledOrders: number;
  rejectedOrders: number;
  
  // Rates
  acceptanceRate: number; // percentage
  completionRate: number; // percentage
  cancellationRate: number; // percentage
  onTimeDeliveryRate: number; // percentage
  
  // Ratings
  averageRating: number;
  totalRatings: number;
  ratingDistribution: {
    1: number;
    2: number;
    3: number;
    4: number;
    5: number;
  };
  
  // Earnings
  totalEarnings: number;
  averageEarningsPerOrder: number;
  totalTips: number;
  averageTipPerOrder: number;
  
  // Time periods
  period: 'today' | 'week' | 'month' | 'year' | 'all';
  startDate: string;
  endDate: string;
}

export interface DailyPerformance {
  date: string;
  ordersCompleted: number;
  totalEarnings: number;
  averageRating: number;
  averageDeliveryTime: number;
  totalDistance: number;
  hoursWorked: number;
}

export interface WeeklyPerformance {
  week: string; // ISO week format
  year: number;
  ordersCompleted: number;
  totalEarnings: number;
  averageRating: number;
  averageDeliveryTime: number;
  totalDistance: number;
  hoursWorked: number;
  dailyBreakdown: DailyPerformance[];
}

export interface MonthlyPerformance {
  month: number;
  year: number;
  ordersCompleted: number;
  totalEarnings: number;
  averageRating: number;
  averageDeliveryTime: number;
  totalDistance: number;
  hoursWorked: number;
  weeklyBreakdown: WeeklyPerformance[];
}

export interface RatingTrend {
  date: string;
  rating: number;
  ordersCount: number;
}

export interface DeliveryTimeAnalysis {
  timeSlot: string; // e.g., "09:00-10:00"
  averageTime: number;
  ordersCount: number;
  efficiency: number; // percentage
}

export interface LocationAnalysis {
  area: string;
  city: string;
  ordersCount: number;
  averageDeliveryTime: number;
  averageDistance: number;
  averageEarnings: number;
  averageRating: number;
}

export interface DeliveryHistoryState {
  orders: DeliveryOrder[];
  filteredOrders: DeliveryOrder[];
  currentFilter: OrderHistoryFilter;
  selectedOrder: DeliveryOrder | null;
  
  // Performance data
  performanceMetrics: PerformanceMetrics | null;
  dailyPerformance: DailyPerformance[];
  weeklyPerformance: WeeklyPerformance[];
  monthlyPerformance: MonthlyPerformance[];
  
  // Analytics
  ratingTrends: RatingTrend[];
  deliveryTimeAnalysis: DeliveryTimeAnalysis[];
  locationAnalysis: LocationAnalysis[];
  
  // UI state
  isLoading: boolean;
  error: string | null;
  selectedPeriod: 'today' | 'week' | 'month' | 'year' | 'all';
}

export interface DeliveryHistoryContextType {
  state: DeliveryHistoryState;
  
  // Order history functions
  fetchOrderHistory: (filter?: OrderHistoryFilter) => Promise<void>;
  fetchOrderDetails: (orderId: string) => Promise<void>;
  applyFilter: (filter: OrderHistoryFilter) => void;
  clearFilter: () => void;
  setSelectedOrder: (order: DeliveryOrder | null) => void;
  
  // Performance functions
  fetchPerformanceMetrics: (period?: string) => Promise<void>;
  fetchDailyPerformance: (startDate: string, endDate: string) => Promise<void>;
  fetchWeeklyPerformance: (year: number) => Promise<void>;
  fetchMonthlyPerformance: (year: number) => Promise<void>;
  
  // Analytics functions
  fetchRatingTrends: (period: string) => Promise<void>;
  fetchDeliveryTimeAnalysis: (period: string) => Promise<void>;
  fetchLocationAnalysis: (period: string) => Promise<void>;
  
  // Utility functions
  setSelectedPeriod: (period: 'today' | 'week' | 'month' | 'year' | 'all') => void;
  exportOrderHistory: (format: 'csv' | 'pdf') => Promise<Blob>;
  clearError: () => void;
}
