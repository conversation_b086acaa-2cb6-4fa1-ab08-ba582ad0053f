import React from 'react';
import { createStackNavigator } from '@react-navigation/stack';
import { DashboardStackParamList } from '../types';

import DashboardScreen from '../screens/dashboard/DashboardScreen';
import OrderRequestsScreen from '../screens/dashboard/OrderRequestsScreen';

const Stack = createStackNavigator<DashboardStackParamList>();

const DashboardNavigator: React.FC = () => {
  return (
    <Stack.Navigator
      id={"DashboardStack" as any}
      screenOptions={{
        headerShown: false,
      }}
    >
      <Stack.Screen name="DashboardMain" component={DashboardScreen} />
      <Stack.Screen name="OrderRequests" component={OrderRequestsScreen} />
    </Stack.Navigator>
  );
};

export default DashboardNavigator;
