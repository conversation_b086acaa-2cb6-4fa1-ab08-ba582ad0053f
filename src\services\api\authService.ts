import { apiClient, ApiResponse, retryRequest, createFormData } from './apiConfig';
import { RegistrationData } from '../../types/auth';

// Auth API types
export interface LoginRequest {
  email: string;
  password: string;
  deviceId?: string;
}

export interface LoginResponse {
  user: {
    id: string;
    email: string;
    firstName: string;
    lastName: string;
    phone: string;
    isVerified: boolean;
    verificationStatus: 'pending' | 'verified' | 'rejected';
    documentStatus: {
      overall: 'pending' | 'verified' | 'rejected' | 'incomplete';
      documents: {
        [key: string]: {
          status: 'pending' | 'verified' | 'rejected' | 'not_uploaded';
          uploadedAt?: string;
          verifiedAt?: string;
          rejectionReason?: string;
        };
      };
    };
    profile: {
      profilePhoto?: string;
      dateOfBirth?: string;
      address?: string;
      city?: string;
      emergencyContact?: {
        name: string;
        phone: string;
        relationship: string;
      };
    };
    vehicle?: {
      type: string;
      make?: string;
      model?: string;
      year?: string;
      plateNumber?: string;
      color?: string;
    };
    bankInfo?: {
      accountType: string;
      accountNumber?: string;
      bankName?: string;
      iban?: string;
      mobileWallet?: {
        provider: string;
        number: string;
      };
    };
    isDemoAccount: boolean;
    createdAt: string;
    updatedAt: string;
  };
  tokens: {
    accessToken: string;
    refreshToken: string;
    expiresIn: number;
  };
}

export interface RegisterRequest extends RegistrationData {
  deviceId?: string;
}

export interface RegisterResponse {
  user: {
    id: string;
    email: string;
    firstName: string;
    lastName: string;
    phone: string;
    verificationStatus: 'pending';
    isDemoAccount: boolean;
  };
  message: string;
  nextSteps: string[];
}

export interface OTPRequest {
  email: string;
  otp: string;
}

export interface OTPResponse {
  verified: boolean;
  message: string;
}

export interface ForgotPasswordRequest {
  email: string;
}

export interface ResetPasswordRequest {
  email: string;
  otp: string;
  newPassword: string;
}

// Authentication Service
export class AuthService {
  // Login user
  static async login(credentials: LoginRequest): Promise<ApiResponse<LoginResponse>> {
    return retryRequest(async () => {
      const response = await apiClient.post<ApiResponse<LoginResponse>>('/auth/login', credentials);
      return response.data;
    });
  }

  // Register new user
  static async register(userData: RegisterRequest): Promise<ApiResponse<RegisterResponse>> {
    return retryRequest(async () => {
      // Create form data for file uploads
      const formData = createFormData(userData);
      
      const response = await apiClient.post<ApiResponse<RegisterResponse>>('/auth/register', formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });
      return response.data;
    });
  }

  // Verify OTP
  static async verifyOTP(otpData: OTPRequest): Promise<ApiResponse<OTPResponse>> {
    return retryRequest(async () => {
      const response = await apiClient.post<ApiResponse<OTPResponse>>('/auth/verify-otp', otpData);
      return response.data;
    });
  }

  // Resend OTP
  static async resendOTP(email: string): Promise<ApiResponse<{ message: string }>> {
    return retryRequest(async () => {
      const response = await apiClient.post<ApiResponse<{ message: string }>>('/auth/resend-otp', { email });
      return response.data;
    });
  }

  // Forgot password
  static async forgotPassword(data: ForgotPasswordRequest): Promise<ApiResponse<{ message: string }>> {
    return retryRequest(async () => {
      const response = await apiClient.post<ApiResponse<{ message: string }>>('/auth/forgot-password', data);
      return response.data;
    });
  }

  // Reset password
  static async resetPassword(data: ResetPasswordRequest): Promise<ApiResponse<{ message: string }>> {
    return retryRequest(async () => {
      const response = await apiClient.post<ApiResponse<{ message: string }>>('/auth/reset-password', data);
      return response.data;
    });
  }

  // Get current user profile
  static async getCurrentUser(): Promise<ApiResponse<LoginResponse['user']>> {
    return retryRequest(async () => {
      const response = await apiClient.get<ApiResponse<LoginResponse['user']>>('/auth/me');
      return response.data;
    });
  }

  // Refresh token
  static async refreshToken(refreshToken: string): Promise<ApiResponse<LoginResponse['tokens']>> {
    return retryRequest(async () => {
      const response = await apiClient.post<ApiResponse<LoginResponse['tokens']>>('/auth/refresh', {
        refreshToken,
      });
      return response.data;
    });
  }

  // Logout
  static async logout(): Promise<ApiResponse<{ message: string }>> {
    return retryRequest(async () => {
      const response = await apiClient.post<ApiResponse<{ message: string }>>('/auth/logout');
      return response.data;
    });
  }

  // Update profile
  static async updateProfile(profileData: Partial<LoginResponse['user']['profile']>): Promise<ApiResponse<LoginResponse['user']>> {
    return retryRequest(async () => {
      const response = await apiClient.put<ApiResponse<LoginResponse['user']>>('/auth/profile', profileData);
      return response.data;
    });
  }

  // Change password
  static async changePassword(data: {
    currentPassword: string;
    newPassword: string;
  }): Promise<ApiResponse<{ message: string }>> {
    return retryRequest(async () => {
      const response = await apiClient.put<ApiResponse<{ message: string }>>('/auth/change-password', data);
      return response.data;
    });
  }

  // Delete account
  static async deleteAccount(password: string): Promise<ApiResponse<{ message: string }>> {
    return retryRequest(async () => {
      const response = await apiClient.delete<ApiResponse<{ message: string }>>('/auth/account', {
        data: { password },
      });
      return response.data;
    });
  }
}

export default AuthService;
