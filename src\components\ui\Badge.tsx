import React from 'react';
import { View, Text, ViewStyle, TextStyle } from 'react-native';

interface BadgeProps {
  text: string;
  variant?: 'default' | 'success' | 'warning' | 'error' | 'info' | 'primary' | 'secondary';
  size?: 'xs' | 'sm' | 'md' | 'lg' | 'xl';
  style?: ViewStyle;
}

const Badge: React.FC<BadgeProps> = ({
  text,
  variant = 'default',
  size = 'md',
  style,
}) => {
  const getBadgeStyles = (): ViewStyle => {
    const baseStyles: ViewStyle = {
      borderRadius: 20, // More rounded for modern look
      alignSelf: 'flex-start',
      justifyContent: 'center',
      alignItems: 'center',
      shadowColor: '#000',
      shadowOffset: { width: 0, height: 2 },
      shadowOpacity: 0.1,
      shadowRadius: 4,
      elevation: 2,
    };

    // Enhanced size styles
    const sizeStyles: Record<string, ViewStyle> = {
      xs: { paddingHorizontal: 6, paddingVertical: 2, minHeight: 18 },
      sm: { paddingHorizontal: 8, paddingVertical: 3, minHeight: 22 },
      md: { paddingHorizontal: 12, paddingVertical: 6, minHeight: 28 },
      lg: { paddingHorizontal: 16, paddingVertical: 8, minHeight: 32 },
      xl: { paddingHorizontal: 20, paddingVertical: 10, minHeight: 36 },
    };

    // Enhanced variant styles with modern colors
    const variantStyles: Record<string, ViewStyle> = {
      default: {
        backgroundColor: '#f1f5f9',
        borderWidth: 1,
        borderColor: '#e2e8f0',
      },
      primary: {
        backgroundColor: '#dc2626',
        shadowColor: '#dc2626',
        shadowOpacity: 0.3,
      },
      secondary: {
        backgroundColor: '#64748b',
        shadowColor: '#64748b',
        shadowOpacity: 0.2,
      },
      success: {
        backgroundColor: '#10b981',
        shadowColor: '#10b981',
        shadowOpacity: 0.2,
      },
      warning: {
        backgroundColor: '#f59e0b',
        shadowColor: '#f59e0b',
        shadowOpacity: 0.2,
      },
      error: {
        backgroundColor: '#dc2626',
        shadowColor: '#dc2626',
        shadowOpacity: 0.3,
      },
      info: {
        backgroundColor: '#3b82f6',
        shadowColor: '#3b82f6',
        shadowOpacity: 0.2,
      },
    };

    return {
      ...baseStyles,
      ...sizeStyles[size],
      ...variantStyles[variant],
    };
  };

  const getTextStyles = (): TextStyle => {
    // Enhanced size styles
    const sizeStyles: Record<string, TextStyle> = {
      xs: { fontSize: 10 },
      sm: { fontSize: 11 },
      md: { fontSize: 12 },
      lg: { fontSize: 14 },
      xl: { fontSize: 16 },
    };

    // Enhanced variant styles with better contrast
    const variantStyles: Record<string, TextStyle> = {
      default: { color: '#475569' },
      primary: { color: '#ffffff' },
      secondary: { color: '#ffffff' },
      success: { color: '#ffffff' },
      warning: { color: '#ffffff' },
      error: { color: '#ffffff' },
      info: { color: '#ffffff' },
    };

    return {
      fontWeight: '600', // Bolder for better readability
      ...sizeStyles[size],
      ...variantStyles[variant],
    };
  };

  return (
    <View style={[getBadgeStyles(), style]}>
      <Text style={getTextStyles()}>{text}</Text>
    </View>
  );
};

export default Badge;
