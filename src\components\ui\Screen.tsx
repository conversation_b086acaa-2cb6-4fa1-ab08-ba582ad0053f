import React from 'react';
import {
  View,
  ScrollView,
  KeyboardAvoidingView,
  Platform,
  ViewStyle,
  StatusBar,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';

interface ScreenProps {
  children: React.ReactNode;
  scrollable?: boolean;
  keyboardAvoiding?: boolean;
  backgroundColor?: string;
  padding?: 'none' | 'sm' | 'md' | 'lg' | 'xl';
  style?: ViewStyle;
  contentContainerStyle?: ViewStyle;
  showsVerticalScrollIndicator?: boolean;
  refreshControl?: React.ReactElement;
}

const Screen: React.FC<ScreenProps> = ({
  children,
  scrollable = false,
  keyboardAvoiding = true,
  backgroundColor = '#f8fafc',
  padding = 'md',
  style,
  contentContainerStyle,
  showsVerticalScrollIndicator = false,
  refreshControl,
}) => {
  const paddingStyles: Record<string, ViewStyle> = {
    none: { padding: 0 },
    sm: { padding: 12 },
    md: { padding: 20 },
    lg: { padding: 24 },
    xl: { padding: 32 },
  };

  const baseContainerStyle: ViewStyle = {
    flex: 1,
    backgroundColor,
  };

  const contentStyle: ViewStyle = {
    ...paddingStyles[padding],
    ...(scrollable ? {} : { flex: 1 }),
  };

  const renderContent = () => {
    if (scrollable) {
      return (
        <ScrollView
          style={{ flex: 1 }}
          contentContainerStyle={[contentStyle, contentContainerStyle]}
          keyboardShouldPersistTaps="handled"
          showsVerticalScrollIndicator={showsVerticalScrollIndicator}
          refreshControl={refreshControl}
        >
          {children}
        </ScrollView>
      );
    }

    return (
      <View style={[contentStyle, contentContainerStyle]}>
        {children}
      </View>
    );
  };

  const screenContent = keyboardAvoiding ? (
    <KeyboardAvoidingView
      style={{ flex: 1 }}
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
    >
      {renderContent()}
    </KeyboardAvoidingView>
  ) : (
    renderContent()
  );

  return (
    <View style={[baseContainerStyle, style]}>
      <StatusBar barStyle="dark-content" backgroundColor={backgroundColor} />
      <SafeAreaView style={{ flex: 1 }}>
        {screenContent}
      </SafeAreaView>
    </View>
  );
};

export default Screen;
