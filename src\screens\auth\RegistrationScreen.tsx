import React, { useState, useRef, useEffect } from 'react';
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  Alert,
  KeyboardAvoidingView,
  Platform,
  Image,
  Animated,
  Modal,
  Switch,
  TextInput,
  StyleSheet,
  Dimensions,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import * as ImagePicker from 'expo-image-picker';
import { Button, Input, Card } from '../../components/ui';
import { RegistrationData, VehicleType, BankAccountType } from '../../types/auth';

const { width } = Dimensions.get('window');

interface RegistrationScreenProps {
  navigation?: any;
  onComplete?: (data: RegistrationData) => void;
  onBack?: () => void;
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F9FAFB',
  },
  section: {
    backgroundColor: '#FFFFFF',
    marginHorizontal: 16,
    marginTop: 16,
    borderRadius: 20,
    padding: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  headerSection: {
    backgroundColor: '#FFFFFF',
    paddingHorizontal: 20,
    paddingTop: 20,
    paddingBottom: 24,
    borderBottomLeftRadius: 24,
    borderBottomRightRadius: 24,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.1,
    shadowRadius: 12,
    elevation: 8,
  },
  sectionHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 20,
  },
  sectionIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#1F2937',
    marginBottom: 2,
  },
  sectionSubtitle: {
    fontSize: 14,
    color: '#6B7280',
  },
  vehicleGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  vehicleCard: {
    width: (width - 72) / 2,
    marginBottom: 16,
    borderRadius: 16,
    overflow: 'hidden',
    borderWidth: 2,
  },
  vehicleCardContent: {
    padding: 20,
    alignItems: 'center',
  },
  vehicleIconContainer: {
    width: 60,
    height: 60,
    borderRadius: 30,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 12,
  },
  vehicleLabel: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 6,
    textAlign: 'center',
  },
  vehicleDescription: {
    fontSize: 12,
    textAlign: 'center',
    lineHeight: 16,
  },
  uploadButton: {
    borderWidth: 2,
    borderStyle: 'dashed',
    borderRadius: 16,
    backgroundColor: '#FAFAFA',
    minHeight: 120,
    overflow: 'hidden',
  },
  uploadButtonError: {
    borderColor: '#EF4444',
  },
  uploadedContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
  },
  uploadedImage: {
    width: 60,
    height: 60,
    borderRadius: 12,
    marginRight: 16,
  },
  uploadedInfo: {
    flex: 1,
  },
  uploadedText: {
    fontSize: 14,
    fontWeight: '600',
    color: '#10B981',
    marginLeft: 8,
  },
  uploadPlaceholder: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  uploadText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#374151',
    marginTop: 12,
    textAlign: 'center',
  },
  uploadSubtext: {
    fontSize: 12,
    color: '#6B7280',
    marginTop: 4,
    textAlign: 'center',
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0,0,0,0.5)',
    justifyContent: 'flex-end',
  },
  modalContent: {
    backgroundColor: 'white',
    borderTopLeftRadius: 24,
    borderTopRightRadius: 24,
    paddingTop: 8,
    paddingBottom: 40,
    paddingHorizontal: 20,
  },
  modalHandle: {
    width: 40,
    height: 4,
    backgroundColor: '#D1D5DB',
    borderRadius: 2,
    alignSelf: 'center',
    marginBottom: 20,
  },
  modalTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#1F2937',
    marginBottom: 20,
    textAlign: 'center',
  },
  modalOption: {
    flexDirection: 'row',
    alignItems: 'center',
    borderRadius: 16,
    padding: 16,
    marginBottom: 12,
  },
  modalOptionIcon: {
    width: 48,
    height: 48,
    borderRadius: 24,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
  },
  modalOptionText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1F2937',
    marginBottom: 2,
  },
  modalOptionSubtext: {
    fontSize: 14,
    color: '#6B7280',
  },
  modalCancelButton: {
    backgroundColor: '#F3F4F6',
    borderRadius: 16,
    padding: 16,
    alignItems: 'center',
  },
  modalCancelText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#6B7280',
  },
});

const STEPS = [
  { id: 1, title: 'Personal Info', icon: 'person-outline', description: 'Basic information' },
  { id: 2, title: 'Vehicle & Documents', icon: 'car-outline', description: 'Vehicle details & documents' },
  { id: 3, title: 'Payment Methods', icon: 'card-outline', description: 'Payment details' },
  { id: 4, title: 'Summary', icon: 'checkmark-circle-outline', description: 'Review & confirm' },
];

const VEHICLE_OPTIONS = [
  { type: VehicleType.BICYCLE, label: 'Bicycle', icon: 'bicycle-outline', emoji: '🚲', color: '#10B981', description: 'Eco-friendly delivery option' },
  { type: VehicleType.MOTORCYCLE, label: 'Motorcycle', icon: 'car-sport-outline', emoji: '🏍️', color: '#F59E0B', description: 'Fast and efficient delivery' },
  { type: VehicleType.SCOOTER, label: 'Scooter', icon: 'car-outline', emoji: '🛵', color: '#8B5CF6', description: 'Compact and maneuverable' },
  { type: VehicleType.CAR, label: 'Car', icon: 'car-outline', emoji: '🚗', color: '#EF4444', description: 'Large capacity delivery' },
];

const PAYMENT_METHOD_OPTIONS = [
  {
    type: BankAccountType.BANK_ACCOUNT,
    label: 'Bank Account',
    icon: 'card-outline',
    emoji: '🏦',
    color: '#EF4444',
    description: 'Traditional bank account transfer',
  },
  {
    type: BankAccountType.JAZZCASH,
    label: 'JazzCash',
    icon: 'phone-portrait-outline',
    emoji: '📱',
    color: '#EF4444',
    description: 'Mobile wallet payments',
  },
  {
    type: BankAccountType.EASYPAISA,
    label: 'EasyPaisa',
    icon: 'phone-portrait-outline',
    emoji: '💳',
    color: '#DC2626',
    description: 'Digital wallet solution',
  },
];

const COUNTRY_CODES = [
  {
    code: '+92',
    country: 'Pakistan',
    flag: '🇵🇰',
    phoneLength: 10, // After country code
  },
];

// Document type interface
interface DocumentRequirement {
  key: string;
  label: string;
  icon: string;
  required: boolean;
  isInput?: boolean;
  description?: string;
}

// Vehicle-specific document requirements
const getDocumentTypesForVehicle = (vehicleType: VehicleType): DocumentRequirement[] => {
  const baseDocuments = [
    { key: 'cnicFront', label: 'CNIC Front', icon: 'id-card-outline', required: true },
    { key: 'cnicBack', label: 'CNIC Back', icon: 'id-card-outline', required: true },
  ];

  const vehicleDocuments = {
    [VehicleType.BICYCLE]: [
      { key: 'bicyclePhoto', label: 'Bicycle Photo', icon: 'bicycle-outline', required: false, description: 'Optional — for support/logistics recognition' },
    ],
    [VehicleType.MOTORCYCLE]: [
      { key: 'plateNumber', label: 'Plate Number', icon: 'car-outline', required: true, isInput: true },
      { key: 'motorcycleLicense', label: 'Motorcycle Driving License', icon: 'card-outline', required: true },
      { key: 'vehicleRegistration', label: 'Vehicle Registration', icon: 'document-text-outline', required: false },
      { key: 'motorcyclePhoto', label: 'Motorcycle Photo', icon: 'camera-outline', required: false, description: 'Optional — for support/logistics recognition' },
    ],
    [VehicleType.CAR]: [
      { key: 'plateNumber', label: 'Plate Number', icon: 'car-outline', required: true, isInput: true },
      { key: 'drivingLicense', label: 'Driving License', icon: 'card-outline', required: true },
      { key: 'vehicleRegistration', label: 'Vehicle Registration', icon: 'document-text-outline', required: false },
      { key: 'carPhoto', label: 'Car Photo', icon: 'camera-outline', required: false, description: 'Optional — for support/logistics recognition' },
    ],
    [VehicleType.SCOOTER]: [
      { key: 'plateNumber', label: 'Plate Number', icon: 'car-outline', required: true, isInput: true },
      { key: 'scooterLicense', label: 'Driving License (valid for scooter/2-wheelers)', icon: 'card-outline', required: true },
      { key: 'vehicleRegistration', label: 'Vehicle Registration', icon: 'document-text-outline', required: false },
      { key: 'scooterPhoto', label: 'Photo', icon: 'camera-outline', required: false, description: 'Optional — for support/logistics recognition' },
    ],
  };

  return [...baseDocuments, ...vehicleDocuments[vehicleType]];
};

const RegistrationScreen: React.FC<RegistrationScreenProps> = ({ navigation, onComplete, onBack }) => {
  const [currentStep, setCurrentStep] = useState(1);
  const [formData, setFormData] = useState<Partial<RegistrationData>>({
    firstName: '',
    lastName: '',
    email: '',
    phoneNumber: '',
    password: '',
    confirmPassword: '',
    cnic: '',
    vehicleType: VehicleType.BICYCLE,
    vehicleDetails: {
      make: '',
      model: '',
      year: new Date().getFullYear(),
      color: '',
      plateNumber: '',
      registrationNumber: '',
    },
    bankDetails: {
      accountType: BankAccountType.BANK_ACCOUNT,
      accountNumber: '',
      accountTitle: '',
      bankName: '',
      iban: '',
    },
    documents: {},
    agreeToTerms: false,
  });

  const [errors, setErrors] = useState<Record<string, string>>({});
  const [isLoading, setIsLoading] = useState(false);
  const [showImagePicker, setShowImagePicker] = useState(false);
  const [currentDocumentType, setCurrentDocumentType] = useState<string>('');
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [selectedCountryCode, setSelectedCountryCode] = useState(COUNTRY_CODES[0]);
  const [showCountryPicker, setShowCountryPicker] = useState(false);

  const [completedSteps, setCompletedSteps] = useState<Set<number>>(new Set());

  // Animation refs
  const progressAnim = useRef(new Animated.Value(0)).current;
  const slideAnim = useRef(new Animated.Value(0)).current;
  const paymentSelectAnim = useRef(new Animated.Value(0)).current;
  const checkAnimRefs = useRef<Record<number, any>>({});

  // Initialize check animations for completed steps
  useEffect(() => {
    STEPS.forEach(step => {
      if (!checkAnimRefs.current[step.id]) {
        checkAnimRefs.current[step.id] = new Animated.Value(0);
      }
    });
  }, []);

  // Update progress animation when step changes
  useEffect(() => {
    Animated.timing(progressAnim, {
      toValue: (currentStep - 1) / (STEPS.length - 1),
      duration: 300,
      useNativeDriver: false,
    }).start();

    // Slide animation for step content
    Animated.timing(slideAnim, {
      toValue: 0,
      duration: 300,
      useNativeDriver: true,
    }).start();
  }, [currentStep]);
  const validateStep = (step: number): boolean => {
    const newErrors: Record<string, string> = {};

    switch (step) {
      case 1: // Personal Info
        if (!formData.firstName?.trim()) newErrors.firstName = 'First name is required';
        if (!formData.email?.trim()) {
          newErrors.email = 'Email is required';
        } else if (!formData.email.includes('@')) {
          newErrors.email = 'Email must contain @ symbol';
        }
        if (!formData.phoneNumber?.trim()) {
          newErrors.phoneNumber = 'Phone number is required';
        } else if (!isValidPhoneNumber(formData.phoneNumber)) {
          newErrors.phoneNumber = 'Please enter a valid phone number';
        }
        const passwordError = validatePassword(formData.password || '');
        if (passwordError) {
          newErrors.password = passwordError;
        }
        if (formData.password !== formData.confirmPassword) {
          newErrors.confirmPassword = 'Passwords do not match';
        }
        if (!formData.cnic?.trim()) {
          newErrors.cnic = 'CNIC is required';
        } else if (!/^\d{5}-\d{7}-\d{1}$/.test(formData.cnic)) {
          newErrors.cnic = 'CNIC format should be 12345-1234567-1';
        }
        break;

      case 2: // Vehicle Details & Documents
        // Validate vehicle type selection
        if (!formData.vehicleType) {
          newErrors.vehicleType = 'Please select a vehicle type';
        }

        // Only validate plate number for vehicles that require it
        if (formData.vehicleType !== VehicleType.BICYCLE && !formData.vehicleDetails?.plateNumber?.trim()) {
          newErrors.plateNumber = 'Plate number is required';
        }

        // Validate required documents
        const requiredDocs = getDocumentTypesForVehicle(formData.vehicleType || VehicleType.BICYCLE)
          .filter(doc => doc.required);

        requiredDocs.forEach(doc => {
          if (doc.isInput) {
            // Validate input fields like plate number
            if (doc.key === 'plateNumber' && !formData.vehicleDetails?.plateNumber?.trim()) {
              newErrors[doc.key] = 'Plate number is required';
            }
          } else {
            // Validate document uploads
            if (!formData.documents?.[doc.key as keyof typeof formData.documents]) {
              newErrors[doc.key] = `${doc.label} is required`;
            }
          }
        });
        break;

      case 3: // Payment Methods
        if (!formData.bankDetails?.accountType) newErrors.accountType = 'Please select a payment method';
        if (!formData.bankDetails?.accountNumber?.trim()) newErrors.accountNumber = 'Account number is required';
        if (!formData.bankDetails?.accountTitle?.trim()) newErrors.accountTitle = 'Account title is required';
        if (formData.bankDetails?.accountType === BankAccountType.BANK_ACCOUNT) {
          if (!formData.bankDetails?.bankName?.trim()) newErrors.bankName = 'Bank name is required';
          if (!formData.bankDetails?.iban?.trim()) newErrors.iban = 'IBAN is required';
        }
        break;

      case 4: // Summary & Terms
        if (!formData.agreeToTerms) newErrors.terms = 'You must agree to terms and conditions';
        break;
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleInputChange = (field: string, value: string | boolean) => {
    if (field.includes('.')) {
      const [parent, child] = field.split('.');
      setFormData(prev => ({
        ...prev,
        [parent]: {
          ...(prev[parent as keyof typeof prev] as any || {}),
          [child]: value,
        },
      }));
    } else {
      setFormData(prev => ({ ...prev, [field]: value }));
    }

    // Clear field error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }
  };

  const handleNext = () => {
    if (validateStep(currentStep)) {
      // Mark current step as completed
      setCompletedSteps(prev => new Set([...prev, currentStep]));

      // Animate check mark for completed step
      Animated.spring(checkAnimRefs.current[currentStep], {
        toValue: 1,
        tension: 50,
        friction: 7,
        useNativeDriver: true,
      }).start();

      if (currentStep < STEPS.length) {
        setCurrentStep(currentStep + 1);
      } else {
        handleSubmit();
      }
    }
  };

  const handleBack = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1);
    } else {
      navigation?.goBack() || onBack?.();
    }
  };

  const handleSubmit = async () => {
    setIsLoading(true);
    try {
      const { register } = useAuth();

      // Prepare registration data
      const registrationData = {
        ...formData,
        documents: uploadedDocuments,
      };

      // Call API to register user
      const response = await register(registrationData);

      Alert.alert(
        'Registration Successful!',
        `Your account has been created successfully. ${response.message || 'You can now login and start using the app. Document verification will be processed in the background.'}`,
        [
          {
            text: 'Continue to Login',
            onPress: () => {
              onComplete?.(formData as RegistrationData);
              navigation?.navigate('Login');
            },
          },
        ]
      );
    } catch (error: any) {
      Alert.alert(
        'Registration Failed',
        error.message || 'Registration failed. Please try again.',
        [{ text: 'OK' }]
      );
    } finally {
      setIsLoading(false);
    }
  };

  const handleImagePicker = async (documentType: string) => {
    setCurrentDocumentType(documentType);
    setShowImagePicker(true);
  };

  const pickImage = async (source: 'camera' | 'gallery') => {
    setShowImagePicker(false);

    try {
      let result;

      if (source === 'camera') {
        const { status } = await ImagePicker.requestCameraPermissionsAsync();
        if (status !== 'granted') {
          Alert.alert('Permission Required', 'Camera permission is required to take photos.');
          return;
        }

        result = await ImagePicker.launchCameraAsync({
          mediaTypes: ImagePicker.MediaTypeOptions.Images,
          allowsEditing: true,
          aspect: [4, 3],
          quality: 0.8,
        });
      } else {
        result = await ImagePicker.launchImageLibraryAsync({
          mediaTypes: ImagePicker.MediaTypeOptions.Images,
          allowsEditing: true,
          aspect: [4, 3],
          quality: 0.8,
        });
      }

      if (!result.canceled && result.assets[0]) {
        setFormData(prev => ({
          ...prev,
          documents: {
            ...prev.documents,
            [currentDocumentType]: result.assets[0].uri,
          },
        }));

        // Clear error for this document
        if (errors[currentDocumentType]) {
          setErrors(prev => ({ ...prev, [currentDocumentType]: '' }));
        }
      }
    } catch (error) {
      Alert.alert('Error', 'Failed to pick image. Please try again.');
    }
  };

  // Helper functions
  const isValidEmail = (email: string): boolean => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  };

  const isValidPhoneNumber = (phone: string): boolean => {
    // Remove formatting and check for 11 digits starting with 03
    const numericPhone = phone.replace(/\D/g, '');
    return /^03[0-9]{9}$/.test(numericPhone);
  };

  const formatCNIC = (value: string): string => {
    // Remove all non-numeric characters
    const numericValue = value.replace(/\D/g, '');

    // Apply formatting: 12345-1234567-1
    if (numericValue.length <= 5) {
      return numericValue;
    } else if (numericValue.length <= 12) {
      return `${numericValue.slice(0, 5)}-${numericValue.slice(5)}`;
    } else {
      return `${numericValue.slice(0, 5)}-${numericValue.slice(5, 12)}-${numericValue.slice(12, 13)}`;
    }
  };

  const formatPhoneNumber = (value: string): string => {
    // Remove all non-numeric characters
    const numericValue = value.replace(/\D/g, '');

    // Limit to 11 digits (Pakistani phone numbers)
    const limitedValue = numericValue.slice(0, 11);

    // Apply formatting: 0300-1234567
    if (limitedValue.length <= 4) {
      return limitedValue;
    } else if (limitedValue.length <= 11) {
      return `${limitedValue.slice(0, 4)}-${limitedValue.slice(4)}`;
    }

    return limitedValue;
  };

  const validatePassword = (password: string): string | null => {
    if (!password) return 'Password is required';
    if (password.length < 8) return 'Password must be at least 8 characters';
    if (!/[A-Z]/.test(password)) return 'Password must contain at least one uppercase letter';
    if (!/[a-z]/.test(password)) return 'Password must contain at least one lowercase letter';
    if (!/[0-9]/.test(password)) return 'Password must contain at least one number';
    if (!/[!@#$%^&*()\-_=+{};':",./<>?\\|~\[\]]/.test(password)) return 'Password must contain at least one special character';
    return null;
  };

  const getPasswordStrength = (password: string) => {
    const requirements = [
      { test: password.length >= 8, label: 'At least 8 characters' },
      { test: /[A-Z]/.test(password), label: 'One uppercase letter' },
      { test: /[a-z]/.test(password), label: 'One lowercase letter' },
      { test: /[0-9]/.test(password), label: 'One number' },
      { test: /[!@#$%^&*()\-_=+{};':",./<>?\\|~\[\]]/.test(password), label: 'One special character' },
    ];

    const passedCount = requirements.filter(req => req.test).length;
    const strength = passedCount === 0 ? 'weak' : passedCount <= 2 ? 'weak' : passedCount <= 4 ? 'medium' : 'strong';

    return { requirements, strength, passedCount };
  };

  const renderPasswordValidation = (password: string) => {
    if (!password) return null;

    const { requirements, strength } = getPasswordStrength(password);

    const strengthColors = {
      weak: '#EF4444',
      medium: '#F59E0B',
      strong: '#10B981',
    };

    return (
      <View style={{ marginTop: 8, padding: 12, backgroundColor: '#F9FAFB', borderRadius: 8 }}>
        <Text style={{ fontSize: 12, fontWeight: '600', color: '#374151', marginBottom: 8 }}>
          Password Requirements:
        </Text>
        {requirements.map((req, index) => (
          <View key={index} style={{ flexDirection: 'row', alignItems: 'center', marginBottom: 4 }}>
            <Ionicons
              name={req.test ? 'checkmark-circle' : 'ellipse-outline'}
              size={16}
              color={req.test ? '#10B981' : '#9CA3AF'}
              style={{ marginRight: 8 }}
            />
            <Text style={{
              fontSize: 12,
              color: req.test ? '#10B981' : '#6B7280',
              textDecorationLine: req.test ? 'line-through' : 'none',
            }}>
              {req.label}
            </Text>
          </View>
        ))}
        <View style={{ flexDirection: 'row', alignItems: 'center', marginTop: 8 }}>
          <Text style={{ fontSize: 12, fontWeight: '600', color: '#374151', marginRight: 8 }}>
            Strength:
          </Text>
          <Text style={{
            fontSize: 12,
            fontWeight: '600',
            color: strengthColors[strength],
            textTransform: 'capitalize',
          }}>
            {strength}
          </Text>
        </View>
      </View>
    );
  };

  const renderCountryCodePicker = () => (
    <Modal
      visible={showCountryPicker}
      transparent={true}
      animationType="slide"
      onRequestClose={() => setShowCountryPicker(false)}
    >
      <View style={{
        flex: 1,
        backgroundColor: 'rgba(0,0,0,0.5)',
        justifyContent: 'flex-end',
      }}>
        <View style={{
          backgroundColor: 'white',
          borderTopLeftRadius: 20,
          borderTopRightRadius: 20,
          paddingTop: 20,
          paddingBottom: 40,
          paddingHorizontal: 20,
        }}>
          <View style={{
            flexDirection: 'row',
            justifyContent: 'space-between',
            alignItems: 'center',
            marginBottom: 20,
          }}>
            <Text style={{ fontSize: 18, fontWeight: 'bold', color: '#1F2937' }}>
              Select Country
            </Text>
            <TouchableOpacity onPress={() => setShowCountryPicker(false)}>
              <Ionicons name="close" size={24} color="#6B7280" />
            </TouchableOpacity>
          </View>

          {COUNTRY_CODES.map((country, index) => (
            <TouchableOpacity
              key={index}
              style={{
                flexDirection: 'row',
                alignItems: 'center',
                paddingVertical: 15,
                paddingHorizontal: 10,
                borderRadius: 10,
                backgroundColor: selectedCountryCode.code === country.code ? '#FEF3C7' : 'transparent',
              }}
              onPress={() => {
                setSelectedCountryCode(country);
                setShowCountryPicker(false);
              }}
            >
              <Text style={{ fontSize: 24, marginRight: 12 }}>{country.flag}</Text>
              <View style={{ flex: 1 }}>
                <Text style={{ fontSize: 16, fontWeight: '500', color: '#1F2937' }}>
                  {country.country}
                </Text>
                <Text style={{ fontSize: 14, color: '#6B7280' }}>
                  {country.code}
                </Text>
              </View>
              {selectedCountryCode.code === country.code && (
                <Ionicons name="checkmark-circle" size={20} color="#F59E0B" />
              )}
            </TouchableOpacity>
          ))}
        </View>
      </View>
    </Modal>
  );

  // Legacy helper functions for compatibility
  const updateFormData = (field: string, value: any) => {
    handleInputChange(field, value);
  };

  const updateNestedFormData = (parent: string, field: string, value: any) => {
    handleInputChange(`${parent}.${field}`, value);
  };

  const renderProgressHeader = () => {
    const currentStepData = STEPS.find(step => step.id === currentStep);
    const progressPercentage = ((currentStep - 1) / (STEPS.length - 1)) * 100;

    return (
      <View style={{
        backgroundColor: 'white',
        paddingHorizontal: 20,
        paddingVertical: 16,
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.1,
        shadowRadius: 4,
        elevation: 3,
      }}>
        {/* Header with Back Button */}
        <View style={{
          flexDirection: 'row',
          alignItems: 'center',
          marginBottom: 16,
        }}>
          <TouchableOpacity
            onPress={handleBack}
            style={{
              width: 40,
              height: 40,
              justifyContent: 'center',
              alignItems: 'center',
              marginRight: 12,
            }}
          >
            <Ionicons name="arrow-back" size={24} color="#374151" />
          </TouchableOpacity>
          <Text style={{
            fontSize: 20,
            fontWeight: 'bold',
            color: '#1F2937',
            flex: 1,
          }}>
            Create Account
          </Text>
        </View>

        {/* Current Step Info */}
        <View style={{
          flexDirection: 'row',
          alignItems: 'center',
          marginBottom: 16,
        }}>
          <View style={{
            width: 40,
            height: 40,
            backgroundColor: '#EF4444',
            borderRadius: 20,
            justifyContent: 'center',
            alignItems: 'center',
            marginRight: 12,
          }}>
            <Ionicons
              name={currentStepData?.icon as keyof typeof Ionicons.glyphMap}
              size={20}
              color="white"
            />
          </View>
          <View style={{ flex: 1 }}>
            <Text style={{
              fontSize: 18,
              fontWeight: 'bold',
              color: '#1F2937',
              marginBottom: 2,
            }}>
              {currentStepData?.title}
            </Text>
            <Text style={{
              fontSize: 14,
              color: '#6B7280',
            }}>
              Step {currentStep} of {STEPS.length} • {currentStepData?.description}
            </Text>
          </View>
        </View>

        {/* Progress Bar */}
        <View style={{
          flexDirection: 'row',
          alignItems: 'center',
          marginBottom: 8,
        }}>
          <View style={{
            flex: 1,
            height: 6,
            backgroundColor: '#F3F4F6',
            borderRadius: 3,
            overflow: 'hidden',
          }}>
            <Animated.View
              style={{
                height: '100%',
                backgroundColor: '#EF4444',
                borderRadius: 3,
                width: progressAnim.interpolate({
                  inputRange: [0, 1],
                  outputRange: ['0%', '100%'],
                  extrapolate: 'clamp',
                }),
              }}
            />
          </View>
          <Text style={{
            marginLeft: 12,
            fontSize: 12,
            fontWeight: '600',
            color: '#EF4444',
            minWidth: 35,
          }}>
            {Math.round(progressPercentage)}%
          </Text>
        </View>

        {/* Step Dots */}
        <View style={{
          flexDirection: 'row',
          justifyContent: 'center',
          alignItems: 'center',
        }}>
          {STEPS.map((step, index) => (
            <View key={step.id} style={{ flexDirection: 'row', alignItems: 'center' }}>
              <View style={{
                width: 8,
                height: 8,
                borderRadius: 4,
                backgroundColor: completedSteps.has(step.id)
                  ? '#10B981'
                  : step.id === currentStep
                    ? '#EF4444'
                    : '#D1D5DB',
                marginHorizontal: 4,
              }} />
              {index < STEPS.length - 1 && (
                <View style={{
                  width: 20,
                  height: 1,
                  backgroundColor: step.id < currentStep ? '#EF4444' : '#D1D5DB',
                  marginHorizontal: 4,
                }} />
              )}
            </View>
          ))}
        </View>
      </View>
    );
  };

  const renderPersonalInfoStep = () => (
    <Card className="p-6">
      <Text className="text-xl font-bold text-gray-800 mb-6">Personal Information</Text>
      
      <View className="flex-row space-x-4 mb-4">
        <View className="flex-1">
          <Input
            label="First Name"
            value={formData.firstName || ''}
            onChangeText={(value) => updateFormData('firstName', value)}
            error={errors.firstName}
            placeholder="Enter first name"
          />
        </View>
        <View className="flex-1">
          <Input
            label="Last Name (Optional)"
            value={formData.lastName || ''}
            onChangeText={(value) => updateFormData('lastName', value)}
            error={errors.lastName}
            placeholder="Enter last name"
          />
        </View>
      </View>

      <Input
        label="Email Address"
        value={formData.email || ''}
        onChangeText={(value) => updateFormData('email', value)}
        error={errors.email}
        placeholder="Enter email address"
        keyboardType="email-address"
        autoCapitalize="none"
        className="mb-4"
      />

      {/* Phone Number with Country Code */}
      <View style={{ marginBottom: 16 }}>
        <Text style={{
          fontSize: 14,
          fontWeight: '500',
          color: '#374151',
          marginBottom: 6,
        }}>
          Phone Number
        </Text>
        <View style={{
          flexDirection: 'row',
          alignItems: 'center',
        }}>
          {/* Country Code Picker */}
          <TouchableOpacity
            onPress={() => setShowCountryPicker(true)}
            style={{
              flexDirection: 'row',
              alignItems: 'center',
              backgroundColor: '#ffffff',
              borderWidth: 1,
              borderColor: '#d1d5db',
              borderRadius: 8,
              paddingHorizontal: 12,
              paddingVertical: 12,
              marginRight: 8,
              minWidth: 80,
            }}
          >
            <Text style={{ fontSize: 18, marginRight: 4 }}>{selectedCountryCode.flag}</Text>
            <Text style={{ fontSize: 14, color: '#374151', marginRight: 4 }}>
              {selectedCountryCode.code}
            </Text>
            <Ionicons name="chevron-down" size={16} color="#6B7280" />
          </TouchableOpacity>

          {/* Phone Number Input */}
          <View style={{ flex: 1 }}>
            <Input
              value={formData.phoneNumber || ''}
              onChangeText={(value) => updateFormData('phoneNumber', formatPhoneNumber(value))}
              error={errors.phoneNumber}
              placeholder="0300-1234567"
              keyboardType="phone-pad"
              style={{ marginBottom: 0 }}
            />
          </View>
        </View>
        {errors.phoneNumber && (
          <Text style={{
            fontSize: 12,
            color: '#ef4444',
            marginTop: 4,
          }}>
            {errors.phoneNumber}
          </Text>
        )}
      </View>

      <Input
        label="CNIC Number"
        value={formData.cnic || ''}
        onChangeText={(value) => updateFormData('cnic', formatCNIC(value))}
        error={errors.cnic}
        placeholder="12345-1234567-1"
        keyboardType="numeric"
        className="mb-4"
      />

      {/* Password with Live Validation */}
      <View style={{ marginBottom: 16 }}>
        <Input
          label="Password"
          value={formData.password || ''}
          onChangeText={(value) => updateFormData('password', value)}
          error={errors.password}
          placeholder="Enter password"
          secureTextEntry={!showPassword}
          rightIcon={showPassword ? 'eye-off-outline' : 'eye-outline'}
          onRightIconPress={() => setShowPassword(!showPassword)}
        />
        {renderPasswordValidation(formData.password || '')}
      </View>

      <Input
        label="Confirm Password"
        value={formData.confirmPassword || ''}
        onChangeText={(value) => updateFormData('confirmPassword', value)}
        error={errors.confirmPassword}
        placeholder="Confirm password"
        secureTextEntry={!showConfirmPassword}
        rightIcon={showConfirmPassword ? 'eye-off-outline' : 'eye-outline'}
        onRightIconPress={() => setShowConfirmPassword(!showConfirmPassword)}
      />
    </Card>
  );

  const renderVehicleDetailsStep = () => {
    const completedDocs = formData.vehicleType ?
      getDocumentTypesForVehicle(formData.vehicleType).filter(doc =>
        doc.isInput ? formData.vehicleDetails?.plateNumber :
        formData.documents?.[doc.key as keyof typeof formData.documents]
      ).length : 0;

    const totalDocs = formData.vehicleType ? getDocumentTypesForVehicle(formData.vehicleType).length : 0;
    const progressPercentage = totalDocs > 0 ? Math.round((completedDocs / totalDocs) * 100) : 0;

    return (
      <Animated.View
        style={{
          transform: [{ translateX: slideAnim }],
        }}
      >
        {/* Header Section with Progress */}
        <View style={styles.headerSection}>
          <View style={{ alignItems: 'center', marginBottom: 16 }}>
            <Text style={{
              fontSize: 28,
              fontWeight: 'bold',
              color: '#1F2937',
              marginBottom: 8,
            }}>
              Vehicle Registration
            </Text>
            <Text style={{
              fontSize: 16,
              color: '#6B7280',
              textAlign: 'center',
              lineHeight: 22,
            }}>
              Select your vehicle and upload required documents
            </Text>
          </View>

          {/* Progress Indicator */}
          {formData.vehicleType && (
            <View style={{
              backgroundColor: '#F9FAFB',
              borderRadius: 16,
              padding: 16,
              marginTop: 8,
            }}>
              <View style={{
                flexDirection: 'row',
                justifyContent: 'space-between',
                alignItems: 'center',
                marginBottom: 12,
              }}>
                <Text style={{
                  fontSize: 14,
                  fontWeight: '600',
                  color: '#374151',
                }}>
                  Document Progress
                </Text>
                <Text style={{
                  fontSize: 14,
                  fontWeight: 'bold',
                  color: '#EF4444',
                }}>
                  {completedDocs}/{totalDocs} Complete
                </Text>
              </View>
              <View style={{
                height: 8,
                backgroundColor: '#E5E7EB',
                borderRadius: 4,
                overflow: 'hidden',
              }}>
                <Animated.View style={{
                  height: '100%',
                  backgroundColor: '#EF4444',
                  borderRadius: 4,
                  width: `${progressPercentage}%`,
                }} />
              </View>
            </View>
          )}
        </View>

        <ScrollView style={{ flex: 1 }} showsVerticalScrollIndicator={false}>

          {/* Vehicle Selection Section */}
          <View style={styles.section}>
            <View style={styles.sectionHeader}>
              <View style={[styles.sectionIcon, { backgroundColor: '#FEF3C7' }]}>
                <Ionicons name="car-outline" size={20} color="#F59E0B" />
              </View>
              <View style={{ flex: 1 }}>
                <Text style={styles.sectionTitle}>Choose Your Vehicle</Text>
                <Text style={styles.sectionSubtitle}>Select the vehicle you'll use for deliveries</Text>
              </View>
            </View>

            {/* Vehicle Grid */}
            <View style={styles.vehicleGrid}>
              {VEHICLE_OPTIONS.map((vehicle) => {
                const isSelected = formData.vehicleType === vehicle.type;
                return (
                  <TouchableOpacity
                    key={vehicle.type}
                    onPress={() => {
                      handleInputChange('vehicleType', vehicle.type);
                      setFormData(prev => ({
                        ...prev,
                        documents: {},
                        vehicleDetails: { ...prev.vehicleDetails, plateNumber: '' },
                      }));
                      setErrors({});
                    }}
                    style={[
                      styles.vehicleCard,
                      {
                        borderColor: isSelected ? vehicle.color : '#E5E7EB',
                        backgroundColor: isSelected ? `${vehicle.color}10` : '#FFFFFF',
                        transform: isSelected ? [{ scale: 1.02 }] : [{ scale: 1 }],
                      }
                    ]}
                    activeOpacity={0.8}
                  >
                    {/* Selection Indicator */}
                    {isSelected && (
                      <View style={{
                        position: 'absolute',
                        top: 12,
                        right: 12,
                        width: 24,
                        height: 24,
                        backgroundColor: vehicle.color,
                        borderRadius: 12,
                        justifyContent: 'center',
                        alignItems: 'center',
                        zIndex: 1,
                      }}>
                        <Ionicons name="checkmark" size={16} color="white" />
                      </View>
                    )}

                    <View style={styles.vehicleCardContent}>
                      {/* Vehicle Emoji with Background */}
                      <View style={[
                        styles.vehicleIconContainer,
                        {
                          backgroundColor: isSelected ? `${vehicle.color}20` : '#F9FAFB',
                        }
                      ]}>
                        <Text style={{ fontSize: 28 }}>{vehicle.emoji}</Text>
                      </View>

                      {/* Vehicle Name */}
                      <Text style={[
                        styles.vehicleLabel,
                        {
                          color: isSelected ? vehicle.color : '#374151',
                        }
                      ]}>
                        {vehicle.label}
                      </Text>

                      {/* Vehicle Description */}
                      <Text style={[
                        styles.vehicleDescription,
                        {
                          color: isSelected ? vehicle.color : '#6B7280',
                        }
                      ]}>
                        {vehicle.description}
                      </Text>
                    </View>
                  </TouchableOpacity>
                );
              })}
            </View>

            {errors.vehicleType && (
              <Text style={{
                fontSize: 12,
                color: '#EF4444',
                marginTop: 8,
                textAlign: 'center',
              }}>
                {errors.vehicleType}
              </Text>
            )}
          </View>



          {/* Vehicle Information Section */}
          {formData.vehicleType && formData.vehicleType !== VehicleType.BICYCLE && (
            <View style={styles.section}>
              <View style={styles.sectionHeader}>
                <View style={[styles.sectionIcon, { backgroundColor: '#DBEAFE' }]}>
                  <Ionicons name="information-circle-outline" size={20} color="#3B82F6" />
                </View>
                <View style={{ flex: 1 }}>
                  <Text style={styles.sectionTitle}>Vehicle Information</Text>
                  <Text style={styles.sectionSubtitle}>Enter your vehicle details</Text>
                </View>
              </View>

              <View style={{
                backgroundColor: '#F9FAFB',
                borderRadius: 12,
                padding: 16,
              }}>
                <Text style={{
                  fontSize: 14,
                  fontWeight: '600',
                  color: '#374151',
                  marginBottom: 8,
                }}>
                  Plate Number <Text style={{ color: '#EF4444' }}>*</Text>
                </Text>
                <TextInput
                  placeholder="Enter plate number (e.g., ABC-123)"
                  value={formData.vehicleDetails?.plateNumber || ''}
                  onChangeText={(value: string) =>
                    updateNestedFormData('vehicleDetails', 'plateNumber', value.toUpperCase())
                  }
                  autoCapitalize="characters"
                  style={{
                    backgroundColor: 'white',
                    borderWidth: 1,
                    borderColor: errors.plateNumber ? '#EF4444' : '#D1D5DB',
                    borderRadius: 12,
                    paddingHorizontal: 16,
                    paddingVertical: 14,
                    fontSize: 16,
                    color: '#1F2937',
                  }}
                  placeholderTextColor="#9CA3AF"
                />
                {errors.plateNumber && (
                  <Text style={{
                    fontSize: 12,
                    color: '#EF4444',
                    marginTop: 6,
                  }}>
                    {errors.plateNumber}
                  </Text>
                )}
              </View>
            </View>
          )}



          {/* Document Upload Section */}
          {formData.vehicleType && (
            <>
              {/* Required Documents */}
              {getDocumentTypesForVehicle(formData.vehicleType).filter(doc => !doc.isInput && doc.required).length > 0 && (
                <View style={styles.section}>
                  <View style={styles.sectionHeader}>
                    <View style={[styles.sectionIcon, { backgroundColor: '#FEE2E2' }]}>
                      <Ionicons name="document-text-outline" size={20} color="#EF4444" />
                    </View>
                    <View style={{ flex: 1 }}>
                      <Text style={styles.sectionTitle}>Required Documents</Text>
                      <Text style={styles.sectionSubtitle}>These documents are mandatory</Text>
                    </View>
                  </View>

                  {getDocumentTypesForVehicle(formData.vehicleType)
                    .filter(doc => !doc.isInput && doc.required)
                    .map((doc, index) => {
                      const isCompleted = formData.documents?.[doc.key as keyof typeof formData.documents];
                      return (
                        <View
                          key={doc.key}
                          style={{
                            marginBottom: index === getDocumentTypesForVehicle(formData.vehicleType).filter(
                              doc => !doc.isInput && doc.required
                            ).length - 1 ? 0 : 20,
                          }}
                        >
                          <Text style={{
                            fontSize: 16,
                            fontWeight: '600',
                            color: '#374151',
                            marginBottom: 12,
                          }}>
                            {doc.label} <Text style={{ color: '#EF4444' }}>*</Text>
                          </Text>

                          <TouchableOpacity
                            onPress={() => handleImagePicker(doc.key)}
                            style={[
                              styles.uploadButton,
                              {
                                borderColor: errors[doc.key] ? '#EF4444' : isCompleted ? '#10B981' : '#D1D5DB',
                                backgroundColor: isCompleted ? '#F0FDF4' : '#FAFAFA',
                              },
                              errors[doc.key] && styles.uploadButtonError
                            ]}
                            activeOpacity={0.7}
                          >
                            {isCompleted ? (
                              <View style={styles.uploadedContainer}>
                                <Image
                                  source={{ uri: formData.documents[doc.key as keyof typeof formData.documents] }}
                                  style={styles.uploadedImage}
                                  resizeMode="cover"
                                />
                                <View style={styles.uploadedInfo}>
                                  <View style={{
                                    flexDirection: 'row',
                                    alignItems: 'center',
                                    marginBottom: 4,
                                  }}>
                                    <Ionicons name="checkmark-circle" size={20} color="#10B981" />
                                    <Text style={styles.uploadedText}>Document Uploaded</Text>
                                  </View>
                                  <Text style={{
                                    fontSize: 12,
                                    color: '#6B7280',
                                  }}>
                                    Tap to change or retake
                                  </Text>
                                </View>
                              </View>
                            ) : (
                              <View style={styles.uploadPlaceholder}>
                                <View style={{
                                  width: 48,
                                  height: 48,
                                  backgroundColor: '#F3F4F6',
                                  borderRadius: 24,
                                  justifyContent: 'center',
                                  alignItems: 'center',
                                  marginBottom: 12,
                                }}>
                                  <Ionicons name="camera-outline" size={24} color="#6B7280" />
                                </View>
                                <Text style={styles.uploadText}>Upload {doc.label}</Text>
                                <Text style={styles.uploadSubtext}>Take photo or choose from gallery</Text>
                              </View>
                            )}
                          </TouchableOpacity>

                          {errors[doc.key] && (
                            <Text style={{
                              fontSize: 12,
                              color: '#EF4444',
                              marginTop: 8,
                            }}>
                              {errors[doc.key]}
                            </Text>
                          )}
                        </View>
                      );
                    })}
                </View>
              )}

              {/* Optional Documents */}
              {getDocumentTypesForVehicle(formData.vehicleType).filter(doc => !doc.isInput && !doc.required).length > 0 && (
                <View style={styles.section}>
                  <View style={styles.sectionHeader}>
                    <View style={[styles.sectionIcon, { backgroundColor: '#E0F2FE' }]}>
                      <Ionicons name="document-outline" size={20} color="#0891B2" />
                    </View>
                    <View style={{ flex: 1 }}>
                      <Text style={styles.sectionTitle}>Optional Documents</Text>
                      <Text style={styles.sectionSubtitle}>Upload these to improve your profile</Text>
                    </View>
                  </View>

                  {getDocumentTypesForVehicle(formData.vehicleType)
                    .filter(doc => !doc.isInput && !doc.required)
                    .map((doc, index) => {
                      const isCompleted = formData.documents?.[doc.key as keyof typeof formData.documents];
                      return (
                        <View
                          key={doc.key}
                          style={{
                            marginBottom: index === getDocumentTypesForVehicle(formData.vehicleType).filter(
                              doc => !doc.isInput && !doc.required
                            ).length - 1 ? 0 : 20,
                          }}
                        >
                          <Text style={{
                            fontSize: 16,
                            fontWeight: '600',
                            color: '#374151',
                            marginBottom: 12,
                          }}>
                            {doc.label} <Text style={{ color: '#6B7280', fontWeight: '400' }}>(Optional)</Text>
                          </Text>

                          {doc.description && (
                            <Text style={{
                              fontSize: 14,
                              color: '#6B7280',
                              marginBottom: 12,
                              fontStyle: 'italic',
                            }}>
                              {doc.description}
                            </Text>
                          )}

                          <TouchableOpacity
                            onPress={() => handleImagePicker(doc.key)}
                            style={[
                              styles.uploadButton,
                              {
                                borderColor: errors[doc.key] ? '#EF4444' : isCompleted ? '#10B981' : '#D1D5DB',
                                backgroundColor: isCompleted ? '#F0FDF4' : '#FAFAFA',
                              },
                              errors[doc.key] && styles.uploadButtonError
                            ]}
                            activeOpacity={0.7}
                          >
                            {isCompleted ? (
                              <View style={styles.uploadedContainer}>
                                <Image
                                  source={{ uri: formData.documents[doc.key as keyof typeof formData.documents] }}
                                  style={styles.uploadedImage}
                                  resizeMode="cover"
                                />
                                <View style={styles.uploadedInfo}>
                                  <View style={{
                                    flexDirection: 'row',
                                    alignItems: 'center',
                                    marginBottom: 4,
                                  }}>
                                    <Ionicons name="checkmark-circle" size={20} color="#10B981" />
                                    <Text style={styles.uploadedText}>Document Uploaded</Text>
                                  </View>
                                  <Text style={{
                                    fontSize: 12,
                                    color: '#6B7280',
                                  }}>
                                    Tap to change or retake
                                  </Text>
                                </View>
                              </View>
                            ) : (
                              <View style={styles.uploadPlaceholder}>
                                <View style={{
                                  width: 48,
                                  height: 48,
                                  backgroundColor: '#F3F4F6',
                                  borderRadius: 24,
                                  justifyContent: 'center',
                                  alignItems: 'center',
                                  marginBottom: 12,
                                }}>
                                  <Ionicons name="camera-outline" size={24} color="#6B7280" />
                                </View>
                                <Text style={styles.uploadText}>Upload {doc.label}</Text>
                                <Text style={styles.uploadSubtext}>Take photo or choose from gallery</Text>
                              </View>
                            )}
                          </TouchableOpacity>

                          {errors[doc.key] && (
                            <Text style={{
                              fontSize: 12,
                              color: '#EF4444',
                              marginTop: 8,
                            }}>
                              {errors[doc.key]}
                            </Text>
                          )}
                        </View>
                      );
                    })}
                </View>
              )}
            </>
          )}
        </ScrollView>
      </Animated.View>
    );
  };



  const renderImagePickerModal = () => (
    <Modal
      visible={showImagePicker}
      transparent={true}
      animationType="slide"
      onRequestClose={() => setShowImagePicker(false)}
    >
      <View style={styles.modalOverlay}>
        <View style={styles.modalContent}>
          {/* Handle Bar */}
          <View style={styles.modalHandle} />

          <Text style={styles.modalTitle}>
            Select Image Source
          </Text>

          <TouchableOpacity
            onPress={() => pickImage('camera')}
            style={[styles.modalOption, { backgroundColor: '#FEF3C7' }]}
            activeOpacity={0.8}
          >
            <View style={[styles.modalOptionIcon, { backgroundColor: '#EF4444' }]}>
              <Ionicons name="camera-outline" size={24} color="white" />
            </View>
            <View style={{ flex: 1 }}>
              <Text style={styles.modalOptionText}>
                Take Photo
              </Text>
              <Text style={styles.modalOptionSubtext}>
                Use camera to capture document
              </Text>
            </View>
            <Ionicons name="chevron-forward" size={20} color="#6B7280" />
          </TouchableOpacity>

          <TouchableOpacity
            onPress={() => pickImage('gallery')}
            style={[styles.modalOption, { backgroundColor: '#DBEAFE', marginBottom: 20 }]}
            activeOpacity={0.8}
          >
            <View style={[styles.modalOptionIcon, { backgroundColor: '#3B82F6' }]}>
              <Ionicons name="images-outline" size={24} color="white" />
            </View>
            <View style={{ flex: 1 }}>
              <Text style={styles.modalOptionText}>
                Choose from Gallery
              </Text>
              <Text style={styles.modalOptionSubtext}>
                Select from existing photos
              </Text>
            </View>
            <Ionicons name="chevron-forward" size={20} color="#6B7280" />
          </TouchableOpacity>

          <TouchableOpacity
            onPress={() => setShowImagePicker(false)}
            style={styles.modalCancelButton}
            activeOpacity={0.8}
          >
            <Text style={styles.modalCancelText}>
              Cancel
            </Text>
          </TouchableOpacity>
        </View>
      </View>
    </Modal>
  );

  const renderPaymentMethodsStep = () => {
    return (
      <ScrollView
        style={{ flex: 1 }}
        showsVerticalScrollIndicator={false}
        contentContainerStyle={{ paddingBottom: 100 }}
      >
        {/* Header Section */}
        <View
          style={{
            backgroundColor: "white",
            paddingHorizontal: 20,
            paddingTop: 20,
            paddingBottom: 24,
            borderBottomLeftRadius: 24,
            borderBottomRightRadius: 24,
            shadowColor: "#000",
            shadowOffset: { width: 0, height: 4 },
            shadowOpacity: 0.1,
            shadowRadius: 12,
            elevation: 8,
          }}
        >
          <View style={{ alignItems: "center", marginBottom: 16 }}>
            <Text
              style={{
                fontSize: 28,
                fontWeight: "bold",
                color: "#1F2937",
                marginBottom: 8,
              }}
            >
              Payment Methods
            </Text>
            <Text
              style={{
                fontSize: 16,
                color: "#6B7280",
                textAlign: "center",
                lineHeight: 22,
              }}
            >
              Choose how you'd like to receive your earnings
            </Text>
          </View>
        </View>

        {/* Payment Method Selection */}
        <View
          style={{
            backgroundColor: "white",
            marginHorizontal: 16,
            marginTop: 20,
            borderRadius: 20,
            padding: 20,
            shadowColor: "#000",
            shadowOffset: { width: 0, height: 2 },
            shadowOpacity: 0.1,
            shadowRadius: 8,
            elevation: 4,
          }}
        >
          <View
            style={{
              flexDirection: "row",
              alignItems: "center",
              marginBottom: 20,
            }}
          >
            <View
              style={{
                width: 40,
                height: 40,
                backgroundColor: "#FED7AA",
                borderRadius: 20,
                justifyContent: "center",
                alignItems: "center",
                marginRight: 12,
              }}
            >
              <Ionicons name="wallet-outline" size={20} color="#EF4444" />
            </View>
            <View style={{ flex: 1 }}>
              <Text
                style={{
                  fontSize: 20,
                  fontWeight: "bold",
                  color: "#1F2937",
                  marginBottom: 2,
                }}
              >
                Select Payment Method
              </Text>
              <Text
                style={{
                  fontSize: 14,
                  color: "#6B7280",
                }}
              >
                Choose your preferred payment option
              </Text>
            </View>
          </View>

          {/* Payment Method Cards */}
          <View style={{ gap: 16 }}>
            {PAYMENT_METHOD_OPTIONS.map((method) => {
              const isSelected = formData.bankDetails?.accountType === method.type
              return (
                <TouchableOpacity
                  key={method.type}
                  onPress={() => {
                    updateNestedFormData("bankDetails", "accountType", method.type)
                    // Clear form when switching payment methods
                    setFormData((prev) => ({
                      ...prev,
                      bankDetails: {
                        ...prev.bankDetails,
                        accountType: method.type,
                        accountNumber: "",
                        accountTitle: "",
                        bankName: "",
                        iban: "",
                      },
                    }))
                    setErrors({})

                    // Animate selection
                    Animated.spring(paymentSelectAnim, {
                      toValue: 1,
                      tension: 50,
                      friction: 7,
                      useNativeDriver: true,
                    }).start()
                  }}
                  style={{
                    borderRadius: 16,
                    overflow: "hidden",
                    borderWidth: 2,
                    borderColor: isSelected ? method.color : "#E5E7EB",
                    backgroundColor: isSelected ? `${method.color}10` : "#FFFFFF",
                    transform: isSelected ? [{ scale: 1.02 }] : [{ scale: 1 }],
                  }}
                  activeOpacity={0.8}
                >
                  {/* Selection Indicator */}
                  {isSelected && (
                    <View
                      style={{
                        position: "absolute",
                        top: 12,
                        right: 12,
                        width: 24,
                        height: 24,
                        backgroundColor: method.color,
                        borderRadius: 12,
                        justifyContent: "center",
                        alignItems: "center",
                        zIndex: 1,
                      }}
                    >
                      <Ionicons name="checkmark" size={16} color="white" />
                    </View>
                  )}

                  <View
                    style={{
                      flexDirection: "row",
                      alignItems: "center",
                      padding: 20,
                    }}
                  >
                    {/* Payment Method Icon */}
                    <View
                      style={{
                        width: 60,
                        height: 60,
                        backgroundColor: isSelected ? `${method.color}20` : "#F9FAFB",
                        borderRadius: 30,
                        justifyContent: "center",
                        alignItems: "center",
                        marginRight: 16,
                      }}
                    >
                      <Text style={{ fontSize: 28 }}>{method.emoji}</Text>
                    </View>

                    {/* Payment Method Info */}
                    <View style={{ flex: 1 }}>
                      <Text
                        style={{
                          fontSize: 18,
                          fontWeight: "bold",
                          color: isSelected ? method.color : "#374151",
                          marginBottom: 4,
                        }}
                      >
                        {method.label}
                      </Text>
                      <Text
                        style={{
                          fontSize: 14,
                          color: isSelected ? method.color : "#6B7280",
                          lineHeight: 18,
                        }}
                      >
                        {method.description}
                      </Text>
                    </View>

                    {/* Arrow Icon */}
                    <Ionicons name="chevron-forward" size={20} color={isSelected ? method.color : "#9CA3AF"} />
                  </View>
                </TouchableOpacity>
              )
            })}
          </View>

          {errors.accountType && (
            <Text
              style={{
                fontSize: 12,
                color: "#EF4444",
                marginTop: 12,
                textAlign: "center",
              }}
            >
              {errors.accountType}
            </Text>
          )}
        </View>

        {/* Payment Details Form */}
        {formData.bankDetails?.accountType && (
          <View
            style={{
              backgroundColor: "white",
              marginHorizontal: 16,
              marginTop: 16,
              borderRadius: 20,
              padding: 20,
              shadowColor: "#000",
              shadowOffset: { width: 0, height: 2 },
              shadowOpacity: 0.1,
              shadowRadius: 8,
              elevation: 4,
            }}
          >
            <View
              style={{
                flexDirection: "row",
                alignItems: "center",
                marginBottom: 20,
              }}
            >
              <View
                style={{
                  width: 40,
                  height: 40,
                  backgroundColor: "#DBEAFE",
                  borderRadius: 20,
                  justifyContent: "center",
                  alignItems: "center",
                  marginRight: 12,
                }}
              >
                <Ionicons name="information-circle-outline" size={20} color="#3B82F6" />
              </View>
              <View style={{ flex: 1 }}>
                <Text
                  style={{
                    fontSize: 18,
                    fontWeight: "bold",
                    color: "#1F2937",
                    marginBottom: 2,
                  }}
                >
                  Payment Details
                </Text>
                <Text
                  style={{
                    fontSize: 14,
                    color: "#6B7280",
                  }}
                >
                  Enter your {formData.bankDetails.accountType.replace("_", " ").toLowerCase()} information
                </Text>
              </View>
            </View>

            <View style={{ gap: 16 }}>
              {/* Account Number */}
              <View
                style={{
                  backgroundColor: "#F9FAFB",
                  borderRadius: 12,
                  padding: 16,
                }}
              >
                <Text
                  style={{
                    fontSize: 14,
                    fontWeight: "600",
                    color: "#374151",
                    marginBottom: 8,
                  }}
                >
                  Account Number <Text style={{ color: "#EF4444" }}>*</Text>
                </Text>
                <TextInput
                  placeholder={
                    formData.bankDetails.accountType === BankAccountType.BANK_ACCOUNT
                      ? "Enter bank account number"
                      : "Enter mobile wallet number"
                  }
                  value={formData.bankDetails?.accountNumber || ""}
                  onChangeText={(value: string) => updateNestedFormData("bankDetails", "accountNumber", value)}
                  keyboardType="numeric"
                  style={{
                    backgroundColor: "white",
                    borderWidth: 1,
                    borderColor: errors.accountNumber ? "#EF4444" : "#D1D5DB",
                    borderRadius: 12,
                    paddingHorizontal: 16,
                    paddingVertical: 14,
                    fontSize: 16,
                    color: "#1F2937",
                  }}
                  placeholderTextColor="#9CA3AF"
                />
                {errors.accountNumber && (
                  <Text
                    style={{
                      fontSize: 12,
                      color: "#EF4444",
                      marginTop: 6,
                    }}
                  >
                    {errors.accountNumber}
                  </Text>
                )}
              </View>

              {/* Account Title */}
              <View
                style={{
                  backgroundColor: "#F9FAFB",
                  borderRadius: 12,
                  padding: 16,
                }}
              >
                <Text
                  style={{
                    fontSize: 14,
                    fontWeight: "600",
                    color: "#374151",
                    marginBottom: 8,
                  }}
                >
                  Account Title <Text style={{ color: "#EF4444" }}>*</Text>
                </Text>
                <TextInput
                  placeholder="Enter account holder name"
                  value={formData.bankDetails?.accountTitle || ""}
                  onChangeText={(value: string) => updateNestedFormData("bankDetails", "accountTitle", value)}
                  style={{
                    backgroundColor: "white",
                    borderWidth: 1,
                    borderColor: errors.accountTitle ? "#EF4444" : "#D1D5DB",
                    borderRadius: 12,
                    paddingHorizontal: 16,
                    paddingVertical: 14,
                    fontSize: 16,
                    color: "#1F2937",
                  }}
                  placeholderTextColor="#9CA3AF"
                />
                {errors.accountTitle && (
                  <Text
                    style={{
                      fontSize: 12,
                      color: "#EF4444",
                      marginTop: 6,
                    }}
                  >
                    {errors.accountTitle}
                  </Text>
                )}
              </View>

              {/* Bank-specific fields */}
              {formData.bankDetails?.accountType === BankAccountType.BANK_ACCOUNT && (
                <>
                  {/* Bank Name */}
                  <View
                    style={{
                      backgroundColor: "#F9FAFB",
                      borderRadius: 12,
                      padding: 16,
                    }}
                  >
                    <Text
                      style={{
                        fontSize: 14,
                        fontWeight: "600",
                        color: "#374151",
                        marginBottom: 8,
                      }}
                    >
                      Bank Name <Text style={{ color: "#EF4444" }}>*</Text>
                    </Text>
                    <TextInput
                      placeholder="e.g., HBL, UBL, MCB, Allied Bank"
                      value={formData.bankDetails?.bankName || ""}
                      onChangeText={(value: string) => updateNestedFormData("bankDetails", "bankName", value)}
                      style={{
                        backgroundColor: "white",
                        borderWidth: 1,
                        borderColor: errors.bankName ? "#EF4444" : "#D1D5DB",
                        borderRadius: 12,
                        paddingHorizontal: 16,
                        paddingVertical: 14,
                        fontSize: 16,
                        color: "#1F2937",
                      }}
                      placeholderTextColor="#9CA3AF"
                    />
                    {errors.bankName && (
                      <Text
                        style={{
                          fontSize: 12,
                          color: "#EF4444",
                          marginTop: 6,
                        }}
                      >
                        {errors.bankName}
                      </Text>
                    )}
                  </View>

                  {/* IBAN */}
                  <View
                    style={{
                      backgroundColor: "#F9FAFB",
                      borderRadius: 12,
                      padding: 16,
                    }}
                  >
                    <Text
                      style={{
                        fontSize: 14,
                        fontWeight: "600",
                        color: "#374151",
                        marginBottom: 8,
                      }}
                    >
                      IBAN <Text style={{ color: "#EF4444" }}>*</Text>
                    </Text>
                    <TextInput
                      placeholder="************************"
                      value={formData.bankDetails?.iban || ""}
                      onChangeText={(value: string) => updateNestedFormData("bankDetails", "iban", value.toUpperCase())}
                      autoCapitalize="characters"
                      style={{
                        backgroundColor: "white",
                        borderWidth: 1,
                        borderColor: errors.iban ? "#EF4444" : "#D1D5DB",
                        borderRadius: 12,
                        paddingHorizontal: 16,
                        paddingVertical: 14,
                        fontSize: 16,
                        color: "#1F2937",
                      }}
                      placeholderTextColor="#9CA3AF"
                    />
                    {errors.iban && (
                      <Text
                        style={{
                          fontSize: 12,
                          color: "#EF4444",
                          marginTop: 6,
                        }}
                      >
                        {errors.iban}
                      </Text>
                    )}
                  </View>
                </>
              )}
            </View>
          </View>
        )}
      </ScrollView>
    )
  };

  const renderSummaryStep = () => {
    const getVehicleOption = (type: VehicleType) => VEHICLE_OPTIONS.find((v) => v.type === type)
    const getPaymentOption = (type: BankAccountType) => PAYMENT_METHOD_OPTIONS.find((p) => p.type === type)

    return (
      <ScrollView
        style={{ flex: 1 }}
        showsVerticalScrollIndicator={false}
        contentContainerStyle={{ paddingBottom: 100 }}
      >
        {/* Header Section */}
        <View
          style={{
            backgroundColor: "white",
            paddingHorizontal: 20,
            paddingTop: 20,
            paddingBottom: 24,
            borderBottomLeftRadius: 24,
            borderBottomRightRadius: 24,
            shadowColor: "#000",
            shadowOffset: { width: 0, height: 4 },
            shadowOpacity: 0.1,
            shadowRadius: 12,
            elevation: 8,
          }}
        >
          <View style={{ alignItems: "center", marginBottom: 16 }}>
            <View
              style={{
                width: 60,
                height: 60,
                backgroundColor: "#EF4444",
                borderRadius: 30,
                justifyContent: "center",
                alignItems: "center",
                marginBottom: 12,
              }}
            >
              <Ionicons name="checkmark-circle-outline" size={32} color="white" />
            </View>
            <Text
              style={{
                fontSize: 28,
                fontWeight: "bold",
                color: "#1F2937",
                marginBottom: 8,
              }}
            >
              Review & Confirm
            </Text>
            <Text
              style={{
                fontSize: 16,
                color: "#6B7280",
                textAlign: "center",
                lineHeight: 22,
              }}
            >
              Please review your information before submitting
            </Text>
          </View>
        </View>

        {/* Personal Information Summary */}
        <View
          style={{
            backgroundColor: "white",
            marginHorizontal: 16,
            marginTop: 20,
            borderRadius: 20,
            padding: 20,
            shadowColor: "#000",
            shadowOffset: { width: 0, height: 2 },
            shadowOpacity: 0.1,
            shadowRadius: 8,
            elevation: 4,
          }}
        >
          <View
            style={{
              flexDirection: "row",
              alignItems: "center",
              marginBottom: 20,
            }}
          >
            <View
              style={{
                width: 40,
                height: 40,
                backgroundColor: "#DBEAFE",
                borderRadius: 20,
                justifyContent: "center",
                alignItems: "center",
                marginRight: 12,
              }}
            >
              <Ionicons name="person-outline" size={20} color="#3B82F6" />
            </View>
            <View style={{ flex: 1 }}>
              <Text
                style={{
                  fontSize: 18,
                  fontWeight: "bold",
                  color: "#1F2937",
                  marginBottom: 2,
                }}
              >
                Personal Information
              </Text>
              <Text
                style={{
                  fontSize: 14,
                  color: "#6B7280",
                }}
              >
                Your basic details
              </Text>
            </View>
            <TouchableOpacity
              onPress={() => setCurrentStep(1)}
              style={{
                paddingHorizontal: 12,
                paddingVertical: 6,
                backgroundColor: "#F3F4F6",
                borderRadius: 8,
              }}
            >
              <Text style={{ fontSize: 12, color: "#6B7280", fontWeight: "500" }}>Edit</Text>
            </TouchableOpacity>
          </View>

          <View style={{ gap: 12 }}>
            <View style={{ flexDirection: "row", justifyContent: "space-between" }}>
              <Text style={{ fontSize: 14, color: "#6B7280" }}>Full Name</Text>
              <Text style={{ fontSize: 14, color: "#1F2937", fontWeight: "500" }}>
                {formData.firstName} {formData.lastName}
              </Text>
            </View>
            <View style={{ flexDirection: "row", justifyContent: "space-between" }}>
              <Text style={{ fontSize: 14, color: "#6B7280" }}>Email</Text>
              <Text style={{ fontSize: 14, color: "#1F2937", fontWeight: "500" }}>{formData.email}</Text>
            </View>
            <View style={{ flexDirection: "row", justifyContent: "space-between" }}>
              <Text style={{ fontSize: 14, color: "#6B7280" }}>Phone</Text>
              <Text style={{ fontSize: 14, color: "#1F2937", fontWeight: "500" }}>+92 {formData.phoneNumber}</Text>
            </View>
            <View style={{ flexDirection: "row", justifyContent: "space-between" }}>
              <Text style={{ fontSize: 14, color: "#6B7280" }}>CNIC</Text>
              <Text style={{ fontSize: 14, color: "#1F2937", fontWeight: "500" }}>{formData.cnic}</Text>
            </View>
          </View>
        </View>

        {/* Vehicle Information Summary */}
        <View
          style={{
            backgroundColor: "white",
            marginHorizontal: 16,
            marginTop: 16,
            borderRadius: 20,
            padding: 20,
            shadowColor: "#000",
            shadowOffset: { width: 0, height: 2 },
            shadowOpacity: 0.1,
            shadowRadius: 8,
            elevation: 4,
          }}
        >
          <View
            style={{
              flexDirection: "row",
              alignItems: "center",
              marginBottom: 20,
            }}
          >
            <View
              style={{
                width: 40,
                height: 40,
                backgroundColor: "#FED7AA",
                borderRadius: 20,
                justifyContent: "center",
                alignItems: "center",
                marginRight: 12,
              }}
            >
              <Ionicons name="car-outline" size={20} color="#EF4444" />
            </View>
            <View style={{ flex: 1 }}>
              <Text
                style={{
                  fontSize: 18,
                  fontWeight: "bold",
                  color: "#1F2937",
                  marginBottom: 2,
                }}
              >
                Vehicle Information
              </Text>
              <Text
                style={{
                  fontSize: 14,
                  color: "#6B7280",
                }}
              >
                Your delivery vehicle details
              </Text>
            </View>
            <TouchableOpacity
              onPress={() => setCurrentStep(2)}
              style={{
                paddingHorizontal: 12,
                paddingVertical: 6,
                backgroundColor: "#F3F4F6",
                borderRadius: 8,
              }}
            >
              <Text style={{ fontSize: 12, color: "#6B7280", fontWeight: "500" }}>Edit</Text>
            </TouchableOpacity>
          </View>

          {formData.vehicleType && (
            <View
              style={{
                flexDirection: "row",
                alignItems: "center",
                backgroundColor: "#F9FAFB",
                borderRadius: 12,
                padding: 16,
                marginBottom: 16,
              }}
            >
              <Text style={{ fontSize: 32, marginRight: 12 }}>{getVehicleOption(formData.vehicleType)?.emoji}</Text>
              <View style={{ flex: 1 }}>
                <Text
                  style={{
                    fontSize: 16,
                    fontWeight: "bold",
                    color: "#1F2937",
                    marginBottom: 2,
                  }}
                >
                  {getVehicleOption(formData.vehicleType)?.label}
                </Text>
                <Text
                  style={{
                    fontSize: 12,
                    color: "#6B7280",
                  }}
                >
                  {getVehicleOption(formData.vehicleType)?.description}
                </Text>
              </View>
            </View>
          )}

          {formData.vehicleType !== VehicleType.BICYCLE && (
            <View style={{ gap: 12 }}>
              <View style={{ flexDirection: "row", justifyContent: "space-between" }}>
                <Text style={{ fontSize: 14, color: "#6B7280" }}>Plate Number</Text>
                <Text style={{ fontSize: 14, color: "#1F2937", fontWeight: "500" }}>
                  {formData.vehicleDetails?.plateNumber || "Not provided"}
                </Text>
              </View>
            </View>
          )}

          {/* Document Status */}
          <View style={{ marginTop: 16 }}>
            <Text
              style={{
                fontSize: 14,
                fontWeight: "600",
                color: "#374151",
                marginBottom: 12,
              }}
            >
              Document Status
            </Text>
            {formData.vehicleType &&
              getDocumentTypesForVehicle(formData.vehicleType).map((doc) => {
                const isCompleted = doc.isInput
                  ? formData.vehicleDetails?.plateNumber
                  : formData.documents?.[doc.key as keyof typeof formData.documents]
                return (
                  <View
                    key={doc.key}
                    style={{
                      flexDirection: "row",
                      alignItems: "center",
                      justifyContent: "space-between",
                      paddingVertical: 8,
                    }}
                  >
                    <Text style={{ fontSize: 12, color: "#6B7280", flex: 1 }}>{doc.label}</Text>
                    <View
                      style={{
                        flexDirection: "row",
                        alignItems: "center",
                      }}
                    >
                      <Ionicons
                        name={isCompleted ? "checkmark-circle" : "ellipse-outline"}
                        size={16}
                        color={isCompleted ? "#10B981" : "#D1D5DB"}
                      />
                      <Text
                        style={{
                          fontSize: 12,
                          color: isCompleted ? "#10B981" : "#6B7280",
                          marginLeft: 4,
                          fontWeight: "500",
                        }}
                      >
                        {isCompleted ? "Completed" : doc.required ? "Required" : "Optional"}
                      </Text>
                    </View>
                  </View>
                )
              })}
          </View>
        </View>

        {/* Payment Information Summary */}
        <View
          style={{
            backgroundColor: "white",
            marginHorizontal: 16,
            marginTop: 16,
            borderRadius: 20,
            padding: 20,
            shadowColor: "#000",
            shadowOffset: { width: 0, height: 2 },
            shadowOpacity: 0.1,
            shadowRadius: 8,
            elevation: 4,
          }}
        >
          <View
            style={{
              flexDirection: "row",
              alignItems: "center",
              marginBottom: 20,
            }}
          >
            <View
              style={{
                width: 40,
                height: 40,
                backgroundColor: "#FEE2E2",
                borderRadius: 20,
                justifyContent: "center",
                alignItems: "center",
                marginRight: 12,
              }}
            >
              <Ionicons name="card-outline" size={20} color="#EF4444" />
            </View>
            <View style={{ flex: 1 }}>
              <Text
                style={{
                  fontSize: 18,
                  fontWeight: "bold",
                  color: "#1F2937",
                  marginBottom: 2,
                }}
              >
                Payment Information
              </Text>
              <Text
                style={{
                  fontSize: 14,
                  color: "#6B7280",
                }}
              >
                How you'll receive earnings
              </Text>
            </View>
            <TouchableOpacity
              onPress={() => setCurrentStep(3)}
              style={{
                paddingHorizontal: 12,
                paddingVertical: 6,
                backgroundColor: "#F3F4F6",
                borderRadius: 8,
              }}
            >
              <Text style={{ fontSize: 12, color: "#6B7280", fontWeight: "500" }}>Edit</Text>
            </TouchableOpacity>
          </View>

          {formData.bankDetails?.accountType && (
            <View
              style={{
                flexDirection: "row",
                alignItems: "center",
                backgroundColor: "#F9FAFB",
                borderRadius: 12,
                padding: 16,
                marginBottom: 16,
              }}
            >
              <Text style={{ fontSize: 32, marginRight: 12 }}>
                {getPaymentOption(formData.bankDetails.accountType)?.emoji}
              </Text>
              <View style={{ flex: 1 }}>
                <Text
                  style={{
                    fontSize: 16,
                    fontWeight: "bold",
                    color: "#1F2937",
                    marginBottom: 2,
                  }}
                >
                  {getPaymentOption(formData.bankDetails.accountType)?.label}
                </Text>
                <Text
                  style={{
                    fontSize: 12,
                    color: "#6B7280",
                  }}
                >
                  {getPaymentOption(formData.bankDetails.accountType)?.description}
                </Text>
              </View>
            </View>
          )}

          <View style={{ gap: 12 }}>
            <View style={{ flexDirection: "row", justifyContent: "space-between" }}>
              <Text style={{ fontSize: 14, color: "#6B7280" }}>Account Number</Text>
              <Text style={{ fontSize: 14, color: "#1F2937", fontWeight: "500" }}>
                {formData.bankDetails?.accountNumber || "Not provided"}
              </Text>
            </View>
            <View style={{ flexDirection: "row", justifyContent: "space-between" }}>
              <Text style={{ fontSize: 14, color: "#6B7280" }}>Account Title</Text>
              <Text style={{ fontSize: 14, color: "#1F2937", fontWeight: "500" }}>
                {formData.bankDetails?.accountTitle || "Not provided"}
              </Text>
            </View>
            {formData.bankDetails?.accountType === BankAccountType.BANK_ACCOUNT && (
              <>
                <View style={{ flexDirection: "row", justifyContent: "space-between" }}>
                  <Text style={{ fontSize: 14, color: "#6B7280" }}>Bank Name</Text>
                  <Text style={{ fontSize: 14, color: "#1F2937", fontWeight: "500" }}>
                    {formData.bankDetails?.bankName || "Not provided"}
                  </Text>
                </View>
                <View style={{ flexDirection: "row", justifyContent: "space-between" }}>
                  <Text style={{ fontSize: 14, color: "#6B7280" }}>IBAN</Text>
                  <Text style={{ fontSize: 14, color: "#1F2937", fontWeight: "500" }}>
                    {formData.bankDetails?.iban || "Not provided"}
                  </Text>
                </View>
              </>
            )}
          </View>
        </View>

        {/* Terms and Conditions */}
        <View
          style={{
            backgroundColor: "white",
            marginHorizontal: 16,
            marginTop: 16,
            borderRadius: 20,
            padding: 20,
            shadowColor: "#000",
            shadowOffset: { width: 0, height: 2 },
            shadowOpacity: 0.1,
            shadowRadius: 8,
            elevation: 4,
          }}
        >
          <View
            style={{
              flexDirection: "row",
              alignItems: "center",
              marginBottom: 20,
            }}
          >
            <View
              style={{
                width: 40,
                height: 40,
                backgroundColor: "#F0FDF4",
                borderRadius: 20,
                justifyContent: "center",
                alignItems: "center",
                marginRight: 12,
              }}
            >
              <Ionicons name="document-text-outline" size={20} color="#10B981" />
            </View>
            <View style={{ flex: 1 }}>
              <Text
                style={{
                  fontSize: 18,
                  fontWeight: "bold",
                  color: "#1F2937",
                  marginBottom: 2,
                }}
              >
                Terms & Conditions
              </Text>
              <Text
                style={{
                  fontSize: 14,
                  color: "#6B7280",
                }}
              >
                Agreement and policies
              </Text>
            </View>
          </View>

          <TouchableOpacity
            onPress={() => handleInputChange('agreeToTerms', !formData.agreeToTerms)}
            style={{
              flexDirection: "row",
              alignItems: "center",
              backgroundColor: "#F9FAFB",
              borderRadius: 12,
              padding: 16,
            }}
          >
            <View
              style={{
                width: 24,
                height: 24,
                borderRadius: 6,
                borderWidth: 2,
                borderColor: formData.agreeToTerms ? "#EF4444" : "#D1D5DB",
                backgroundColor: formData.agreeToTerms ? "#EF4444" : "transparent",
                justifyContent: "center",
                alignItems: "center",
                marginRight: 12,
              }}
            >
              {formData.agreeToTerms && (
                <Ionicons name="checkmark" size={16} color="white" />
              )}
            </View>
            <Text
              style={{
                fontSize: 14,
                color: "#374151",
                flex: 1,
                lineHeight: 20,
              }}
            >
              I agree to the{" "}
              <Text style={{ color: "#EF4444", fontWeight: "600" }}>Terms of Service</Text> and{" "}
              <Text style={{ color: "#EF4444", fontWeight: "600" }}>Privacy Policy</Text>
            </Text>
          </TouchableOpacity>

          {errors.terms && (
            <Text
              style={{
                fontSize: 12,
                color: "#EF4444",
                marginTop: 12,
                textAlign: "center",
              }}
            >
              {errors.terms}
            </Text>
          )}
        </View>
      </ScrollView>
    )
  };

  return (
    <View style={{ flex: 1, backgroundColor: '#FFF7ED' }}>
      <SafeAreaView style={{ flex: 1 }}>
        {/* Progress Header */}
        {renderProgressHeader()}

        <KeyboardAvoidingView
          behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
          className="flex-1"
        >
          <ScrollView className="flex-1" showsVerticalScrollIndicator={false}>
            {currentStep === 1 && renderPersonalInfoStep()}
            {currentStep === 2 && renderVehicleDetailsStep()}
            {currentStep === 3 && renderPaymentMethodsStep()}
            {currentStep === 4 && renderSummaryStep()}
          </ScrollView>

          {/* Navigation Buttons */}
          <View className="p-4 bg-white/90 backdrop-blur-sm">
            <Button
              title={currentStep === STEPS.length ? 'Complete Registration' : 'Continue'}
              onPress={handleNext}
              loading={isLoading}
              disabled={isLoading}
              variant="primary"
              size="lg"
              fullWidth
              rightIcon={
                currentStep === STEPS.length ? (
                  <Ionicons name="checkmark-circle-outline" size={20} color="white" />
                ) : (
                  <Ionicons name="arrow-forward" size={20} color="white" />
                )
              }
            />
            {currentStep > 1 && (
              <TouchableOpacity onPress={handleBack} className="py-3 mt-2">
                <Text className="text-center text-gray-600 font-medium">Previous</Text>
              </TouchableOpacity>
            )}

            {/* Sign In Link */}
            <View style={{
              flexDirection: 'row',
              alignItems: 'center',
              justifyContent: 'center',
              marginTop: 16,
            }}>
              <Text style={{
                color: '#6B7280',
                fontSize: 14,
              }}>
                Already have an account?{' '}
              </Text>
              <TouchableOpacity
                onPress={() => navigation?.navigate('Login')}
              >
                <Text style={{
                  color: '#EF4444',
                  fontSize: 14,
                  fontWeight: '600',
                }}>
                  Sign In
                </Text>
              </TouchableOpacity>
            </View>
          </View>
        </KeyboardAvoidingView>

        {/* Image Picker Modal */}
        {renderImagePickerModal()}

        {/* Country Code Picker Modal */}
        {renderCountryCodePicker()}
      </SafeAreaView>
    </View>
  );
};

export default RegistrationScreen;
