import * as SecureStore from 'expo-secure-store';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { 
  LoginCredentials, 
  PhoneLoginCredentials, 
  AuthTokens, 
  AuthUser, 
  LoginResponse 
} from '../../types/auth';
import { STORAGE_KEYS } from '../../utils/constants';

// Demo accounts for testing
const DEMO_ACCOUNTS: {
  email: Record<string, { password: string; user: any }>;
  phone: Record<string, { verificationCode: string; user: any }>;
} = {
  email: {
    '<EMAIL>': {
      password: 'demo1234',
      user: {
        id: 'demo-rider-001',
        email: '<EMAIL>',
        firstName: 'Demo',
        lastName: 'Rider',
        phoneNumber: '******-0123',
        profilePicture: null,
        isEmailVerified: true,
        isPhoneVerified: true,
        createdAt: '2024-01-01T00:00:00Z',
        updatedAt: '2024-01-01T00:00:00Z',
      }
    },
    '<EMAIL>': {
      password: 'rider1234',
      user: {
        id: 'demo-rider-002',
        email: '<EMAIL>',
        firstName: 'John',
        lastName: '<PERSON>',
        phoneNumber: '******-0456',
        profilePicture: null,
        isEmailVerified: true,
        isPhoneVerified: true,
        createdAt: '2024-01-01T00:00:00Z',
        updatedAt: '2024-01-01T00:00:00Z',
      }
    }
  },
  phone: {
    '******-0123': {
      verificationCode: '123456',
      user: {
        id: 'demo-rider-001',
        email: '<EMAIL>',
        firstName: 'Demo',
        lastName: 'Rider',
        phoneNumber: '******-0123',
        profilePicture: null,
        isEmailVerified: true,
        isPhoneVerified: true,
        createdAt: '2024-01-01T00:00:00Z',
        updatedAt: '2024-01-01T00:00:00Z',
      }
    }
  }
};

class MockAuthService {
  // Simulate network delay
  private async delay(ms: number = 1000): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  // Generate mock tokens
  private generateTokens(): AuthTokens {
    const now = Date.now();
    const expiresAt = now + (24 * 60 * 60 * 1000); // 24 hours

    return {
      accessToken: `mock_access_token_${now}`,
      refreshToken: `mock_refresh_token_${now}`,
      expiresAt,
    };
  }

  // Store tokens securely
  async storeTokens(tokens: AuthTokens): Promise<void> {
    try {
      await SecureStore.setItemAsync(STORAGE_KEYS.AUTH_TOKEN, tokens.accessToken);
      await SecureStore.setItemAsync(STORAGE_KEYS.REFRESH_TOKEN, tokens.refreshToken);
      await AsyncStorage.setItem('token_expires_at', tokens.expiresAt.toString());
    } catch (error) {
      console.error('Error storing tokens:', error);
      throw new Error('Failed to store authentication tokens');
    }
  }

  // Retrieve tokens
  async getTokens(): Promise<AuthTokens | null> {
    try {
      const accessToken = await SecureStore.getItemAsync(STORAGE_KEYS.AUTH_TOKEN);
      const refreshToken = await SecureStore.getItemAsync(STORAGE_KEYS.REFRESH_TOKEN);
      const expiresAt = await AsyncStorage.getItem('token_expires_at');

      if (!accessToken || !refreshToken || !expiresAt) {
        return null;
      }

      return {
        accessToken,
        refreshToken,
        expiresAt: parseInt(expiresAt, 10),
      };
    } catch (error) {
      console.error('Error retrieving tokens:', error);
      return null;
    }
  }

  // Store user data
  async storeUserData(user: AuthUser): Promise<void> {
    try {
      await AsyncStorage.setItem(STORAGE_KEYS.USER_DATA, JSON.stringify(user));
    } catch (error) {
      console.error('Error storing user data:', error);
      throw new Error('Failed to store user data');
    }
  }

  // Retrieve user data
  async getUserData(): Promise<AuthUser | null> {
    try {
      const userData = await AsyncStorage.getItem(STORAGE_KEYS.USER_DATA);
      return userData ? JSON.parse(userData) : null;
    } catch (error) {
      console.error('Error retrieving user data:', error);
      return null;
    }
  }

  // Clear all auth data
  async clearAuthData(): Promise<void> {
    try {
      await SecureStore.deleteItemAsync(STORAGE_KEYS.AUTH_TOKEN);
      await SecureStore.deleteItemAsync(STORAGE_KEYS.REFRESH_TOKEN);
      await AsyncStorage.removeItem('token_expires_at');
      await AsyncStorage.removeItem(STORAGE_KEYS.USER_DATA);
    } catch (error) {
      console.error('Error clearing auth data:', error);
    }
  }

  // Check if token is expired
  isTokenExpired(expiresAt: number): boolean {
    return Date.now() >= expiresAt;
  }

  // Mock login with email and password
  async login(credentials: LoginCredentials): Promise<LoginResponse> {
    await this.delay(1500); // Simulate network delay

    const account = DEMO_ACCOUNTS.email[credentials.email.toLowerCase()];
    
    if (!account || account.password !== credentials.password) {
      throw new Error('Invalid email or password. Try <EMAIL> with password: demo1234');
    }

    const tokens = this.generateTokens();
    await this.storeTokens(tokens);
    await this.storeUserData(account.user);

    return {
      user: account.user,
      tokens,
      message: 'Login successful',
    };
  }

  // Mock login with phone number
  async loginWithPhone(credentials: PhoneLoginCredentials): Promise<LoginResponse> {
    await this.delay(1500); // Simulate network delay

    const account = DEMO_ACCOUNTS.phone[credentials.phoneNumber];
    
    if (!account || account.verificationCode !== credentials.verificationCode) {
      throw new Error('Invalid phone number or verification code. Try ******-0123 with code: 123456');
    }

    const tokens = this.generateTokens();
    await this.storeTokens(tokens);
    await this.storeUserData(account.user);

    return {
      user: account.user,
      tokens,
      message: 'Login successful',
    };
  }

  // Mock send verification code
  async sendVerificationCode(phoneNumber: string): Promise<void> {
    await this.delay(1000); // Simulate network delay

    if (!DEMO_ACCOUNTS.phone[phoneNumber]) {
      throw new Error('Phone number not found. Try ******-0123');
    }

    // In a real app, this would send an SMS
    console.log(`Mock verification code sent to ${phoneNumber}: 123456`);
  }

  // Mock refresh token
  async refreshToken(): Promise<AuthTokens> {
    await this.delay(500); // Simulate network delay

    const tokens = await this.getTokens();
    
    if (!tokens?.refreshToken) {
      throw new Error('No refresh token available');
    }

    const newTokens = this.generateTokens();
    await this.storeTokens(newTokens);
    return newTokens;
  }

  // Mock logout
  async logout(): Promise<void> {
    await this.delay(500); // Simulate network delay
    await this.clearAuthData();
  }

  // Check authentication status
  async isAuthenticated(): Promise<boolean> {
    try {
      const tokens = await this.getTokens();
      const userData = await this.getUserData();

      if (!tokens || !userData) {
        return false;
      }

      // Check if token is expired
      if (this.isTokenExpired(tokens.expiresAt)) {
        try {
          await this.refreshToken();
          return true;
        } catch {
          return false;
        }
      }

      return true;
    } catch (error) {
      console.error('Authentication check error:', error);
      return false;
    }
  }

  // Get current user
  async getCurrentUser(): Promise<AuthUser | null> {
    try {
      const isAuth = await this.isAuthenticated();
      if (!isAuth) {
        return null;
      }

      return await this.getUserData();
    } catch (error) {
      console.error('Get current user error:', error);
      return null;
    }
  }

  // Mock forgot password
  async forgotPassword(email: string): Promise<void> {
    await this.delay(1000); // Simulate network delay

    if (!DEMO_ACCOUNTS.email[email.toLowerCase()]) {
      throw new Error('Email not found. Try <EMAIL>');
    }

    // In a real app, this would send an email
    console.log(`Mock password reset email sent to ${email}`);
  }

  // Mock reset password
  async resetPassword(token: string, newPassword: string): Promise<void> {
    await this.delay(1000); // Simulate network delay
    
    // In a real app, this would validate the token and update the password
    console.log('Mock password reset successful');
  }

  // Get demo account info
  getDemoAccounts() {
    return {
      email: Object.keys(DEMO_ACCOUNTS.email).map(email => ({
        email,
        password: DEMO_ACCOUNTS.email[email].password,
      })),
      phone: Object.keys(DEMO_ACCOUNTS.phone).map(phone => ({
        phoneNumber: phone,
        verificationCode: DEMO_ACCOUNTS.phone[phone].verificationCode,
      })),
    };
  }
}

export const mockAuthService = new MockAuthService();
