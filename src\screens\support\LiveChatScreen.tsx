import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  FlatList,
  Alert,
  KeyboardAvoidingView,
  Platform,
  Image,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import * as ImagePicker from 'expo-image-picker';
import { useNavigation } from '@react-navigation/native';
import { ChatMessage, ChatSession, MessageType, MessageStatus, ChatStatus } from '../../types/support';

const LiveChatScreen: React.FC = () => {
  const navigation = useNavigation();
  const flatListRef = useRef<FlatList>(null);
  
  // State
  const [message, setMessage] = useState('');
  const [messages, setMessages] = useState<ChatMessage[]>([]);
  const [chatSession, setChatSession] = useState<ChatSession | null>(null);
  const [isTyping, setIsTyping] = useState(false);
  const [adminTyping, setAdminTyping] = useState(false);
  const [loading, setLoading] = useState(false);

  // Mock chat session
  useEffect(() => {
    initializeChat();
  }, []);

  const initializeChat = () => {
    const mockSession: ChatSession = {
      id: '1',
      riderId: 'rider-1',
      adminId: 'admin-1',
      adminName: 'Sarah Khan',
      subject: 'General Support',
      status: ChatStatus.ACTIVE,
      priority: 'normal',
      messages: [
        {
          id: '1',
          chatId: '1',
          senderId: 'admin-1',
          senderName: 'Sarah Khan',
          senderRole: 'admin',
          type: MessageType.TEXT,
          content: 'Hello! I\'m Sarah from FoodWay support. How can I help you today?',
          status: MessageStatus.READ,
          timestamp: new Date(Date.now() - 300000).toISOString(),
        },
        {
          id: '2',
          chatId: '1',
          senderId: 'system',
          senderName: 'System',
          senderRole: 'system',
          type: MessageType.SYSTEM,
          content: 'Chat session started. Average response time: 2 minutes',
          status: MessageStatus.READ,
          timestamp: new Date(Date.now() - 280000).toISOString(),
        },
      ],
      createdAt: new Date(Date.now() - 300000).toISOString(),
      updatedAt: new Date().toISOString(),
      tags: ['general'],
      lastMessageAt: new Date(Date.now() - 300000).toISOString(),
      unreadCount: 0,
    };

    setChatSession(mockSession);
    setMessages(mockSession.messages);
  };

  const sendMessage = async () => {
    if (!message.trim() || !chatSession) return;

    const newMessage: ChatMessage = {
      id: Date.now().toString(),
      chatId: chatSession.id,
      senderId: 'rider-1',
      senderName: 'Muhammad Ahmed',
      senderRole: 'rider',
      type: MessageType.TEXT,
      content: message.trim(),
      status: MessageStatus.SENDING,
      timestamp: new Date().toISOString(),
    };

    setMessages(prev => [...prev, newMessage]);
    setMessage('');
    setIsTyping(true);

    // Simulate sending
    setTimeout(() => {
      setMessages(prev =>
        prev.map(msg =>
          msg.id === newMessage.id
            ? { ...msg, status: MessageStatus.SENT }
            : msg
        )
      );
      setIsTyping(false);
      
      // Simulate admin response
      setTimeout(() => {
        setAdminTyping(true);
        setTimeout(() => {
          const adminResponse: ChatMessage = {
            id: (Date.now() + 1).toString(),
            chatId: chatSession.id,
            senderId: 'admin-1',
            senderName: 'Sarah Khan',
            senderRole: 'admin',
            type: MessageType.TEXT,
            content: getAdminResponse(newMessage.content),
            status: MessageStatus.READ,
            timestamp: new Date().toISOString(),
          };
          setMessages(prev => [...prev, adminResponse]);
          setAdminTyping(false);
        }, 2000);
      }, 1000);
    }, 1000);
  };

  const getAdminResponse = (userMessage: string): string => {
    const lowerMessage = userMessage.toLowerCase();
    
    if (lowerMessage.includes('payment') || lowerMessage.includes('earning')) {
      return 'I understand you have a question about payments. Let me check your account details. Can you please provide your last order ID?';
    } else if (lowerMessage.includes('order') || lowerMessage.includes('delivery')) {
      return 'I see you\'re asking about an order. Could you please share the order number so I can look into this for you?';
    } else if (lowerMessage.includes('app') || lowerMessage.includes('technical')) {
      return 'I\'m sorry to hear you\'re experiencing technical issues. Can you tell me what specific problem you\'re facing with the app?';
    } else {
      return 'Thank you for reaching out. I\'m here to help you with any questions or concerns. Could you please provide more details about your issue?';
    }
  };

  const handleImagePicker = async () => {
    const { status } = await ImagePicker.requestMediaLibraryPermissionsAsync();
    if (status !== 'granted') {
      Alert.alert('Permission Required', 'Please grant camera roll permissions to send images.');
      return;
    }

    Alert.alert(
      'Send Image',
      'Choose an option',
      [
        { text: 'Camera', onPress: () => openCamera() },
        { text: 'Gallery', onPress: () => openGallery() },
        { text: 'Cancel', style: 'cancel' },
      ]
    );
  };

  const openCamera = async () => {
    const result = await ImagePicker.launchCameraAsync({
      mediaTypes: ImagePicker.MediaTypeOptions.Images,
      allowsEditing: true,
      aspect: [4, 3],
      quality: 0.8,
    });

    if (!result.canceled && result.assets[0]) {
      sendImageMessage(result.assets[0].uri);
    }
  };

  const openGallery = async () => {
    const result = await ImagePicker.launchImageLibraryAsync({
      mediaTypes: ImagePicker.MediaTypeOptions.Images,
      allowsEditing: true,
      aspect: [4, 3],
      quality: 0.8,
    });

    if (!result.canceled && result.assets[0]) {
      sendImageMessage(result.assets[0].uri);
    }
  };

  const sendImageMessage = (imageUri: string) => {
    if (!chatSession) return;

    const imageMessage: ChatMessage = {
      id: Date.now().toString(),
      chatId: chatSession.id,
      senderId: 'rider-1',
      senderName: 'Muhammad Ahmed',
      senderRole: 'rider',
      type: MessageType.IMAGE,
      content: imageUri,
      status: MessageStatus.SENDING,
      timestamp: new Date().toISOString(),
    };

    setMessages(prev => [...prev, imageMessage]);

    // Simulate sending
    setTimeout(() => {
      setMessages(prev =>
        prev.map(msg =>
          msg.id === imageMessage.id
            ? { ...msg, status: MessageStatus.SENT }
            : msg
        )
      );
    }, 1000);
  };

  const endChat = () => {
    Alert.alert(
      'End Chat',
      'Are you sure you want to end this chat session?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'End Chat',
          style: 'destructive',
          onPress: () => {
            Alert.alert(
              'Rate Support',
              'How would you rate your support experience?',
              [
                { text: '⭐', onPress: () => submitRating(1) },
                { text: '⭐⭐', onPress: () => submitRating(2) },
                { text: '⭐⭐⭐', onPress: () => submitRating(3) },
                { text: '⭐⭐⭐⭐', onPress: () => submitRating(4) },
                { text: '⭐⭐⭐⭐⭐', onPress: () => submitRating(5) },
              ]
            );
          },
        },
      ]
    );
  };

  const submitRating = (rating: number) => {
    // Submit rating and navigate back
    Alert.alert('Thank You!', 'Your feedback has been submitted.');
    navigation.goBack();
  };

  const renderMessage = ({ item }: { item: ChatMessage }) => {
    const isOwnMessage = item.senderRole === 'rider';
    const isSystemMessage = item.senderRole === 'system';

    if (isSystemMessage) {
      return (
        <View style={{
          alignItems: 'center',
          marginVertical: 8,
        }}>
          <View style={{
            backgroundColor: '#f3f4f6',
            paddingHorizontal: 12,
            paddingVertical: 6,
            borderRadius: 12,
          }}>
            <Text style={{
              fontSize: 12,
              color: '#6b7280',
              textAlign: 'center',
            }}>
              {item.content}
            </Text>
          </View>
        </View>
      );
    }

    return (
      <View style={{
        flexDirection: 'row',
        justifyContent: isOwnMessage ? 'flex-end' : 'flex-start',
        marginVertical: 4,
        marginHorizontal: 16,
      }}>
        {!isOwnMessage && (
          <View style={{
            width: 32,
            height: 32,
            backgroundColor: '#3b82f6',
            borderRadius: 16,
            alignItems: 'center',
            justifyContent: 'center',
            marginRight: 8,
          }}>
            <Text style={{ color: 'white', fontSize: 12, fontWeight: 'bold' }}>
              {item.senderName.charAt(0)}
            </Text>
          </View>
        )}
        
        <View style={{
          maxWidth: '75%',
          backgroundColor: isOwnMessage ? '#3b82f6' : '#f3f4f6',
          borderRadius: 16,
          borderBottomRightRadius: isOwnMessage ? 4 : 16,
          borderBottomLeftRadius: isOwnMessage ? 16 : 4,
          paddingHorizontal: 12,
          paddingVertical: 8,
        }}>
          {!isOwnMessage && (
            <Text style={{
              fontSize: 12,
              color: '#6b7280',
              marginBottom: 2,
            }}>
              {item.senderName}
            </Text>
          )}
          
          {item.type === MessageType.IMAGE ? (
            <Image
              source={{ uri: item.content }}
              style={{
                width: 200,
                height: 150,
                borderRadius: 8,
              }}
              resizeMode="cover"
            />
          ) : (
            <Text style={{
              color: isOwnMessage ? 'white' : '#1f2937',
              fontSize: 16,
            }}>
              {item.content}
            </Text>
          )}
          
          <View style={{
            flexDirection: 'row',
            justifyContent: 'space-between',
            alignItems: 'center',
            marginTop: 4,
          }}>
            <Text style={{
              fontSize: 11,
              color: isOwnMessage ? '#e5e7eb' : '#9ca3af',
            }}>
              {new Date(item.timestamp).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
            </Text>
            
            {isOwnMessage && (
              <Ionicons
                name={
                  item.status === MessageStatus.SENDING ? 'time-outline' :
                  item.status === MessageStatus.SENT ? 'checkmark-outline' :
                  item.status === MessageStatus.DELIVERED ? 'checkmark-done-outline' :
                  'checkmark-done-outline'
                }
                size={12}
                color="#e5e7eb"
                style={{ marginLeft: 4 }}
              />
            )}
          </View>
        </View>
      </View>
    );
  };

  return (
    <SafeAreaView style={{ flex: 1, backgroundColor: 'white' }}>
      {/* Header */}
      <View style={{
        flexDirection: 'row',
        alignItems: 'center',
        paddingHorizontal: 16,
        paddingVertical: 12,
        borderBottomWidth: 1,
        borderBottomColor: '#e5e7eb',
        backgroundColor: 'white',
      }}>
        <TouchableOpacity
          onPress={() => navigation.goBack()}
          style={{
            width: 40,
            height: 40,
            alignItems: 'center',
            justifyContent: 'center',
            marginRight: 8,
          }}
        >
          <Ionicons name="arrow-back" size={24} color="#374151" />
        </TouchableOpacity>

        <View style={{
          width: 40,
          height: 40,
          backgroundColor: '#3b82f6',
          borderRadius: 20,
          alignItems: 'center',
          justifyContent: 'center',
          marginRight: 12,
        }}>
          <Text style={{ color: 'white', fontSize: 16, fontWeight: 'bold' }}>
            {chatSession?.adminName?.charAt(0) || 'S'}
          </Text>
        </View>

        <View style={{ flex: 1 }}>
          <Text style={{
            fontSize: 16,
            fontWeight: '600',
            color: '#1f2937',
          }}>
            {chatSession?.adminName || 'Support Agent'}
          </Text>
          <View style={{ flexDirection: 'row', alignItems: 'center' }}>
            <View style={{
              width: 8,
              height: 8,
              backgroundColor: '#10b981',
              borderRadius: 4,
              marginRight: 6,
            }} />
            <Text style={{
              fontSize: 12,
              color: '#6b7280',
            }}>
              {adminTyping ? 'Typing...' : 'Online'}
            </Text>
          </View>
        </View>

        <TouchableOpacity
          onPress={endChat}
          style={{
            width: 40,
            height: 40,
            alignItems: 'center',
            justifyContent: 'center',
          }}
        >
          <Ionicons name="call-outline" size={20} color="#374151" />
        </TouchableOpacity>

        <TouchableOpacity
          onPress={endChat}
          style={{
            width: 40,
            height: 40,
            alignItems: 'center',
            justifyContent: 'center',
          }}
        >
          <Ionicons name="ellipsis-vertical" size={20} color="#374151" />
        </TouchableOpacity>
      </View>

      {/* Messages */}
      <KeyboardAvoidingView
        style={{ flex: 1 }}
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        keyboardVerticalOffset={Platform.OS === 'ios' ? 90 : 0}
      >
        <FlatList
          ref={flatListRef}
          data={messages}
          renderItem={renderMessage}
          keyExtractor={(item) => item.id}
          style={{ flex: 1 }}
          contentContainerStyle={{ paddingVertical: 8 }}
          onContentSizeChange={() => flatListRef.current?.scrollToEnd({ animated: true })}
          onLayout={() => flatListRef.current?.scrollToEnd({ animated: true })}
        />

        {/* Typing indicator */}
        {adminTyping && (
          <View style={{
            flexDirection: 'row',
            alignItems: 'center',
            paddingHorizontal: 16,
            paddingVertical: 8,
          }}>
            <View style={{
              width: 32,
              height: 32,
              backgroundColor: '#3b82f6',
              borderRadius: 16,
              alignItems: 'center',
              justifyContent: 'center',
              marginRight: 8,
            }}>
              <Text style={{ color: 'white', fontSize: 12, fontWeight: 'bold' }}>
                {chatSession?.adminName?.charAt(0) || 'S'}
              </Text>
            </View>
            <View style={{
              backgroundColor: '#f3f4f6',
              borderRadius: 16,
              paddingHorizontal: 12,
              paddingVertical: 8,
            }}>
              <Text style={{ color: '#6b7280', fontSize: 14 }}>
                Typing...
              </Text>
            </View>
          </View>
        )}

        {/* Input */}
        <View style={{
          flexDirection: 'row',
          alignItems: 'flex-end',
          paddingHorizontal: 16,
          paddingVertical: 12,
          borderTopWidth: 1,
          borderTopColor: '#e5e7eb',
          backgroundColor: 'white',
        }}>
          <TouchableOpacity
            onPress={handleImagePicker}
            style={{
              width: 40,
              height: 40,
              alignItems: 'center',
              justifyContent: 'center',
              marginRight: 8,
            }}
          >
            <Ionicons name="camera-outline" size={24} color="#6b7280" />
          </TouchableOpacity>

          <View style={{
            flex: 1,
            backgroundColor: '#f9fafb',
            borderRadius: 20,
            borderWidth: 1,
            borderColor: '#e5e7eb',
            paddingHorizontal: 16,
            paddingVertical: 8,
            maxHeight: 100,
          }}>
            <TextInput
              value={message}
              onChangeText={setMessage}
              placeholder="Type a message..."
              placeholderTextColor="#9ca3af"
              multiline
              style={{
                fontSize: 16,
                color: '#1f2937',
                minHeight: 24,
              }}
            />
          </View>

          <TouchableOpacity
            onPress={sendMessage}
            disabled={!message.trim() || isTyping}
            style={{
              width: 40,
              height: 40,
              alignItems: 'center',
              justifyContent: 'center',
              marginLeft: 8,
              backgroundColor: message.trim() && !isTyping ? '#3b82f6' : '#e5e7eb',
              borderRadius: 20,
            }}
          >
            <Ionicons
              name={isTyping ? 'hourglass-outline' : 'send'}
              size={20}
              color={message.trim() && !isTyping ? 'white' : '#9ca3af'}
            />
          </TouchableOpacity>
        </View>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
};

export default LiveChatScreen;
