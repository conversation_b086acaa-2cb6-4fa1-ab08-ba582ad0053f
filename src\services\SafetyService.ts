import {
  EmergencyContact,
  SOSSession,
  LocationShare,
  IncidentReport,
  LegalDocument,
  DocumentAcceptance,
  EmergencyType,
  IncidentType,
  IncidentSeverity,
  DocumentType,
  LocationSharingStatus,
} from '../types/safety';

class SafetyService {
  private static instance: SafetyService;
  private baseUrl = 'https://api.foodway.pk/v1';

  static getInstance(): SafetyService {
    if (!SafetyService.instance) {
      SafetyService.instance = new SafetyService();
    }
    return SafetyService.instance;
  }

  // Emergency Contacts API
  async getEmergencyContacts(riderId: string): Promise<EmergencyContact[]> {
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      return [
        {
          id: 'contact-1',
          riderId,
          name: 'Emergency Contact 1',
          phoneNumber: '+92-300-1234567',
          relationship: 'Family',
          isPrimary: true,
          isActive: true,
          createdAt: '2024-01-01T00:00:00Z',
          updatedAt: '2024-01-01T00:00:00Z',
        },
        {
          id: 'contact-2',
          riderId,
          name: 'Emergency Contact 2',
          phoneNumber: '+92-301-7654321',
          relationship: 'Friend',
          isPrimary: false,
          isActive: true,
          createdAt: '2024-01-01T00:00:00Z',
          updatedAt: '2024-01-01T00:00:00Z',
        },
      ];
    } catch (error) {
      console.error('Error fetching emergency contacts:', error);
      throw new Error('Failed to fetch emergency contacts');
    }
  }

  async addEmergencyContact(contact: Omit<EmergencyContact, 'id' | 'createdAt' | 'updatedAt'>): Promise<EmergencyContact> {
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      const newContact: EmergencyContact = {
        ...contact,
        id: `contact-${Date.now()}`,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      };
      
      return newContact;
    } catch (error) {
      console.error('Error adding emergency contact:', error);
      throw new Error('Failed to add emergency contact');
    }
  }

  async updateEmergencyContact(contactId: string, updates: Partial<EmergencyContact>): Promise<EmergencyContact> {
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // Mock updated contact
      const updatedContact: EmergencyContact = {
        id: contactId,
        riderId: 'rider-1',
        name: 'Updated Contact',
        phoneNumber: '+92-300-1234567',
        relationship: 'Family',
        isPrimary: true,
        isActive: true,
        createdAt: '2024-01-01T00:00:00Z',
        updatedAt: new Date().toISOString(),
        ...updates,
      };
      
      return updatedContact;
    } catch (error) {
      console.error('Error updating emergency contact:', error);
      throw new Error('Failed to update emergency contact');
    }
  }

  async deleteEmergencyContact(contactId: string): Promise<void> {
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
    } catch (error) {
      console.error('Error deleting emergency contact:', error);
      throw new Error('Failed to delete emergency contact');
    }
  }

  // SOS Session API
  async startSOSSession(
    riderId: string,
    emergencyType: EmergencyType,
    location: any,
    emergencyContacts: string[]
  ): Promise<SOSSession> {
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      const sosSession: SOSSession = {
        id: `sos-${Date.now()}`,
        riderId,
        emergencyType,
        location,
        contactedNumbers: [
          { number: '15', contactedAt: new Date().toISOString(), responseTime: null },
          { number: '1122', contactedAt: new Date().toISOString(), responseTime: null },
        ],
        startTime: new Date().toISOString(),
        status: 'active',
        adminNotified: true,
        emergencyContactsNotified: emergencyContacts,
      };
      
      return sosSession;
    } catch (error) {
      console.error('Error starting SOS session:', error);
      throw new Error('Failed to start SOS session');
    }
  }

  async endSOSSession(sosId: string, notes?: string): Promise<SOSSession> {
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      const endedSession: SOSSession = {
        id: sosId,
        riderId: 'rider-1',
        emergencyType: EmergencyType.MEDICAL,
        location: { latitude: 0, longitude: 0, address: 'Test Location', timestamp: new Date().toISOString() },
        contactedNumbers: [],
        startTime: new Date().toISOString(),
        endTime: new Date().toISOString(),
        status: 'resolved',
        adminNotified: true,
        emergencyContactsNotified: [],
        notes,
      };
      
      return endedSession;
    } catch (error) {
      console.error('Error ending SOS session:', error);
      throw new Error('Failed to end SOS session');
    }
  }

  async getSOSHistory(riderId: string): Promise<SOSSession[]> {
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      return [
        {
          id: 'sos-1',
          riderId,
          emergencyType: EmergencyType.ACCIDENT,
          location: {
            latitude: 24.8607,
            longitude: 67.0011,
            address: 'Karachi, Pakistan',
            timestamp: '2024-01-15T10:30:00Z',
          },
          contactedNumbers: [
            { number: '15', contactedAt: '2024-01-15T10:30:00Z', responseTime: 120 },
          ],
          startTime: '2024-01-15T10:30:00Z',
          endTime: '2024-01-15T11:00:00Z',
          status: 'resolved',
          adminNotified: true,
          emergencyContactsNotified: ['contact-1'],
          notes: 'Minor accident, resolved quickly',
        },
      ];
    } catch (error) {
      console.error('Error fetching SOS history:', error);
      throw new Error('Failed to fetch SOS history');
    }
  }

  // Location Sharing API
  async startLocationShare(
    riderId: string,
    recipientId: string,
    recipientType: string,
    recipientName: string,
    reason: string,
    duration?: number
  ): Promise<LocationShare> {
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      const locationShare: LocationShare = {
        id: `share-${Date.now()}`,
        riderId,
        recipientId,
        recipientType: recipientType as any,
        recipientName,
        status: LocationSharingStatus.ACTIVE,
        startTime: new Date().toISOString(),
        duration,
        location: {
          latitude: 24.8607,
          longitude: 67.0011,
          address: 'Karachi, Pakistan',
          timestamp: new Date().toISOString(),
        },
        shareReason: reason,
        autoStop: !!duration,
      };
      
      return locationShare;
    } catch (error) {
      console.error('Error starting location share:', error);
      throw new Error('Failed to start location sharing');
    }
  }

  async stopLocationShare(shareId: string): Promise<void> {
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
    } catch (error) {
      console.error('Error stopping location share:', error);
      throw new Error('Failed to stop location sharing');
    }
  }

  async getLocationShares(riderId: string): Promise<LocationShare[]> {
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      return [
        {
          id: 'share-1',
          riderId,
          recipientId: 'admin-1',
          recipientType: 'admin',
          recipientName: 'FoodWay Admin',
          status: LocationSharingStatus.ACTIVE,
          startTime: new Date().toISOString(),
          location: {
            latitude: 24.8607,
            longitude: 67.0011,
            address: 'Karachi, Pakistan',
            timestamp: new Date().toISOString(),
          },
          shareReason: 'Emergency SOS',
          autoStop: false,
        },
      ];
    } catch (error) {
      console.error('Error fetching location shares:', error);
      throw new Error('Failed to fetch location shares');
    }
  }

  // Incident Reporting API
  async submitIncidentReport(
    report: Omit<IncidentReport, 'id' | 'reportedAt' | 'updatedAt' | 'status'>
  ): Promise<IncidentReport> {
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1500));
      
      const newReport: IncidentReport = {
        ...report,
        id: `incident-${Date.now()}`,
        reportedAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        status: 'reported',
      };
      
      return newReport;
    } catch (error) {
      console.error('Error submitting incident report:', error);
      throw new Error('Failed to submit incident report');
    }
  }

  async getIncidentReports(riderId: string): Promise<IncidentReport[]> {
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      return [
        {
          id: 'incident-1',
          riderId,
          type: IncidentType.TRAFFIC_ACCIDENT,
          severity: IncidentSeverity.MEDIUM,
          title: 'Minor Traffic Accident',
          description: 'Small collision with another vehicle at intersection',
          location: {
            latitude: 24.8607,
            longitude: 67.0011,
            address: 'Main Street, Karachi',
            timestamp: '2024-01-10T14:30:00Z',
          },
          evidence: {
            photos: ['photo1.jpg', 'photo2.jpg'],
          },
          damages: ['vehicle'],
          policeReport: {
            filed: true,
            reportNumber: 'PR-2024-001',
            officerName: 'Officer Khan',
            stationName: 'Karachi Central',
            filedAt: '2024-01-10T15:00:00Z',
          },
          followUpRequired: true,
          reportedAt: '2024-01-10T14:45:00Z',
          updatedAt: '2024-01-10T14:45:00Z',
          status: 'under_review',
        },
      ];
    } catch (error) {
      console.error('Error fetching incident reports:', error);
      throw new Error('Failed to fetch incident reports');
    }
  }

  async updateIncidentReport(reportId: string, updates: Partial<IncidentReport>): Promise<IncidentReport> {
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // Mock updated report
      const updatedReport: IncidentReport = {
        id: reportId,
        riderId: 'rider-1',
        type: IncidentType.TRAFFIC_ACCIDENT,
        severity: IncidentSeverity.MEDIUM,
        title: 'Updated Incident',
        description: 'Updated description',
        location: {
          latitude: 24.8607,
          longitude: 67.0011,
          address: 'Karachi, Pakistan',
          timestamp: new Date().toISOString(),
        },
        evidence: { photos: [] },
        damages: [],
        followUpRequired: false,
        reportedAt: '2024-01-01T00:00:00Z',
        updatedAt: new Date().toISOString(),
        status: 'reported',
        ...updates,
      };
      
      return updatedReport;
    } catch (error) {
      console.error('Error updating incident report:', error);
      throw new Error('Failed to update incident report');
    }
  }

  // Legal Documents API
  async getLegalDocuments(): Promise<LegalDocument[]> {
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // Documents are defined in TermsPoliciesScreen - this would typically fetch from API
      return [];
    } catch (error) {
      console.error('Error fetching legal documents:', error);
      throw new Error('Failed to fetch legal documents');
    }
  }

  async acceptDocument(
    riderId: string,
    documentId: string,
    documentVersion: string
  ): Promise<DocumentAcceptance> {
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      const acceptance: DocumentAcceptance = {
        id: `acc-${Date.now()}`,
        riderId,
        documentId,
        documentVersion,
        acceptedAt: new Date().toISOString(),
        ipAddress: '***********',
        deviceInfo: {
          platform: 'iOS',
          version: '17.0',
          model: 'iPhone 12',
        },
        isRequired: true,
      };
      
      return acceptance;
    } catch (error) {
      console.error('Error accepting document:', error);
      throw new Error('Failed to accept document');
    }
  }

  async getDocumentAcceptances(riderId: string): Promise<DocumentAcceptance[]> {
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      return [
        {
          id: 'acc-1',
          riderId,
          documentId: 'rider-agreement',
          documentVersion: '2.1',
          acceptedAt: '2024-02-01T10:00:00Z',
          ipAddress: '***********',
          deviceInfo: {
            platform: 'iOS',
            version: '17.0',
            model: 'iPhone 12',
          },
          isRequired: true,
        },
      ];
    } catch (error) {
      console.error('Error fetching document acceptances:', error);
      throw new Error('Failed to fetch document acceptances');
    }
  }
}

export default SafetyService;
