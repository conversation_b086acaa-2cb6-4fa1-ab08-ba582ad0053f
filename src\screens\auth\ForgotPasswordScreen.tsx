import React, { useState, useRef, useEffect } from 'react';
import {
  View,
  Text,
  ScrollView,
  KeyboardAvoidingView,
  Platform,
  TouchableOpacity,
  Animated,
  Alert,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import { Button, Input, LoadingSpinner } from '../../components/ui';
import { isValidEmail } from '../../utils/helpers';

interface ForgotPasswordScreenProps {
  navigation?: any;
  onBack?: () => void;
}

const ForgotPasswordScreen: React.FC<ForgotPasswordScreenProps> = ({
  navigation,
  onBack,
}) => {
  const [email, setEmail] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [emailSent, setEmailSent] = useState(false);
  const [error, setError] = useState('');

  // Animation refs
  const fadeAnim = useRef(new Animated.Value(0)).current;
  const slideAnim = useRef(new Animated.Value(30)).current;
  const scaleAnim = useRef(new Animated.Value(0.8)).current;

  useEffect(() => {
    // Start entrance animations
    Animated.parallel([
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 800,
        useNativeDriver: true,
      }),
      Animated.timing(slideAnim, {
        toValue: 0,
        duration: 800,
        useNativeDriver: true,
      }),
      Animated.spring(scaleAnim, {
        toValue: 1,
        tension: 50,
        friction: 7,
        useNativeDriver: true,
      }),
    ]).start();
  }, []);

  const handleBack = () => {
    if (onBack) {
      onBack();
    } else {
      navigation?.goBack();
    }
  };

  const validateEmail = (): boolean => {
    if (!email.trim()) {
      setError('Email address is required');
      return false;
    }

    if (!isValidEmail(email.trim())) {
      setError('Please enter a valid email address');
      return false;
    }

    setError('');
    return true;
  };

  const handleSendResetEmail = async () => {
    if (!validateEmail()) {
      return;
    }

    setIsLoading(true);
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      setEmailSent(true);
      
      Alert.alert(
        'Reset Email Sent! 📧',
        `We've sent password reset instructions to ${email}. Please check your email and follow the instructions to reset your password.`,
        [
          {
            text: 'Back to Login',
            onPress: () => navigation?.navigate('Login'),
          },
        ]
      );
    } catch (error: any) {
      Alert.alert(
        'Error',
        error.message || 'Failed to send reset email. Please try again.',
        [{ text: 'OK' }]
      );
    } finally {
      setIsLoading(false);
    }
  };

  const handleResendEmail = () => {
    setEmailSent(false);
    setEmail('');
    setError('');
  };

  if (isLoading) {
    return (
      <View style={{ flex: 1, backgroundColor: '#f1f5f9' }}>
        <SafeAreaView style={{ flex: 1 }}>
          <LoadingSpinner message="Sending reset email..." color="#dc2626" />
        </SafeAreaView>
      </View>
    );
  }

  return (
    <View style={{ flex: 1, backgroundColor: '#f1f5f9' }}>
      <SafeAreaView style={{ flex: 1 }}>
        {/* Enhanced Header */}
        <View style={{
          backgroundColor: '#dc2626',
          paddingHorizontal: 20,
          paddingTop: 20,
          paddingBottom: 28,
          borderBottomLeftRadius: 28,
          borderBottomRightRadius: 28,
          shadowColor: '#dc2626',
          shadowOffset: { width: 0, height: 12 },
          shadowOpacity: 0.3,
          shadowRadius: 20,
          elevation: 16,
        }}>
          <View style={{
            flexDirection: 'row',
            alignItems: 'center',
            marginBottom: 20,
          }}>
            <TouchableOpacity
              onPress={handleBack}
              style={{
                width: 48,
                height: 48,
                borderRadius: 24,
                backgroundColor: 'rgba(255,255,255,0.2)',
                justifyContent: 'center',
                alignItems: 'center',
                marginRight: 16,
                borderWidth: 2,
                borderColor: 'rgba(255,255,255,0.3)',
              }}
            >
              <Ionicons name="arrow-back" size={24} color="white" />
            </TouchableOpacity>
            <View style={{ flex: 1 }}>
              <Text style={{
                fontSize: 24,
                fontWeight: 'bold',
                color: 'white',
                marginBottom: 4,
              }}>
                Reset Password
              </Text>
              <Text style={{
                fontSize: 14,
                color: 'rgba(255,255,255,0.8)',
              }}>
                We'll help you get back in
              </Text>
            </View>
          </View>
        </View>

        <KeyboardAvoidingView
          style={{ flex: 1 }}
          behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        >
          <ScrollView
            contentContainerStyle={{
              flexGrow: 1,
              paddingHorizontal: 24,
              paddingVertical: 40,
            }}
            showsVerticalScrollIndicator={false}
          >
            {/* Icon and Title */}
            <Animated.View
              style={{
                alignItems: 'center',
                marginBottom: 40,
                opacity: fadeAnim,
                transform: [
                  { translateY: slideAnim },
                  { scale: scaleAnim },
                ],
              }}
            >
              <View style={{
                width: 100,
                height: 100,
                backgroundColor: 'white',
                borderRadius: 50,
                justifyContent: 'center',
                alignItems: 'center',
                marginBottom: 24,
                shadowColor: '#dc2626',
                shadowOffset: { width: 0, height: 8 },
                shadowOpacity: 0.15,
                shadowRadius: 20,
                elevation: 12,
                borderWidth: 4,
                borderColor: '#fef2f2',
              }}>
                <Ionicons name="lock-closed" size={48} color="#dc2626" />
              </View>

              <Text style={{
                fontSize: 28,
                fontWeight: 'bold',
                color: '#111827',
                textAlign: 'center',
                marginBottom: 12,
              }}>
                Forgot Password?
              </Text>

              <Text style={{
                fontSize: 16,
                color: '#6b7280',
                textAlign: 'center',
                lineHeight: 24,
                maxWidth: 300,
              }}>
                {emailSent 
                  ? 'Check your email for reset instructions'
                  : 'Enter your email address and we\'ll send you instructions to reset your password'
                }
              </Text>
            </Animated.View>

            {/* Form */}
            <Animated.View
              style={{
                opacity: fadeAnim,
                transform: [{ translateY: slideAnim }],
              }}
            >
              {!emailSent ? (
                <>
                  {/* Email Input */}
                  <Input
                    label="Email Address"
                    placeholder="Enter your email"
                    value={email}
                    onChangeText={(value: string) => {
                      setEmail(value);
                      if (error) setError('');
                    }}
                    error={error}
                    leftIcon="mail"
                    keyboardType="email-address"
                    autoCapitalize="none"
                    autoCorrect={false}
                    required
                    variant="default"
                    size="lg"
                  />

                  {/* Send Reset Email Button */}
                  <Button
                    title="Send Reset Email"
                    onPress={handleSendResetEmail}
                    variant="primary"
                    size="lg"
                    fullWidth
                    style={{ marginTop: 8, marginBottom: 24 }}
                  />
                </>
              ) : (
                <View style={{
                  backgroundColor: 'white',
                  borderRadius: 20,
                  padding: 24,
                  shadowColor: '#10b981',
                  shadowOffset: { width: 0, height: 8 },
                  shadowOpacity: 0.1,
                  shadowRadius: 16,
                  elevation: 8,
                  borderWidth: 2,
                  borderColor: '#d1fae5',
                  alignItems: 'center',
                  marginBottom: 24,
                }}>
                  <Ionicons name="checkmark-circle" size={64} color="#10b981" />
                  <Text style={{
                    fontSize: 20,
                    fontWeight: 'bold',
                    color: '#111827',
                    marginTop: 16,
                    marginBottom: 8,
                    textAlign: 'center',
                  }}>
                    Email Sent Successfully!
                  </Text>
                  <Text style={{
                    fontSize: 16,
                    color: '#6b7280',
                    textAlign: 'center',
                    lineHeight: 24,
                    marginBottom: 20,
                  }}>
                    We've sent password reset instructions to {email}
                  </Text>
                  
                  <Button
                    title="Resend Email"
                    onPress={handleResendEmail}
                    variant="outline"
                    size="md"
                  />
                </View>
              )}

              {/* Back to Login */}
              <TouchableOpacity
                onPress={() => navigation?.navigate('Login')}
                style={{
                  alignItems: 'center',
                  paddingVertical: 16,
                }}
              >
                <Text style={{
                  color: '#dc2626',
                  fontSize: 16,
                  fontWeight: '600',
                }}>
                  Back to Login
                </Text>
              </TouchableOpacity>
            </Animated.View>
          </ScrollView>
        </KeyboardAvoidingView>
      </SafeAreaView>
    </View>
  );
};

export default ForgotPasswordScreen;
