import React from 'react';
import { View, Text, ScrollView, TouchableOpacity, Alert } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import { mockAuthService } from '../../services/auth/mockAuthService';
import * as Clipboard from 'expo-clipboard';

interface DemoCredentialsScreenProps {
  onClose: () => void;
}

const DemoCredentialsScreen: React.FC<DemoCredentialsScreenProps> = ({ onClose }) => {
  const demoAccounts = mockAuthService.getDemoAccounts();

  const copyToClipboard = async (text: string, label: string) => {
    await Clipboard.setStringAsync(text);
    Alert.alert('Copied!', `${label} copied to clipboard`);
  };

  const CredentialCard = ({ 
    title, 
    email, 
    password, 
    phoneNumber, 
    verificationCode 
  }: {
    title: string;
    email?: string;
    password?: string;
    phoneNumber?: string;
    verificationCode?: string;
  }) => (
    <View className="bg-white rounded-lg p-4 mb-4 shadow-sm border border-gray-100">
      <Text className="text-lg font-semibold text-gray-800 mb-3">{title}</Text>
      
      {email && (
        <View className="mb-2">
          <Text className="text-sm text-gray-600 mb-1">Email:</Text>
          <TouchableOpacity 
            onPress={() => copyToClipboard(email, 'Email')}
            className="flex-row items-center justify-between bg-gray-50 p-3 rounded-lg"
          >
            <Text className="text-gray-800 font-mono">{email}</Text>
            <Ionicons name="copy-outline" size={16} color="#6B7280" />
          </TouchableOpacity>
        </View>
      )}
      
      {password && (
        <View className="mb-2">
          <Text className="text-sm text-gray-600 mb-1">Password:</Text>
          <TouchableOpacity 
            onPress={() => copyToClipboard(password, 'Password')}
            className="flex-row items-center justify-between bg-gray-50 p-3 rounded-lg"
          >
            <Text className="text-gray-800 font-mono">{password}</Text>
            <Ionicons name="copy-outline" size={16} color="#6B7280" />
          </TouchableOpacity>
        </View>
      )}
      
      {phoneNumber && (
        <View className="mb-2">
          <Text className="text-sm text-gray-600 mb-1">Phone Number:</Text>
          <TouchableOpacity 
            onPress={() => copyToClipboard(phoneNumber, 'Phone number')}
            className="flex-row items-center justify-between bg-gray-50 p-3 rounded-lg"
          >
            <Text className="text-gray-800 font-mono">{phoneNumber}</Text>
            <Ionicons name="copy-outline" size={16} color="#6B7280" />
          </TouchableOpacity>
        </View>
      )}
      
      {verificationCode && (
        <View>
          <Text className="text-sm text-gray-600 mb-1">Verification Code:</Text>
          <TouchableOpacity 
            onPress={() => copyToClipboard(verificationCode, 'Verification code')}
            className="flex-row items-center justify-between bg-gray-50 p-3 rounded-lg"
          >
            <Text className="text-gray-800 font-mono">{verificationCode}</Text>
            <Ionicons name="copy-outline" size={16} color="#6B7280" />
          </TouchableOpacity>
        </View>
      )}
    </View>
  );

  return (
    <SafeAreaView className="flex-1 bg-gray-50">
      {/* Header */}
      <View className="flex-row items-center justify-between p-4 bg-white border-b border-gray-200">
        <Text className="text-xl font-bold text-gray-800">Demo Accounts</Text>
        <TouchableOpacity 
          onPress={onClose}
          className="p-2 rounded-full bg-gray-100"
        >
          <Ionicons name="close" size={24} color="#374151" />
        </TouchableOpacity>
      </View>

      <ScrollView className="flex-1 p-4">
        {/* Info Banner */}
        <View className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
          <View className="flex-row items-start">
            <Ionicons name="information-circle" size={20} color="#3B82F6" className="mr-2 mt-0.5" />
            <View className="flex-1 ml-2">
              <Text className="text-blue-800 font-semibold mb-1">Demo Mode</Text>
              <Text className="text-blue-700 text-sm leading-5">
                This app is running in demo mode with mock authentication. Use the email credentials below to test the app features. Tap any credential to copy it to your clipboard.
              </Text>
            </View>
          </View>
        </View>

        {/* Email Login Accounts */}
        <Text className="text-lg font-bold text-gray-800 mb-4">Email Login</Text>
        {demoAccounts.email.map((account, index) => (
          <CredentialCard
            key={`email-${index}`}
            title={`Demo Account ${index + 1}`}
            email={account.email}
            password={account.password}
          />
        ))}



        {/* Instructions */}
        <View className="bg-orange-50 border border-orange-200 rounded-lg p-4 mt-6 mb-8">
          <View className="flex-row items-start">
            <Ionicons name="bulb" size={20} color="#F97316" className="mr-2 mt-0.5" />
            <View className="flex-1 ml-2">
              <Text className="text-orange-800 font-semibold mb-2">How to Use</Text>
              <Text className="text-orange-700 text-sm leading-5 mb-2">
                1. Copy the email and password from the demo account above
              </Text>
              <Text className="text-orange-700 text-sm leading-5 mb-2">
                2. Paste them into the login form
              </Text>
              <Text className="text-orange-700 text-sm leading-5">
                3. All app features are fully functional with mock data
              </Text>
            </View>
          </View>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

export default DemoCredentialsScreen;
