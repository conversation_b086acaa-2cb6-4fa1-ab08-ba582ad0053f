import React, { useState, useRef, useEffect } from 'react';
import {
  View,
  Text,
  ScrollView,
  KeyboardAvoidingView,
  Platform,
  Alert,
  Modal,
  TouchableOpacity,
  Animated,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import { LoadingSpinner, Input, Button } from '../../components/ui';
import { useAuth } from '../../context/AuthContext';
import { isValidEmail } from '../../utils/helpers';
import { VALIDATION_RULES } from '../../utils/constants';
import DemoCredentialsScreen from './DemoCredentialsScreen';



interface LoginScreenProps {
  navigation?: any;
}

const LoginScreen: React.FC<LoginScreenProps> = ({ navigation }) => {
  const { state, login, clearError } = useAuth();
  const [formData, setFormData] = useState({
    email: '',
    password: '',
  });
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [showPassword, setShowPassword] = useState(false);
  const [showDemoCredentials, setShowDemoCredentials] = useState(false);

  // Animation refs
  const fadeAnim = useRef(new Animated.Value(0)).current;
  const slideAnim = useRef(new Animated.Value(30)).current;
  const shakeAnim = useRef(new Animated.Value(0)).current;
  const buttonScaleAnim = useRef(new Animated.Value(1)).current;

  // Initialize animations
  useEffect(() => {
    // Clear any previous errors when component mounts
    clearError();

    // Start entrance animations
    Animated.parallel([
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 800,
        useNativeDriver: true,
      }),
      Animated.timing(slideAnim, {
        toValue: 0,
        duration: 600,
        useNativeDriver: true,
      }),
    ]).start();
  }, []);

  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    // Email validation
    if (!formData.email.trim()) {
      newErrors.email = 'Email is required';
    } else if (!isValidEmail(formData.email)) {
      newErrors.email = 'Please enter a valid email address';
    }

    // Password validation
    if (!formData.password) {
      newErrors.password = 'Password is required';
    } else if (formData.password.length < VALIDATION_RULES.PASSWORD_MIN_LENGTH) {
      newErrors.password = `Password must be at least ${VALIDATION_RULES.PASSWORD_MIN_LENGTH} characters`;
    }

    setErrors(newErrors);

    // Shake animation on validation error
    if (Object.keys(newErrors).length > 0) {
      Animated.sequence([
        Animated.timing(shakeAnim, { toValue: 10, duration: 100, useNativeDriver: true }),
        Animated.timing(shakeAnim, { toValue: -10, duration: 100, useNativeDriver: true }),
        Animated.timing(shakeAnim, { toValue: 10, duration: 100, useNativeDriver: true }),
        Animated.timing(shakeAnim, { toValue: 0, duration: 100, useNativeDriver: true }),
      ]).start();
    }

    return Object.keys(newErrors).length === 0;
  };

  const handleLogin = async () => {
    // Button press animation
    Animated.sequence([
      Animated.timing(buttonScaleAnim, { toValue: 0.95, duration: 100, useNativeDriver: true }),
      Animated.timing(buttonScaleAnim, { toValue: 1, duration: 100, useNativeDriver: true }),
    ]).start();

    if (!validateForm()) {
      return;
    }

    try {
      clearError();
      const credentials = {
        email: formData.email.trim().toLowerCase(),
        password: formData.password,
      };

      await login(credentials);
    } catch (error: any) {
      Alert.alert('Login Failed', error.message);
    }
  };

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    
    // Clear field error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }
  };

  const handlePasswordToggle = () => {
    setShowPassword(!showPassword);
  };

  const handleForgotPassword = () => {
    navigation?.navigate('ForgotPassword');
  };



  if (state.isLoading) {
    return (
      <View style={{ flex: 1, backgroundColor: '#f8fafc' }}>
        <SafeAreaView style={{ flex: 1 }}>
          <LoadingSpinner message="Signing you in..." color="#dc2626" />
        </SafeAreaView>
      </View>
    );
  }

  return (
    <View style={{ flex: 1, backgroundColor: '#f8fafc' }}>
      <SafeAreaView style={{ flex: 1 }}>
        <KeyboardAvoidingView
          style={{ flex: 1 }}
          behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        >
          <ScrollView
            contentContainerStyle={{
              flexGrow: 1,
              justifyContent: 'center',
              paddingHorizontal: 24,
              paddingVertical: 40,
            }}
            keyboardShouldPersistTaps="handled"
            showsVerticalScrollIndicator={false}
          >
            {/* Header */}
            <Animated.View
              style={{
                alignItems: 'center',
                marginBottom: 48,
                opacity: fadeAnim,
                transform: [{ translateY: slideAnim }],
              }}
            >
              {/* Logo */}
              <View
                style={{
                  width: 80,
                  height: 80,
                  backgroundColor: '#dc2626',
                  borderRadius: 40,
                  justifyContent: 'center',
                  alignItems: 'center',
                  marginBottom: 24,
                  shadowColor: '#dc2626',
                  shadowOffset: { width: 0, height: 4 },
                  shadowOpacity: 0.3,
                  shadowRadius: 8,
                  elevation: 8,
                }}
              >
                <Ionicons name="bicycle" size={40} color="white" />
              </View>

              <Text style={{
                fontSize: 32,
                fontWeight: 'bold',
                color: '#111827',
                marginBottom: 8,
                textAlign: 'center',
              }}>
                Welcome Back
              </Text>

              <Text style={{
                fontSize: 16,
                color: '#6b7280',
                textAlign: 'center',
              }}>
                Sign in to your FoodWay Rider account
              </Text>
            </Animated.View>

            {/* Login Form */}
            <Animated.View
              style={{
                opacity: fadeAnim,
                transform: [{ translateY: slideAnim }],
              }}
            >
              {/* Email Input */}
              <Input
                label="Email Address"
                placeholder="Enter your email"
                value={formData.email}
                onChangeText={(value: string) => handleInputChange('email', value)}
                error={errors.email}
                leftIcon="mail"
                keyboardType="email-address"
                autoCapitalize="none"
                autoCorrect={false}
                required
              />

              {/* Password Input */}
              <Input
                label="Password"
                placeholder="Enter your password"
                value={formData.password}
                onChangeText={(value: string) => handleInputChange('password', value)}
                error={errors.password}
                leftIcon="lock-closed"
                rightIcon={showPassword ? "eye-off" : "eye"}
                onRightIconPress={handlePasswordToggle}
                secureTextEntry={!showPassword}
                required
                containerStyle={{ marginBottom: 24 }}
              />

              {/* Error Display */}
              {state.error && (
                <View style={{
                  backgroundColor: '#fef2f2',
                  padding: 12,
                  borderRadius: 8,
                  marginBottom: 20,
                  borderLeftWidth: 4,
                  borderLeftColor: '#dc2626',
                }}>
                  <View style={{ flexDirection: 'row', alignItems: 'center' }}>
                    <Ionicons name="alert-circle" size={16} color="#dc2626" />
                    <Text style={{
                      color: '#dc2626',
                      fontSize: 14,
                      marginLeft: 8,
                      flex: 1,
                    }}>
                      {state.error}
                    </Text>
                  </View>
                </View>
              )}

              {/* Sign In Button */}
              <Button
                title={state.isLoading ? 'Signing In...' : 'Sign In'}
                onPress={handleLogin}
                disabled={state.isLoading}
                loading={state.isLoading}
                leftIcon={state.isLoading ? 'refresh' : 'log-in'}
                variant="primary"
                size="md"
                fullWidth
                style={{ marginBottom: 20 }}
              />

              {/* Forgot Password */}
              <TouchableOpacity
                onPress={handleForgotPassword}
                style={{ alignItems: 'center', paddingVertical: 16 }}
              >
                <Text style={{
                  color: '#dc2626',
                  fontSize: 16,
                  fontWeight: '500',
                }}>
                  Forgot Password?
                </Text>
              </TouchableOpacity>
            </Animated.View>

            {/* Demo Credentials & Sign Up Section */}
            <View style={{
              marginTop: 32,
              alignItems: 'center',
            }}>
              {/* Demo Credentials Button */}
              <Button
                title="View Demo Credentials"
                onPress={() => setShowDemoCredentials(true)}
                variant="outline"
                size="md"
                style={{ marginBottom: 24 }}
              />

              {/* Sign Up Link */}
              <View style={{
                flexDirection: 'row',
                alignItems: 'center',
                justifyContent: 'center',
              }}>
                <Text style={{
                  color: '#6b7280',
                  fontSize: 16,
                }}>
                  Don't have an account?
                </Text>
                <TouchableOpacity
                  onPress={() => navigation?.navigate('RegistrationInfo')}
                >
                  <Text style={{
                    color: '#dc2626',
                    fontSize: 16,
                    fontWeight: '600',
                  }}>
                    Sign Up
                  </Text>
                </TouchableOpacity>
              </View>
            </View>
          </ScrollView>
        </KeyboardAvoidingView>

        {/* Demo Credentials Modal */}
        <Modal
          visible={showDemoCredentials}
          animationType="slide"
          presentationStyle="pageSheet"
        >
          <DemoCredentialsScreen onClose={() => setShowDemoCredentials(false)} />
        </Modal>
      </SafeAreaView>
    </View>
  );
};

export default LoginScreen;
