import React, { createContext, useContext, useReducer, ReactNode } from 'react';
import {
  SafetyState,
  SafetyAction,
  SafetyContextType,
  EmergencyType,
  EmergencyContact,
  SOSSession,
  LocationShare,
  IncidentReport,
  LegalDocument,
  DocumentAcceptance,
  LocationSharingStatus,
} from '../types/safety';

// Initial state
const initialState: SafetyState = {
  emergencyContacts: [],
  activeSOS: null,
  sosHistory: [],
  locationShares: [],
  incidentReports: [],
  legalDocuments: [],
  documentAcceptances: [],
  currentLocation: null,
  locationSharingStatus: LocationSharingStatus.INACTIVE,
  loading: false,
  error: null,
};

// Reducer
const safetyReducer = (state: SafetyState, action: SafetyAction): SafetyState => {
  switch (action.type) {
    case 'SET_LOADING':
      return { ...state, loading: action.payload };
    
    case 'SET_ERROR':
      return { ...state, error: action.payload, loading: false };
    
    case 'SET_EMERGENCY_CONTACTS':
      return { ...state, emergencyContacts: action.payload };
    
    case 'ADD_EMERGENCY_CONTACT':
      return {
        ...state,
        emergencyContacts: [...state.emergencyContacts, action.payload],
      };
    
    case 'UPDATE_EMERGENCY_CONTACT':
      return {
        ...state,
        emergencyContacts: state.emergencyContacts.map(contact =>
          contact.id === action.payload.id ? { ...contact, ...action.payload.updates } : contact
        ),
      };
    
    case 'DELETE_EMERGENCY_CONTACT':
      return {
        ...state,
        emergencyContacts: state.emergencyContacts.filter(contact => contact.id !== action.payload),
      };
    
    case 'START_SOS':
      return {
        ...state,
        activeSOS: action.payload,
        locationSharingStatus: LocationSharingStatus.ACTIVE,
      };
    
    case 'END_SOS':
      return {
        ...state,
        activeSOS: null,
        sosHistory: state.activeSOS ? [...state.sosHistory, state.activeSOS] : state.sosHistory,
        locationSharingStatus: LocationSharingStatus.INACTIVE,
      };
    
    case 'SET_SOS_HISTORY':
      return { ...state, sosHistory: action.payload };
    
    case 'ADD_LOCATION_SHARE':
      return {
        ...state,
        locationShares: [...state.locationShares, action.payload],
      };
    
    case 'UPDATE_LOCATION_SHARE':
      return {
        ...state,
        locationShares: state.locationShares.map(share =>
          share.id === action.payload.id ? { ...share, ...action.payload.updates } : share
        ),
      };
    
    case 'SET_LOCATION_SHARES':
      return { ...state, locationShares: action.payload };
    
    case 'SET_INCIDENT_REPORTS':
      return { ...state, incidentReports: action.payload };
    
    case 'ADD_INCIDENT_REPORT':
      return {
        ...state,
        incidentReports: [...state.incidentReports, action.payload],
      };
    
    case 'UPDATE_INCIDENT_REPORT':
      return {
        ...state,
        incidentReports: state.incidentReports.map(report =>
          report.id === action.payload.id ? { ...report, ...action.payload.updates } : report
        ),
      };
    
    case 'SET_LEGAL_DOCUMENTS':
      return { ...state, legalDocuments: action.payload };
    
    case 'SET_DOCUMENT_ACCEPTANCES':
      return { ...state, documentAcceptances: action.payload };
    
    case 'ADD_DOCUMENT_ACCEPTANCE':
      return {
        ...state,
        documentAcceptances: [...state.documentAcceptances.filter(acc => acc.documentId !== action.payload.documentId), action.payload],
      };
    
    case 'SET_CURRENT_LOCATION':
      return { ...state, currentLocation: action.payload };
    
    case 'SET_LOCATION_SHARING_STATUS':
      return { ...state, locationSharingStatus: action.payload };
    
    default:
      return state;
  }
};

// Context
const SafetyContext = createContext<SafetyContextType | undefined>(undefined);

// Provider
interface SafetyProviderProps {
  children: ReactNode;
}

export const SafetyProvider: React.FC<SafetyProviderProps> = ({ children }) => {
  const [state, dispatch] = useReducer(safetyReducer, initialState);

  // Emergency SOS functions
  const startSOS = async (emergencyType: EmergencyType, location: any): Promise<SOSSession> => {
    dispatch({ type: 'SET_LOADING', payload: true });
    
    try {
      const sosSession: SOSSession = {
        id: `sos-${Date.now()}`,
        riderId: 'rider-1',
        emergencyType,
        location,
        contactedNumbers: [],
        startTime: new Date().toISOString(),
        status: 'active',
        adminNotified: true,
        emergencyContactsNotified: [],
      };

      dispatch({ type: 'START_SOS', payload: sosSession });
      dispatch({ type: 'SET_LOADING', payload: false });
      
      return sosSession;
    } catch (error) {
      dispatch({ type: 'SET_ERROR', payload: 'Failed to start SOS session' });
      throw error;
    }
  };

  const endSOS = async (sosId: string, notes?: string): Promise<void> => {
    dispatch({ type: 'SET_LOADING', payload: true });
    
    try {
      if (state.activeSOS && state.activeSOS.id === sosId) {
        const updatedSOS = {
          ...state.activeSOS,
          endTime: new Date().toISOString(),
          status: 'resolved' as const,
          notes,
        };
        
        dispatch({ type: 'END_SOS', payload: updatedSOS });
      }
      
      dispatch({ type: 'SET_LOADING', payload: false });
    } catch (error) {
      dispatch({ type: 'SET_ERROR', payload: 'Failed to end SOS session' });
      throw error;
    }
  };

  const addEmergencyContact = async (contact: Omit<EmergencyContact, 'id'>): Promise<EmergencyContact> => {
    dispatch({ type: 'SET_LOADING', payload: true });
    
    try {
      const newContact: EmergencyContact = {
        ...contact,
        id: `contact-${Date.now()}`,
      };

      dispatch({ type: 'ADD_EMERGENCY_CONTACT', payload: newContact });
      dispatch({ type: 'SET_LOADING', payload: false });
      
      return newContact;
    } catch (error) {
      dispatch({ type: 'SET_ERROR', payload: 'Failed to add emergency contact' });
      throw error;
    }
  };

  const updateEmergencyContact = async (contactId: string, updates: Partial<EmergencyContact>): Promise<void> => {
    dispatch({ type: 'SET_LOADING', payload: true });
    
    try {
      dispatch({ type: 'UPDATE_EMERGENCY_CONTACT', payload: { id: contactId, updates } });
      dispatch({ type: 'SET_LOADING', payload: false });
    } catch (error) {
      dispatch({ type: 'SET_ERROR', payload: 'Failed to update emergency contact' });
      throw error;
    }
  };

  const deleteEmergencyContact = async (contactId: string): Promise<void> => {
    dispatch({ type: 'SET_LOADING', payload: true });
    
    try {
      dispatch({ type: 'DELETE_EMERGENCY_CONTACT', payload: contactId });
      dispatch({ type: 'SET_LOADING', payload: false });
    } catch (error) {
      dispatch({ type: 'SET_ERROR', payload: 'Failed to delete emergency contact' });
      throw error;
    }
  };

  // Location sharing functions
  const startLocationShare = async (
    recipientId: string,
    recipientType: string,
    recipientName: string,
    reason: string,
    duration?: number
  ): Promise<LocationShare> => {
    dispatch({ type: 'SET_LOADING', payload: true });
    
    try {
      const locationShare: LocationShare = {
        id: `share-${Date.now()}`,
        riderId: 'rider-1',
        recipientId,
        recipientType: recipientType as any,
        recipientName,
        status: LocationSharingStatus.ACTIVE,
        startTime: new Date().toISOString(),
        duration,
        location: state.currentLocation || {
          latitude: 0,
          longitude: 0,
          address: 'Unknown location',
          timestamp: new Date().toISOString(),
        },
        shareReason: reason,
        autoStop: !!duration,
      };

      dispatch({ type: 'ADD_LOCATION_SHARE', payload: locationShare });
      dispatch({ type: 'SET_LOCATION_SHARING_STATUS', payload: LocationSharingStatus.ACTIVE });
      dispatch({ type: 'SET_LOADING', payload: false });
      
      return locationShare;
    } catch (error) {
      dispatch({ type: 'SET_ERROR', payload: 'Failed to start location sharing' });
      throw error;
    }
  };

  const stopLocationShare = async (shareId: string): Promise<void> => {
    dispatch({ type: 'SET_LOADING', payload: true });
    
    try {
      dispatch({
        type: 'UPDATE_LOCATION_SHARE',
        payload: {
          id: shareId,
          updates: {
            status: LocationSharingStatus.INACTIVE,
            endTime: new Date().toISOString(),
          },
        },
      });
      
      // Check if there are any other active shares
      const activeShares = state.locationShares.filter(share => 
        share.id !== shareId && share.status === LocationSharingStatus.ACTIVE
      );
      
      if (activeShares.length === 0) {
        dispatch({ type: 'SET_LOCATION_SHARING_STATUS', payload: LocationSharingStatus.INACTIVE });
      }
      
      dispatch({ type: 'SET_LOADING', payload: false });
    } catch (error) {
      dispatch({ type: 'SET_ERROR', payload: 'Failed to stop location sharing' });
      throw error;
    }
  };

  const updateLocationSharingStatus = async (status: LocationSharingStatus): Promise<void> => {
    dispatch({ type: 'SET_LOCATION_SHARING_STATUS', payload: status });
  };

  // Incident reporting functions
  const submitIncidentReport = async (
    report: Omit<IncidentReport, 'id' | 'reportedAt' | 'updatedAt' | 'status'>
  ): Promise<IncidentReport> => {
    dispatch({ type: 'SET_LOADING', payload: true });
    
    try {
      const newReport: IncidentReport = {
        ...report,
        id: `incident-${Date.now()}`,
        reportedAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        status: 'reported' as any,
      };

      dispatch({ type: 'ADD_INCIDENT_REPORT', payload: newReport });
      dispatch({ type: 'SET_LOADING', payload: false });
      
      return newReport;
    } catch (error) {
      dispatch({ type: 'SET_ERROR', payload: 'Failed to submit incident report' });
      throw error;
    }
  };

  const updateIncidentReport = async (reportId: string, updates: Partial<IncidentReport>): Promise<void> => {
    dispatch({ type: 'SET_LOADING', payload: true });
    
    try {
      dispatch({
        type: 'UPDATE_INCIDENT_REPORT',
        payload: {
          id: reportId,
          updates: {
            ...updates,
            updatedAt: new Date().toISOString(),
          },
        },
      });
      dispatch({ type: 'SET_LOADING', payload: false });
    } catch (error) {
      dispatch({ type: 'SET_ERROR', payload: 'Failed to update incident report' });
      throw error;
    }
  };

  // Legal documents functions
  const loadLegalDocuments = async (): Promise<void> => {
    dispatch({ type: 'SET_LOADING', payload: true });
    
    try {
      // Simulate API call - documents would be loaded from the TermsPoliciesScreen
      dispatch({ type: 'SET_LOADING', payload: false });
    } catch (error) {
      dispatch({ type: 'SET_ERROR', payload: 'Failed to load legal documents' });
      throw error;
    }
  };

  const acceptDocument = async (documentId: string, documentVersion: string): Promise<DocumentAcceptance> => {
    dispatch({ type: 'SET_LOADING', payload: true });
    
    try {
      const acceptance: DocumentAcceptance = {
        id: `acc-${Date.now()}`,
        riderId: 'rider-1',
        documentId,
        documentVersion,
        acceptedAt: new Date().toISOString(),
        ipAddress: '***********',
        deviceInfo: {
          platform: 'iOS',
          version: '17.0',
          model: 'iPhone 12',
        },
        isRequired: true,
      };

      dispatch({ type: 'ADD_DOCUMENT_ACCEPTANCE', payload: acceptance });
      dispatch({ type: 'SET_LOADING', payload: false });
      
      return acceptance;
    } catch (error) {
      dispatch({ type: 'SET_ERROR', payload: 'Failed to accept document' });
      throw error;
    }
  };

  const getDocumentAcceptance = (documentId: string): DocumentAcceptance | null => {
    return state.documentAcceptances.find(acc => acc.documentId === documentId) || null;
  };

  // Location functions
  const getCurrentLocation = async (): Promise<void> => {
    // This would typically use expo-location
    // For now, we'll use a mock location
    const mockLocation = {
      latitude: 24.8607,
      longitude: 67.0011,
      address: 'Karachi, Pakistan',
      timestamp: new Date().toISOString(),
    };
    
    dispatch({ type: 'SET_CURRENT_LOCATION', payload: mockLocation });
  };

  const updateCurrentLocation = (location: any): void => {
    dispatch({ type: 'SET_CURRENT_LOCATION', payload: location });
  };

  const contextValue: SafetyContextType = {
    // State
    emergencyContacts: state.emergencyContacts,
    activeSOS: state.activeSOS,
    sosHistory: state.sosHistory,
    locationShares: state.locationShares,
    incidentReports: state.incidentReports,
    legalDocuments: state.legalDocuments,
    documentAcceptances: state.documentAcceptances,
    currentLocation: state.currentLocation,
    locationSharingStatus: state.locationSharingStatus,
    loading: state.loading,
    error: state.error,

    // Functions
    startSOS,
    endSOS,
    addEmergencyContact,
    updateEmergencyContact,
    deleteEmergencyContact,
    startLocationShare,
    stopLocationShare,
    updateLocationSharingStatus,
    submitIncidentReport,
    updateIncidentReport,
    loadLegalDocuments,
    acceptDocument,
    getDocumentAcceptance,
    getCurrentLocation,
    updateCurrentLocation,
  };

  return (
    <SafetyContext.Provider value={contextValue}>
      {children}
    </SafetyContext.Provider>
  );
};

// Hook
export const useSafety = (): SafetyContextType => {
  const context = useContext(SafetyContext);
  if (context === undefined) {
    throw new Error('useSafety must be used within a SafetyProvider');
  }
  return context;
};
