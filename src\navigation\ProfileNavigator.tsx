import React, { useState } from 'react';
import { createStackNavigator } from '@react-navigation/stack';
import { ProfileStackParamList } from '../types';
import { DocumentStatus, DocumentVerificationStatus } from '../types/auth';

// Profile screens
import ProfileMainScreen from '../screens/profile/ProfileMainScreen';
import ProfileScreen from '../screens/profile/ProfileScreen';
import EditProfileScreen from '../screens/profile/EditProfileScreen';
import VehicleInfoScreen from '../screens/profile/VehicleInfoScreen';
import PaymentMethodsScreen from '../screens/profile/PaymentMethodsScreen';
import SettingsScreen from '../screens/profile/SettingsScreen';
import SettingsMainScreen from '../screens/settings/SettingsMainScreen';
import DocumentStatusScreen from '../screens/profile/DocumentStatusScreen';

const Stack = createStackNavigator<ProfileStackParamList>();

const ProfileNavigator: React.FC = () => {
  return (
    <Stack.Navigator
      id={"ProfileStack" as any}
      screenOptions={{
        headerShown: false,
      }}
    >
      <Stack.Screen name="ProfileMain" component={ProfileMainScreen} />
      <Stack.Screen name="EditProfile" component={EditProfileScreen} />
      <Stack.Screen name="ProfileLegacy" component={ProfileScreen} />
      <Stack.Screen name="VehicleInfo" component={VehicleInfoScreen} />
      <Stack.Screen name="PaymentMethods" component={PaymentMethodsScreen} />
      <Stack.Screen name="BankInfo" component={PaymentMethodsScreen} />
      <Stack.Screen name="Settings" component={SettingsMainScreen} />
      <Stack.Screen name="SettingsLegacy" component={SettingsScreen} />
      <Stack.Screen name="DocumentStatus" component={DocumentStatusScreen} />
    </Stack.Navigator>
  );
};



export default ProfileNavigator;
