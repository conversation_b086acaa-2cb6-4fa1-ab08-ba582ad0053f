import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  Text,
  ScrollView,
  RefreshControl,
  TouchableOpacity,
  FlatList,
  Modal,
  TextInput,
  Animated,
  Dimensions,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import { useNavigation } from '@react-navigation/native';
import { Card, Button, Badge, LoadingSpinner } from '../../components/ui';
import { formatCurrency, formatDate, formatTime } from '../../utils/helpers';
import { getStatusColor, getStatusText, getOrderTypeIcon } from '../../utils/orderHelpers';
import {
  DeliveryOrder,
  OrderStatus,
  OrderType,
  OrderHistoryFilter
} from '../../types/deliveryHistory';

// Mock data for now - will be replaced with context
const mockOrders: DeliveryOrder[] = [
  {
    id: '1',
    orderNumber: 'ORD-2024-001',
    status: OrderStatus.DELIVERED,
    type: OrderType.FOOD,
    customer: {
      id: 'c1',
      name: '<PERSON>',
      phone: '+92 300 1234567',
      rating: 4.8,
    },
    restaurant: {
      id: 'r1',
      name: 'KFC DHA',
      address: 'DHA Phase 2, Lahore',
      phone: '+92 42 1234567',
      category: 'Fast Food',
    },
    pickupLocation: {
      latitude: 31.4697,
      longitude: 74.4142,
      address: 'DHA Phase 2, Lahore',
      area: 'DHA',
      city: 'Lahore',
    },
    deliveryLocation: {
      latitude: 31.4504,
      longitude: 74.3588,
      address: 'Gulberg III, Lahore',
      area: 'Gulberg',
      city: 'Lahore',
    },
    items: [
      { id: '1', name: 'Zinger Burger', quantity: 2, price: 800 },
      { id: '2', name: 'Fries', quantity: 1, price: 300 },
    ],
    totalAmount: 1100,
    deliveryFee: 150,
    tip: 100,
    timeline: {
      orderPlaced: '2024-01-20T12:00:00Z',
      orderAccepted: '2024-01-20T12:02:00Z',
      arrivedAtRestaurant: '2024-01-20T12:15:00Z',
      orderPickedUp: '2024-01-20T12:25:00Z',
      deliveryStarted: '2024-01-20T12:26:00Z',
      orderDelivered: '2024-01-20T12:45:00Z',
    },
    metrics: {
      totalDistance: 5.2,
      deliveryTime: 19,
      pickupTime: 10,
      waitingTime: 8,
      averageSpeed: 16.4,
    },
    rating: {
      overall: 5,
      delivery: 5,
      communication: 4,
      timeliness: 5,
      comment: 'Great service, very fast delivery!',
      ratedAt: '2024-01-20T13:00:00Z',
    },
    paymentMethod: 'Cash',
    createdAt: '2024-01-20T12:00:00Z',
    updatedAt: '2024-01-20T12:45:00Z',
  },
  // Add more mock orders...
];

const { height: screenHeight } = Dimensions.get('window');

const OrderHistoryScreen: React.FC = () => {
  const navigation = useNavigation();
  const [orders, setOrders] = useState<DeliveryOrder[]>(mockOrders);
  const [filteredOrders, setFilteredOrders] = useState<DeliveryOrder[]>(mockOrders);
  const [refreshing, setRefreshing] = useState(false);
  const [showFilterModal, setShowFilterModal] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedTab, setSelectedTab] = useState<'all' | 'completed' | 'cancelled' | 'pending'>('all');

  // Animation values
  const slideAnim = useRef(new Animated.Value(screenHeight)).current;
  const backdropAnim = useRef(new Animated.Value(0)).current;
  
  const [filter, setFilter] = useState<OrderHistoryFilter>({
    sortBy: 'date',
    sortOrder: 'desc',
  });



  useEffect(() => {
    applyFilters();
  }, [orders, selectedTab, searchQuery, filter]);

  const applyFilters = () => {
    let filtered = [...orders];

    // Apply tab filter
    if (selectedTab !== 'all') {
      const statusMap = {
        completed: [OrderStatus.DELIVERED],
        cancelled: [OrderStatus.CANCELLED, OrderStatus.REJECTED],
        pending: [OrderStatus.PENDING, OrderStatus.ACCEPTED, OrderStatus.PICKED_UP, OrderStatus.IN_TRANSIT],
      };
      filtered = filtered.filter(order => statusMap[selectedTab].includes(order.status));
    }

    // Apply search filter
    if (searchQuery) {
      filtered = filtered.filter(order =>
        order.orderNumber.toLowerCase().includes(searchQuery.toLowerCase()) ||
        order.customer.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        order.restaurant.name.toLowerCase().includes(searchQuery.toLowerCase())
      );
    }

    // Apply additional filters
    if (filter.status && filter.status.length > 0) {
      filtered = filtered.filter(order => filter.status!.includes(order.status));
    }

    if (filter.type && filter.type.length > 0) {
      filtered = filtered.filter(order => filter.type!.includes(order.type));
    }

    if (filter.startDate) {
      filtered = filtered.filter(order => new Date(order.createdAt) >= new Date(filter.startDate!));
    }

    if (filter.endDate) {
      filtered = filtered.filter(order => new Date(order.createdAt) <= new Date(filter.endDate!));
    }

    if (filter.area) {
      filtered = filtered.filter(order => 
        order.pickupLocation.area.toLowerCase().includes(filter.area!.toLowerCase()) ||
        order.deliveryLocation.area.toLowerCase().includes(filter.area!.toLowerCase())
      );
    }

    // Apply sorting
    filtered.sort((a, b) => {
      let aValue: any, bValue: any;
      
      switch (filter.sortBy) {
        case 'date':
          aValue = new Date(a.createdAt);
          bValue = new Date(b.createdAt);
          break;
        case 'amount':
          aValue = a.totalAmount;
          bValue = b.totalAmount;
          break;
        case 'rating':
          aValue = a.rating?.overall || 0;
          bValue = b.rating?.overall || 0;
          break;
        case 'distance':
          aValue = a.metrics?.totalDistance || 0;
          bValue = b.metrics?.totalDistance || 0;
          break;
        default:
          aValue = new Date(a.createdAt);
          bValue = new Date(b.createdAt);
      }

      if (filter.sortOrder === 'asc') {
        return aValue > bValue ? 1 : -1;
      } else {
        return aValue < bValue ? 1 : -1;
      }
    });

    setFilteredOrders(filtered);
  };

  const onRefresh = async () => {
    setRefreshing(true);
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 1000));
    setRefreshing(false);
  };

  // Modal animation functions
  const openFilterModal = () => {
    setShowFilterModal(true);
    Animated.parallel([
      Animated.timing(backdropAnim, {
        toValue: 1,
        duration: 300,
        useNativeDriver: true,
      }),
      Animated.spring(slideAnim, {
        toValue: 0,
        tension: 65,
        friction: 11,
        useNativeDriver: true,
      }),
    ]).start();
  };

  const closeFilterModal = () => {
    Animated.parallel([
      Animated.timing(backdropAnim, {
        toValue: 0,
        duration: 250,
        useNativeDriver: true,
      }),
      Animated.spring(slideAnim, {
        toValue: screenHeight,
        tension: 65,
        friction: 11,
        useNativeDriver: true,
      }),
    ]).start(() => {
      setShowFilterModal(false);
    });
  };



  const renderTabBar = () => (
    <View style={{
      flexDirection: 'row',
      backgroundColor: '#ffffff',
      paddingHorizontal: 20,
      paddingVertical: 12,
      borderBottomWidth: 1,
      borderBottomColor: '#e5e7eb',
    }}>
      {[
        { key: 'all', label: 'All', count: orders.length },
        { key: 'completed', label: 'Completed', count: orders.filter(o => o.status === OrderStatus.DELIVERED).length },
        { key: 'pending', label: 'Active', count: orders.filter(o => [OrderStatus.PENDING, OrderStatus.ACCEPTED, OrderStatus.PICKED_UP, OrderStatus.IN_TRANSIT].includes(o.status)).length },
        { key: 'cancelled', label: 'Cancelled', count: orders.filter(o => [OrderStatus.CANCELLED, OrderStatus.REJECTED].includes(o.status)).length },
      ].map((tab) => (
        <TouchableOpacity
          key={tab.key}
          onPress={() => setSelectedTab(tab.key as any)}
          style={{
            flex: 1,
            alignItems: 'center',
            paddingVertical: 8,
            borderBottomWidth: 2,
            borderBottomColor: selectedTab === tab.key ? '#f97316' : 'transparent',
          }}
        >
          <Text style={{
            fontSize: 14,
            fontWeight: selectedTab === tab.key ? '600' : '400',
            color: selectedTab === tab.key ? '#f97316' : '#6b7280',
          }}>
            {tab.label}
          </Text>
          <Text style={{
            fontSize: 12,
            color: selectedTab === tab.key ? '#f97316' : '#9ca3af',
            marginTop: 2,
          }}>
            {tab.count}
          </Text>
        </TouchableOpacity>
      ))}
    </View>
  );

  const renderSearchAndFilter = () => (
    <View style={{
      flexDirection: 'row',
      alignItems: 'center',
      paddingHorizontal: 20,
      paddingVertical: 12,
      backgroundColor: '#ffffff',
      gap: 12,
    }}>
      <View style={{
        flex: 1,
        flexDirection: 'row',
        alignItems: 'center',
        backgroundColor: '#f9fafb',
        borderRadius: 8,
        paddingHorizontal: 12,
        paddingVertical: 8,
      }}>
        <Ionicons name="search" size={20} color="#6b7280" />
        <TextInput
          style={{
            flex: 1,
            marginLeft: 8,
            fontSize: 16,
            color: '#111827',
          }}
          placeholder="Search orders..."
          value={searchQuery}
          onChangeText={setSearchQuery}
        />
      </View>
      
      <TouchableOpacity
        onPress={openFilterModal}
        style={{
          flexDirection: 'row',
          alignItems: 'center',
          backgroundColor: '#f97316',
          paddingHorizontal: 12,
          paddingVertical: 8,
          borderRadius: 8,
        }}
      >
        <Ionicons name="filter" size={16} color="white" />
        <Text style={{ color: 'white', fontWeight: '600', marginLeft: 4 }}>
          Filter
        </Text>
      </TouchableOpacity>
    </View>
  );

  const renderOrderItem = ({ item }: { item: DeliveryOrder }) => (
    <TouchableOpacity
      onPress={() => navigation.navigate('OrderDetail' as never, { orderId: item.id } as never)}
      style={{ marginHorizontal: 20, marginBottom: 12 }}
    >
      <Card variant="elevated" padding="md">
        <View style={{ flexDirection: 'row', justifyContent: 'space-between', alignItems: 'flex-start', marginBottom: 12 }}>
          <View style={{ flex: 1 }}>
            <Text style={{ fontSize: 16, fontWeight: 'bold', color: '#111827' }}>
              {item.orderNumber}
            </Text>
            <Text style={{ fontSize: 14, color: '#6b7280', marginTop: 2 }}>
              {formatDate(item.createdAt)} • {formatTime(item.createdAt)}
            </Text>
          </View>
          <Badge
            text={getStatusText(item.status)}
            style={{ backgroundColor: getStatusColor(item.status) }}
          />
        </View>

        <View style={{ flexDirection: 'row', alignItems: 'center', marginBottom: 12 }}>
          <View style={{
            width: 32,
            height: 32,
            borderRadius: 16,
            backgroundColor: '#f97316',
            alignItems: 'center',
            justifyContent: 'center',
            marginRight: 12,
          }}>
            <Ionicons name={getOrderTypeIcon(item.type)} size={16} color="white" />
          </View>
          <View style={{ flex: 1 }}>
            <Text style={{ fontSize: 14, fontWeight: '600', color: '#111827' }}>
              {item.restaurant.name}
            </Text>
            <Text style={{ fontSize: 12, color: '#6b7280' }}>
              {item.customer.name} • {item.deliveryLocation.area}
            </Text>
          </View>
        </View>

        <View style={{ flexDirection: 'row', justifyContent: 'space-between', alignItems: 'center' }}>
          <View style={{ flexDirection: 'row', alignItems: 'center', gap: 16 }}>
            <View style={{ alignItems: 'center' }}>
              <Text style={{ fontSize: 14, fontWeight: 'bold', color: '#111827' }}>
                {formatCurrency(item.totalAmount + item.deliveryFee + item.tip)}
              </Text>
              <Text style={{ fontSize: 12, color: '#6b7280' }}>Total</Text>
            </View>
            
            {item.metrics && (
              <View style={{ alignItems: 'center' }}>
                <Text style={{ fontSize: 14, fontWeight: 'bold', color: '#111827' }}>
                  {item.metrics.totalDistance.toFixed(1)} km
                </Text>
                <Text style={{ fontSize: 12, color: '#6b7280' }}>Distance</Text>
              </View>
            )}
            
            {item.rating && (
              <View style={{ flexDirection: 'row', alignItems: 'center' }}>
                <Ionicons name="star" size={14} color="#f59e0b" />
                <Text style={{ fontSize: 14, fontWeight: 'bold', color: '#111827', marginLeft: 4 }}>
                  {item.rating.overall.toFixed(1)}
                </Text>
              </View>
            )}
          </View>
          
          <Ionicons name="chevron-forward" size={20} color="#9ca3af" />
        </View>
      </Card>
    </TouchableOpacity>
  );

  return (
    <SafeAreaView style={{ flex: 1, backgroundColor: '#f8fafc' }}>
      <View style={{
        paddingHorizontal: 20,
        paddingVertical: 16,
        backgroundColor: '#ffffff',
        borderBottomWidth: 1,
        borderBottomColor: '#e5e7eb',
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between',
      }}>
        <View style={{ flexDirection: 'row', alignItems: 'center' }}>
          <TouchableOpacity
            onPress={() => navigation.goBack()}
            style={{ marginRight: 16 }}
          >
            <Ionicons name="arrow-back" size={24} color="#111827" />
          </TouchableOpacity>
          <Text style={{ fontSize: 24, fontWeight: 'bold', color: '#111827' }}>
            Order History
          </Text>
        </View>
        
        <TouchableOpacity
          onPress={() => navigation.navigate('OrderHistoryAnalytics' as never)}
          style={{
            flexDirection: 'row',
            alignItems: 'center',
            backgroundColor: '#f97316',
            paddingHorizontal: 12,
            paddingVertical: 6,
            borderRadius: 8,
          }}
        >
          <Ionicons name="analytics" size={16} color="white" />
          <Text style={{ color: 'white', fontWeight: '600', marginLeft: 4 }}>
            Analytics
          </Text>
        </TouchableOpacity>
      </View>

      {renderTabBar()}
      {renderSearchAndFilter()}

      <FlatList
        data={filteredOrders}
        renderItem={renderOrderItem}
        keyExtractor={(item) => item.id}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
        showsVerticalScrollIndicator={false}
        contentContainerStyle={{ paddingVertical: 12 }}
        ListEmptyComponent={
          <View style={{ alignItems: 'center', paddingVertical: 40 }}>
            <Ionicons name="document-text-outline" size={64} color="#d1d5db" />
            <Text style={{ fontSize: 18, fontWeight: 'bold', color: '#6b7280', marginTop: 16 }}>
              No orders found
            </Text>
            <Text style={{ fontSize: 14, color: '#9ca3af', textAlign: 'center', marginTop: 4 }}>
              Try adjusting your filters or search criteria
            </Text>
          </View>
        }
      />

      {/* Filter Modal */}
      <Modal
        visible={showFilterModal}
        animationType="none"
        transparent={true}
        onRequestClose={closeFilterModal}
      >
        <View style={{ flex: 1 }}>
          {/* Backdrop */}
          <Animated.View
            style={{
              position: 'absolute',
              top: 0,
              left: 0,
              right: 0,
              bottom: 0,
              backgroundColor: 'rgba(0, 0, 0, 0.5)',
              opacity: backdropAnim,
            }}
          >
            <TouchableOpacity
              style={{ flex: 1 }}
              activeOpacity={1}
              onPress={closeFilterModal}
            />
          </Animated.View>

          {/* Modal Content */}
          <Animated.View
            style={{
              position: 'absolute',
              bottom: 0,
              left: 0,
              right: 0,
              backgroundColor: '#f8fafc',
              borderTopLeftRadius: 20,
              borderTopRightRadius: 20,
              height: screenHeight * 0.85,
              transform: [{ translateY: slideAnim }],
            }}
          >
            {/* Header */}
            <View style={{
              paddingHorizontal: 20,
              paddingVertical: 16,
              backgroundColor: '#ffffff',
              borderBottomWidth: 1,
              borderBottomColor: '#e5e7eb',
              flexDirection: 'row',
              alignItems: 'center',
              justifyContent: 'space-between',
              borderTopLeftRadius: 20,
              borderTopRightRadius: 20,
            }}>
              <Text style={{ fontSize: 20, fontWeight: 'bold', color: '#111827' }}>
                Filter Orders
              </Text>
              <TouchableOpacity onPress={closeFilterModal}>
                <Ionicons name="close" size={24} color="#111827" />
              </TouchableOpacity>
            </View>

            {/* Scrollable Content */}
            <ScrollView
              style={{ flex: 1 }}
              contentContainerStyle={{ padding: 20, paddingBottom: 100 }}
              showsVerticalScrollIndicator={true}
            >
              {/* Order Status Filter */}
            <Card variant="elevated" padding="lg" style={{ marginBottom: 20 }}>
              <Text style={{ fontSize: 16, fontWeight: '600', color: '#111827', marginBottom: 12 }}>
                Order Status
              </Text>
              <View style={{ flexDirection: 'row', flexWrap: 'wrap', gap: 8 }}>
                {Object.values(OrderStatus).map((status) => (
                  <TouchableOpacity
                    key={status}
                    onPress={() => {
                      const currentStatuses = filter.status || [];
                      const newStatuses = currentStatuses.includes(status)
                        ? currentStatuses.filter(s => s !== status)
                        : [...currentStatuses, status];
                      setFilter({ ...filter, status: newStatuses });
                    }}
                    style={{
                      paddingHorizontal: 12,
                      paddingVertical: 6,
                      borderRadius: 16,
                      borderWidth: 1,
                      borderColor: filter.status?.includes(status) ? '#f97316' : '#e5e7eb',
                      backgroundColor: filter.status?.includes(status) ? '#fef3e2' : '#ffffff',
                    }}
                  >
                    <Text style={{
                      fontSize: 14,
                      color: filter.status?.includes(status) ? '#f97316' : '#6b7280',
                      fontWeight: filter.status?.includes(status) ? '600' : '400',
                    }}>
                      {getStatusText(status)}
                    </Text>
                  </TouchableOpacity>
                ))}
              </View>
            </Card>

            {/* Order Type Filter */}
            <Card variant="elevated" padding="lg" style={{ marginBottom: 20 }}>
              <Text style={{ fontSize: 16, fontWeight: '600', color: '#111827', marginBottom: 12 }}>
                Order Type
              </Text>
              <View style={{ flexDirection: 'row', flexWrap: 'wrap', gap: 8 }}>
                {Object.values(OrderType).map((type) => (
                  <TouchableOpacity
                    key={type}
                    onPress={() => {
                      const currentTypes = filter.type || [];
                      const newTypes = currentTypes.includes(type)
                        ? currentTypes.filter(t => t !== type)
                        : [...currentTypes, type];
                      setFilter({ ...filter, type: newTypes });
                    }}
                    style={{
                      paddingHorizontal: 12,
                      paddingVertical: 6,
                      borderRadius: 16,
                      borderWidth: 1,
                      borderColor: filter.type?.includes(type) ? '#f97316' : '#e5e7eb',
                      backgroundColor: filter.type?.includes(type) ? '#fef3e2' : '#ffffff',
                    }}
                  >
                    <Text style={{
                      fontSize: 14,
                      color: filter.type?.includes(type) ? '#f97316' : '#6b7280',
                      fontWeight: filter.type?.includes(type) ? '600' : '400',
                    }}>
                      {type.charAt(0).toUpperCase() + type.slice(1)}
                    </Text>
                  </TouchableOpacity>
                ))}
              </View>
            </Card>

            {/* Date Range Filter */}
            <Card variant="elevated" padding="lg" style={{ marginBottom: 20 }}>
              <Text style={{ fontSize: 16, fontWeight: '600', color: '#111827', marginBottom: 12 }}>
                Date Range
              </Text>
              <View style={{ gap: 12 }}>
                <TextInput
                  style={{
                    borderWidth: 1,
                    borderColor: '#d1d5db',
                    borderRadius: 8,
                    padding: 12,
                    fontSize: 16,
                    backgroundColor: '#ffffff',
                  }}
                  placeholder="Start Date (YYYY-MM-DD)"
                  value={filter.startDate || ''}
                  onChangeText={(text) => setFilter({ ...filter, startDate: text })}
                />
                <TextInput
                  style={{
                    borderWidth: 1,
                    borderColor: '#d1d5db',
                    borderRadius: 8,
                    padding: 12,
                    fontSize: 16,
                    backgroundColor: '#ffffff',
                  }}
                  placeholder="End Date (YYYY-MM-DD)"
                  value={filter.endDate || ''}
                  onChangeText={(text) => setFilter({ ...filter, endDate: text })}
                />
              </View>
            </Card>

            {/* Area Filter */}
            <Card variant="elevated" padding="lg" style={{ marginBottom: 20 }}>
              <Text style={{ fontSize: 16, fontWeight: '600', color: '#111827', marginBottom: 12 }}>
                Area
              </Text>
              <TextInput
                style={{
                  borderWidth: 1,
                  borderColor: '#d1d5db',
                  borderRadius: 8,
                  padding: 12,
                  fontSize: 16,
                  backgroundColor: '#ffffff',
                }}
                placeholder="Enter area name"
                value={filter.area || ''}
                onChangeText={(text) => setFilter({ ...filter, area: text })}
              />
            </Card>

            {/* Sort Options */}
            <Card variant="elevated" padding="lg" style={{ marginBottom: 20 }}>
              <Text style={{ fontSize: 16, fontWeight: '600', color: '#111827', marginBottom: 12 }}>
                Sort By
              </Text>
              <View style={{ gap: 8 }}>
                {[
                  { key: 'date', label: 'Date' },
                  { key: 'amount', label: 'Amount' },
                  { key: 'rating', label: 'Rating' },
                  { key: 'distance', label: 'Distance' },
                ].map((option) => (
                  <TouchableOpacity
                    key={option.key}
                    onPress={() => setFilter({ ...filter, sortBy: option.key as any })}
                    style={{
                      flexDirection: 'row',
                      alignItems: 'center',
                      paddingVertical: 8,
                    }}
                  >
                    <View style={{
                      width: 20,
                      height: 20,
                      borderRadius: 10,
                      borderWidth: 2,
                      borderColor: filter.sortBy === option.key ? '#f97316' : '#d1d5db',
                      alignItems: 'center',
                      justifyContent: 'center',
                      marginRight: 12,
                    }}>
                      {filter.sortBy === option.key && (
                        <View style={{
                          width: 10,
                          height: 10,
                          borderRadius: 5,
                          backgroundColor: '#f97316',
                        }} />
                      )}
                    </View>
                    <Text style={{
                      fontSize: 14,
                      color: '#111827',
                      fontWeight: filter.sortBy === option.key ? '600' : '400',
                    }}>
                      {option.label}
                    </Text>
                  </TouchableOpacity>
                ))}
              </View>

              <View style={{ flexDirection: 'row', marginTop: 12, gap: 8 }}>
                <TouchableOpacity
                  onPress={() => setFilter({ ...filter, sortOrder: 'asc' })}
                  style={{
                    flex: 1,
                    padding: 8,
                    borderRadius: 8,
                    borderWidth: 1,
                    borderColor: filter.sortOrder === 'asc' ? '#f97316' : '#e5e7eb',
                    backgroundColor: filter.sortOrder === 'asc' ? '#fef3e2' : '#ffffff',
                    alignItems: 'center',
                  }}
                >
                  <Text style={{
                    fontSize: 14,
                    color: filter.sortOrder === 'asc' ? '#f97316' : '#6b7280',
                    fontWeight: filter.sortOrder === 'asc' ? '600' : '400',
                  }}>
                    Ascending
                  </Text>
                </TouchableOpacity>
                <TouchableOpacity
                  onPress={() => setFilter({ ...filter, sortOrder: 'desc' })}
                  style={{
                    flex: 1,
                    padding: 8,
                    borderRadius: 8,
                    borderWidth: 1,
                    borderColor: filter.sortOrder === 'desc' ? '#f97316' : '#e5e7eb',
                    backgroundColor: filter.sortOrder === 'desc' ? '#fef3e2' : '#ffffff',
                    alignItems: 'center',
                  }}
                >
                  <Text style={{
                    fontSize: 14,
                    color: filter.sortOrder === 'desc' ? '#f97316' : '#6b7280',
                    fontWeight: filter.sortOrder === 'desc' ? '600' : '400',
                  }}>
                    Descending
                  </Text>
                </TouchableOpacity>
              </View>
            </Card>
            </ScrollView>

            {/* Fixed Bottom Buttons */}
            <View style={{
              padding: 20,
              paddingBottom: 30,
              backgroundColor: '#ffffff',
              borderTopWidth: 1,
              borderTopColor: '#e5e7eb',
            }}>
              <View style={{ flexDirection: 'row', gap: 12 }}>
                <Button
                  title="Clear Filters"
                  variant="outline"
                  onPress={() => {
                    setFilter({ sortBy: 'date', sortOrder: 'desc' });
                    closeFilterModal();
                  }}
                  style={{ flex: 1 }}
                />
                <Button
                  title="Apply Filters"
                  onPress={closeFilterModal}
                  style={{ flex: 1 }}
                />
              </View>
            </View>
          </Animated.View>
        </View>
      </Modal>
    </SafeAreaView>
  );
};

export default OrderHistoryScreen;
