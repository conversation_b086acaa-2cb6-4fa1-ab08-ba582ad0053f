import React from 'react';
import { View, Text, TouchableOpacity, ViewStyle } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import Modal from './Modal';

interface AlertButton {
  text: string;
  onPress: () => void;
  style?: 'default' | 'cancel' | 'destructive';
}

interface AlertProps {
  isVisible: boolean;
  title: string;
  message?: string;
  icon?: keyof typeof Ionicons.glyphMap;
  variant?: 'info' | 'success' | 'warning' | 'error';
  buttons: AlertButton[];
  onClose: () => void;
}

const Alert: React.FC<AlertProps> = ({
  isVisible,
  title,
  message,
  icon,
  variant = 'info',
  buttons,
  onClose,
}) => {
  const getVariantStyles = () => {
    switch (variant) {
      case 'success':
        return {
          iconColor: '#10b981',
          iconBg: '#d1fae5',
          borderColor: '#10b981',
        };
      case 'warning':
        return {
          iconColor: '#f59e0b',
          iconBg: '#fef3c7',
          borderColor: '#f59e0b',
        };
      case 'error':
        return {
          iconColor: '#dc2626',
          iconBg: '#fee2e2',
          borderColor: '#dc2626',
        };
      default:
        return {
          iconColor: '#3b82f6',
          iconBg: '#dbeafe',
          borderColor: '#3b82f6',
        };
    }
  };

  const getDefaultIcon = () => {
    if (icon) return icon;
    
    switch (variant) {
      case 'success': return 'checkmark-circle';
      case 'warning': return 'warning';
      case 'error': return 'alert-circle';
      default: return 'information-circle';
    }
  };

  const getButtonStyle = (buttonStyle: string = 'default'): ViewStyle => {
    const baseStyle: ViewStyle = {
      flex: 1,
      paddingVertical: 14,
      paddingHorizontal: 20,
      borderRadius: 12,
      alignItems: 'center',
      justifyContent: 'center',
      marginHorizontal: 4,
    };

    switch (buttonStyle) {
      case 'destructive':
        return {
          ...baseStyle,
          backgroundColor: '#dc2626',
          shadowColor: '#dc2626',
          shadowOffset: { width: 0, height: 4 },
          shadowOpacity: 0.3,
          shadowRadius: 8,
          elevation: 6,
        };
      case 'cancel':
        return {
          ...baseStyle,
          backgroundColor: '#f8fafc',
          borderWidth: 2,
          borderColor: '#e2e8f0',
        };
      default:
        return {
          ...baseStyle,
          backgroundColor: '#dc2626',
          shadowColor: '#dc2626',
          shadowOffset: { width: 0, height: 4 },
          shadowOpacity: 0.2,
          shadowRadius: 8,
          elevation: 4,
        };
    }
  };

  const getButtonTextColor = (buttonStyle: string = 'default'): string => {
    switch (buttonStyle) {
      case 'cancel': return '#374151';
      default: return '#ffffff';
    }
  };

  const variantStyles = getVariantStyles();

  return (
    <Modal
      isVisible={isVisible}
      onClose={onClose}
      variant="center"
      size="sm"
      showCloseButton={false}
      closeOnBackdrop={false}
    >
      <View style={{ padding: 24, alignItems: 'center' }}>
        {/* Icon */}
        <View style={{
          width: 80,
          height: 80,
          borderRadius: 40,
          backgroundColor: variantStyles.iconBg,
          alignItems: 'center',
          justifyContent: 'center',
          marginBottom: 24,
          borderWidth: 3,
          borderColor: variantStyles.borderColor,
          shadowColor: variantStyles.iconColor,
          shadowOffset: { width: 0, height: 4 },
          shadowOpacity: 0.2,
          shadowRadius: 8,
          elevation: 6,
        }}>
          <Ionicons
            name={getDefaultIcon() as any}
            size={40}
            color={variantStyles.iconColor}
          />
        </View>

        {/* Title */}
        <Text style={{
          fontSize: 20,
          fontWeight: 'bold',
          color: '#111827',
          textAlign: 'center',
          marginBottom: 8,
        }}>
          {title}
        </Text>

        {/* Message */}
        {message && (
          <Text style={{
            fontSize: 16,
            color: '#6b7280',
            textAlign: 'center',
            lineHeight: 24,
            marginBottom: 24,
          }}>
            {message}
          </Text>
        )}

        {/* Buttons */}
        <View style={{
          flexDirection: 'row',
          width: '100%',
          marginTop: 8,
        }}>
          {buttons.map((button, index) => (
            <TouchableOpacity
              key={index}
              style={getButtonStyle(button.style)}
              onPress={() => {
                button.onPress();
                onClose();
              }}
            >
              <Text style={{
                fontSize: 16,
                fontWeight: 'bold',
                color: getButtonTextColor(button.style),
              }}>
                {button.text}
              </Text>
            </TouchableOpacity>
          ))}
        </View>
      </View>
    </Modal>
  );
};

export default Alert;
