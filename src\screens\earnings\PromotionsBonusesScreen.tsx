import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  Alert,
  StatusBar,
  Animated,
  RefreshControl,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import { useNavigation } from '@react-navigation/native';
import { LinearGradient } from 'expo-linear-gradient';

interface Promotion {
  id: string;
  title: string;
  description: string;
  type: 'bonus' | 'challenge' | 'reward';
  amount: number;
  progress: number;
  target: number;
  expiresAt: string;
  status: 'active' | 'completed' | 'expired';
  icon: string;
  color: string;
}

interface BonusHistory {
  id: string;
  type: string;
  amount: number;
  date: string;
  description: string;
  icon: string;
  color: string;
}

const PromotionsBonusesScreen: React.FC = () => {
  const navigation = useNavigation();
  const [refreshing, setRefreshing] = useState(false);
  const [fadeAnimation] = useState(new Animated.Value(0));
  const [selectedTab, setSelectedTab] = useState<'active' | 'history'>('active');

  const [activePromotions] = useState<Promotion[]>([
    {
      id: '1',
      title: 'Peak Hour Champion',
      description: 'Complete 10 deliveries during peak hours (6-9 PM)',
      type: 'challenge',
      amount: 500,
      progress: 7,
      target: 10,
      expiresAt: '2024-01-25',
      status: 'active',
      icon: 'flash',
      color: '#f59e0b',
    },
    {
      id: '2',
      title: 'Weekend Warrior',
      description: 'Complete 15 deliveries this weekend',
      type: 'bonus',
      amount: 750,
      progress: 12,
      target: 15,
      expiresAt: '2024-01-21',
      status: 'active',
      icon: 'trophy',
      color: '#10b981',
    },
    {
      id: '3',
      title: 'Customer Favorite',
      description: 'Maintain 4.8+ rating for 20 deliveries',
      type: 'reward',
      amount: 300,
      progress: 18,
      target: 20,
      expiresAt: '2024-01-30',
      status: 'active',
      icon: 'star',
      color: '#dc2626',
    },
    {
      id: '4',
      title: 'Speed Demon',
      description: 'Complete deliveries under 25 minutes average',
      type: 'challenge',
      amount: 400,
      progress: 20,
      target: 20,
      expiresAt: '2024-01-23',
      status: 'completed',
      icon: 'speedometer',
      color: '#3b82f6',
    },
  ]);

  const [bonusHistory] = useState<BonusHistory[]>([
    {
      id: '1',
      type: 'Peak Hour Bonus',
      amount: 45,
      date: '2024-01-20',
      description: 'Delivery during high demand period',
      icon: 'flash',
      color: '#f59e0b',
    },
    {
      id: '2',
      type: 'Weather Bonus',
      amount: 25,
      date: '2024-01-19',
      description: 'Delivery during rainy weather',
      icon: 'rainy',
      color: '#3b82f6',
    },
    {
      id: '3',
      type: 'Completion Bonus',
      amount: 500,
      date: '2024-01-18',
      description: 'Weekend Warrior challenge completed',
      icon: 'trophy',
      color: '#10b981',
    },
    {
      id: '4',
      type: 'Rating Bonus',
      amount: 300,
      date: '2024-01-17',
      description: 'Customer Favorite reward claimed',
      icon: 'star',
      color: '#dc2626',
    },
  ]);

  useEffect(() => {
    Animated.timing(fadeAnimation, {
      toValue: 1,
      duration: 800,
      useNativeDriver: true,
    }).start();
  }, []);

  const onRefresh = async () => {
    setRefreshing(true);
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 1000));
    setRefreshing(false);
  };

  const handleClaimBonus = (promotion: Promotion) => {
    if (promotion.status === 'completed') {
      Alert.alert(
        'Claim Bonus',
        `Congratulations! You've earned PKR ${promotion.amount} for completing "${promotion.title}".`,
        [
          { text: 'Cancel', style: 'cancel' },
          { text: 'Claim Now', onPress: () => claimBonus(promotion) },
        ]
      );
    }
  };

  const claimBonus = (promotion: Promotion) => {
    Alert.alert('Success!', `PKR ${promotion.amount} has been added to your wallet.`);
  };

  const getProgressPercentage = (progress: number, target: number) => {
    return Math.min((progress / target) * 100, 100);
  };

  const renderPromotionCard = (promotion: Promotion) => (
    <TouchableOpacity
      key={promotion.id}
      onPress={() => promotion.status === 'completed' && handleClaimBonus(promotion)}
      style={{
        backgroundColor: 'white',
        marginHorizontal: 20,
        marginVertical: 8,
        borderRadius: 20,
        padding: 20,
        shadowColor: promotion.color,
        shadowOffset: { width: 0, height: 8 },
        shadowOpacity: 0.1,
        shadowRadius: 16,
        elevation: 8,
        borderWidth: 1,
        borderColor: `${promotion.color}20`,
      }}
    >
      <View style={{
        flexDirection: 'row',
        alignItems: 'center',
        marginBottom: 16,
      }}>
        <View style={{
          width: 56,
          height: 56,
          backgroundColor: `${promotion.color}20`,
          borderRadius: 28,
          alignItems: 'center',
          justifyContent: 'center',
          marginRight: 16,
        }}>
          <Ionicons name={promotion.icon as any} size={28} color={promotion.color} />
        </View>
        
        <View style={{ flex: 1 }}>
          <Text style={{
            fontSize: 18,
            fontWeight: 'bold',
            color: '#111827',
            marginBottom: 4,
          }}>
            {promotion.title}
          </Text>
          <Text style={{
            fontSize: 14,
            color: '#6b7280',
            lineHeight: 20,
          }}>
            {promotion.description}
          </Text>
        </View>
        
        <View style={{
          backgroundColor: `${promotion.color}20`,
          paddingHorizontal: 12,
          paddingVertical: 6,
          borderRadius: 16,
        }}>
          <Text style={{
            fontSize: 14,
            fontWeight: 'bold',
            color: promotion.color,
          }}>
            PKR {promotion.amount}
          </Text>
        </View>
      </View>

      {/* Progress Bar */}
      <View style={{
        backgroundColor: '#f3f4f6',
        height: 8,
        borderRadius: 4,
        marginBottom: 12,
      }}>
        <View style={{
          width: `${getProgressPercentage(promotion.progress, promotion.target)}%`,
          height: '100%',
          backgroundColor: promotion.color,
          borderRadius: 4,
        }} />
      </View>

      <View style={{
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
      }}>
        <Text style={{
          fontSize: 14,
          color: '#6b7280',
          fontWeight: '500',
        }}>
          {promotion.progress}/{promotion.target} completed
        </Text>
        
        <View style={{
          flexDirection: 'row',
          alignItems: 'center',
        }}>
          {promotion.status === 'completed' && (
            <View style={{
              backgroundColor: '#10b981',
              paddingHorizontal: 8,
              paddingVertical: 4,
              borderRadius: 12,
              marginRight: 8,
            }}>
              <Text style={{
                fontSize: 10,
                fontWeight: 'bold',
                color: 'white',
              }}>
                READY TO CLAIM
              </Text>
            </View>
          )}
          
          <Text style={{
            fontSize: 12,
            color: '#6b7280',
          }}>
            Expires: {new Date(promotion.expiresAt).toLocaleDateString()}
          </Text>
        </View>
      </View>
    </TouchableOpacity>
  );

  const renderBonusHistoryItem = (bonus: BonusHistory) => (
    <View key={bonus.id} style={{
      flexDirection: 'row',
      alignItems: 'center',
      paddingVertical: 16,
      paddingHorizontal: 20,
      borderBottomWidth: 1,
      borderBottomColor: '#f3f4f6',
    }}>
      <View style={{
        width: 48,
        height: 48,
        backgroundColor: `${bonus.color}20`,
        borderRadius: 24,
        alignItems: 'center',
        justifyContent: 'center',
        marginRight: 16,
      }}>
        <Ionicons name={bonus.icon as any} size={24} color={bonus.color} />
      </View>
      
      <View style={{ flex: 1 }}>
        <Text style={{
          fontSize: 16,
          fontWeight: 'bold',
          color: '#111827',
          marginBottom: 4,
        }}>
          {bonus.type}
        </Text>
        <Text style={{
          fontSize: 14,
          color: '#6b7280',
          marginBottom: 2,
        }}>
          {bonus.description}
        </Text>
        <Text style={{
          fontSize: 12,
          color: '#9ca3af',
        }}>
          {new Date(bonus.date).toLocaleDateString()}
        </Text>
      </View>
      
      <Text style={{
        fontSize: 18,
        fontWeight: 'bold',
        color: '#10b981',
      }}>
        +PKR {bonus.amount}
      </Text>
    </View>
  );

  const renderTabBar = () => (
    <View style={{
      backgroundColor: 'white',
      marginHorizontal: 20,
      marginTop: -12,
      borderRadius: 20,
      padding: 8,
      shadowColor: '#dc2626',
      shadowOffset: { width: 0, height: 8 },
      shadowOpacity: 0.1,
      shadowRadius: 16,
      elevation: 8,
      borderWidth: 1,
      borderColor: 'rgba(220, 38, 38, 0.1)',
    }}>
      <View style={{
        flexDirection: 'row',
        gap: 4,
      }}>
        {[
          { key: 'active', label: 'Active Promotions', icon: 'flash' },
          { key: 'history', label: 'Bonus History', icon: 'time' },
        ].map((tab) => (
          <TouchableOpacity
            key={tab.key}
            onPress={() => setSelectedTab(tab.key as any)}
            style={{
              flex: 1,
              paddingVertical: 12,
              paddingHorizontal: 16,
              borderRadius: 16,
              backgroundColor: selectedTab === tab.key ? '#dc2626' : 'transparent',
              alignItems: 'center',
              flexDirection: 'row',
              justifyContent: 'center',
            }}
          >
            <Ionicons
              name={tab.icon as any}
              size={16}
              color={selectedTab === tab.key ? 'white' : '#6b7280'}
              style={{ marginRight: 8 }}
            />
            <Text style={{
              fontSize: 14,
              fontWeight: 'bold',
              color: selectedTab === tab.key ? 'white' : '#6b7280',
            }}>
              {tab.label}
            </Text>
          </TouchableOpacity>
        ))}
      </View>
    </View>
  );

  return (
    <View style={{ flex: 1, backgroundColor: '#f1f5f9' }}>
      <SafeAreaView style={{ flex: 1 }}>
        <StatusBar barStyle="light-content" backgroundColor="#dc2626" />
        
        {/* Enhanced Header */}
        <View style={{
          backgroundColor: '#dc2626',
          paddingHorizontal: 20,
          paddingTop: 20,
          paddingBottom: 28,
          borderBottomLeftRadius: 28,
          borderBottomRightRadius: 28,
          shadowColor: '#dc2626',
          shadowOffset: { width: 0, height: 12 },
          shadowOpacity: 0.3,
          shadowRadius: 20,
          elevation: 16,
        }}>
          <View style={{
            flexDirection: 'row',
            alignItems: 'center',
            marginBottom: 20,
          }}>
            <TouchableOpacity
              onPress={() => navigation.goBack()}
              style={{
                width: 48,
                height: 48,
                borderRadius: 24,
                backgroundColor: 'rgba(255,255,255,0.2)',
                justifyContent: 'center',
                alignItems: 'center',
                marginRight: 16,
                borderWidth: 2,
                borderColor: 'rgba(255,255,255,0.3)',
              }}
            >
              <Ionicons name="arrow-back" size={24} color="white" />
            </TouchableOpacity>
            
            <View style={{ flex: 1 }}>
              <Text style={{
                fontSize: 24,
                fontWeight: 'bold',
                color: 'white',
                marginBottom: 4,
              }}>
                🎯 Promotions & Bonuses
              </Text>
              <Text style={{
                fontSize: 14,
                color: 'rgba(255,255,255,0.8)',
                fontWeight: '500',
              }}>
                Earn extra rewards and bonuses
              </Text>
            </View>
          </View>
        </View>

        {renderTabBar()}

        <ScrollView
          style={{ flex: 1 }}
          refreshControl={<RefreshControl refreshing={refreshing} onRefresh={onRefresh} />}
          showsVerticalScrollIndicator={false}
        >
          <Animated.View style={{ opacity: fadeAnimation }}>
            {selectedTab === 'active' ? (
              <View style={{ marginTop: 16 }}>
                {activePromotions.map(renderPromotionCard)}
              </View>
            ) : (
              <View style={{
                backgroundColor: 'white',
                marginHorizontal: 20,
                marginTop: 16,
                borderRadius: 20,
                shadowColor: '#000',
                shadowOffset: { width: 0, height: 8 },
                shadowOpacity: 0.1,
                shadowRadius: 16,
                elevation: 8,
                borderWidth: 1,
                borderColor: 'rgba(0, 0, 0, 0.05)',
              }}>
                <View style={{
                  paddingHorizontal: 20,
                  paddingVertical: 20,
                  borderBottomWidth: 1,
                  borderBottomColor: '#f3f4f6',
                }}>
                  <Text style={{
                    fontSize: 20,
                    fontWeight: 'bold',
                    color: '#111827',
                  }}>
                    💰 Recent Bonuses
                  </Text>
                  <Text style={{
                    fontSize: 14,
                    color: '#6b7280',
                    marginTop: 4,
                  }}>
                    Your bonus earning history
                  </Text>
                </View>

                {bonusHistory.map(renderBonusHistoryItem)}
              </View>
            )}

            <View style={{ height: 32 }} />
          </Animated.View>
        </ScrollView>
      </SafeAreaView>
    </View>
  );
};

export default PromotionsBonusesScreen;
