import React, { useEffect, useState } from 'react';
import {
  View,
  Text,
  ScrollView,
  RefreshControl,
  TouchableOpacity,
  Alert,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import { useNavigation } from '@react-navigation/native';
import { Card, Button, Badge, LoadingSpinner } from '../../components/ui';
import { useOrders } from '../../context/OrderContext';
import { Order, OrderStatus } from '../../types/orders';
import { formatCurrency, formatDistance, formatDuration } from '../../utils/helpers';

const OrderListScreen: React.FC = () => {
  const navigation = useNavigation();
  const { state, fetchAvailableOrders, acceptOrder, declineOrder, clearError } = useOrders();
  const [refreshing, setRefreshing] = useState(false);

  useEffect(() => {
    fetchAvailableOrders();
  }, []);

  const onRefresh = async () => {
    setRefreshing(true);
    await fetchAvailableOrders();
    setRefreshing(false);
  };

  const handleAcceptOrder = async (orderId: string) => {
    try {
      await acceptOrder(orderId);
      Alert.alert('Success', 'Order accepted successfully!');
    } catch (error: any) {
      Alert.alert('Error', error.message);
    }
  };

  const handleDeclineOrder = async (orderId: string) => {
    Alert.alert(
      'Decline Order',
      'Are you sure you want to decline this order?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Decline',
          style: 'destructive',
          onPress: async () => {
            try {
              await declineOrder(orderId);
            } catch (error: any) {
              Alert.alert('Error', error.message);
            }
          },
        },
      ]
    );
  };

  const handleOrderDetails = (order: Order) => {
    (navigation as any).navigate('OrderDetails', { orderId: order.id });
  };

  const renderOrderCard = (order: Order) => (
    <View key={order.id} style={{
      backgroundColor: 'white',
      marginHorizontal: 20,
      marginBottom: 16,
      borderRadius: 20,
      padding: 20,
      shadowColor: '#dc2626',
      shadowOffset: { width: 0, height: 8 },
      shadowOpacity: 0.1,
      shadowRadius: 16,
      elevation: 8,
      borderWidth: 1,
      borderColor: 'rgba(220, 38, 38, 0.05)',
    }}>
      <View style={{ flexDirection: 'row', justifyContent: 'space-between', marginBottom: 16 }}>
        <View style={{ flex: 1 }}>
          <View style={{ flexDirection: 'row', alignItems: 'center', marginBottom: 8 }}>
            <View style={{
              width: 48,
              height: 48,
              backgroundColor: '#fef2f2',
              borderRadius: 24,
              justifyContent: 'center',
              alignItems: 'center',
              marginRight: 12,
              borderWidth: 2,
              borderColor: '#dc2626',
            }}>
              <Ionicons name="restaurant" size={24} color="#dc2626" />
            </View>
            <View style={{ flex: 1 }}>
              <Text style={{ fontSize: 18, fontWeight: 'bold', color: '#111827' }}>
                {order.restaurant.name}
              </Text>
              <Text style={{ fontSize: 14, color: '#6b7280' }}>
                Order #{order.orderNumber}
              </Text>
            </View>
          </View>
        </View>
        <View style={{
          backgroundColor: '#d1fae5',
          paddingHorizontal: 16,
          paddingVertical: 8,
          borderRadius: 16,
          borderWidth: 2,
          borderColor: '#10b981',
          shadowColor: '#10b981',
          shadowOffset: { width: 0, height: 4 },
          shadowOpacity: 0.2,
          shadowRadius: 8,
          elevation: 6,
        }}>
          <Text style={{
            color: '#065f46',
            fontWeight: 'bold',
            fontSize: 16,
          }}>
            {formatCurrency(order.estimatedEarnings)}
          </Text>
        </View>
      </View>

      <View style={{
        backgroundColor: '#f8fafc',
        borderRadius: 16,
        padding: 16,
        marginBottom: 16,
        borderWidth: 1,
        borderColor: '#e2e8f0',
      }}>
        <View style={{
          flexDirection: 'row',
          alignItems: 'center',
          marginBottom: 12,
        }}>
          <View style={{
            width: 32,
            height: 32,
            backgroundColor: '#fef2f2',
            borderRadius: 16,
            justifyContent: 'center',
            alignItems: 'center',
            marginRight: 12,
          }}>
            <Ionicons name="location" size={16} color="#dc2626" />
          </View>
          <Text style={{ fontSize: 15, color: '#374151', flex: 1, fontWeight: '500' }}>
            {order.restaurant.address.street}, {order.restaurant.address.city}
          </Text>
        </View>

        <View style={{
          flexDirection: 'row',
          alignItems: 'center',
        }}>
          <View style={{
            width: 32,
            height: 32,
            backgroundColor: '#dbeafe',
            borderRadius: 16,
            justifyContent: 'center',
            alignItems: 'center',
            marginRight: 12,
          }}>
            <Ionicons name="navigate" size={16} color="#3b82f6" />
          </View>
          <Text style={{ fontSize: 15, color: '#374151', flex: 1, fontWeight: '500' }}>
            {order.deliveryAddress.street}, {order.deliveryAddress.city}
          </Text>
        </View>
      </View>

      <View style={{
        flexDirection: 'row',
        justifyContent: 'space-between',
        marginBottom: 20,
        gap: 12,
      }}>
        <View style={{
          alignItems: 'center',
          flex: 1,
          backgroundColor: '#fef3c7',
          paddingVertical: 12,
          borderRadius: 16,
          borderWidth: 2,
          borderColor: '#f59e0b',
        }}>
          <View style={{
            width: 32,
            height: 32,
            backgroundColor: '#f59e0b',
            borderRadius: 16,
            justifyContent: 'center',
            alignItems: 'center',
            marginBottom: 8,
          }}>
            <Ionicons name="car" size={16} color="white" />
          </View>
          <Text style={{ fontSize: 16, fontWeight: 'bold', color: '#92400e' }}>
            {formatDistance(order.estimatedDistance)}
          </Text>
          <Text style={{ fontSize: 12, color: '#92400e', fontWeight: '600' }}>Distance</Text>
        </View>

        <View style={{
          alignItems: 'center',
          flex: 1,
          backgroundColor: '#e0e7ff',
          paddingVertical: 12,
          borderRadius: 16,
          borderWidth: 2,
          borderColor: '#6366f1',
        }}>
          <View style={{
            width: 32,
            height: 32,
            backgroundColor: '#6366f1',
            borderRadius: 16,
            justifyContent: 'center',
            alignItems: 'center',
            marginBottom: 8,
          }}>
            <Ionicons name="time" size={16} color="white" />
          </View>
          <Text style={{ fontSize: 16, fontWeight: 'bold', color: '#4338ca' }}>
            {formatDuration(order.estimatedDuration)}
          </Text>
          <Text style={{ fontSize: 12, color: '#4338ca', fontWeight: '600' }}>Duration</Text>
        </View>

        <View style={{
          alignItems: 'center',
          flex: 1,
          backgroundColor: '#dcfce7',
          paddingVertical: 12,
          borderRadius: 16,
          borderWidth: 2,
          borderColor: '#22c55e',
        }}>
          <View style={{
            width: 32,
            height: 32,
            backgroundColor: '#22c55e',
            borderRadius: 16,
            justifyContent: 'center',
            alignItems: 'center',
            marginBottom: 8,
          }}>
            <Ionicons name="cash" size={16} color="white" />
          </View>
          <Text style={{ fontSize: 16, fontWeight: 'bold', color: '#15803d' }}>
            {formatCurrency(order.total)}
          </Text>
          <Text style={{ fontSize: 12, color: '#15803d', fontWeight: '600' }}>Order Total</Text>
        </View>
      </View>

      {/* Enhanced View Details Button */}
      <TouchableOpacity
        onPress={() => handleOrderDetails(order)}
        style={{
          flexDirection: 'row',
          alignItems: 'center',
          justifyContent: 'center',
          backgroundColor: '#f1f5f9',
          paddingVertical: 12,
          paddingHorizontal: 16,
          borderRadius: 16,
          marginBottom: 16,
          borderWidth: 2,
          borderColor: '#e2e8f0',
        }}
      >
        <Ionicons name="eye" size={18} color="#6b7280" />
        <Text style={{ fontSize: 16, fontWeight: '600', color: '#6b7280', marginLeft: 8 }}>
          View Details
        </Text>
      </TouchableOpacity>

      <View style={{ flexDirection: 'row', gap: 12 }}>
        <Button
          title="Decline"
          variant="outline"
          onPress={() => handleDeclineOrder(order.id)}
          style={{
            flex: 1,
            borderColor: '#dc2626',
            borderWidth: 2,
          }}
          textStyle={{ color: '#dc2626', fontWeight: 'bold' }}
        />
        <Button
          title="Accept Order"
          variant="primary"
          onPress={() => handleAcceptOrder(order.id)}
          style={{
            flex: 1,
            backgroundColor: '#dc2626',
            shadowColor: '#dc2626',
            shadowOffset: { width: 0, height: 6 },
            shadowOpacity: 0.3,
            shadowRadius: 12,
            elevation: 8,
          }}
          textStyle={{ fontWeight: 'bold' }}
        />
      </View>
    </View>
  );

  const renderHeader = () => (
    <View style={{
      backgroundColor: '#dc2626',
      paddingHorizontal: 20,
      paddingTop: 20,
      paddingBottom: 28,
      borderBottomLeftRadius: 28,
      borderBottomRightRadius: 28,
      shadowColor: '#dc2626',
      shadowOffset: { width: 0, height: 12 },
      shadowOpacity: 0.3,
      shadowRadius: 20,
      elevation: 16,
    }}>
      <View style={{
        flexDirection: 'row',
        alignItems: 'center',
        marginBottom: 20,
      }}>
        <View style={{
          width: 56,
          height: 56,
          backgroundColor: 'rgba(255,255,255,0.2)',
          borderRadius: 28,
          justifyContent: 'center',
          alignItems: 'center',
          marginRight: 16,
          borderWidth: 2,
          borderColor: 'rgba(255,255,255,0.3)',
        }}>
          <Ionicons name="receipt" size={28} color="white" />
        </View>
        <View style={{ flex: 1 }}>
          <Text style={{
            fontSize: 24,
            fontWeight: 'bold',
            color: 'white',
            marginBottom: 4,
          }}>
            Available Orders
          </Text>
          <Text style={{
            fontSize: 14,
            color: 'rgba(255,255,255,0.8)',
          }}>
            {state.availableOrders.length} orders waiting
          </Text>
        </View>
        <TouchableOpacity
          onPress={() => navigation.navigate('History' as never)}
          style={{
            flexDirection: 'row',
            alignItems: 'center',
            paddingVertical: 12,
            paddingHorizontal: 16,
            backgroundColor: 'rgba(255,255,255,0.2)',
            borderRadius: 16,
            borderWidth: 2,
            borderColor: 'rgba(255,255,255,0.3)',
          }}
        >
          <Ionicons name="time" size={18} color="white" />
          <Text style={{ color: 'white', fontSize: 16, fontWeight: 'bold', marginLeft: 6 }}>
            History
          </Text>
        </TouchableOpacity>
      </View>
    </View>
  );

  const renderEmptyState = () => (
    <View style={{
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
      paddingHorizontal: 40,
    }}>
      <View style={{
        width: 120,
        height: 120,
        backgroundColor: '#fef2f2',
        borderRadius: 60,
        justifyContent: 'center',
        alignItems: 'center',
        marginBottom: 24,
        borderWidth: 4,
        borderColor: '#dc2626',
        shadowColor: '#dc2626',
        shadowOffset: { width: 0, height: 8 },
        shadowOpacity: 0.2,
        shadowRadius: 16,
        elevation: 12,
      }}>
        <Ionicons name="receipt" size={48} color="#dc2626" />
      </View>
      <Text style={{
        fontSize: 24,
        fontWeight: 'bold',
        color: '#111827',
        marginBottom: 12,
        textAlign: 'center',
      }}>
        No Orders Available
      </Text>
      <Text style={{
        fontSize: 16,
        color: '#6b7280',
        textAlign: 'center',
        lineHeight: 24,
        maxWidth: 280,
      }}>
        There are no orders available right now. Pull down to refresh or check back later.
      </Text>
    </View>
  );

  if (state.isLoading && !refreshing) {
    return (
      <View style={{ flex: 1, backgroundColor: '#f1f5f9' }}>
        <SafeAreaView style={{ flex: 1 }}>
          {renderHeader()}
          <LoadingSpinner message="Loading orders..." />
        </SafeAreaView>
      </View>
    );
  }

  return (
    <View style={{ flex: 1, backgroundColor: '#f1f5f9' }}>
      <SafeAreaView style={{ flex: 1 }}>
        {renderHeader()}
      
      {state.availableOrders.length === 0 ? (
        <ScrollView
          contentContainerStyle={{ flex: 1 }}
          refreshControl={
            <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
          }
        >
          {renderEmptyState()}
        </ScrollView>
      ) : (
        <ScrollView
          style={{ flex: 1 }}
          refreshControl={
            <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
          }
          showsVerticalScrollIndicator={false}
        >
          <View style={{ paddingVertical: 8 }}>
            {state.availableOrders.map(renderOrderCard)}
          </View>
          
          {/* Bottom spacing */}
          <View style={{ height: 20 }} />
        </ScrollView>
      )}
      </SafeAreaView>
    </View>
  );
};

export default OrderListScreen;
