import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  Alert,
  Share,
  Clipboard,
  Platform,
  ToastAndroid,
  StatusBar,
  Animated,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import { useNavigation } from '@react-navigation/native';
import { LinearGradient } from 'expo-linear-gradient';

interface ReferralData {
  referralCode: string;
  totalInvited: number;
  totalJoined: number;
  totalEarnings: number;
  pendingEarnings: number;
  recentReferrals: {
    id: string;
    name: string;
    status: 'pending' | 'completed' | 'active';
    earnings: number;
    joinedDate: string;
  }[];
}

const ReferralProgramScreen: React.FC = () => {
  const navigation = useNavigation();
  const [referralData, setReferralData] = useState<ReferralData>({
    referralCode: 'RIDER2024',
    totalInvited: 15,
    totalJoined: 8,
    totalEarnings: 4000,
    pendingEarnings: 1500,
    recentReferrals: [
      { id: '1', name: '<PERSON>', status: 'completed', earnings: 500, joinedDate: '2024-01-15' },
      { id: '2', name: 'Sara Ali', status: 'active', earnings: 500, joinedDate: '2024-01-12' },
      { id: '3', name: 'Hassan Sheikh', status: 'pending', earnings: 0, joinedDate: '2024-01-10' },
      { id: '4', name: 'Fatima Malik', status: 'completed', earnings: 500, joinedDate: '2024-01-08' },
    ],
  });

  const [fadeAnimation] = useState(new Animated.Value(0));

  useEffect(() => {
    Animated.timing(fadeAnimation, {
      toValue: 1,
      duration: 800,
      useNativeDriver: true,
    }).start();
  }, []);

  const handleShareReferralCode = async () => {
    try {
      await Share.share({
        message: `🚀 Join FoodWay as a delivery rider using my referral code: ${referralData.referralCode}\n\n💰 Start earning up to PKR 50,000/month\n🎁 Get PKR 500 bonus on your first delivery\n📱 Download the FoodWay Rider app now!\n\n#FoodWayRider #DeliveryJobs #EarnMoney`,
        title: 'Join FoodWay Delivery - Earn Big!',
      });
    } catch (error) {
      console.error('Error sharing referral code:', error);
    }
  };

  const handleCopyReferralCode = async () => {
    try {
      await Clipboard.setString(referralData.referralCode);

      if (Platform.OS === 'android') {
        ToastAndroid.showWithGravityAndOffset(
          '✅ Referral code copied to clipboard!',
          ToastAndroid.LONG,
          ToastAndroid.BOTTOM,
          25,
          50
        );
      } else {
        Alert.alert('✅ Copied!', 'Referral code copied to clipboard');
      }
    } catch (error) {
      console.error('Error copying referral code:', error);
      Alert.alert('Error', 'Failed to copy referral code');
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed': return '#10b981';
      case 'active': return '#3b82f6';
      case 'pending': return '#f59e0b';
      default: return '#6b7280';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed': return 'checkmark-circle';
      case 'active': return 'person';
      case 'pending': return 'time';
      default: return 'help-circle';
    }
  };

  const renderStatsCard = (title: string, value: string, subtitle: string, icon: string, color: string) => (
    <View style={{
      flex: 1,
      backgroundColor: 'white',
      borderRadius: 16,
      padding: 16,
      marginHorizontal: 4,
      shadowColor: color,
      shadowOffset: { width: 0, height: 4 },
      shadowOpacity: 0.1,
      shadowRadius: 8,
      elevation: 4,
      borderWidth: 1,
      borderColor: `${color}20`,
    }}>
      <View style={{
        width: 40,
        height: 40,
        backgroundColor: `${color}20`,
        borderRadius: 20,
        alignItems: 'center',
        justifyContent: 'center',
        marginBottom: 12,
      }}>
        <Ionicons name={icon as any} size={20} color={color} />
      </View>
      <Text style={{
        fontSize: 24,
        fontWeight: 'bold',
        color: '#111827',
        marginBottom: 4,
      }}>
        {value}
      </Text>
      <Text style={{
        fontSize: 12,
        fontWeight: '600',
        color: '#111827',
        marginBottom: 2,
      }}>
        {title}
      </Text>
      <Text style={{
        fontSize: 10,
        color: '#6b7280',
      }}>
        {subtitle}
      </Text>
    </View>
  );

  const renderReferralItem = (referral: ReferralData['recentReferrals'][0]) => (
    <View key={referral.id} style={{
      flexDirection: 'row',
      alignItems: 'center',
      paddingVertical: 16,
      paddingHorizontal: 20,
      borderBottomWidth: 1,
      borderBottomColor: '#f3f4f6',
    }}>
      <View style={{
        width: 48,
        height: 48,
        backgroundColor: `${getStatusColor(referral.status)}20`,
        borderRadius: 24,
        alignItems: 'center',
        justifyContent: 'center',
        marginRight: 16,
      }}>
        <Ionicons name={getStatusIcon(referral.status) as any} size={24} color={getStatusColor(referral.status)} />
      </View>
      
      <View style={{ flex: 1 }}>
        <Text style={{
          fontSize: 16,
          fontWeight: 'bold',
          color: '#111827',
          marginBottom: 4,
        }}>
          {referral.name}
        </Text>
        <Text style={{
          fontSize: 12,
          color: '#6b7280',
        }}>
          Joined: {new Date(referral.joinedDate).toLocaleDateString()}
        </Text>
      </View>
      
      <View style={{ alignItems: 'flex-end' }}>
        <View style={{
          backgroundColor: `${getStatusColor(referral.status)}20`,
          paddingHorizontal: 8,
          paddingVertical: 4,
          borderRadius: 12,
          marginBottom: 4,
        }}>
          <Text style={{
            fontSize: 10,
            fontWeight: 'bold',
            color: getStatusColor(referral.status),
            textTransform: 'uppercase',
          }}>
            {referral.status}
          </Text>
        </View>
        <Text style={{
          fontSize: 14,
          fontWeight: 'bold',
          color: referral.earnings > 0 ? '#10b981' : '#6b7280',
        }}>
          {referral.earnings > 0 ? `PKR ${referral.earnings}` : 'Pending'}
        </Text>
      </View>
    </View>
  );

  return (
    <View style={{ flex: 1, backgroundColor: '#f1f5f9' }}>
      <SafeAreaView style={{ flex: 1 }}>
        <StatusBar barStyle="light-content" backgroundColor="#dc2626" />
        
        {/* Enhanced Header */}
        <View style={{
          backgroundColor: '#dc2626',
          paddingHorizontal: 20,
          paddingTop: 20,
          paddingBottom: 28,
          borderBottomLeftRadius: 28,
          borderBottomRightRadius: 28,
          shadowColor: '#dc2626',
          shadowOffset: { width: 0, height: 12 },
          shadowOpacity: 0.3,
          shadowRadius: 20,
          elevation: 16,
        }}>
          <View style={{
            flexDirection: 'row',
            alignItems: 'center',
            marginBottom: 20,
          }}>
            <TouchableOpacity
              onPress={() => navigation.goBack()}
              style={{
                width: 48,
                height: 48,
                borderRadius: 24,
                backgroundColor: 'rgba(255,255,255,0.2)',
                justifyContent: 'center',
                alignItems: 'center',
                marginRight: 16,
                borderWidth: 2,
                borderColor: 'rgba(255,255,255,0.3)',
              }}
            >
              <Ionicons name="arrow-back" size={24} color="white" />
            </TouchableOpacity>
            
            <View style={{ flex: 1 }}>
              <Text style={{
                fontSize: 24,
                fontWeight: 'bold',
                color: 'white',
                marginBottom: 4,
              }}>
                🎁 Referral Program
              </Text>
              <Text style={{
                fontSize: 14,
                color: 'rgba(255,255,255,0.8)',
                fontWeight: '500',
              }}>
                Invite friends and earn rewards
              </Text>
            </View>
          </View>
        </View>

        <ScrollView style={{ flex: 1 }} showsVerticalScrollIndicator={false}>
          <Animated.View style={{ opacity: fadeAnimation }}>
            {/* Referral Code Card */}
            <View style={{
              backgroundColor: 'white',
              marginHorizontal: 20,
              marginTop: -12,
              borderRadius: 20,
              padding: 24,
              shadowColor: '#dc2626',
              shadowOffset: { width: 0, height: 8 },
              shadowOpacity: 0.1,
              shadowRadius: 16,
              elevation: 8,
              borderWidth: 1,
              borderColor: 'rgba(220, 38, 38, 0.1)',
            }}>
              <LinearGradient
                colors={['#dc2626', '#f87171']}
                start={{ x: 0, y: 0 }}
                end={{ x: 1, y: 0 }}
                style={{
                  borderRadius: 16,
                  padding: 20,
                  marginBottom: 20,
                }}
              >
                <Text style={{
                  fontSize: 18,
                  fontWeight: 'bold',
                  color: 'white',
                  marginBottom: 8,
                  textAlign: 'center',
                }}>
                  🎉 Earn PKR 500 per referral!
                </Text>
                <Text style={{
                  fontSize: 14,
                  color: 'rgba(255,255,255,0.9)',
                  textAlign: 'center',
                  lineHeight: 20,
                }}>
                  Share your code and earn when friends join FoodWay
                </Text>
              </LinearGradient>

              {/* Referral Code Display */}
              <View style={{
                backgroundColor: '#f8fafc',
                borderRadius: 16,
                padding: 20,
                borderWidth: 2,
                borderColor: '#dc2626',
                borderStyle: 'dashed',
                marginBottom: 16,
              }}>
                <Text style={{
                  fontSize: 14,
                  color: '#6b7280',
                  marginBottom: 8,
                  textAlign: 'center',
                  fontWeight: '600',
                }}>
                  Your Referral Code
                </Text>

                <TouchableOpacity
                  onPress={handleCopyReferralCode}
                  style={{
                    backgroundColor: 'white',
                    borderRadius: 12,
                    padding: 16,
                    borderWidth: 1,
                    borderColor: '#e5e7eb',
                    marginBottom: 16,
                  }}
                >
                  <View style={{
                    flexDirection: 'row',
                    alignItems: 'center',
                    justifyContent: 'center',
                  }}>
                    <Text style={{
                      fontSize: 28,
                      fontWeight: 'bold',
                      color: '#dc2626',
                      letterSpacing: 4,
                      fontFamily: 'monospace',
                      marginRight: 12,
                    }}>
                      {referralData.referralCode}
                    </Text>
                    <Ionicons name="copy-outline" size={24} color="#dc2626" />
                  </View>
                  <Text style={{
                    fontSize: 12,
                    color: '#6b7280',
                    textAlign: 'center',
                    marginTop: 4,
                  }}>
                    Tap to copy
                  </Text>
                </TouchableOpacity>

                {/* Action Buttons */}
                <View style={{
                  flexDirection: 'row',
                  gap: 12,
                }}>
                  <TouchableOpacity
                    onPress={handleCopyReferralCode}
                    style={{
                      flex: 1,
                      backgroundColor: '#3b82f6',
                      borderRadius: 16,
                      paddingVertical: 14,
                      flexDirection: 'row',
                      alignItems: 'center',
                      justifyContent: 'center',
                    }}
                  >
                    <Ionicons name="copy-outline" size={18} color="white" />
                    <Text style={{
                      fontSize: 14,
                      fontWeight: 'bold',
                      color: 'white',
                      marginLeft: 8,
                    }}>
                      Copy Code
                    </Text>
                  </TouchableOpacity>

                  <TouchableOpacity
                    onPress={handleShareReferralCode}
                    style={{
                      flex: 1,
                      backgroundColor: '#dc2626',
                      borderRadius: 16,
                      paddingVertical: 14,
                      flexDirection: 'row',
                      alignItems: 'center',
                      justifyContent: 'center',
                    }}
                  >
                    <Ionicons name="share-outline" size={18} color="white" />
                    <Text style={{
                      fontSize: 14,
                      fontWeight: 'bold',
                      color: 'white',
                      marginLeft: 8,
                    }}>
                      Share Now
                    </Text>
                  </TouchableOpacity>
                </View>
              </View>
            </View>

            {/* Stats Cards */}
            <View style={{
              flexDirection: 'row',
              paddingHorizontal: 20,
              marginTop: 16,
              gap: 8,
            }}>
              {renderStatsCard('Invited', referralData.totalInvited.toString(), 'Total invitations', 'person-add', '#3b82f6')}
              {renderStatsCard('Joined', referralData.totalJoined.toString(), 'Successful referrals', 'checkmark-circle', '#10b981')}
              {renderStatsCard('Earned', `PKR ${referralData.totalEarnings}`, 'Total earnings', 'cash', '#dc2626')}
            </View>

            {/* Recent Referrals */}
            <View style={{
              backgroundColor: 'white',
              marginHorizontal: 20,
              marginTop: 16,
              borderRadius: 20,
              shadowColor: '#000',
              shadowOffset: { width: 0, height: 8 },
              shadowOpacity: 0.1,
              shadowRadius: 16,
              elevation: 8,
              borderWidth: 1,
              borderColor: 'rgba(0, 0, 0, 0.05)',
            }}>
              <View style={{
                paddingHorizontal: 20,
                paddingVertical: 20,
                borderBottomWidth: 1,
                borderBottomColor: '#f3f4f6',
              }}>
                <Text style={{
                  fontSize: 20,
                  fontWeight: 'bold',
                  color: '#111827',
                }}>
                  📋 Recent Referrals
                </Text>
                <Text style={{
                  fontSize: 14,
                  color: '#6b7280',
                  marginTop: 4,
                }}>
                  Track your referral progress
                </Text>
              </View>

              {referralData.recentReferrals.map(renderReferralItem)}
            </View>

            <View style={{ height: 32 }} />
          </Animated.View>
        </ScrollView>
      </SafeAreaView>
    </View>
  );
};

export default ReferralProgramScreen;
