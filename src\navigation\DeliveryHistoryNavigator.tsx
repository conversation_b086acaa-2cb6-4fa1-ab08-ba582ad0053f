import React from 'react';
import { createStackNavigator } from '@react-navigation/stack';
import OrderHistoryScreen from '../screens/delivery/OrderHistoryScreen';
import OrderDetailScreen from '../screens/delivery/OrderDetailScreen';
import OrderHistoryAnalyticsScreen from '../screens/analytics/OrderHistoryAnalyticsScreen';

export type DeliveryHistoryStackParamList = {
  OrderHistory: undefined;
  OrderDetail: { orderId: string };
  OrderHistoryAnalytics: undefined;
};

const Stack = createStackNavigator<DeliveryHistoryStackParamList>();

const DeliveryHistoryNavigator: React.FC = () => {
  return (
    <Stack.Navigator
      screenOptions={{
        headerShown: false,
        cardStyle: { backgroundColor: '#f8fafc' },
      }}
    >
      <Stack.Screen
        name="OrderHistory"
        component={OrderHistoryScreen}
        options={{
          title: 'Order History',
        }}
      />
      <Stack.Screen
        name="OrderDetail"
        component={OrderDetailScreen}
        options={{
          title: 'Order Details',
        }}
      />
      <Stack.Screen
        name="OrderHistoryAnalytics"
        component={OrderHistoryAnalyticsScreen}
        options={{
          title: 'Order Analytics',
        }}
      />
    </Stack.Navigator>
  );
};

export default DeliveryHistoryNavigator;
