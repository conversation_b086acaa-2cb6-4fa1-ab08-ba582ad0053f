import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  Alert,
  Linking,
  Modal,
  Animated,
  Vibration,
  Dimensions,
  StatusBar,
  Image,
  TextInput,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import { LinearGradient } from 'expo-linear-gradient';
import { BlurView } from 'expo-blur';
import * as ImagePicker from 'expo-image-picker';
import { useNavigation, useRoute } from '@react-navigation/native';
import { Card, Button, Badge } from '../../components/ui';
import { useOrders } from '../../context/OrderContext';
import { Order, OrderStatus } from '../../types/orders';
import { formatCurrency, formatDistance, formatDuration } from '../../utils/helpers';
import OrderTrackingMap from '../../components/maps/OrderTrackingMap';
import DeliveryProofModal from '../../components/orders/DeliveryProofModal';

interface RouteParams {
  orderId: string;
}

const { width: screenWidth, height: screenHeight } = Dimensions.get('window');

const DeliveryFlowScreen: React.FC = () => {
  const navigation = useNavigation();
  const route = useRoute();
  const { orderId } = route.params as RouteParams;
  const { state, updateOrderStatus, markAsDelivered } = useOrders();

  // Core state
  const [order, setOrder] = useState<Order | null>(null);
  const [showMap, setShowMap] = useState(true); // Always show map for delivery
  const [showDeliveryProof, setShowDeliveryProof] = useState(false);

  // Enhanced delivery state
  const [showBottomSheet, setShowBottomSheet] = useState(false);
  const [showOTPInput, setShowOTPInput] = useState(false);
  const [showPhotoUpload, setShowPhotoUpload] = useState(false);
  const [showSOSModal, setShowSOSModal] = useState(false);
  const [deliveryOTP, setDeliveryOTP] = useState('');
  const [deliveryPhoto, setDeliveryPhoto] = useState<string | null>(null);
  const [estimatedETA, setEstimatedETA] = useState(15); // minutes
  const [distanceToCustomer, setDistanceToCustomer] = useState(2.5); // km

  // Animation refs
  const fadeAnimation = useRef(new Animated.Value(0)).current;
  const slideAnimation = useRef(new Animated.Value(50)).current;
  const bottomSheetAnimation = useRef(new Animated.Value(0)).current;
  const sosAnimation = useRef(new Animated.Value(1)).current;
  const riderIconAnimation = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    const foundOrder = state.acceptedOrders.find(o => o.id === orderId) ||
                      state.completedOrders.find(o => o.id === orderId);
    setOrder(foundOrder || null);

    // Start entrance animations
    Animated.parallel([
      Animated.timing(fadeAnimation, {
        toValue: 1,
        duration: 800,
        useNativeDriver: true,
      }),
      Animated.timing(slideAnimation, {
        toValue: 0,
        duration: 800,
        useNativeDriver: true,
      }),
    ]).start();

    // Start rider icon animation (continuous rotation)
    const startRiderAnimation = () => {
      Animated.loop(
        Animated.timing(riderIconAnimation, {
          toValue: 1,
          duration: 2000,
          useNativeDriver: true,
        })
      ).start();
    };
    startRiderAnimation();

    // Start SOS button pulse animation
    const startSOSPulse = () => {
      Animated.loop(
        Animated.sequence([
          Animated.timing(sosAnimation, {
            toValue: 1.1,
            duration: 1000,
            useNativeDriver: true,
          }),
          Animated.timing(sosAnimation, {
            toValue: 1,
            duration: 1000,
            useNativeDriver: true,
          }),
        ])
      ).start();
    };
    startSOSPulse();

    // Simulate real-time ETA and distance updates
    const updateInterval = setInterval(() => {
      setEstimatedETA(prev => Math.max(1, prev - 0.1));
      setDistanceToCustomer(prev => Math.max(0.1, prev - 0.05));
    }, 10000); // Update every 10 seconds

    return () => {
      clearInterval(updateInterval);
    };
  }, [orderId, state]);

  const handleStartNavigation = () => {
    if (!order) return;

    const { lat, lng } = order.deliveryAddress.coordinates;
    const url = `https://www.google.com/maps/dir/?api=1&destination=${lat},${lng}&travelmode=driving`;

    Linking.openURL(url).catch(() => {
      Alert.alert('Error', 'Could not open navigation app');
    });
  };

  const handleShowBottomSheet = () => {
    setShowBottomSheet(true);
    Animated.timing(bottomSheetAnimation, {
      toValue: 1,
      duration: 300,
      useNativeDriver: true,
    }).start();
  };

  const handleHideBottomSheet = () => {
    Animated.timing(bottomSheetAnimation, {
      toValue: 0,
      duration: 300,
      useNativeDriver: true,
    }).start(() => {
      setShowBottomSheet(false);
      setShowOTPInput(false);
      setShowPhotoUpload(false);
    });
  };

  const handleTakeDeliveryPhoto = async () => {
    const { status } = await ImagePicker.requestCameraPermissionsAsync();
    if (status !== 'granted') {
      Alert.alert('Permission Required', 'Camera permission is needed to take delivery photos.');
      return;
    }

    const result = await ImagePicker.launchCameraAsync({
      mediaTypes: ImagePicker.MediaTypeOptions.Images,
      allowsEditing: true,
      aspect: [4, 3],
      quality: 0.8,
    });

    if (!result.canceled && result.assets[0]) {
      setDeliveryPhoto(result.assets[0].uri);
      Vibration.vibrate(100);
      Alert.alert('Success', 'Delivery photo captured successfully!');
    }
  };

  const handleMarkAsDelivered = async () => {
    if (!order) return;

    if (showOTPInput && deliveryOTP.length !== 6) {
      Alert.alert('Invalid OTP', 'Please enter the 6-digit OTP from the customer.');
      return;
    }

    if (showPhotoUpload && !deliveryPhoto) {
      Alert.alert('Photo Required', 'Please take a delivery photo before marking as delivered.');
      return;
    }

    try {
      Vibration.vibrate([0, 100, 50, 100]);

      const proofData = {
        deliveryTime: new Date(),
        deliveryMethod: showOTPInput ? 'otp' : 'photo',
        otp: deliveryOTP || undefined,
        photoUri: deliveryPhoto || undefined,
      };

      await markAsDelivered(order.id, proofData);
      setOrder(prev => prev ? { ...prev, status: OrderStatus.DELIVERED } : null);

      // Hide bottom sheet
      handleHideBottomSheet();

      // Navigate to Trip Summary
      const tripData = {
        orderId: order.id,
        orderNumber: order.orderNumber,
        deliveryTime: 28, // Mock delivery time in minutes
        distance: 3.2, // Mock distance in km
        baseFare: 6.50,
        tip: 3.00,
        bonus: 2.00,
        totalPayout: 11.50,
        customerRating: 5,
        restaurant: {
          name: order.restaurant.name,
          address: order.restaurant.address.street,
        },
        customer: {
          name: `${order.customer.firstName} ${order.customer.lastName}`,
          address: order.deliveryAddress.street,
        },
        completedAt: new Date().toISOString(),
      };

      navigation.navigate('TripSummary' as never, { tripData } as never);
    } catch (error: any) {
      Alert.alert('Error', error.message || 'Failed to mark delivery as complete');
    }
  };

  const handleContactCustomer = (method: 'call' | 'chat') => {
    if (!order) return;

    Vibration.vibrate(50);

    if (method === 'call') {
      const phoneUrl = `tel:${order.customer.phone}`;
      Linking.openURL(phoneUrl);
    } else {
      Alert.alert('Chat', 'Opening chat with customer...');
    }
  };

  const handleEmergencySOS = () => {
    Vibration.vibrate([0, 500, 200, 500]);
    setShowSOSModal(true);
  };



  const handleDeliveryConfirmed = async (proofData: any) => {
    if (!order) return;
    
    try {
      await markAsDelivered(order.id, proofData);
      setOrder(prev => prev ? { ...prev, status: OrderStatus.DELIVERED } : null);
      setShowDeliveryProof(false);
      
      Alert.alert(
        'Delivery Complete!', 
        'Order has been successfully delivered.',
        [
          { 
            text: 'Back to Dashboard', 
            onPress: () => navigation.navigate('Dashboard')
          }
        ]
      );
    } catch (error: any) {
      Alert.alert('Error', error.message);
    }
  };

  const handleCannotDeliver = () => {
    Alert.alert(
      'Cannot Deliver',
      'Please select a reason:',
      [
        { text: 'Customer not available', onPress: () => reportCannotDeliver('Customer not available') },
        { text: 'Wrong address', onPress: () => reportCannotDeliver('Wrong address') },
        { text: 'Customer refused order', onPress: () => reportCannotDeliver('Customer refused order') },
        { text: 'Other', onPress: () => reportCannotDeliver('Other') },
        { text: 'Cancel', style: 'cancel' },
      ]
    );
  };

  const reportCannotDeliver = async (reason: string) => {
    if (!order) return;
    
    try {
      await updateOrderStatus(order.id, OrderStatus.CANCELLED);
      setOrder(prev => prev ? { ...prev, status: OrderStatus.CANCELLED } : null);
      
      Alert.alert(
        'Order Cancelled', 
        `Reason: ${reason}`,
        [
          { 
            text: 'Back to Dashboard', 
            onPress: () => navigation.navigate('Dashboard')
          }
        ]
      );
    } catch (error: any) {
      Alert.alert('Error', error.message);
    }
  };

  const handleCallCustomer = () => {
    if (!order) return;

    const phoneUrl = `tel:${order.customer.phone}`;
    Linking.openURL(phoneUrl).catch(() => {
      Alert.alert('Error', 'Could not make phone call');
    });
  };

  const renderETAHeader = () => (
    <Animated.View style={{
      opacity: fadeAnimation,
      transform: [{ translateY: slideAnimation }],
    }}>
      <LinearGradient
        colors={['#3b82f6', '#2563eb']}
        style={{
          paddingTop: StatusBar.currentHeight || 0,
          paddingHorizontal: 20,
          paddingBottom: 16,
        }}
      >
        <View style={{
          flexDirection: 'row',
          alignItems: 'center',
          justifyContent: 'space-between',
          marginTop: 16,
        }}>
          <TouchableOpacity
            onPress={() => navigation.goBack()}
            style={{
              width: 40,
              height: 40,
              borderRadius: 20,
              backgroundColor: 'rgba(255,255,255,0.2)',
              justifyContent: 'center',
              alignItems: 'center',
            }}
          >
            <Ionicons name="arrow-back" size={20} color="white" />
          </TouchableOpacity>

          <View style={{ flex: 1, alignItems: 'center' }}>
            <Text style={{
              fontSize: 18,
              fontWeight: 'bold',
              color: 'white',
            }}>
              Delivering Order
            </Text>
            {order && (
              <Text style={{
                fontSize: 12,
                color: 'rgba(255,255,255,0.8)',
              }}>
                #{order.orderNumber}
              </Text>
            )}
          </View>

          <View style={{ width: 40 }} />
        </View>

        {/* ETA and Distance Row */}
        <View style={{
          flexDirection: 'row',
          justifyContent: 'space-around',
          marginTop: 16,
          backgroundColor: 'rgba(255,255,255,0.1)',
          borderRadius: 16,
          padding: 16,
        }}>
          <View style={{ alignItems: 'center' }}>
            <View style={{
              flexDirection: 'row',
              alignItems: 'center',
              marginBottom: 4,
            }}>
              <Ionicons name="time" size={16} color="white" style={{ marginRight: 4 }} />
              <Text style={{
                fontSize: 20,
                fontWeight: 'bold',
                color: 'white',
              }}>
                {Math.round(estimatedETA)} min
              </Text>
            </View>
            <Text style={{
              fontSize: 12,
              color: 'rgba(255,255,255,0.8)',
            }}>
              ETA
            </Text>
          </View>

          <View style={{
            width: 1,
            backgroundColor: 'rgba(255,255,255,0.3)',
            marginHorizontal: 16,
          }} />

          <View style={{ alignItems: 'center' }}>
            <View style={{
              flexDirection: 'row',
              alignItems: 'center',
              marginBottom: 4,
            }}>
              <Ionicons name="location" size={16} color="white" style={{ marginRight: 4 }} />
              <Text style={{
                fontSize: 20,
                fontWeight: 'bold',
                color: 'white',
              }}>
                {distanceToCustomer.toFixed(1)} km
              </Text>
            </View>
            <Text style={{
              fontSize: 12,
              color: 'rgba(255,255,255,0.8)',
            }}>
              Distance
            </Text>
          </View>
        </View>
      </LinearGradient>
    </Animated.View>
  );

  const renderHeader = () => (
    <View style={{
      flexDirection: 'row',
      alignItems: 'center',
      paddingHorizontal: 20,
      paddingVertical: 16,
      backgroundColor: '#ffffff',
      borderBottomWidth: 1,
      borderBottomColor: '#e5e7eb',
    }}>
      <TouchableOpacity
        onPress={() => navigation.goBack()}
        style={{
          width: 40,
          height: 40,
          borderRadius: 20,
          backgroundColor: '#f3f4f6',
          justifyContent: 'center',
          alignItems: 'center',
          marginRight: 16,
        }}
      >
        <Ionicons name="arrow-back" size={20} color="#374151" />
      </TouchableOpacity>
      
      <View style={{ flex: 1 }}>
        <Text style={{
          fontSize: 20,
          fontWeight: 'bold',
          color: '#111827',
        }}>
          Delivery Flow
        </Text>
        {order && (
          <Text style={{
            fontSize: 14,
            color: '#6b7280',
          }}>
            #{order.orderNumber}
          </Text>
        )}
      </View>

      <Badge
        text="Out for Delivery"
        variant="success"
      />
    </View>
  );

  const renderBottomSheet = () => {
    if (!showBottomSheet) return null;

    return (
      <Modal
        visible={showBottomSheet}
        transparent
        animationType="none"
        onRequestClose={handleHideBottomSheet}
      >
        <View style={{
          flex: 1,
          backgroundColor: 'rgba(0,0,0,0.5)',
          justifyContent: 'flex-end',
        }}>
          <TouchableOpacity
            style={{ flex: 1 }}
            onPress={handleHideBottomSheet}
          />

          <Animated.View style={{
            transform: [{
              translateY: bottomSheetAnimation.interpolate({
                inputRange: [0, 1],
                outputRange: [400, 0],
              })
            }],
          }}>
            <BlurView intensity={95} style={{
              backgroundColor: 'rgba(255,255,255,0.95)',
              borderTopLeftRadius: 24,
              borderTopRightRadius: 24,
              paddingTop: 8,
            }}>
              {/* Handle */}
              <View style={{
                width: 40,
                height: 4,
                backgroundColor: '#d1d5db',
                borderRadius: 2,
                alignSelf: 'center',
                marginBottom: 20,
              }} />

              {/* Header */}
              <View style={{
                paddingHorizontal: 20,
                paddingBottom: 20,
                borderBottomWidth: 1,
                borderBottomColor: '#e5e7eb',
              }}>
                <Text style={{
                  fontSize: 20,
                  fontWeight: 'bold',
                  color: '#111827',
                  textAlign: 'center',
                }}>
                  Complete Delivery
                </Text>
                <Text style={{
                  fontSize: 14,
                  color: '#6b7280',
                  textAlign: 'center',
                  marginTop: 4,
                }}>
                  Choose delivery confirmation method
                </Text>
              </View>

              {/* Delivery Options */}
              <View style={{ padding: 20 }}>
                <View style={{
                  flexDirection: 'row',
                  marginBottom: 20,
                  gap: 12,
                }}>
                  <TouchableOpacity
                    onPress={() => {
                      setShowOTPInput(true);
                      setShowPhotoUpload(false);
                    }}
                    style={{
                      flex: 1,
                      backgroundColor: showOTPInput ? '#3b82f6' : 'white',
                      borderWidth: 2,
                      borderColor: showOTPInput ? '#3b82f6' : '#e5e7eb',
                      borderRadius: 16,
                      padding: 16,
                      alignItems: 'center',
                    }}
                  >
                    <Ionicons
                      name="keypad"
                      size={24}
                      color={showOTPInput ? 'white' : '#6b7280'}
                      style={{ marginBottom: 8 }}
                    />
                    <Text style={{
                      fontSize: 14,
                      fontWeight: '600',
                      color: showOTPInput ? 'white' : '#6b7280',
                    }}>
                      OTP Verification
                    </Text>
                  </TouchableOpacity>

                  <TouchableOpacity
                    onPress={() => {
                      setShowPhotoUpload(true);
                      setShowOTPInput(false);
                    }}
                    style={{
                      flex: 1,
                      backgroundColor: showPhotoUpload ? '#10b981' : 'white',
                      borderWidth: 2,
                      borderColor: showPhotoUpload ? '#10b981' : '#e5e7eb',
                      borderRadius: 16,
                      padding: 16,
                      alignItems: 'center',
                    }}
                  >
                    <Ionicons
                      name="camera"
                      size={24}
                      color={showPhotoUpload ? 'white' : '#6b7280'}
                      style={{ marginBottom: 8 }}
                    />
                    <Text style={{
                      fontSize: 14,
                      fontWeight: '600',
                      color: showPhotoUpload ? 'white' : '#6b7280',
                    }}>
                      Photo Proof
                    </Text>
                  </TouchableOpacity>
                </View>

                {/* OTP Input */}
                {showOTPInput && (
                  <View style={{ marginBottom: 20 }}>
                    <Text style={{
                      fontSize: 16,
                      fontWeight: '600',
                      color: '#111827',
                      marginBottom: 12,
                    }}>
                      Enter 6-digit OTP from customer:
                    </Text>
                    <TextInput
                      value={deliveryOTP}
                      onChangeText={setDeliveryOTP}
                      placeholder="000000"
                      keyboardType="numeric"
                      maxLength={6}
                      style={{
                        backgroundColor: 'white',
                        borderWidth: 2,
                        borderColor: '#e5e7eb',
                        borderRadius: 12,
                        padding: 16,
                        fontSize: 18,
                        textAlign: 'center',
                        letterSpacing: 4,
                      }}
                    />
                  </View>
                )}

                {/* Photo Upload */}
                {showPhotoUpload && (
                  <View style={{ marginBottom: 20 }}>
                    <Text style={{
                      fontSize: 16,
                      fontWeight: '600',
                      color: '#111827',
                      marginBottom: 12,
                    }}>
                      Take delivery photo:
                    </Text>

                    {deliveryPhoto ? (
                      <View style={{ alignItems: 'center' }}>
                        <Image
                          source={{ uri: deliveryPhoto }}
                          style={{
                            width: 200,
                            height: 150,
                            borderRadius: 12,
                            marginBottom: 12,
                          }}
                          resizeMode="cover"
                        />
                        <Text style={{ color: '#10b981', fontWeight: '600' }}>
                          ✓ Photo captured successfully!
                        </Text>
                      </View>
                    ) : (
                      <TouchableOpacity
                        onPress={handleTakeDeliveryPhoto}
                        style={{
                          backgroundColor: '#f3f4f6',
                          borderWidth: 2,
                          borderColor: '#e5e7eb',
                          borderStyle: 'dashed',
                          borderRadius: 12,
                          padding: 32,
                          alignItems: 'center',
                        }}
                      >
                        <Ionicons name="camera" size={32} color="#6b7280" style={{ marginBottom: 8 }} />
                        <Text style={{ color: '#6b7280', fontWeight: '600' }}>
                          Tap to take photo
                        </Text>
                      </TouchableOpacity>
                    )}
                  </View>
                )}

                {/* Contact Customer Buttons */}
                <View style={{
                  flexDirection: 'row',
                  marginBottom: 20,
                  gap: 12,
                }}>
                  <TouchableOpacity
                    onPress={() => handleContactCustomer('call')}
                    style={{
                      flex: 1,
                      backgroundColor: 'white',
                      borderWidth: 2,
                      borderColor: '#e5e7eb',
                      borderRadius: 12,
                      padding: 12,
                      flexDirection: 'row',
                      alignItems: 'center',
                      justifyContent: 'center',
                    }}
                  >
                    <Ionicons name="call" size={20} color="#3b82f6" style={{ marginRight: 8 }} />
                    <Text style={{ color: '#3b82f6', fontWeight: '600' }}>Call</Text>
                  </TouchableOpacity>

                  <TouchableOpacity
                    onPress={() => handleContactCustomer('chat')}
                    style={{
                      flex: 1,
                      backgroundColor: 'white',
                      borderWidth: 2,
                      borderColor: '#e5e7eb',
                      borderRadius: 12,
                      padding: 12,
                      flexDirection: 'row',
                      alignItems: 'center',
                      justifyContent: 'center',
                    }}
                  >
                    <Ionicons name="chatbubble" size={20} color="#10b981" style={{ marginRight: 8 }} />
                    <Text style={{ color: '#10b981', fontWeight: '600' }}>Chat</Text>
                  </TouchableOpacity>
                </View>

                {/* Mark as Delivered Button */}
                <TouchableOpacity
                  onPress={handleMarkAsDelivered}
                  disabled={
                    (showOTPInput && deliveryOTP.length !== 6) ||
                    (showPhotoUpload && !deliveryPhoto)
                  }
                  style={{
                    opacity:
                      (showOTPInput && deliveryOTP.length !== 6) ||
                      (showPhotoUpload && !deliveryPhoto) ? 0.5 : 1,
                  }}
                >
                  <LinearGradient
                    colors={['#10b981', '#059669']}
                    style={{
                      borderRadius: 16,
                      padding: 16,
                      flexDirection: 'row',
                      alignItems: 'center',
                      justifyContent: 'center',
                    }}
                  >
                    <Ionicons name="checkmark-circle" size={20} color="white" style={{ marginRight: 8 }} />
                    <Text style={{ color: 'white', fontSize: 16, fontWeight: '600' }}>
                      Mark as Delivered
                    </Text>
                  </LinearGradient>
                </TouchableOpacity>
              </View>
            </BlurView>
          </Animated.View>
        </View>
      </Modal>
    );
  };

  const renderDeliveryInfo = () => (
    <Card variant="elevated" margin="md" padding="lg">
      <View style={{
        flexDirection: 'row',
        alignItems: 'center',
        marginBottom: 16,
      }}>
        <View style={{
          width: 50,
          height: 50,
          backgroundColor: '#10b981',
          borderRadius: 25,
          justifyContent: 'center',
          alignItems: 'center',
          marginRight: 12,
        }}>
          <Ionicons name="home" size={24} color="white" />
        </View>
        
        <View style={{ flex: 1 }}>
          <Text style={{
            fontSize: 18,
            fontWeight: 'bold',
            color: '#111827',
            marginBottom: 2,
          }}>
            {order?.customer.firstName} {order?.customer.lastName}
          </Text>
          <Text style={{
            fontSize: 14,
            color: '#6b7280',
          }}>
            {order?.deliveryAddress.street}
          </Text>
        </View>
      </View>

      <View style={{
        backgroundColor: '#f8fafc',
        padding: 16,
        borderRadius: 12,
        marginBottom: 16,
      }}>
        <View style={{
          flexDirection: 'row',
          justifyContent: 'space-between',
          marginBottom: 8,
        }}>
          <Text style={{ fontSize: 14, color: '#6b7280' }}>Distance to Customer</Text>
          <Text style={{ fontSize: 14, fontWeight: '500', color: '#111827' }}>
            {formatDistance(order?.estimatedDistance || 0)}
          </Text>
        </View>
        
        <View style={{
          flexDirection: 'row',
          justifyContent: 'space-between',
          marginBottom: 8,
        }}>
          <Text style={{ fontSize: 14, color: '#6b7280' }}>Estimated Delivery Time</Text>
          <Text style={{ fontSize: 14, fontWeight: '500', color: '#111827' }}>
            {formatDuration(order?.estimatedDuration || 0)}
          </Text>
        </View>
        
        <View style={{
          flexDirection: 'row',
          justifyContent: 'space-between',
        }}>
          <Text style={{ fontSize: 14, color: '#6b7280' }}>Delivery Earnings</Text>
          <Text style={{ fontSize: 14, fontWeight: '500', color: '#059669' }}>
            {formatCurrency(order?.estimatedEarnings || 0)}
          </Text>
        </View>
      </View>

      <Text style={{
        fontSize: 16,
        fontWeight: 'bold',
        color: '#111827',
        marginBottom: 8,
      }}>
        Full Address
      </Text>
      <Text style={{
        fontSize: 14,
        color: '#6b7280',
        lineHeight: 20,
      }}>
        {order?.deliveryAddress.street}, {order?.deliveryAddress.city}, {order?.deliveryAddress.state} {order?.deliveryAddress.zipCode}
      </Text>

      {order?.specialInstructions && (
        <View style={{
          backgroundColor: '#fef3c7',
          padding: 12,
          borderRadius: 8,
          borderLeftWidth: 4,
          borderLeftColor: '#f59e0b',
          marginTop: 16,
        }}>
          <Text style={{
            fontSize: 12,
            fontWeight: '600',
            color: '#92400e',
            marginBottom: 4,
          }}>
            Delivery Instructions
          </Text>
          <Text style={{
            fontSize: 14,
            color: '#b45309',
          }}>
            {order.specialInstructions}
          </Text>
        </View>
      )}
    </Card>
  );

  const renderNavigationActions = () => (
    <Card variant="elevated" margin="md" padding="lg">
      <Text style={{
        fontSize: 18,
        fontWeight: 'bold',
        color: '#111827',
        marginBottom: 16,
      }}>
        Navigation & Communication
      </Text>

      <View style={{
        flexDirection: 'row',
        justifyContent: 'space-between',
        marginBottom: 16,
      }}>
        <Button
          title="Google Maps"
          leftIcon="navigate"
          onPress={handleStartNavigation}
          style={{ flex: 1, marginRight: 8 }}
          size="lg"
        />
        
        <Button
          title="Live Map"
          variant="outline"
          leftIcon="map"
          onPress={() => setShowMap(true)}
          style={{ flex: 1, marginLeft: 8 }}
          size="lg"
        />
      </View>

      <View style={{
        flexDirection: 'row',
        justifyContent: 'space-between',
      }}>
        <Button
          title="Call Customer"
          variant="outline"
          leftIcon="call"
          onPress={handleCallCustomer}
          style={{ flex: 1, marginRight: 8 }}
          size="lg"
        />
        
        <Button
          title="Chat Customer"
          variant="outline"
          leftIcon="chatbubble"
          onPress={() => Alert.alert('Coming Soon', 'Chat feature will be available soon')}
          style={{ flex: 1, marginLeft: 8 }}
          size="lg"
        />
      </View>
    </Card>
  );

  const renderDeliveryActions = () => (
    <View style={{ padding: 20 }}>
      <Button
        title="Mark as Delivered"
        leftIcon="checkmark-circle"
        onPress={handleMarkAsDelivered}
        fullWidth
        size="lg"
        style={{ marginBottom: 12 }}
      />
      
      <Button
        title="Cannot Deliver"
        variant="outline"
        leftIcon="close-circle"
        onPress={handleCannotDeliver}
        fullWidth
        size="lg"
      />
    </View>
  );

  if (!order) {
    return (
      <SafeAreaView style={{ flex: 1, backgroundColor: '#f8fafc' }}>
        {renderHeader()}
        <View style={{
          flex: 1,
          justifyContent: 'center',
          alignItems: 'center',
        }}>
          <Text style={{
            fontSize: 18,
            color: '#6b7280',
          }}>
            Order not found
          </Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <View style={{ flex: 1, backgroundColor: '#000' }}>
      <StatusBar barStyle="light-content" backgroundColor="#3b82f6" />

      {/* ETA Header */}
      {renderETAHeader()}

      {/* Full Map View */}
      <View style={{ flex: 1, position: 'relative' }}>
        <OrderTrackingMap
          order={order}
          onClose={() => {}} // No close button for full screen
          showFullScreen={true}
        />

        {/* Animated Rider Icon Overlay */}
        <Animated.View style={{
          position: 'absolute',
          top: '50%',
          left: '50%',
          transform: [
            { translateX: -15 },
            { translateY: -15 },
            {
              rotate: riderIconAnimation.interpolate({
                inputRange: [0, 1],
                outputRange: ['0deg', '360deg'],
              })
            }
          ],
        }}>
          <View style={{
            width: 30,
            height: 30,
            borderRadius: 15,
            backgroundColor: '#3b82f6',
            alignItems: 'center',
            justifyContent: 'center',
            borderWidth: 3,
            borderColor: 'white',
            shadowColor: '#000',
            shadowOffset: { width: 0, height: 2 },
            shadowOpacity: 0.3,
            shadowRadius: 4,
            elevation: 5,
          }}>
            <Ionicons name="bicycle" size={16} color="white" />
          </View>
        </Animated.View>

        {/* Floating SOS Button */}
        <Animated.View style={{
          position: 'absolute',
          bottom: 120,
          right: 20,
          transform: [{ scale: sosAnimation }],
        }}>
          <TouchableOpacity
            onPress={handleEmergencySOS}
            style={{
              width: 60,
              height: 60,
              borderRadius: 30,
              backgroundColor: '#ef4444',
              alignItems: 'center',
              justifyContent: 'center',
              shadowColor: '#000',
              shadowOffset: { width: 0, height: 4 },
              shadowOpacity: 0.3,
              shadowRadius: 8,
              elevation: 8,
            }}
          >
            <Ionicons name="warning" size={28} color="white" />
          </TouchableOpacity>
        </Animated.View>

        {/* Bottom Action Button */}
        <View style={{
          position: 'absolute',
          bottom: 40,
          left: 20,
          right: 20,
        }}>
          <TouchableOpacity
            onPress={handleShowBottomSheet}
            style={{
              backgroundColor: 'white',
              borderRadius: 16,
              padding: 16,
              flexDirection: 'row',
              alignItems: 'center',
              justifyContent: 'center',
              shadowColor: '#000',
              shadowOffset: { width: 0, height: 4 },
              shadowOpacity: 0.1,
              shadowRadius: 8,
              elevation: 5,
            }}
          >
            <Ionicons name="checkmark-circle" size={20} color="#10b981" style={{ marginRight: 8 }} />
            <Text style={{
              color: '#10b981',
              fontSize: 16,
              fontWeight: '600'
            }}>
              Complete Delivery
            </Text>
          </TouchableOpacity>
        </View>
      </View>

      {/* Bottom Sheet */}
      {renderBottomSheet()}

      {/* SOS Modal */}
      <Modal
        visible={showSOSModal}
        transparent
        animationType="fade"
        onRequestClose={() => setShowSOSModal(false)}
      >
        <View style={{
          flex: 1,
          backgroundColor: 'rgba(0,0,0,0.8)',
          justifyContent: 'center',
          alignItems: 'center',
          padding: 20,
        }}>
          <View style={{
            backgroundColor: 'white',
            borderRadius: 20,
            padding: 24,
            width: '100%',
            maxWidth: 300,
          }}>
            <View style={{
              alignItems: 'center',
              marginBottom: 20,
            }}>
              <View style={{
                width: 60,
                height: 60,
                borderRadius: 30,
                backgroundColor: '#ef4444',
                alignItems: 'center',
                justifyContent: 'center',
                marginBottom: 16,
              }}>
                <Ionicons name="warning" size={30} color="white" />
              </View>
              <Text style={{
                fontSize: 20,
                fontWeight: 'bold',
                color: '#111827',
                textAlign: 'center',
              }}>
                Emergency SOS
              </Text>
              <Text style={{
                fontSize: 14,
                color: '#6b7280',
                textAlign: 'center',
                marginTop: 8,
              }}>
                Need immediate assistance?
              </Text>
            </View>

            <View style={{ gap: 12 }}>
              <TouchableOpacity
                onPress={() => {
                  setShowSOSModal(false);
                  Linking.openURL('tel:911');
                }}
                style={{
                  backgroundColor: '#ef4444',
                  borderRadius: 12,
                  padding: 16,
                  flexDirection: 'row',
                  alignItems: 'center',
                  justifyContent: 'center',
                }}
              >
                <Ionicons name="call" size={20} color="white" style={{ marginRight: 8 }} />
                <Text style={{ color: 'white', fontSize: 16, fontWeight: '600' }}>
                  Call Emergency (911)
                </Text>
              </TouchableOpacity>

              <TouchableOpacity
                onPress={() => {
                  setShowSOSModal(false);
                  navigation.navigate('EmergencySOSScreen' as never);
                }}
                style={{
                  backgroundColor: '#f97316',
                  borderRadius: 12,
                  padding: 16,
                  flexDirection: 'row',
                  alignItems: 'center',
                  justifyContent: 'center',
                }}
              >
                <Ionicons name="shield-checkmark" size={20} color="white" style={{ marginRight: 8 }} />
                <Text style={{ color: 'white', fontSize: 16, fontWeight: '600' }}>
                  Safety Center
                </Text>
              </TouchableOpacity>

              <TouchableOpacity
                onPress={() => setShowSOSModal(false)}
                style={{
                  backgroundColor: '#f3f4f6',
                  borderRadius: 12,
                  padding: 16,
                  alignItems: 'center',
                }}
              >
                <Text style={{ color: '#6b7280', fontSize: 16, fontWeight: '600' }}>
                  Cancel
                </Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </Modal>

      {/* Legacy Delivery Proof Modal (fallback) */}
      <DeliveryProofModal
        visible={showDeliveryProof}
        onClose={() => setShowDeliveryProof(false)}
        onConfirm={handleDeliveryConfirmed}
        orderNumber={order.orderNumber}
      />
    </View>
  );
};

export default DeliveryFlowScreen;
