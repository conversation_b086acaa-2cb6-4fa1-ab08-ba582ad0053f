import React, { createContext, useContext, useReducer, useEffect } from 'react';
import {
  OrderState,
  OrderContextType,
  Order,
  OrderStatus,
  OrderHistoryFilters,
} from '../types/orders';
import { apiClient } from '../services/api/apiClient';

// Initial state
const initialState: OrderState = {
  availableOrders: [],
  acceptedOrders: [],
  completedOrders: [],
  orderHistory: [],
  currentOrder: null,
  isLoading: false,
  error: null,
};

// Action types
type OrderAction =
  | { type: 'SET_LOADING'; payload: boolean }
  | { type: 'SET_ERROR'; payload: string | null }
  | { type: 'SET_AVAILABLE_ORDERS'; payload: Order[] }
  | { type: 'SET_ACCEPTED_ORDERS'; payload: Order[] }
  | { type: 'SET_ORDER_HISTORY'; payload: Order[] }
  | { type: 'SET_CURRENT_ORDER'; payload: Order | null }
  | { type: 'ACCEPT_ORDER'; payload: Order }
  | { type: 'DECLINE_ORDER'; payload: string }
  | { type: 'UPDATE_ORDER_STATUS'; payload: { orderId: string; status: OrderStatus } }
  | { type: 'CLEAR_ERROR' };

// Reducer
const orderReducer = (state: OrderState, action: OrderAction): OrderState => {
  switch (action.type) {
    case 'SET_LOADING':
      return {
        ...state,
        isLoading: action.payload,
      };
    case 'SET_ERROR':
      return {
        ...state,
        error: action.payload,
        isLoading: false,
      };
    case 'SET_AVAILABLE_ORDERS':
      return {
        ...state,
        availableOrders: action.payload,
        isLoading: false,
        error: null,
      };
    case 'SET_ACCEPTED_ORDERS':
      return {
        ...state,
        acceptedOrders: action.payload,
        isLoading: false,
        error: null,
      };
    case 'SET_ORDER_HISTORY':
      return {
        ...state,
        orderHistory: action.payload,
        isLoading: false,
        error: null,
      };
    case 'SET_CURRENT_ORDER':
      return {
        ...state,
        currentOrder: action.payload,
      };
    case 'ACCEPT_ORDER':
      return {
        ...state,
        availableOrders: state.availableOrders.filter(order => order.id !== action.payload.id),
        acceptedOrders: [...state.acceptedOrders, { ...action.payload, status: OrderStatus.ACCEPTED }],
        currentOrder: action.payload,
        error: null,
      };
    case 'DECLINE_ORDER':
      return {
        ...state,
        availableOrders: state.availableOrders.filter(order => order.id !== action.payload),
        error: null,
      };
    case 'UPDATE_ORDER_STATUS':
      const { orderId, status } = action.payload;
      
      // Update in accepted orders
      const updatedAcceptedOrders = state.acceptedOrders.map(order =>
        order.id === orderId ? { ...order, status } : order
      );
      
      // If order is delivered, move to history
      if (status === OrderStatus.DELIVERED) {
        const completedOrder = updatedAcceptedOrders.find(order => order.id === orderId);
        return {
          ...state,
          acceptedOrders: updatedAcceptedOrders.filter(order => order.id !== orderId),
          orderHistory: completedOrder ? [completedOrder, ...state.orderHistory] : state.orderHistory,
          currentOrder: state.currentOrder?.id === orderId ? null : state.currentOrder,
          error: null,
        };
      }
      
      return {
        ...state,
        acceptedOrders: updatedAcceptedOrders,
        currentOrder: state.currentOrder?.id === orderId 
          ? { ...state.currentOrder, status } 
          : state.currentOrder,
        error: null,
      };
    case 'CLEAR_ERROR':
      return {
        ...state,
        error: null,
      };
    default:
      return state;
  }
};

// Create context
const OrderContext = createContext<OrderContextType | undefined>(undefined);

// Provider component
interface OrderProviderProps {
  children: React.ReactNode;
}

export const OrderProvider: React.FC<OrderProviderProps> = ({ children }) => {
  const [state, dispatch] = useReducer(orderReducer, initialState);

  // Fetch available orders
  const fetchAvailableOrders = async () => {
    try {
      dispatch({ type: 'SET_LOADING', payload: true });
      dispatch({ type: 'CLEAR_ERROR' });
      
      // Mock data for now - replace with actual API call
      const mockOrders: Order[] = [
        {
          id: '1',
          orderNumber: 'FW-001',
          customer: {
            id: 'c1',
            firstName: 'John',
            lastName: 'Doe',
            phoneNumber: '+**********',
            email: '<EMAIL>',
          },
          restaurant: {
            id: 'r1',
            name: 'Pizza Palace',
            address: {
              id: 'a1',
              street: '123 Main St',
              city: 'New York',
              state: 'NY',
              zipCode: '10001',
              country: 'USA',
              latitude: 40.7128,
              longitude: -74.0060,
            },
            phoneNumber: '+1234567891',
            rating: 4.5,
            preparationTime: 15,
          },
          items: [
            {
              id: 'i1',
              name: 'Margherita Pizza',
              quantity: 1,
              price: 1200,
            },
          ],
          pickupAddress: {
            id: 'a1',
            street: '123 Main St',
            city: 'New York',
            state: 'NY',
            zipCode: '10001',
            country: 'USA',
            latitude: 40.7128,
            longitude: -74.0060,
          },
          deliveryAddress: {
            id: 'a2',
            street: '456 Oak Ave',
            city: 'New York',
            state: 'NY',
            zipCode: '10002',
            country: 'USA',
            latitude: 40.7589,
            longitude: -73.9851,
          },
          status: OrderStatus.AVAILABLE,
          paymentMethod: 'card' as any,
          subtotal: 1200,
          deliveryFee: 150,
          tip: 100,
          total: 1450,
          estimatedEarnings: 250,
          estimatedDistance: 2.5,
          estimatedDuration: 25,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
        },
        // Add more mock orders...
      ];
      
      dispatch({ type: 'SET_AVAILABLE_ORDERS', payload: mockOrders });
    } catch (error: any) {
      dispatch({
        type: 'SET_ERROR',
        payload: error.message || 'Failed to fetch available orders',
      });
    }
  };

  // Accept order
  const acceptOrder = async (orderId: string) => {
    try {
      dispatch({ type: 'SET_LOADING', payload: true });
      dispatch({ type: 'CLEAR_ERROR' });
      
      // Find the order to accept
      const orderToAccept = state.availableOrders.find(order => order.id === orderId);
      if (!orderToAccept) {
        throw new Error('Order not found');
      }
      
      // Mock API call - replace with actual API call
      // await apiClient.post(`/orders/${orderId}/accept`);
      
      dispatch({ type: 'ACCEPT_ORDER', payload: orderToAccept });
    } catch (error: any) {
      dispatch({
        type: 'SET_ERROR',
        payload: error.message || 'Failed to accept order',
      });
      throw error;
    } finally {
      dispatch({ type: 'SET_LOADING', payload: false });
    }
  };

  // Decline order
  const declineOrder = async (orderId: string) => {
    try {
      dispatch({ type: 'SET_LOADING', payload: true });
      dispatch({ type: 'CLEAR_ERROR' });
      
      // Mock API call - replace with actual API call
      // await apiClient.post(`/orders/${orderId}/decline`);
      
      dispatch({ type: 'DECLINE_ORDER', payload: orderId });
    } catch (error: any) {
      dispatch({
        type: 'SET_ERROR',
        payload: error.message || 'Failed to decline order',
      });
      throw error;
    } finally {
      dispatch({ type: 'SET_LOADING', payload: false });
    }
  };

  // Update order status
  const updateOrderStatus = async (orderId: string, status: OrderStatus) => {
    try {
      dispatch({ type: 'SET_LOADING', payload: true });
      dispatch({ type: 'CLEAR_ERROR' });
      
      // Mock API call - replace with actual API call
      // await apiClient.patch(`/orders/${orderId}/status`, { status });
      
      dispatch({ type: 'UPDATE_ORDER_STATUS', payload: { orderId, status } });
    } catch (error: any) {
      dispatch({
        type: 'SET_ERROR',
        payload: error.message || 'Failed to update order status',
      });
      throw error;
    } finally {
      dispatch({ type: 'SET_LOADING', payload: false });
    }
  };

  // Fetch order history
  const fetchOrderHistory = async (filters?: OrderHistoryFilters) => {
    try {
      dispatch({ type: 'SET_LOADING', payload: true });
      dispatch({ type: 'CLEAR_ERROR' });
      
      // Mock data for now - replace with actual API call
      const mockHistory: Order[] = [];
      
      dispatch({ type: 'SET_ORDER_HISTORY', payload: mockHistory });
    } catch (error: any) {
      dispatch({
        type: 'SET_ERROR',
        payload: error.message || 'Failed to fetch order history',
      });
    }
  };

  const startPickupTimer = async (orderId: string) => {
    try {
      // In a real app, this would start a timer on the server
      console.log(`Starting pickup timer for order ${orderId}`);
      // For now, just update the status
      await updateOrderStatus(orderId, OrderStatus.ARRIVED_AT_RESTAURANT);
    } catch (error: any) {
      dispatch({
        type: 'SET_ERROR',
        payload: error.message || 'Failed to start pickup timer',
      });
    }
  };

  const markAsPickedUp = async (orderId: string, waitTime: number) => {
    try {
      // In a real app, this would send the wait time to the server
      console.log(`Marking order ${orderId} as picked up with wait time: ${waitTime}s`);
      await updateOrderStatus(orderId, OrderStatus.PICKED_UP);
    } catch (error: any) {
      dispatch({
        type: 'SET_ERROR',
        payload: error.message || 'Failed to mark as picked up',
      });
    }
  };

  const markAsDelivered = async (orderId: string, proofData: any) => {
    try {
      // In a real app, this would upload the proof data to the server
      console.log(`Marking order ${orderId} as delivered with proof:`, proofData);
      await updateOrderStatus(orderId, OrderStatus.DELIVERED);
    } catch (error: any) {
      dispatch({
        type: 'SET_ERROR',
        payload: error.message || 'Failed to mark as delivered',
      });
    }
  };

  const clearError = () => {
    dispatch({ type: 'CLEAR_ERROR' });
  };

  const contextValue: OrderContextType = {
    state,
    fetchAvailableOrders,
    acceptOrder,
    declineOrder,
    updateOrderStatus,
    startPickupTimer,
    markAsPickedUp,
    markAsDelivered,
    fetchOrderHistory,
    clearError,
  };

  return (
    <OrderContext.Provider value={contextValue}>
      {children}
    </OrderContext.Provider>
  );
};

// Hook to use order context
export const useOrders = (): OrderContextType => {
  const context = useContext(OrderContext);
  if (context === undefined) {
    throw new Error('useOrders must be used within an OrderProvider');
  }
  return context;
};
