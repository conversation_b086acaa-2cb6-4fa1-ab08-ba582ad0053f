import React, { useEffect, useState } from 'react';
import {
  View,
  Text,
  ScrollView,
  RefreshControl,
  TouchableOpacity,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import { useNavigation } from '@react-navigation/native';
import { Card, Badge, LoadingSpinner } from '../../components/ui';
import { useOrders } from '../../context/OrderContext';
import { Order } from '../../types/orders';
import { formatCurrency, formatDate, timeAgo } from '../../utils/helpers';

const OrderHistoryScreen: React.FC = () => {
  const navigation = useNavigation();
  const { state, fetchOrderHistory } = useOrders();
  const [refreshing, setRefreshing] = useState(false);

  useEffect(() => {
    fetchOrderHistory();
  }, []);

  const onRefresh = async () => {
    setRefreshing(true);
    await fetchOrderHistory();
    setRefreshing(false);
  };

  const handleOrderDetails = (order: Order) => {
    // navigation.navigate('OrderDetails' as any, { orderId: order.id });
  };

  const renderOrderCard = (order: Order) => (
    <TouchableOpacity
      key={order.id}
      onPress={() => handleOrderDetails(order)}
      activeOpacity={0.7}
    >
      <Card variant="elevated" margin="sm" padding="lg">
        <View style={{ flexDirection: 'row', justifyContent: 'space-between', marginBottom: 8 }}>
          <View style={{ flex: 1 }}>
            <Text style={{ fontSize: 16, fontWeight: 'bold', color: '#111827' }}>
              {order.restaurant.name}
            </Text>
            <Text style={{ fontSize: 14, color: '#6b7280' }}>
              Order #{order.orderNumber}
            </Text>
          </View>
          <View style={{ alignItems: 'flex-end' }}>
            <Badge
              text={formatCurrency(order.estimatedEarnings)}
              variant="success"
            />
            <Text style={{ fontSize: 12, color: '#6b7280', marginTop: 4 }}>
              {timeAgo(order.updatedAt)}
            </Text>
          </View>
        </View>

        <View style={{
          flexDirection: 'row',
          alignItems: 'center',
          marginBottom: 8,
        }}>
          <Ionicons name="location-outline" size={16} color="#6b7280" />
          <Text style={{ fontSize: 14, color: '#6b7280', marginLeft: 4, flex: 1 }}>
            {order.deliveryAddress.street}, {order.deliveryAddress.city}
          </Text>
        </View>

        <View style={{
          flexDirection: 'row',
          justifyContent: 'space-between',
          alignItems: 'center',
          paddingTop: 12,
          borderTopWidth: 1,
          borderTopColor: '#e5e7eb',
        }}>
          <Text style={{ fontSize: 14, color: '#6b7280' }}>
            {formatDate(order.createdAt, 'DISPLAY_DATETIME')}
          </Text>
          <View style={{ flexDirection: 'row', alignItems: 'center' }}>
            <Text style={{ fontSize: 14, color: '#6b7280', marginRight: 4 }}>
              View Details
            </Text>
            <Ionicons name="chevron-forward" size={16} color="#6b7280" />
          </View>
        </View>
      </Card>
    </TouchableOpacity>
  );

  const renderHeader = () => (
    <View style={{
      flexDirection: 'row',
      alignItems: 'center',
      paddingHorizontal: 20,
      paddingVertical: 16,
      backgroundColor: '#ffffff',
      borderBottomWidth: 1,
      borderBottomColor: '#e5e7eb',
    }}>
      <TouchableOpacity
        onPress={() => navigation.goBack()}
        style={{ marginRight: 16 }}
      >
        <Ionicons name="arrow-back" size={24} color="#374151" />
      </TouchableOpacity>
      
      <Text style={{ fontSize: 24, fontWeight: 'bold', color: '#111827' }}>
        Order History
      </Text>
    </View>
  );

  const renderEmptyState = () => (
    <View style={{
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
      paddingHorizontal: 40,
    }}>
      <Ionicons name="time-outline" size={64} color="#d1d5db" />
      <Text style={{
        fontSize: 18,
        fontWeight: 'bold',
        color: '#374151',
        marginTop: 16,
        marginBottom: 8,
        textAlign: 'center',
      }}>
        No Order History
      </Text>
      <Text style={{
        fontSize: 14,
        color: '#6b7280',
        textAlign: 'center',
        lineHeight: 20,
      }}>
        Your completed orders will appear here. Start delivering to build your history!
      </Text>
    </View>
  );

  if (state.isLoading && !refreshing) {
    return (
      <SafeAreaView style={{ flex: 1, backgroundColor: '#f8fafc' }}>
        {renderHeader()}
        <LoadingSpinner message="Loading order history..." />
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={{ flex: 1, backgroundColor: '#f8fafc' }}>
      {renderHeader()}
      
      {state.orderHistory.length === 0 ? (
        <ScrollView
          contentContainerStyle={{ flex: 1 }}
          refreshControl={
            <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
          }
        >
          {renderEmptyState()}
        </ScrollView>
      ) : (
        <ScrollView
          style={{ flex: 1 }}
          refreshControl={
            <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
          }
          showsVerticalScrollIndicator={false}
        >
          <View style={{ paddingVertical: 8 }}>
            {state.orderHistory.map(renderOrderCard)}
          </View>
          
          {/* Bottom spacing */}
          <View style={{ height: 20 }} />
        </ScrollView>
      )}
    </SafeAreaView>
  );
};

export default OrderHistoryScreen;
