# API Integration Guide - FoodWay Rider App

## Overview

The FoodWay Rider App now includes comprehensive API integration with axios for authentication, registration, and document verification. This guide explains how to connect the app to your backend.

## 🔧 Configuration

### API Base URL
Update the base URL in `src/services/api/apiConfig.ts`:

```typescript
export const API_CONFIG = {
  BASE_URL: __DEV__ ? 'http://localhost:3000/api' : 'https://your-production-api.com/api',
  TIMEOUT: 30000,
  RETRY_ATTEMPTS: 3,
  RETRY_DELAY: 1000,
};
```

### Environment Variables
Create a `.env` file in your project root:

```env
API_BASE_URL_DEV=http://localhost:3000/api
API_BASE_URL_PROD=https://your-production-api.com/api
API_TIMEOUT=30000
```

## 📡 Required Backend Endpoints

### Authentication Endpoints

#### 1. POST `/auth/register`
**Purpose**: Register a new rider account
**Content-Type**: `multipart/form-data` (for file uploads)

**Request Body**:
```json
{
  "firstName": "John",
  "lastName": "Doe",
  "email": "<EMAIL>",
  "phone": "+************",
  "password": "SecurePassword123!",
  "vehicleType": "motorcycle",
  "vehiclePlateNumber": "ABC-123",
  "bankAccountType": "bank_account",
  "accountNumber": "**********",
  "agreeToTerms": true,
  "deviceId": "device_unique_id",
  "documents": {
    "cnic": "file_upload",
    "driving_license": "file_upload",
    "vehicle_registration": "file_upload"
  }
}
```

**Response**:
```json
{
  "success": true,
  "data": {
    "user": {
      "id": "user_id",
      "email": "<EMAIL>",
      "firstName": "John",
      "lastName": "Doe",
      "phone": "+************",
      "verificationStatus": "pending",
      "isDemoAccount": false
    },
    "message": "Registration successful. Please check your email for verification.",
    "nextSteps": [
      "Verify your email address",
      "Wait for document verification",
      "Complete profile setup"
    ]
  },
  "message": "Registration successful"
}
```

#### 2. POST `/auth/login`
**Purpose**: Authenticate rider and get access tokens

**Request Body**:
```json
{
  "email": "<EMAIL>",
  "password": "SecurePassword123!",
  "deviceId": "device_unique_id"
}
```

**Response**:
```json
{
  "success": true,
  "data": {
    "user": {
      "id": "user_id",
      "email": "<EMAIL>",
      "firstName": "John",
      "lastName": "Doe",
      "phone": "+************",
      "isVerified": true,
      "verificationStatus": "verified",
      "documentStatus": {
        "overall": "verified",
        "documents": {
          "cnic": {
            "status": "verified",
            "uploadedAt": "2024-01-15T10:00:00Z",
            "verifiedAt": "2024-01-16T14:30:00Z"
          },
          "driving_license": {
            "status": "verified",
            "uploadedAt": "2024-01-15T10:05:00Z",
            "verifiedAt": "2024-01-16T14:35:00Z"
          }
        }
      },
      "profile": {
        "profilePhoto": "https://example.com/photos/user_id.jpg",
        "dateOfBirth": "1990-01-01",
        "address": "123 Main St, Karachi",
        "city": "Karachi"
      },
      "vehicle": {
        "type": "motorcycle",
        "make": "Honda",
        "model": "CD 70",
        "year": "2020",
        "plateNumber": "ABC-123",
        "color": "Red"
      },
      "bankInfo": {
        "accountType": "bank_account",
        "accountNumber": "****7890",
        "bankName": "HBL",
        "iban": "PK**HABB****7890"
      },
      "isDemoAccount": false,
      "createdAt": "2024-01-15T09:00:00Z",
      "updatedAt": "2024-01-16T14:35:00Z"
    },
    "tokens": {
      "accessToken": "jwt_access_token",
      "refreshToken": "jwt_refresh_token",
      "expiresIn": 3600
    }
  },
  "message": "Login successful"
}
```

#### 3. GET `/auth/me`
**Purpose**: Get current user profile
**Headers**: `Authorization: Bearer {access_token}`

**Response**: Same user object as login response

#### 4. POST `/auth/refresh`
**Purpose**: Refresh access token

**Request Body**:
```json
{
  "refreshToken": "jwt_refresh_token"
}
```

**Response**:
```json
{
  "success": true,
  "data": {
    "accessToken": "new_jwt_access_token",
    "refreshToken": "new_jwt_refresh_token",
    "expiresIn": 3600
  }
}
```

#### 5. POST `/auth/logout`
**Purpose**: Logout user and invalidate tokens
**Headers**: `Authorization: Bearer {access_token}`

### Document Verification Endpoints

#### 1. GET `/documents/verification-status`
**Purpose**: Get overall verification status
**Headers**: `Authorization: Bearer {access_token}`

**Response**:
```json
{
  "success": true,
  "data": {
    "overall": "verified",
    "canGoOnline": true,
    "documents": {
      "cnic": {
        "documentId": "doc_id_1",
        "documentType": "cnic",
        "status": "verified",
        "uploadedAt": "2024-01-15T10:00:00Z",
        "verifiedAt": "2024-01-16T14:30:00Z"
      },
      "driving_license": {
        "documentId": "doc_id_2",
        "documentType": "driving_license",
        "status": "pending",
        "uploadedAt": "2024-01-15T10:05:00Z"
      },
      "vehicle_registration": {
        "documentId": "doc_id_3",
        "documentType": "vehicle_registration",
        "status": "rejected",
        "uploadedAt": "2024-01-15T10:10:00Z",
        "rejectionReason": "Document is not clear, please upload a clearer image"
      }
    },
    "requiredDocuments": ["cnic", "driving_license", "vehicle_registration"],
    "pendingDocuments": ["driving_license"],
    "verifiedDocuments": ["cnic"],
    "rejectedDocuments": ["vehicle_registration"],
    "lastUpdated": "2024-01-16T14:30:00Z"
  }
}
```

#### 2. POST `/documents/upload`
**Purpose**: Upload a document for verification
**Content-Type**: `multipart/form-data`
**Headers**: `Authorization: Bearer {access_token}`

**Request Body**:
```json
{
  "documentType": "cnic",
  "file": "file_upload",
  "metadata": {
    "documentNumber": "12345-6789012-3",
    "expiryDate": "2030-12-31",
    "issueDate": "2020-01-01"
  }
}
```

**Response**:
```json
{
  "success": true,
  "data": {
    "documentId": "doc_id_123",
    "documentType": "cnic",
    "status": "pending",
    "uploadedAt": "2024-01-15T10:00:00Z",
    "message": "Document uploaded successfully and is under review"
  }
}
```

## 🔐 Authentication Flow

### Registration Flow
1. User fills registration form
2. App calls `POST /auth/register` with form data and documents
3. Backend creates account with `verificationStatus: "pending"`
4. User receives success message and is redirected to login
5. Documents are queued for manual verification

### Login Flow
1. User enters credentials
2. App calls `POST /auth/login`
3. Backend returns user data with verification status
4. App stores tokens and user data locally
5. App checks `verificationStatus` and `canGoOnline` status

### Going Online Flow
1. User tries to toggle online status
2. App checks `canGoOnline()` from AuthContext
3. If `false` and not demo account:
   - Show verification required alert
   - Redirect to DocumentStatus screen
4. If `true` or demo account:
   - Allow going online

## 🚫 Demo Account Handling

Demo accounts bypass verification:
- Email contains "demo" or "test"
- `isDemoAccount: true` in user object
- Can always go online regardless of verification status
- Use mock service instead of API calls

## 🔄 Error Handling

The app includes comprehensive error handling:
- Network errors
- Token expiration (auto-refresh)
- API errors with user-friendly messages
- Retry mechanism for failed requests

## 📱 App Behavior

### Verification Status Impact
- **verified**: Can go online, full app access
- **pending**: Cannot go online, can use other features
- **rejected**: Cannot go online, must re-upload documents
- **incomplete**: Cannot go online, must upload missing documents

### Demo Accounts
- Always show as verified
- Can go online immediately
- Use mock data for all operations

## 🛠 Backend Implementation Notes

1. **File Storage**: Use cloud storage (AWS S3, Google Cloud) for document uploads
2. **Security**: Implement proper JWT token validation and refresh logic
3. **Verification**: Manual or automated document verification workflow
4. **Notifications**: Send push notifications for verification status updates
5. **Audit Trail**: Log all document uploads and verification actions

## 📋 Testing

Use these test accounts:
- **Demo Account**: `<EMAIL>` / `password123`
- **Real Account**: Create through registration flow

The app automatically detects demo accounts and uses appropriate service (mock vs API).
