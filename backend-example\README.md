# FoodWay Rider API - Backend Example

This is a simple Express.js backend server that demonstrates the API endpoints required for the FoodWay Rider App.

## 🚀 Quick Start

### Prerequisites
- Node.js (v14 or higher)
- npm or yarn

### Installation

1. Navigate to the backend-example directory:
```bash
cd backend-example
```

2. Install dependencies:
```bash
npm install
```

3. Create uploads directory:
```bash
mkdir uploads
```

4. Start the server:
```bash
npm run dev
```

The server will start on `http://localhost:3000`

### Health Check
Visit `http://localhost:3000/api/health` to verify the server is running.

## 📡 API Endpoints

### Authentication
- `POST /api/auth/register` - Register new rider
- `POST /api/auth/login` - Login rider
- `GET /api/auth/me` - Get current user
- `POST /api/auth/refresh` - Refresh access token
- `POST /api/auth/logout` - Logout rider

### Document Verification
- `GET /api/documents/verification-status` - Get verification status
- `POST /api/documents/upload` - Upload document
- `GET /api/documents/status` - Get all documents status

## 🧪 Testing

### Test Registration
```bash
curl -X POST http://localhost:3000/api/auth/register \
  -F "firstName=John" \
  -F "lastName=Doe" \
  -F "email=<EMAIL>" \
  -F "phone=+************" \
  -F "password=SecurePassword123!" \
  -F "vehicleType=motorcycle" \
  -F "vehiclePlateNumber=ABC-123" \
  -F "bankAccountType=bank_account" \
  -F "accountNumber=**********" \
  -F "agreeToTerms=true" \
  -F "deviceId=test_device_123"
```

### Test Login
```bash
curl -X POST http://localhost:3000/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "SecurePassword123!",
    "deviceId": "test_device_123"
  }'
```

### Test Document Upload
```bash
curl -X POST http://localhost:3000/api/documents/upload \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN" \
  -F "documentType=cnic" \
  -F "file=@/path/to/your/document.jpg"
```

## 🔧 Configuration

### Environment Variables
Create a `.env` file:

```env
PORT=3000
JWT_SECRET=your-super-secret-jwt-key
NODE_ENV=development
```

### File Upload Settings
- Maximum file size: 5MB
- Allowed formats: JPEG, JPG, PNG, PDF
- Upload directory: `./uploads/`

## 🗄️ Data Storage

This example uses in-memory storage for simplicity. In production, you should use:

- **Database**: PostgreSQL, MongoDB, or MySQL
- **File Storage**: AWS S3, Google Cloud Storage, or similar
- **Cache**: Redis for session management
- **Queue**: Bull or similar for background jobs

## 🔐 Security Features

- Password hashing with bcrypt
- JWT token authentication
- File type validation
- File size limits
- CORS enabled

## 📝 Production Considerations

1. **Database Integration**:
   ```javascript
   // Replace in-memory arrays with database queries
   const user = await User.findOne({ email });
   ```

2. **File Storage**:
   ```javascript
   // Use cloud storage instead of local files
   const uploadResult = await s3.upload(params).promise();
   ```

3. **Environment Variables**:
   ```javascript
   const JWT_SECRET = process.env.JWT_SECRET;
   const DB_URL = process.env.DATABASE_URL;
   ```

4. **Error Handling**:
   ```javascript
   app.use((error, req, res, next) => {
     console.error(error);
     res.status(500).json({ success: false, message: 'Internal server error' });
   });
   ```

5. **Rate Limiting**:
   ```javascript
   const rateLimit = require('express-rate-limit');
   app.use('/api/', rateLimit({ windowMs: 15 * 60 * 1000, max: 100 }));
   ```

6. **Input Validation**:
   ```javascript
   const { body, validationResult } = require('express-validator');
   ```

7. **Logging**:
   ```javascript
   const winston = require('winston');
   ```

## 🔄 Document Verification Workflow

1. **Upload**: Rider uploads documents via app
2. **Storage**: Files stored securely with metadata
3. **Queue**: Documents queued for verification
4. **Review**: Manual or automated verification
5. **Update**: Status updated in database
6. **Notify**: Push notification sent to rider
7. **Access**: Rider can go online when verified

## 📱 App Integration

The React Native app is configured to work with this backend:

1. Update `API_CONFIG.BASE_URL` in `src/services/api/apiConfig.ts`
2. Ensure the server is running on `http://localhost:3000`
3. Test registration and login flows
4. Verify document upload functionality

## 🐛 Troubleshooting

### Common Issues

1. **CORS Errors**: Ensure CORS is properly configured
2. **File Upload Fails**: Check file size and format
3. **Token Errors**: Verify JWT secret and expiration
4. **Port Conflicts**: Change PORT in .env file

### Debug Mode
```bash
DEBUG=* npm run dev
```

## 📚 Next Steps

1. Integrate with a real database
2. Implement proper file storage
3. Add comprehensive error handling
4. Set up automated testing
5. Deploy to production environment
6. Add monitoring and logging
7. Implement background job processing

This example provides a solid foundation for building a production-ready API for the FoodWay Rider App.
