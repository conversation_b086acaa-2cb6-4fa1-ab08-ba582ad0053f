// Type declarations for React Native 0.79.x compatibility
declare module 'react-native' {
  export * from 'react-native/types';
  
  // Core components
  export const View: any;
  export const Text: any;
  export const ScrollView: any;
  export const TouchableOpacity: any;
  export const TextInput: any;
  export const Image: any;
  export const Modal: any;
  export const Alert: any;
  export const Platform: any;
  export const Dimensions: any;
  export const Animated: any;
  export const ActivityIndicator: any;
  export const Switch: any;
  export const RefreshControl: any;
  export const KeyboardAvoidingView: any;
  export const Keyboard: any;
  export const Linking: any;
  export const StyleSheet: any;
  
  // Types
  export type ViewStyle = any;
  export type TextStyle = any;
  export type ViewProps = any;
  export type TextInputProps = any;
  export type TouchableOpacityProps = any;
  export type ActivityIndicatorProps = any;
  export type KeyboardAvoidingViewProps = any;
  export type ImageSourcePropType = any;
  export type ImageURISource = any;
  export type ImageRequireSource = any;
  export type NativeSyntheticEvent<T = any> = any;
  export type ProcessedColorValue = any;
  export type ColorValue = any;
  export type HostComponent<T = any> = any;
  
  // Functions
  export const findNodeHandle: any;
  export const requireNativeComponent: any;
  export const processColor: any;
  
  // Modules
  export const NativeModules: any;
  export const UIManager: any;
  export const TurboModule: any;
  export const TurboModuleRegistry: any;
}

declare module 'react-native-safe-area-context' {
  export const SafeAreaView: any;
  export const useSafeAreaInsets: any;
  export const SafeAreaProvider: any;
}

declare module 'react-native-maps' {
  export default class MapView {
    static PROVIDER_GOOGLE: string;
    static PROVIDER_DEFAULT: string;
  }
  
  export const Marker: any;
  export const Polyline: any;
  export const Circle: any;
  export const Polygon: any;
  export const Callout: any;
  export const AnimatedRegion: any;
  
  export interface Region {
    latitude: number;
    longitude: number;
    latitudeDelta: number;
    longitudeDelta: number;
  }
  
  export interface LatLng {
    latitude: number;
    longitude: number;
  }
  
  export interface MapViewProps {
    provider?: string;
    style?: any;
    initialRegion?: Region;
    region?: Region;
    showsUserLocation?: boolean;
    showsMyLocationButton?: boolean;
    showsTraffic?: boolean;
    mapType?: string;
    onRegionChange?: (region: Region) => void;
    onRegionChangeComplete?: (region: Region) => void;
    onPress?: (event: any) => void;
    children?: React.ReactNode;
  }
  
  export interface MarkerProps {
    coordinate: LatLng;
    title?: string;
    description?: string;
    image?: any;
    pinColor?: string;
    anchor?: { x: number; y: number };
    centerOffset?: { x: number; y: number };
    onPress?: () => void;
    tracksViewChanges?: boolean;
    zIndex?: number;
    children?: React.ReactNode;
  }
}
