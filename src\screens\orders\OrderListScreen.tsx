import React, { useEffect, useState } from 'react';
import {
  View,
  Text,
  ScrollView,
  RefreshControl,
  TouchableOpacity,
  Alert,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import { useNavigation } from '@react-navigation/native';
import { Card, Button, Badge, LoadingSpinner } from '../../components/ui';
import { useOrders } from '../../context/OrderContext';
import { Order, OrderStatus } from '../../types/orders';
import { formatCurrency, formatDistance, formatDuration } from '../../utils/helpers';

const OrderListScreen: React.FC = () => {
  const navigation = useNavigation();
  const { state, fetchAvailableOrders, acceptOrder, declineOrder, clearError } = useOrders();
  const [refreshing, setRefreshing] = useState(false);

  useEffect(() => {
    fetchAvailableOrders();
  }, []);

  const onRefresh = async () => {
    setRefreshing(true);
    await fetchAvailableOrders();
    setRefreshing(false);
  };

  const handleAcceptOrder = async (orderId: string) => {
    try {
      await acceptOrder(orderId);
      Alert.alert('Success', 'Order accepted successfully!');
    } catch (error: any) {
      Alert.alert('Error', error.message);
    }
  };

  const handleDeclineOrder = async (orderId: string) => {
    Alert.alert(
      'Decline Order',
      'Are you sure you want to decline this order?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Decline',
          style: 'destructive',
          onPress: async () => {
            try {
              await declineOrder(orderId);
            } catch (error: any) {
              Alert.alert('Error', error.message);
            }
          },
        },
      ]
    );
  };

  const handleOrderDetails = (order: Order) => {
    (navigation as any).navigate('OrderDetails', { orderId: order.id });
  };

  const renderOrderCard = (order: Order) => (
    <Card key={order.id} variant="elevated" margin="sm" padding="lg">
      <View style={{ flexDirection: 'row', justifyContent: 'space-between', marginBottom: 12 }}>
        <View>
          <Text style={{ fontSize: 16, fontWeight: 'bold', color: '#111827' }}>
            {order.restaurant.name}
          </Text>
          <Text style={{ fontSize: 14, color: '#6b7280' }}>
            Order #{order.orderNumber}
          </Text>
        </View>
        <Badge
          text={formatCurrency(order.estimatedEarnings)}
          variant="success"
        />
      </View>

      <View style={{
        flexDirection: 'row',
        alignItems: 'center',
        marginBottom: 8,
      }}>
        <Ionicons name="location-outline" size={16} color="#6b7280" />
        <Text style={{ fontSize: 14, color: '#6b7280', marginLeft: 4, flex: 1 }}>
          {order.restaurant.address.street}, {order.restaurant.address.city}
        </Text>
      </View>

      <View style={{
        flexDirection: 'row',
        alignItems: 'center',
        marginBottom: 16,
      }}>
        <Ionicons name="navigate-outline" size={16} color="#6b7280" />
        <Text style={{ fontSize: 14, color: '#6b7280', marginLeft: 4, flex: 1 }}>
          {order.deliveryAddress.street}, {order.deliveryAddress.city}
        </Text>
      </View>

      <View style={{
        flexDirection: 'row',
        justifyContent: 'space-between',
        marginBottom: 16,
        paddingVertical: 12,
        paddingHorizontal: 16,
        backgroundColor: '#f9fafb',
        borderRadius: 8,
      }}>
        <View style={{ alignItems: 'center' }}>
          <Text style={{ fontSize: 16, fontWeight: 'bold', color: '#111827' }}>
            {formatDistance(order.estimatedDistance)}
          </Text>
          <Text style={{ fontSize: 12, color: '#6b7280' }}>Distance</Text>
        </View>
        
        <View style={{ alignItems: 'center' }}>
          <Text style={{ fontSize: 16, fontWeight: 'bold', color: '#111827' }}>
            {formatDuration(order.estimatedDuration)}
          </Text>
          <Text style={{ fontSize: 12, color: '#6b7280' }}>Duration</Text>
        </View>
        
        <View style={{ alignItems: 'center' }}>
          <Text style={{ fontSize: 16, fontWeight: 'bold', color: '#111827' }}>
            {formatCurrency(order.total)}
          </Text>
          <Text style={{ fontSize: 12, color: '#6b7280' }}>Order Total</Text>
        </View>
      </View>

      {/* View Details Button */}
      <TouchableOpacity
        onPress={() => handleOrderDetails(order)}
        style={{
          flexDirection: 'row',
          alignItems: 'center',
          justifyContent: 'center',
          backgroundColor: '#f3f4f6',
          paddingVertical: 8,
          paddingHorizontal: 12,
          borderRadius: 8,
          marginBottom: 12,
        }}
      >
        <Ionicons name="eye-outline" size={16} color="#374151" />
        <Text style={{ fontSize: 14, fontWeight: '500', color: '#374151', marginLeft: 6 }}>
          View Details
        </Text>
      </TouchableOpacity>

      <View style={{ flexDirection: 'row', gap: 8 }}>
        <Button
          title="Decline"
          variant="outline"
          onPress={() => handleDeclineOrder(order.id)}
          style={{ flex: 1 }}
        />
        <Button
          title="Accept"
          onPress={() => handleAcceptOrder(order.id)}
          style={{ flex: 1 }}
        />
      </View>
    </Card>
  );

  const renderHeader = () => (
    <View style={{
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      paddingHorizontal: 20,
      paddingVertical: 16,
      backgroundColor: '#ffffff',
      borderBottomWidth: 1,
      borderBottomColor: '#e5e7eb',
    }}>
      <Text style={{ fontSize: 24, fontWeight: 'bold', color: '#111827' }}>
        Available Orders
      </Text>
      
      <TouchableOpacity
        onPress={() => navigation.navigate('History' as never)}
        style={{
          flexDirection: 'row',
          alignItems: 'center',
          paddingVertical: 8,
          paddingHorizontal: 12,
          backgroundColor: '#f3f4f6',
          borderRadius: 8,
        }}
      >
        <Ionicons name="time-outline" size={16} color="#374151" />
        <Text style={{ color: '#374151', fontSize: 14, fontWeight: '500', marginLeft: 4 }}>
          History
        </Text>
      </TouchableOpacity>
    </View>
  );

  const renderEmptyState = () => (
    <View style={{
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
      paddingHorizontal: 40,
    }}>
      <Ionicons name="receipt-outline" size={64} color="#d1d5db" />
      <Text style={{
        fontSize: 18,
        fontWeight: 'bold',
        color: '#374151',
        marginTop: 16,
        marginBottom: 8,
        textAlign: 'center',
      }}>
        No Orders Available
      </Text>
      <Text style={{
        fontSize: 14,
        color: '#6b7280',
        textAlign: 'center',
        lineHeight: 20,
      }}>
        There are no orders available right now. Pull down to refresh or check back later.
      </Text>
    </View>
  );

  if (state.isLoading && !refreshing) {
    return (
      <SafeAreaView style={{ flex: 1, backgroundColor: '#f8fafc' }}>
        {renderHeader()}
        <LoadingSpinner message="Loading orders..." />
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={{ flex: 1, backgroundColor: '#f8fafc' }}>
      {renderHeader()}
      
      {state.availableOrders.length === 0 ? (
        <ScrollView
          contentContainerStyle={{ flex: 1 }}
          refreshControl={
            <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
          }
        >
          {renderEmptyState()}
        </ScrollView>
      ) : (
        <ScrollView
          style={{ flex: 1 }}
          refreshControl={
            <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
          }
          showsVerticalScrollIndicator={false}
        >
          <View style={{ paddingVertical: 8 }}>
            {state.availableOrders.map(renderOrderCard)}
          </View>
          
          {/* Bottom spacing */}
          <View style={{ height: 20 }} />
        </ScrollView>
      )}
    </SafeAreaView>
  );
};

export default OrderListScreen;
