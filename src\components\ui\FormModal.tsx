import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  ScrollView,
  KeyboardAvoidingView,
  Platform,
  Animated,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import Modal from './Modal';
import Input from './Input';

interface FormField {
  key: string;
  label: string;
  type: 'text' | 'email' | 'phone' | 'password' | 'textarea' | 'number';
  placeholder?: string;
  required?: boolean;
  validation?: (value: string) => string | null;
  multiline?: boolean;
  maxLength?: number;
  keyboardType?: 'default' | 'email-address' | 'numeric' | 'phone-pad';
}

interface FormModalProps {
  isVisible: boolean;
  onClose: () => void;
  onSubmit: (data: Record<string, string>) => void;
  title: string;
  subtitle?: string;
  fields: FormField[];
  submitText?: string;
  cancelText?: string;
  loading?: boolean;
  initialData?: Record<string, string>;
  icon?: keyof typeof Ionicons.glyphMap;
}

const FormModal: React.FC<FormModalProps> = ({
  isVisible,
  onClose,
  onSubmit,
  title,
  subtitle,
  fields,
  submitText = 'Submit',
  cancelText = 'Cancel',
  loading = false,
  initialData = {},
  icon,
}) => {
  const [formData, setFormData] = useState<Record<string, string>>(initialData);
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [fadeAnimation] = useState(new Animated.Value(0));

  useEffect(() => {
    if (isVisible) {
      setFormData(initialData);
      setErrors({});
      Animated.timing(fadeAnimation, {
        toValue: 1,
        duration: 300,
        useNativeDriver: true,
      }).start();
    } else {
      fadeAnimation.setValue(0);
    }
  }, [isVisible, initialData]);

  const handleInputChange = (key: string, value: string) => {
    setFormData(prev => ({ ...prev, [key]: value }));
    
    // Clear error when user starts typing
    if (errors[key]) {
      setErrors(prev => ({ ...prev, [key]: '' }));
    }
  };

  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};
    let isValid = true;

    fields.forEach(field => {
      const value = formData[field.key] || '';
      
      // Required field validation
      if (field.required && !value.trim()) {
        newErrors[field.key] = `${field.label} is required`;
        isValid = false;
        return;
      }

      // Custom validation
      if (field.validation && value) {
        const validationError = field.validation(value);
        if (validationError) {
          newErrors[field.key] = validationError;
          isValid = false;
        }
      }

      // Built-in validations
      if (value) {
        switch (field.type) {
          case 'email':
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            if (!emailRegex.test(value)) {
              newErrors[field.key] = 'Please enter a valid email address';
              isValid = false;
            }
            break;
          case 'phone':
            const phoneRegex = /^[\+]?[1-9][\d]{0,15}$/;
            if (!phoneRegex.test(value.replace(/\s/g, ''))) {
              newErrors[field.key] = 'Please enter a valid phone number';
              isValid = false;
            }
            break;
        }
      }
    });

    setErrors(newErrors);
    return isValid;
  };

  const handleSubmit = () => {
    if (validateForm() && !loading) {
      onSubmit(formData);
    }
  };

  const getKeyboardType = (field: FormField) => {
    if (field.keyboardType) return field.keyboardType;
    
    switch (field.type) {
      case 'email': return 'email-address';
      case 'phone': return 'phone-pad';
      case 'number': return 'numeric';
      default: return 'default';
    }
  };

  const renderField = (field: FormField) => (
    <View key={field.key} style={{ marginBottom: 20 }}>
      <Text style={{
        fontSize: 16,
        fontWeight: '600',
        color: '#111827',
        marginBottom: 8,
      }}>
        {field.label}
        {field.required && (
          <Text style={{ color: '#dc2626' }}> *</Text>
        )}
      </Text>
      
      <Input
        value={formData[field.key] || ''}
        onChangeText={(value) => handleInputChange(field.key, value)}
        placeholder={field.placeholder || `Enter ${field.label.toLowerCase()}`}
        secureTextEntry={field.type === 'password'}
        multiline={field.multiline || field.type === 'textarea'}
        numberOfLines={field.type === 'textarea' ? 4 : 1}
        maxLength={field.maxLength}
        keyboardType={getKeyboardType(field)}
        error={errors[field.key]}
        editable={!loading}
      />
    </View>
  );

  return (
    <Modal
      isVisible={isVisible}
      onClose={onClose}
      variant="center"
      size="lg"
      showCloseButton={false}
      closeOnBackdrop={!loading}
    >
      <KeyboardAvoidingView
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        style={{ flex: 1 }}
      >
        <Animated.View style={{ opacity: fadeAnimation, flex: 1 }}>
          {/* Header */}
          <View style={{
            paddingHorizontal: 24,
            paddingTop: 24,
            paddingBottom: 16,
            borderBottomWidth: 1,
            borderBottomColor: '#f3f4f6',
          }}>
            <View style={{
              flexDirection: 'row',
              alignItems: 'center',
              marginBottom: subtitle ? 8 : 0,
            }}>
              {icon && (
                <View style={{
                  width: 48,
                  height: 48,
                  backgroundColor: '#dc262620',
                  borderRadius: 24,
                  alignItems: 'center',
                  justifyContent: 'center',
                  marginRight: 16,
                }}>
                  <Ionicons name={icon} size={24} color="#dc2626" />
                </View>
              )}
              
              <View style={{ flex: 1 }}>
                <Text style={{
                  fontSize: 20,
                  fontWeight: 'bold',
                  color: '#111827',
                }}>
                  {title}
                </Text>
                
                {subtitle && (
                  <Text style={{
                    fontSize: 14,
                    color: '#6b7280',
                    marginTop: 4,
                  }}>
                    {subtitle}
                  </Text>
                )}
              </View>

              <TouchableOpacity
                onPress={onClose}
                disabled={loading}
                style={{
                  width: 40,
                  height: 40,
                  borderRadius: 20,
                  backgroundColor: '#f3f4f6',
                  alignItems: 'center',
                  justifyContent: 'center',
                  opacity: loading ? 0.5 : 1,
                }}
              >
                <Ionicons name="close" size={20} color="#6b7280" />
              </TouchableOpacity>
            </View>
          </View>

          {/* Form Content */}
          <ScrollView
            style={{ flex: 1 }}
            contentContainerStyle={{ padding: 24 }}
            showsVerticalScrollIndicator={false}
          >
            {fields.map(renderField)}
          </ScrollView>

          {/* Footer */}
          <View style={{
            paddingHorizontal: 24,
            paddingVertical: 20,
            borderTopWidth: 1,
            borderTopColor: '#f3f4f6',
            backgroundColor: '#f8fafc',
          }}>
            <View style={{
              flexDirection: 'row',
              gap: 12,
            }}>
              {/* Cancel Button */}
              <TouchableOpacity
                onPress={onClose}
                disabled={loading}
                style={{
                  flex: 1,
                  backgroundColor: 'transparent',
                  borderWidth: 2,
                  borderColor: '#e5e7eb',
                  borderRadius: 16,
                  paddingVertical: 16,
                  alignItems: 'center',
                  opacity: loading ? 0.5 : 1,
                }}
              >
                <Text style={{
                  fontSize: 16,
                  fontWeight: 'bold',
                  color: '#6b7280',
                }}>
                  {cancelText}
                </Text>
              </TouchableOpacity>

              {/* Submit Button */}
              <TouchableOpacity
                onPress={handleSubmit}
                disabled={loading}
                style={{
                  flex: 1,
                  backgroundColor: '#dc2626',
                  borderRadius: 16,
                  paddingVertical: 16,
                  alignItems: 'center',
                  flexDirection: 'row',
                  justifyContent: 'center',
                  opacity: loading ? 0.7 : 1,
                  shadowColor: '#dc2626',
                  shadowOffset: { width: 0, height: 4 },
                  shadowOpacity: 0.3,
                  shadowRadius: 8,
                  elevation: 6,
                }}
              >
                {loading && (
                  <View style={{
                    width: 20,
                    height: 20,
                    borderRadius: 10,
                    borderWidth: 2,
                    borderColor: 'rgba(255,255,255,0.3)',
                    borderTopColor: 'white',
                    marginRight: 8,
                  }} />
                )}
                <Text style={{
                  fontSize: 16,
                  fontWeight: 'bold',
                  color: 'white',
                }}>
                  {loading ? 'Submitting...' : submitText}
                </Text>
              </TouchableOpacity>
            </View>
          </View>
        </Animated.View>
      </KeyboardAvoidingView>
    </Modal>
  );
};

export default FormModal;
