// API Services Export
export { default as AuthService } from './authService';
export { default as DocumentService } from './documentService';
export { default as OrderService } from './orderService';
export { default as DashboardService } from './dashboardService';
export { default as ProfileService } from './profileService';

// API Configuration
export { apiClient, API_CONFIG, STORAGE_KEYS } from './apiConfig';

// Re-export types for convenience
export type { ApiResponse, ApiError } from './apiConfig';
export type { LoginResponse, RegisterResponse } from './authService';
export type { Order, OrderRequest, OrderStatusUpdate } from './orderService';
export type { DashboardStats, RiderStatus, AvailableOrder } from './dashboardService';
export type { RiderProfile, VehicleInfo, PaymentMethod } from './profileService';

// Service instances for easy importing
export const apiServices = {
  auth: AuthService,
  documents: DocumentService,
  orders: OrderService,
  dashboard: DashboardService,
  profile: ProfileService,
};

// Helper function to check if API is available
export const checkApiHealth = async (): Promise<boolean> => {
  try {
    const response = await apiClient.get('/health');
    return response.data.success === true;
  } catch (error) {
    console.error('API health check failed:', error);
    return false;
  }
};

// Helper function to get API base URL
export const getApiBaseUrl = (): string => {
  return API_CONFIG.BASE_URL;
};

// Helper function to check if using demo mode
export const isDemoMode = (email?: string): boolean => {
  if (!email) return false;
  return email.toLowerCase().includes('demo') || email.toLowerCase().includes('test');
};
