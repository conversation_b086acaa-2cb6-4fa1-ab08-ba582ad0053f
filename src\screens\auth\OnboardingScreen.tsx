import React, { useState, useRef, useEffect } from 'react';
import {
  View,
  Text,
  ScrollView,
  Dimensions,
  TouchableOpacity,
  Animated,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import { LinearGradient } from 'expo-linear-gradient';
import { OnboardingSlide } from '../../types/auth';

const { width, height } = Dimensions.get('window');

interface OnboardingScreenProps {
  onComplete: () => void;
  onSkip: () => void;
}

const onboardingSlides: OnboardingSlide[] = [
  {
    id: '1',
    title: 'Welcome to FoodWay Rider',
    description: 'Join thousands of riders earning money by delivering delicious food to hungry customers across the city.',
    image: '🚴‍♂️',
    icon: 'bicycle',
  },
  {
    id: '2',
    title: 'Flexible Working Hours',
    description: 'Work when you want, where you want. Set your own schedule and earn money on your terms.',
    image: '⏰',
    icon: 'time',
  },
  {
    id: '3',
    title: 'Easy Earnings',
    description: 'Get paid instantly after each delivery. Track your earnings in real-time and cash out anytime.',
    image: '💰',
    icon: 'cash',
  },
  {
    id: '4',
    title: 'Safe & Secure',
    description: 'Your safety is our priority. Built-in safety features and 24/7 support to keep you protected.',
    image: '🛡️',
    icon: 'shield-checkmark',
  },
  {
    id: '5',
    title: 'Ready to Start?',
    description: 'Complete your registration and start earning with FoodWay today. Your journey begins now!',
    image: '🚀',
    icon: 'rocket',
  },
];

const OnboardingScreen: React.FC<OnboardingScreenProps> = ({ onComplete, onSkip }) => {
  const [currentIndex, setCurrentIndex] = useState(0);
  const scrollViewRef = useRef<any>(null);
  const fadeAnim = useRef(new Animated.Value(1)).current;
  const slideAnim = useRef(new Animated.Value(0)).current;
  const scaleAnim = useRef(new Animated.Value(0.8)).current;
  const buttonScaleAnim = useRef(new Animated.Value(1)).current;

  useEffect(() => {
    // Initial animation
    Animated.parallel([
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 800,
        useNativeDriver: true,
      }),
      Animated.spring(scaleAnim, {
        toValue: 1,
        tension: 50,
        friction: 7,
        useNativeDriver: true,
      }),
    ]).start();
  }, []);

  useEffect(() => {
    // Animate slide transition
    Animated.sequence([
      Animated.timing(slideAnim, {
        toValue: -20,
        duration: 200,
        useNativeDriver: true,
      }),
      Animated.timing(slideAnim, {
        toValue: 0,
        duration: 300,
        useNativeDriver: true,
      }),
    ]).start();
  }, [currentIndex]);

  const handleScroll = (event: any) => {
    const contentOffset = event.nativeEvent.contentOffset.x;
    const index = Math.round(contentOffset / width);
    setCurrentIndex(index);
  };

  const goToSlide = (index: number) => {
    scrollViewRef.current?.scrollTo({ x: index * width, animated: true });
    setCurrentIndex(index);
  };

  const handleNext = () => {
    // Button press animation
    Animated.sequence([
      Animated.timing(buttonScaleAnim, { toValue: 0.95, duration: 100, useNativeDriver: true }),
      Animated.timing(buttonScaleAnim, { toValue: 1, duration: 100, useNativeDriver: true }),
    ]).start();

    if (currentIndex < onboardingSlides.length - 1) {
      goToSlide(currentIndex + 1);
    } else {
      onComplete();
    }
  };

  const handlePrevious = () => {
    if (currentIndex > 0) {
      goToSlide(currentIndex - 1);
    }
  };

  const renderSlide = (slide: OnboardingSlide, index: number) => (
    <View key={slide.id} style={{ width, minHeight: height * 0.7 }} className="flex-1 px-8 justify-center">
      <Animated.View
        style={{
          opacity: fadeAnim,
          transform: [
            { translateY: slideAnim },
            { scale: scaleAnim },
          ],
        }}
        className="items-center"
      >
        {/* Enhanced Icon Container with Gradient */}
        <View className="items-center mb-12">
          <LinearGradient
            colors={['#FEF3C7', '#FDE68A', '#F59E0B']}
            style={{
              width: 160,
              height: 160,
              borderRadius: 80,
              justifyContent: 'center',
              alignItems: 'center',
              marginBottom: 20,
              shadowColor: '#EF4444',
              shadowOffset: { width: 0, height: 8 },
              shadowOpacity: 0.3,
              shadowRadius: 16,
              elevation: 12,
            }}
          >
            <View style={{
              width: 120,
              height: 120,
              backgroundColor: 'white',
              borderRadius: 60,
              justifyContent: 'center',
              alignItems: 'center',
              shadowColor: '#000',
              shadowOffset: { width: 0, height: 4 },
              shadowOpacity: 0.1,
              shadowRadius: 8,
              elevation: 6,
            }}>
              <Text style={{ fontSize: 48 }}>{slide.image}</Text>
            </View>
          </LinearGradient>

          {/* Floating Icon Badge */}
          <View style={{
            position: 'absolute',
            bottom: 10,
            right: width * 0.25,
            width: 48,
            height: 48,
            backgroundColor: '#EF4444',
            borderRadius: 24,
            justifyContent: 'center',
            alignItems: 'center',
            borderWidth: 3,
            borderColor: 'white',
            shadowColor: '#EF4444',
            shadowOffset: { width: 0, height: 4 },
            shadowOpacity: 0.4,
            shadowRadius: 8,
            elevation: 8,
          }}>
            <Ionicons name={slide.icon as any} size={24} color="white" />
          </View>
        </View>

        {/* Enhanced Content */}
        <View className="items-center px-4">
          <Text style={{
            fontSize: 28,
            fontWeight: 'bold',
            color: '#1F2937',
            textAlign: 'center',
            marginBottom: 16,
            lineHeight: 36,
          }}>
            {slide.title}
          </Text>
          <Text style={{
            fontSize: 16,
            color: '#6B7280',
            textAlign: 'center',
            lineHeight: 24,
            paddingHorizontal: 8,
          }}>
            {slide.description}
          </Text>
        </View>
      </Animated.View>
    </View>
  );

  return (
    <LinearGradient
      colors={['#FFFFFF', '#FEF3C7', '#FFFFFF']}
      style={{ flex: 1 }}
    >
      <SafeAreaView style={{ flex: 1 }}>
        {/* Enhanced Skip Button */}
        <View style={{
          flexDirection: 'row',
          justifyContent: 'space-between',
          alignItems: 'center',
          paddingHorizontal: 20,
          paddingVertical: 16,
        }}>
          <View style={{ width: 60 }} />
          <TouchableOpacity
            onPress={onSkip}
            style={{
              paddingHorizontal: 16,
              paddingVertical: 8,
              borderRadius: 20,
              backgroundColor: 'rgba(239, 68, 68, 0.1)',
            }}
          >
            <Text style={{
              color: '#EF4444',
              fontWeight: '600',
              fontSize: 14,
            }}>
              Skip
            </Text>
          </TouchableOpacity>
        </View>

        {/* Slides */}
        <ScrollView
          ref={scrollViewRef}
          horizontal
          pagingEnabled
          showsHorizontalScrollIndicator={false}
          onScroll={handleScroll}
          scrollEventThrottle={16}
          style={{ flex: 1 }}
        >
          {onboardingSlides.map(renderSlide)}
        </ScrollView>

        {/* Enhanced Bottom Section */}
        <View style={{
          paddingHorizontal: 32,
          paddingBottom: 32,
          paddingTop: 16,
        }}>
          {/* Enhanced Pagination Dots */}
          <View style={{
            flexDirection: 'row',
            justifyContent: 'center',
            alignItems: 'center',
            marginBottom: 32,
          }}>
            {onboardingSlides.map((_, index) => (
              <TouchableOpacity
                key={index}
                onPress={() => goToSlide(index)}
                style={{
                  width: index === currentIndex ? 24 : 8,
                  height: 8,
                  borderRadius: 4,
                  marginHorizontal: 4,
                  backgroundColor: index === currentIndex ? '#EF4444' : '#E5E7EB',
                }}
              />
            ))}
          </View>

          {/* Enhanced Navigation Buttons */}
          <View style={{
            flexDirection: 'row',
            justifyContent: 'space-between',
            alignItems: 'center',
          }}>
            <TouchableOpacity
              onPress={handlePrevious}
              disabled={currentIndex === 0}
              style={{
                flexDirection: 'row',
                alignItems: 'center',
                paddingHorizontal: 20,
                paddingVertical: 12,
                borderRadius: 12,
                backgroundColor: currentIndex === 0 ? 'transparent' : 'rgba(107, 114, 128, 0.1)',
                opacity: currentIndex === 0 ? 0.3 : 1,
              }}
            >
              <Ionicons name="chevron-back" size={20} color="#6B7280" />
              <Text style={{
                color: '#6B7280',
                marginLeft: 4,
                fontWeight: '500',
              }}>
                Previous
              </Text>
            </TouchableOpacity>

            <Animated.View style={{ transform: [{ scale: buttonScaleAnim }] }}>
              <TouchableOpacity
                onPress={handleNext}
                style={{
                  backgroundColor: '#EF4444',
                  flexDirection: 'row',
                  alignItems: 'center',
                  paddingHorizontal: 32,
                  paddingVertical: 16,
                  borderRadius: 16,
                  shadowColor: '#EF4444',
                  shadowOffset: { width: 0, height: 8 },
                  shadowOpacity: 0.3,
                  shadowRadius: 16,
                  elevation: 8,
                }}
              >
                <Text style={{
                  color: 'white',
                  fontWeight: '600',
                  fontSize: 16,
                  marginRight: 8,
                }}>
                  {currentIndex === onboardingSlides.length - 1 ? 'Get Started' : 'Next'}
                </Text>
                <Ionicons
                  name={currentIndex === onboardingSlides.length - 1 ? 'rocket' : 'chevron-forward'}
                  size={20}
                  color="white"
                />
              </TouchableOpacity>
            </Animated.View>
          </View>
        </View>
      </SafeAreaView>
    </LinearGradient>
  );
};

export default OnboardingScreen;
