import {
  RiderProfile,
  Vehicle,
  PaymentMethod,
  UserSettings,
  Document,
  DocumentStatus,
  VehicleType,
  PaymentMethodType,
  Language,
  PrivacySetting,
} from '../types/profile';

// Mock profile data
export const mockProfile: RiderProfile = {
  id: '1',
  firstName: '<PERSON>',
  lastName: '<PERSON>',
  email: '<EMAIL>',
  phone: '+92 300 1234567',
  profilePicture: null,
  cnic: '12345-6789012-3',
  dateOfBirth: '1995-06-15',
  address: {
    street: 'Street 123, Block A',
    city: 'Lahore',
    state: 'Punjab',
    postalCode: '54000',
    country: 'Pakistan',
  },
  emergencyContact: {
    name: '<PERSON>',
    phone: '+92 301 2345678',
    relationship: 'Brother',
  },
  documents: [
    {
      id: '1',
      type: 'cnic',
      name: '<PERSON><PERSON><PERSON>',
      status: DocumentStatus.VERIFIED,
      uploadedAt: '2023-01-16',
      verifiedAt: '2023-01-17',
    },
    {
      id: '2',
      type: 'driving_license',
      name: 'Driving License',
      status: DocumentStatus.VERIFIED,
      uploadedAt: '2023-01-16',
      verifiedAt: '2023-01-18',
    },
    {
      id: '3',
      type: 'profile_photo',
      name: 'Profile Photo',
      status: DocumentStatus.VERIFIED,
      uploadedAt: '2023-01-16',
      verifiedAt: '2023-01-17',
    },
  ],
  preferredZones: ['DHA', 'Gulberg', 'Johar Town'],
  rating: 4.8,
  totalDeliveries: 1250,
  isActive: true,
  isVerified: true,
  joinedAt: '2023-01-15',
  lastActiveAt: new Date().toISOString(),
};

// Mock vehicles data
export const mockVehicles: Vehicle[] = [
  {
    id: '1',
    type: VehicleType.MOTORCYCLE,
    make: 'Honda',
    model: 'CD 70',
    year: 2022,
    color: 'Red',
    plateNumber: 'LES-1234',
    engineNumber: 'CD70-123456',
    chassisNumber: 'CH-789012',
    registrationDate: '2022-03-15',
    insuranceExpiry: '2024-03-15',
    fitnessExpiry: '2024-03-15',
    documents: [
      {
        id: '4',
        type: 'vehicle_registration',
        name: 'Registration Certificate',
        status: DocumentStatus.VERIFIED,
        uploadedAt: '2023-01-16',
        verifiedAt: '2023-01-18',
      },
      {
        id: '5',
        type: 'insurance',
        name: 'Insurance Certificate',
        status: DocumentStatus.VERIFIED,
        uploadedAt: '2023-01-16',
        verifiedAt: '2023-01-18',
      },
      {
        id: '6',
        type: 'fitness_certificate',
        name: 'Fitness Certificate',
        status: DocumentStatus.PENDING,
        uploadedAt: '2023-02-01',
      },
    ],
    isActive: true,
    createdAt: '2023-01-16',
    updatedAt: '2023-01-16',
  },
  {
    id: '2',
    type: VehicleType.MOTORCYCLE,
    make: 'Yamaha',
    model: 'YBR 125',
    year: 2021,
    color: 'Blue',
    plateNumber: 'LES-5678',
    engineNumber: 'YBR-789012',
    chassisNumber: 'CH-345678',
    registrationDate: '2021-08-20',
    insuranceExpiry: '2024-08-20',
    fitnessExpiry: '2024-08-20',
    documents: [
      {
        id: '7',
        type: 'vehicle_registration',
        name: 'Registration Certificate',
        status: DocumentStatus.VERIFIED,
        uploadedAt: '2023-01-20',
        verifiedAt: '2023-01-22',
      },
      {
        id: '8',
        type: 'insurance',
        name: 'Insurance Certificate',
        status: DocumentStatus.EXPIRED,
        uploadedAt: '2023-01-20',
        verifiedAt: '2023-01-22',
        expiryDate: '2023-12-31',
      },
    ],
    isActive: false,
    createdAt: '2023-01-20',
    updatedAt: '2023-01-20',
  },
];

// Mock payment methods data
export const mockPaymentMethods: PaymentMethod[] = [
  {
    id: '1',
    type: PaymentMethodType.BANK_ACCOUNT,
    name: 'HBL Account',
    isDefault: true,
    isActive: true,
    isVerified: true,
    createdAt: '2023-01-16',
    updatedAt: '2023-01-16',
    details: {
      bankName: 'Habib Bank Limited',
      accountTitle: 'Muhammad Ahmed',
      accountNumber: '***********',
      iban: 'PK36HABB00***********234',
    },
  },
  {
    id: '2',
    type: PaymentMethodType.JAZZCASH,
    name: 'JazzCash Wallet',
    isDefault: false,
    isActive: true,
    isVerified: true,
    createdAt: '2023-02-01',
    updatedAt: '2023-02-01',
    details: {
      phoneNumber: '+92 300 1234567',
      accountTitle: 'Muhammad Ahmed',
    },
  },
  {
    id: '3',
    type: PaymentMethodType.EASYPAISA,
    name: 'EasyPaisa Wallet',
    isDefault: false,
    isActive: false,
    isVerified: false,
    createdAt: '2023-02-15',
    updatedAt: '2023-02-15',
    details: {
      phoneNumber: '+92 301 2345678',
      accountTitle: 'Muhammad Ahmed',
    },
  },
  {
    id: '4',
    type: PaymentMethodType.SADAPAY,
    name: 'SadaPay Wallet',
    isDefault: false,
    isActive: true,
    isVerified: true,
    createdAt: '2023-03-01',
    updatedAt: '2023-03-01',
    details: {
      phoneNumber: '+92 300 1234567',
      accountTitle: 'Muhammad Ahmed',
    },
  },
];

// Mock settings data
export const mockSettings: UserSettings = {
  language: Language.ENGLISH,
  notifications: {
    orderRequests: true,
    orderUpdates: true,
    earnings: true,
    promotions: false,
    systemUpdates: true,
    marketing: false,
  },
  privacy: {
    shareLocation: PrivacySetting.WHILE_WORKING,
    shareProfile: PrivacySetting.PRIVATE,
    shareEarnings: PrivacySetting.PRIVATE,
    shareRating: PrivacySetting.PUBLIC,
  },
  preferences: {
    autoAcceptOrders: false,
    soundEnabled: true,
    vibrationEnabled: true,
    darkMode: false,
    dataUsage: 'normal',
  },
  appVersion: '1.0.0',
  buildNumber: '100',
};

// Profile service class
class ProfileService {
  private static instance: ProfileService;
  private profile: RiderProfile | null = null;
  private vehicles: Vehicle[] = [];
  private paymentMethods: PaymentMethod[] = [];
  private settings: UserSettings = mockSettings;

  private constructor() {
    // Initialize with mock data
    this.profile = mockProfile;
    this.vehicles = mockVehicles;
    this.paymentMethods = mockPaymentMethods;
  }

  public static getInstance(): ProfileService {
    if (!ProfileService.instance) {
      ProfileService.instance = new ProfileService();
    }
    return ProfileService.instance;
  }

  // Profile methods
  async getProfile(): Promise<RiderProfile | null> {
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 500));
    return this.profile;
  }

  async updateProfile(updates: Partial<RiderProfile>): Promise<RiderProfile> {
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 800));
    
    if (!this.profile) {
      throw new Error('Profile not found');
    }
    
    this.profile = { ...this.profile, ...updates };
    return this.profile;
  }

  async uploadProfilePicture(imageUri: string): Promise<string> {
    // Simulate upload delay
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    // In a real app, this would upload to a cloud service and return the URL
    const uploadedUrl = `https://api.foodway.pk/uploads/profile/${Date.now()}.jpg`;
    
    if (this.profile) {
      this.profile.profilePicture = uploadedUrl;
    }
    
    return uploadedUrl;
  }

  // Vehicle methods
  async getVehicles(): Promise<Vehicle[]> {
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 500));
    return this.vehicles;
  }

  async addVehicle(vehicle: Omit<Vehicle, 'id' | 'createdAt' | 'updatedAt'>): Promise<Vehicle> {
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    const newVehicle: Vehicle = {
      ...vehicle,
      id: Date.now().toString(),
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    };
    
    this.vehicles.push(newVehicle);
    return newVehicle;
  }

  async updateVehicle(id: string, updates: Partial<Vehicle>): Promise<Vehicle> {
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 500));
    
    const vehicleIndex = this.vehicles.findIndex(v => v.id === id);
    if (vehicleIndex === -1) {
      throw new Error('Vehicle not found');
    }
    
    this.vehicles[vehicleIndex] = {
      ...this.vehicles[vehicleIndex],
      ...updates,
      updatedAt: new Date().toISOString(),
    };
    
    return this.vehicles[vehicleIndex];
  }

  async deleteVehicle(id: string): Promise<void> {
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 500));
    
    const vehicleIndex = this.vehicles.findIndex(v => v.id === id);
    if (vehicleIndex === -1) {
      throw new Error('Vehicle not found');
    }
    
    this.vehicles.splice(vehicleIndex, 1);
  }

  async uploadVehicleDocument(vehicleId: string, documentType: string, imageUri: string): Promise<Document> {
    // Simulate upload delay
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    const vehicle = this.vehicles.find(v => v.id === vehicleId);
    if (!vehicle) {
      throw new Error('Vehicle not found');
    }
    
    // In a real app, this would upload to a cloud service
    const uploadedUrl = `https://api.foodway.pk/uploads/documents/${Date.now()}.jpg`;
    
    const documentIndex = vehicle.documents.findIndex(d => d.type === documentType);
    if (documentIndex !== -1) {
      // Update existing document
      vehicle.documents[documentIndex] = {
        ...vehicle.documents[documentIndex],
        status: DocumentStatus.PENDING,
        uploadedAt: new Date().toISOString(),
        url: uploadedUrl,
      };
      return vehicle.documents[documentIndex];
    } else {
      // Add new document
      const newDocument: Document = {
        id: Date.now().toString(),
        type: documentType,
        name: documentType.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase()),
        status: DocumentStatus.PENDING,
        uploadedAt: new Date().toISOString(),
        url: uploadedUrl,
      };
      vehicle.documents.push(newDocument);
      return newDocument;
    }
  }

  // Payment method methods
  async getPaymentMethods(): Promise<PaymentMethod[]> {
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 500));
    return this.paymentMethods;
  }

  async addPaymentMethod(method: Omit<PaymentMethod, 'id' | 'createdAt' | 'updatedAt'>): Promise<PaymentMethod> {
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 1000));

    const newMethod: PaymentMethod = {
      ...method,
      id: Date.now().toString(),
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    };

    this.paymentMethods.push(newMethod);
    return newMethod;
  }

  async updatePaymentMethod(id: string, updates: Partial<PaymentMethod>): Promise<PaymentMethod> {
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 500));

    const methodIndex = this.paymentMethods.findIndex(m => m.id === id);
    if (methodIndex === -1) {
      throw new Error('Payment method not found');
    }

    this.paymentMethods[methodIndex] = {
      ...this.paymentMethods[methodIndex],
      ...updates,
      updatedAt: new Date().toISOString(),
    };

    return this.paymentMethods[methodIndex];
  }

  async deletePaymentMethod(id: string): Promise<void> {
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 500));

    const methodIndex = this.paymentMethods.findIndex(m => m.id === id);
    if (methodIndex === -1) {
      throw new Error('Payment method not found');
    }

    this.paymentMethods.splice(methodIndex, 1);
  }

  // Settings methods
  async getSettings(): Promise<UserSettings> {
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 300));
    return this.settings;
  }

  async updateSettings(updates: Partial<UserSettings>): Promise<UserSettings> {
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 500));

    this.settings = { ...this.settings, ...updates };
    return this.settings;
  }

  // Document methods
  async updateDocumentStatus(documentId: string, status: DocumentStatus): Promise<void> {
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 500));

    // Update document status in profile
    if (this.profile) {
      const profileDocIndex = this.profile.documents.findIndex(d => d.id === documentId);
      if (profileDocIndex !== -1) {
        this.profile.documents[profileDocIndex].status = status;
        if (status === DocumentStatus.VERIFIED) {
          this.profile.documents[profileDocIndex].verifiedAt = new Date().toISOString();
        }
        return;
      }
    }

    // Update document status in vehicles
    for (const vehicle of this.vehicles) {
      const vehicleDocIndex = vehicle.documents.findIndex(d => d.id === documentId);
      if (vehicleDocIndex !== -1) {
        vehicle.documents[vehicleDocIndex].status = status;
        if (status === DocumentStatus.VERIFIED) {
          vehicle.documents[vehicleDocIndex].verifiedAt = new Date().toISOString();
        }
        return;
      }
    }

    throw new Error('Document not found');
  }

  // Utility methods
  async verifyPaymentMethod(id: string): Promise<PaymentMethod> {
    // Simulate verification process
    await new Promise(resolve => setTimeout(resolve, 2000));

    const method = this.paymentMethods.find(m => m.id === id);
    if (!method) {
      throw new Error('Payment method not found');
    }

    method.isVerified = true;
    method.updatedAt = new Date().toISOString();

    return method;
  }

  async setActiveVehicle(id: string): Promise<void> {
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 500));

    // Set all vehicles to inactive
    this.vehicles.forEach(vehicle => {
      vehicle.isActive = false;
    });

    // Set the selected vehicle to active
    const vehicle = this.vehicles.find(v => v.id === id);
    if (!vehicle) {
      throw new Error('Vehicle not found');
    }

    vehicle.isActive = true;
    vehicle.updatedAt = new Date().toISOString();
  }

  async setDefaultPaymentMethod(id: string): Promise<void> {
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 500));

    // Set all payment methods to non-default
    this.paymentMethods.forEach(method => {
      method.isDefault = false;
    });

    // Set the selected payment method to default
    const method = this.paymentMethods.find(m => m.id === id);
    if (!method) {
      throw new Error('Payment method not found');
    }

    method.isDefault = true;
    method.updatedAt = new Date().toISOString();
  }

  // Reset data (for testing purposes)
  resetData(): void {
    this.profile = mockProfile;
    this.vehicles = [...mockVehicles];
    this.paymentMethods = [...mockPaymentMethods];
    this.settings = { ...mockSettings };
  }
}

// Export singleton instance
export const profileService = ProfileService.getInstance();
export default profileService;
