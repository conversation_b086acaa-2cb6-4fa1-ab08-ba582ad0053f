export interface Coordinates {
  latitude: number;
  longitude: number;
}

export interface LocationData {
  coords: Coordinates;
  accuracy: number;
  altitude?: number;
  altitudeAccuracy?: number;
  heading?: number;
  speed?: number;
  timestamp: number;
}

export interface LocationState {
  currentLocation: LocationData | null;
  isTracking: boolean;
  hasPermission: boolean;
  permissionStatus: 'granted' | 'denied' | 'undetermined';
  error: string | null;
  isLoading: boolean;
}

export interface LocationContextType {
  state: LocationState;
  startTracking: () => Promise<void>;
  stopTracking: () => void;
  getCurrentLocation: () => Promise<LocationData>;
  requestPermission: () => Promise<boolean>;
  requestPermissions: () => Promise<boolean>;
  startLocationTracking: () => Promise<void>;
  stopLocationTracking: () => void;
  clearError: () => void;
}

export interface NavigationRoute {
  distance: number; // in meters
  duration: number; // in seconds
  steps: NavigationStep[];
  overview: Coordinates[];
}

export interface NavigationStep {
  instruction: string;
  distance: number;
  duration: number;
  startLocation: Coordinates;
  endLocation: Coordinates;
  maneuver: string;
}

export interface MapRegion {
  latitude: number;
  longitude: number;
  latitudeDelta: number;
  longitudeDelta: number;
}

export interface MapMarker {
  id: string;
  coordinate: Coordinates;
  title: string;
  description?: string;
  type: MarkerType;
}

export enum MarkerType {
  RIDER = 'rider',
  PICKUP = 'pickup',
  DELIVERY = 'delivery',
  RESTAURANT = 'restaurant',
  CUSTOMER = 'customer',
}

export interface GeofenceRegion {
  id: string;
  center: Coordinates;
  radius: number; // in meters
  identifier: string;
}

import * as Location from 'expo-location';

export type LocationPermissionStatus = Location.PermissionStatus;

export interface LocationPermissionInfo {
  granted: boolean;
  canAskAgain: boolean;
  status: LocationPermissionStatus;
}
