import React, { useState, useRef, useEffect } from 'react';
import {
  View,
  Text,
  ScrollView,
  Dimensions,
  TouchableOpacity,
  Animated,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';

import { OnboardingSlide } from '../../types/auth';

const { width, height } = Dimensions.get('window');

interface OnboardingScreenProps {
  onComplete: () => void;
  onSkip: () => void;
}

const onboardingSlides: OnboardingSlide[] = [
  {
    id: '1',
    title: 'Welcome to FoodWay Rider',
    description: 'Join thousands of riders earning money by delivering delicious food to hungry customers across the city.',
    image: '🚴‍♂️',
    icon: 'bicycle',
  },
  {
    id: '2',
    title: 'Flexible Working Hours',
    description: 'Work when you want, where you want. Set your own schedule and earn money on your terms.',
    image: '⏰',
    icon: 'time',
  },
  {
    id: '3',
    title: 'Easy Earnings',
    description: 'Get paid instantly after each delivery. Track your earnings in real-time and cash out anytime.',
    image: '💰',
    icon: 'cash',
  },
  {
    id: '4',
    title: 'Safe & Secure',
    description: 'Your safety is our priority. Built-in safety features and 24/7 support to keep you protected.',
    image: '🛡️',
    icon: 'shield-checkmark',
  },
  {
    id: '5',
    title: 'Ready to Start?',
    description: 'Complete your registration and start earning with FoodWay today. Your journey begins now!',
    image: '🚀',
    icon: 'rocket',
  },
];

const OnboardingScreen: React.FC<OnboardingScreenProps> = ({ onComplete, onSkip }) => {
  const [currentIndex, setCurrentIndex] = useState(0);
  const scrollViewRef = useRef<any>(null);
  const fadeAnim = useRef(new Animated.Value(1)).current;
  const slideAnim = useRef(new Animated.Value(0)).current;
  const scaleAnim = useRef(new Animated.Value(0.8)).current;
  const buttonScaleAnim = useRef(new Animated.Value(1)).current;

  useEffect(() => {
    // Initial animation
    Animated.parallel([
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 800,
        useNativeDriver: true,
      }),
      Animated.spring(scaleAnim, {
        toValue: 1,
        tension: 50,
        friction: 7,
        useNativeDriver: true,
      }),
    ]).start();
  }, []);

  useEffect(() => {
    // Animate slide transition
    Animated.sequence([
      Animated.timing(slideAnim, {
        toValue: -20,
        duration: 200,
        useNativeDriver: true,
      }),
      Animated.timing(slideAnim, {
        toValue: 0,
        duration: 300,
        useNativeDriver: true,
      }),
    ]).start();
  }, [currentIndex]);

  const handleScroll = (event: any) => {
    const contentOffset = event.nativeEvent.contentOffset.x;
    const index = Math.round(contentOffset / width);
    setCurrentIndex(index);
  };

  const goToSlide = (index: number) => {
    scrollViewRef.current?.scrollTo({ x: index * width, animated: true });
    setCurrentIndex(index);
  };

  const handleNext = () => {
    // Button press animation
    Animated.sequence([
      Animated.timing(buttonScaleAnim, { toValue: 0.95, duration: 100, useNativeDriver: true }),
      Animated.timing(buttonScaleAnim, { toValue: 1, duration: 100, useNativeDriver: true }),
    ]).start();

    if (currentIndex < onboardingSlides.length - 1) {
      goToSlide(currentIndex + 1);
    } else {
      onComplete();
    }
  };

  const handlePrevious = () => {
    if (currentIndex > 0) {
      goToSlide(currentIndex - 1);
    }
  };

  const renderSlide = (slide: OnboardingSlide, index: number) => (
    <View key={slide.id} style={{ width, minHeight: height * 0.7 }} className="flex-1 px-8 justify-center">
      <Animated.View
        style={{
          opacity: fadeAnim,
          transform: [
            { translateY: slideAnim },
            { scale: scaleAnim },
          ],
        }}
        className="items-center"
      >
        {/* Enhanced Icon Container with Red Theme */}
        <View style={{ alignItems: 'center', marginBottom: 48 }}>
          <View style={{
            width: 180,
            height: 180,
            borderRadius: 90,
            backgroundColor: '#dc2626',
            justifyContent: 'center',
            alignItems: 'center',
            marginBottom: 24,
            shadowColor: '#dc2626',
            shadowOffset: { width: 0, height: 12 },
            shadowOpacity: 0.4,
            shadowRadius: 24,
            elevation: 16,
          }}>
            <View style={{
              width: 140,
              height: 140,
              backgroundColor: 'white',
              borderRadius: 70,
              justifyContent: 'center',
              alignItems: 'center',
              shadowColor: '#000',
              shadowOffset: { width: 0, height: 6 },
              shadowOpacity: 0.15,
              shadowRadius: 12,
              elevation: 8,
            }}>
              <Text style={{ fontSize: 64 }}>{slide.image}</Text>
            </View>
          </View>

          {/* Enhanced Floating Icon Badge */}
          <View style={{
            position: 'absolute',
            bottom: 15,
            right: width * 0.22,
            width: 56,
            height: 56,
            backgroundColor: '#dc2626',
            borderRadius: 28,
            justifyContent: 'center',
            alignItems: 'center',
            borderWidth: 4,
            borderColor: 'white',
            shadowColor: '#dc2626',
            shadowOffset: { width: 0, height: 6 },
            shadowOpacity: 0.5,
            shadowRadius: 12,
            elevation: 12,
          }}>
            <Ionicons name={slide.icon as any} size={28} color="white" />
          </View>
        </View>

        {/* Enhanced Content */}
        <View style={{ alignItems: 'center', paddingHorizontal: 20 }}>
          <Text style={{
            fontSize: 32,
            fontWeight: 'bold',
            color: '#111827',
            textAlign: 'center',
            marginBottom: 20,
            lineHeight: 40,
          }}>
            {slide.title}
          </Text>
          <Text style={{
            fontSize: 18,
            color: '#6b7280',
            textAlign: 'center',
            lineHeight: 28,
            paddingHorizontal: 16,
            maxWidth: 320,
          }}>
            {slide.description}
          </Text>
        </View>
      </Animated.View>
    </View>
  );

  return (
    <View style={{ flex: 1, backgroundColor: '#f1f5f9' }}>
      <SafeAreaView style={{ flex: 1 }}>
        {/* Enhanced Skip Button */}
        <View style={{
          flexDirection: 'row',
          justifyContent: 'space-between',
          alignItems: 'center',
          paddingHorizontal: 24,
          paddingVertical: 20,
        }}>
          <View style={{ width: 80 }} />
          <TouchableOpacity
            onPress={onSkip}
            style={{
              paddingHorizontal: 20,
              paddingVertical: 12,
              borderRadius: 24,
              backgroundColor: 'white',
              borderWidth: 2,
              borderColor: '#dc2626',
              shadowColor: '#dc2626',
              shadowOffset: { width: 0, height: 4 },
              shadowOpacity: 0.1,
              shadowRadius: 8,
              elevation: 4,
            }}
          >
            <Text style={{
              color: '#dc2626',
              fontWeight: 'bold',
              fontSize: 16,
            }}>
              Skip
            </Text>
          </TouchableOpacity>
        </View>

        {/* Slides */}
        <ScrollView
          ref={scrollViewRef}
          horizontal
          pagingEnabled
          showsHorizontalScrollIndicator={false}
          onScroll={handleScroll}
          scrollEventThrottle={16}
          style={{ flex: 1 }}
        >
          {onboardingSlides.map(renderSlide)}
        </ScrollView>

        {/* Enhanced Bottom Section */}
        <View style={{
          paddingHorizontal: 32,
          paddingBottom: 32,
          paddingTop: 16,
        }}>
          {/* Enhanced Pagination Dots */}
          <View style={{
            flexDirection: 'row',
            justifyContent: 'center',
            alignItems: 'center',
            marginBottom: 40,
          }}>
            {onboardingSlides.map((_, index) => (
              <TouchableOpacity
                key={index}
                onPress={() => goToSlide(index)}
                style={{
                  width: index === currentIndex ? 32 : 12,
                  height: 12,
                  borderRadius: 6,
                  marginHorizontal: 6,
                  backgroundColor: index === currentIndex ? '#dc2626' : '#e2e8f0',
                  shadowColor: index === currentIndex ? '#dc2626' : 'transparent',
                  shadowOffset: { width: 0, height: 2 },
                  shadowOpacity: index === currentIndex ? 0.3 : 0,
                  shadowRadius: 4,
                  elevation: index === currentIndex ? 4 : 0,
                }}
              />
            ))}
          </View>

          {/* Enhanced Navigation Buttons */}
          <View style={{
            flexDirection: 'row',
            justifyContent: 'space-between',
            alignItems: 'center',
          }}>
            <TouchableOpacity
              onPress={handlePrevious}
              disabled={currentIndex === 0}
              style={{
                flexDirection: 'row',
                alignItems: 'center',
                paddingHorizontal: 24,
                paddingVertical: 16,
                borderRadius: 20,
                backgroundColor: currentIndex === 0 ? 'transparent' : 'white',
                borderWidth: currentIndex === 0 ? 0 : 2,
                borderColor: currentIndex === 0 ? 'transparent' : '#e2e8f0',
                opacity: currentIndex === 0 ? 0.3 : 1,
                shadowColor: currentIndex === 0 ? 'transparent' : '#000',
                shadowOffset: { width: 0, height: 4 },
                shadowOpacity: currentIndex === 0 ? 0 : 0.1,
                shadowRadius: 8,
                elevation: currentIndex === 0 ? 0 : 4,
              }}
            >
              <Ionicons name="chevron-back" size={24} color="#6b7280" />
              <Text style={{
                color: '#6b7280',
                marginLeft: 8,
                fontWeight: '600',
                fontSize: 16,
              }}>
                Previous
              </Text>
            </TouchableOpacity>

            <Animated.View style={{ transform: [{ scale: buttonScaleAnim }] }}>
              <TouchableOpacity
                onPress={handleNext}
                style={{
                  backgroundColor: '#dc2626',
                  flexDirection: 'row',
                  alignItems: 'center',
                  paddingHorizontal: 36,
                  paddingVertical: 20,
                  borderRadius: 24,
                  shadowColor: '#dc2626',
                  shadowOffset: { width: 0, height: 12 },
                  shadowOpacity: 0.4,
                  shadowRadius: 20,
                  elevation: 12,
                }}
              >
                <Text style={{
                  color: 'white',
                  fontWeight: 'bold',
                  fontSize: 18,
                  marginRight: 12,
                }}>
                  {currentIndex === onboardingSlides.length - 1 ? 'Get Started' : 'Next'}
                </Text>
                <Ionicons
                  name={currentIndex === onboardingSlides.length - 1 ? 'rocket' : 'chevron-forward'}
                  size={24}
                  color="white"
                />
              </TouchableOpacity>
            </Animated.View>
          </View>
        </View>
      </SafeAreaView>
    </View>
  );
};

export default OnboardingScreen;
