import {
  EarningsData,
  EarningsSummary,
  DailyEarnings,
  WeeklyEarnings,
  MonthlyEarnings,
  PerformanceMetrics,
  PayoutInfo,
  EarningsFilter,
  WalletBalance,
  WithdrawalMethod,
  WithdrawalRequest,
  PayoutSchedule,
  PayoutSettings,
  Deduction,
  Bonus,
  EarningsStatus,
  PaymentMethod,
  WithdrawalStatus,
} from '../../types/earnings';

class MockEarningsService {
  // Simulate network delay
  private delay(ms: number = 1000): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  // Get earnings summary for different periods
  async getEarningsSummary(): Promise<EarningsSummary> {
    await this.delay(800);
    
    return {
      today: {
        total: 3750,
        deliveryFee: 2550,
        tips: 960,
        bonuses: 240,
        orders: 12,
        hours: 8.5,
        averagePerOrder: 313,
      },
      week: {
        total: 14550,
        deliveryFee: 9600,
        tips: 3750,
        bonuses: 1200,
        orders: 48,
        hours: 35.5,
        averagePerOrder: 303,
      },
      month: {
        total: 56250,
        deliveryFee: 37500,
        tips: 14250,
        bonuses: 4500,
        orders: 185,
        hours: 142.5,
        averagePerOrder: 304,
      },
      allTime: {
        total: 377250,
        deliveryFee: 255000,
        tips: 86250,
        bonuses: 36000,
        orders: 1250,
        hours: 980.5,
        averagePerOrder: 302,
      },
    };
  }

  // Get recent earnings with optional limit
  async getRecentEarnings(limit: number = 20): Promise<EarningsData[]> {
    await this.delay(600);
    
    const mockEarnings: EarningsData[] = [
      {
        id: 'earn-1',
        orderNumber: 'FW-2024-001',
        riderId: 'rider-1',
        restaurant: {
          id: 'rest-1',
          name: 'Pizza Palace',
          address: 'Main Street, Karachi',
        },
        customer: {
          id: 'cust-1',
          name: 'Ahmed Ali',
          address: 'DHA Phase 2, Karachi',
        },
        deliveryFee: 120,
        tips: 50,
        bonuses: 20,
        total: 190,
        distance: 3.2,
        duration: 25,
        status: EarningsStatus.PAID,
        paymentMethod: PaymentMethod.CASH,
        completedAt: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),
        paidAt: new Date(Date.now() - 1 * 60 * 60 * 1000).toISOString(),
      },
      {
        id: 'earn-2',
        orderNumber: 'FW-2024-002',
        riderId: 'rider-1',
        restaurant: {
          id: 'rest-2',
          name: 'Burger House',
          address: 'Clifton, Karachi',
        },
        customer: {
          id: 'cust-2',
          name: 'Sara Khan',
          address: 'Gulshan-e-Iqbal, Karachi',
        },
        deliveryFee: 100,
        tips: 30,
        bonuses: 0,
        total: 130,
        distance: 2.8,
        duration: 20,
        status: EarningsStatus.PENDING,
        paymentMethod: PaymentMethod.CARD,
        completedAt: new Date(Date.now() - 4 * 60 * 60 * 1000).toISOString(),
        paidAt: null,
      },
    ];

    return mockEarnings.slice(0, limit);
  }

  // Get wallet balance
  async getWalletBalance(): Promise<WalletBalance> {
    await this.delay(500);
    
    return {
      availableBalance: 7350,
      pendingBalance: 2550,
      totalBalance: 9900,
      lastUpdated: new Date().toISOString(),
    };
  }

  // Get daily earnings breakdown
  async getDailyEarnings(startDate: string, endDate: string): Promise<DailyEarnings[]> {
    await this.delay(700);
    
    const mockDaily: DailyEarnings[] = [];
    const start = new Date(startDate);
    const end = new Date(endDate);
    
    for (let d = new Date(start); d <= end; d.setDate(d.getDate() + 1)) {
      mockDaily.push({
        date: d.toISOString().split('T')[0],
        total: Math.random() * 1500 + 600,
        deliveryFee: Math.random() * 900 + 450,
        tips: Math.random() * 450 + 150,
        bonuses: Math.random() * 300,
        orders: Math.floor(Math.random() * 15) + 5,
        hours: Math.random() * 8 + 4,
      });
    }
    
    return mockDaily;
  }

  // Get performance metrics
  async getPerformanceMetrics(): Promise<PerformanceMetrics> {
    await this.delay(600);
    
    return {
      totalEarnings: 377250,
      totalOrders: 1250,
      totalHours: 980.5,
      averageRating: 4.8,
      completionRate: 98.5,
      onTimeRate: 95.2,
      customerSatisfaction: 4.7,
      averageDeliveryTime: 22.5,
      totalDistance: 2850.5,
      fuelEfficiency: 15.2,
    };
  }

  // Get payouts
  async getPayouts(): Promise<PayoutInfo[]> {
    await this.delay(500);
    
    return [
      {
        id: 'payout-1',
        amount: 15000,
        method: PaymentMethod.BANK_TRANSFER,
        status: WithdrawalStatus.COMPLETED,
        requestedAt: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString(),
        processedAt: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000).toISOString(),
        transactionId: 'TXN-001',
      },
      {
        id: 'payout-2',
        amount: 9000,
        method: PaymentMethod.JAZZCASH,
        status: WithdrawalStatus.PENDING,
        requestedAt: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000).toISOString(),
        processedAt: null,
        transactionId: null,
      },
    ];
  }

  // Get deductions
  async getDeductions(): Promise<Deduction[]> {
    await this.delay(400);
    
    return [
      {
        id: 'ded-1',
        type: 'FUEL_ALLOWANCE' as any,
        amount: 150,
        description: 'Fuel allowance deduction',
        date: new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString(),
        orderId: 'FW-2024-001',
      },
    ];
  }

  // Get bonuses
  async getBonuses(): Promise<Bonus[]> {
    await this.delay(400);
    
    return [
      {
        id: 'bonus-1',
        type: 'PEAK_HOUR' as any,
        amount: 300,
        description: 'Peak hour bonus',
        date: new Date(Date.now() - 12 * 60 * 60 * 1000).toISOString(),
        orderId: 'FW-2024-001',
      },
    ];
  }

  // Other methods with basic implementations
  async getEarnings(filter: EarningsFilter): Promise<EarningsData[]> {
    return this.getRecentEarnings(20);
  }

  async getWeeklyEarnings(startDate: string, endDate: string): Promise<WeeklyEarnings[]> {
    await this.delay(600);
    return [];
  }

  async getMonthlyEarnings(year: number): Promise<MonthlyEarnings[]> {
    await this.delay(600);
    return [];
  }

  async getWithdrawalMethods(): Promise<WithdrawalMethod[]> {
    await this.delay(300);
    return [];
  }

  async requestWithdrawal(request: WithdrawalRequest): Promise<PayoutInfo> {
    await this.delay(1000);
    throw new Error('Mock implementation');
  }

  async getWithdrawalHistory(): Promise<PayoutInfo[]> {
    return this.getPayouts();
  }

  async getPayoutSchedule(): Promise<PayoutSchedule[]> {
    await this.delay(400);
    return [];
  }

  async getPayoutSettings(): Promise<PayoutSettings> {
    await this.delay(300);
    throw new Error('Mock implementation');
  }

  async updatePayoutSettings(settings: Partial<PayoutSettings>): Promise<PayoutSettings> {
    await this.delay(500);
    throw new Error('Mock implementation');
  }
}

export const mockEarningsService = new MockEarningsService();
