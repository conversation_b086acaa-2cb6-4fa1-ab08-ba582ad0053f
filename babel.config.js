module.exports = function (api) {
  api.cache(true);

  const isProduction = process.env.NODE_ENV === 'production';

  const plugins = [];

  // Production optimizations only
  if (isProduction) {
    plugins.push(
      // Remove console.log statements in production
      'babel-plugin-transform-remove-console',
      // Remove PropTypes in production
      'babel-plugin-transform-react-remove-prop-types'
    );
  }

  return {
    presets: ['babel-preset-expo'],
    plugins,
  };
};
