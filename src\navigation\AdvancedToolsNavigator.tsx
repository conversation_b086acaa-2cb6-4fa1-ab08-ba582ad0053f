import React from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  ScrollView,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { createStackNavigator } from '@react-navigation/stack';
import { useNavigation } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import { Ionicons } from '@expo/vector-icons';

// Advanced Tools Screens
import HeatmapScreen from '../screens/advancedTools/HeatmapScreen';
import PerformanceScreen from '../screens/advancedTools/PerformanceScreen';
import TrainingScreen from '../screens/advancedTools/TrainingScreen';
import TrainingMainScreen from '../screens/training/TrainingMainScreen';
import NotificationsScreen from '../screens/advancedTools/NotificationsScreen';
import LocationTrackingScreen from '../screens/advancedTools/LocationTrackingScreen';
import LocationHistoryScreen from '../screens/advancedTools/LocationHistoryScreen';

// Types
import { AdvancedToolsStackParamList } from '../types';

const Stack = createStackNavigator<AdvancedToolsStackParamList>();

const AdvancedToolsNavigator: React.FC = () => {
  return (
    <Stack.Navigator
      screenOptions={{
        headerStyle: {
          backgroundColor: '#ef4444',
          elevation: 0,
          shadowOpacity: 0,
        },
        headerTintColor: 'white',
        headerTitleStyle: {
          fontWeight: '600',
          fontSize: 18,
        },
        headerBackVisible: true,
        cardStyle: {
          backgroundColor: '#f9fafb',
        },
      }}
    >
      {/* Main Advanced Tools Hub */}
      <Stack.Screen
        name="AdvancedToolsHub"
        component={AdvancedToolsHubScreen}
        options={{
          title: 'Advanced Tools',
          headerLeft: () => null,
        }}
      />

      {/* Heatmap & Demand Map */}
      <Stack.Screen
        name="Heatmap"
        component={HeatmapScreen}
        options={{
          title: 'Heatmap & Demand',
          headerBackImage: () => (
            <Ionicons name="arrow-back" size={24} color="white" style={{ marginLeft: 8 }} />
          ),
        }}
      />

      {/* Performance & Gamification */}
      <Stack.Screen
        name="Performance"
        component={PerformanceScreen}
        options={{
          title: 'Performance & Badges',
          headerBackImage: () => (
            <Ionicons name="arrow-back" size={24} color="white" style={{ marginLeft: 8 }} />
          ),
        }}
      />

      {/* Training Videos */}
      <Stack.Screen
        name="Training"
        component={TrainingMainScreen}
        options={{
          title: 'Training Center',
          headerShown: false,
        }}
      />

      {/* Legacy Training Screen */}
      <Stack.Screen
        name="TrainingLegacy"
        component={TrainingScreen}
        options={{
          title: 'Training Videos (Legacy)',
          headerBackImage: () => (
            <Ionicons name="arrow-back" size={24} color="white" style={{ marginLeft: 8 }} />
          ),
        }}
      />

      {/* Push Notifications */}
      <Stack.Screen
        name="Notifications"
        component={NotificationsScreen}
        options={{
          title: 'Push Notifications',
          headerBackImage: () => (
            <Ionicons name="arrow-back" size={24} color="white" style={{ marginLeft: 8 }} />
          ),
        }}
      />

      {/* Location Tracking */}
      <Stack.Screen
        name="LocationTracking"
        component={LocationTrackingScreen}
        options={{
          title: 'Location Tracking',
          headerBackImage: () => (
            <Ionicons name="arrow-back" size={24} color="white" style={{ marginLeft: 8 }} />
          ),
        }}
      />

      {/* Location History */}
      <Stack.Screen
        name="LocationHistory"
        component={LocationHistoryScreen}
        options={{
          title: 'Location History',
          headerShown: false,
        }}
      />
    </Stack.Navigator>
  );
};

// Advanced Tools Hub Screen Component
const AdvancedToolsHubScreen: React.FC = () => {
  const navigation = useNavigation<StackNavigationProp<AdvancedToolsStackParamList>>();

  const toolsData = [
    {
      id: 'heatmap',
      title: 'Heatmap & Demand',
      description: 'View busy areas with surge opportunities',
      icon: 'map' as const,
      color: '#ef4444',
      screen: 'Heatmap' as const,
      features: ['Live demand map', 'Surge areas', 'Earnings forecast'],
    },
    {
      id: 'performance',
      title: 'Performance & Badges',
      description: 'Track achievements and compete with others',
      icon: 'trophy' as const,
      color: '#fbbf24',
      screen: 'Performance' as const,
      features: ['Badge collection', 'Leaderboards', 'Performance stats'],
    },
    {
      id: 'training',
      title: 'Training Videos',
      description: 'Learn skills and earn certificates',
      icon: 'school' as const,
      color: '#3b82f6',
      screen: 'Training' as const,
      features: ['Video courses', 'Certificates', 'XP rewards'],
    },
    {
      id: 'notifications',
      title: 'Push Notifications',
      description: 'Manage notification preferences',
      icon: 'notifications' as const,
      color: '#8b5cf6',
      screen: 'Notifications' as const,
      features: ['Custom settings', 'Priority levels', 'Quiet hours'],
    },
    {
      id: 'location',
      title: 'Location Tracking',
      description: 'Background location and safety features',
      icon: 'location' as const,
      color: '#10b981',
      screen: 'LocationTracking' as const,
      features: ['Real-time tracking', 'Privacy controls', 'Safety features'],
    },
  ];

  const renderToolCard = (tool: typeof toolsData[0]) => (
    <TouchableOpacity
      key={tool.id}
      onPress={() => navigation.navigate(tool.screen)}
      style={{
        backgroundColor: 'white',
        borderRadius: 16,
        marginHorizontal: 16,
        marginBottom: 16,
        padding: 20,
        elevation: 3,
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.1,
        shadowRadius: 8,
        borderLeftWidth: 4,
        borderLeftColor: tool.color,
      }}
    >
      <View style={{
        flexDirection: 'row',
        alignItems: 'center',
        marginBottom: 12,
      }}>
        <View style={{
          width: 48,
          height: 48,
          borderRadius: 24,
          backgroundColor: tool.color + '20',
          alignItems: 'center',
          justifyContent: 'center',
          marginRight: 16,
        }}>
          <Ionicons name={tool.icon} size={24} color={tool.color} />
        </View>
        
        <View style={{ flex: 1 }}>
          <Text style={{
            fontSize: 18,
            fontWeight: '600',
            color: '#1f2937',
            marginBottom: 4,
          }}>
            {tool.title}
          </Text>
          <Text style={{
            fontSize: 14,
            color: '#6b7280',
          }}>
            {tool.description}
          </Text>
        </View>
        
        <Ionicons name="chevron-forward" size={20} color="#9ca3af" />
      </View>
      
      <View style={{
        flexDirection: 'row',
        flexWrap: 'wrap',
        gap: 8,
      }}>
        {tool.features.map((feature, index) => (
          <View
            key={index}
            style={{
              backgroundColor: tool.color + '10',
              paddingHorizontal: 8,
              paddingVertical: 4,
              borderRadius: 12,
            }}
          >
            <Text style={{
              fontSize: 12,
              color: tool.color,
              fontWeight: '500',
            }}>
              {feature}
            </Text>
          </View>
        ))}
      </View>
    </TouchableOpacity>
  );

  return (
    <SafeAreaView style={{ flex: 1, backgroundColor: '#f9fafb' }}>
      {/* Header */}
      <View style={{
        backgroundColor: 'white',
        paddingHorizontal: 16,
        paddingVertical: 20,
        borderBottomWidth: 1,
        borderBottomColor: '#e5e7eb',
      }}>
        <Text style={{
          fontSize: 24,
          fontWeight: 'bold',
          color: '#1f2937',
          marginBottom: 8,
        }}>
          Advanced Tools
        </Text>
        <Text style={{
          fontSize: 16,
          color: '#6b7280',
        }}>
          Powerful features to enhance your delivery experience
        </Text>
      </View>

      {/* Tools Grid */}
      <ScrollView
        style={{ flex: 1 }}
        contentContainerStyle={{ paddingVertical: 20 }}
        showsVerticalScrollIndicator={false}
      >
        {toolsData.map(renderToolCard)}
        
        {/* Coming Soon Section */}
        <View style={{
          backgroundColor: 'white',
          borderRadius: 16,
          marginHorizontal: 16,
          marginBottom: 16,
          padding: 20,
          borderWidth: 2,
          borderColor: '#e5e7eb',
          borderStyle: 'dashed',
        }}>
          <View style={{
            alignItems: 'center',
            paddingVertical: 16,
          }}>
            <Ionicons name="construct-outline" size={48} color="#9ca3af" />
            <Text style={{
              fontSize: 18,
              fontWeight: '600',
              color: '#6b7280',
              marginTop: 12,
              marginBottom: 8,
            }}>
              More Tools Coming Soon
            </Text>
            <Text style={{
              fontSize: 14,
              color: '#9ca3af',
              textAlign: 'center',
            }}>
              We're constantly working on new features to make your delivery experience even better.
            </Text>
          </View>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

export default AdvancedToolsNavigator;
