import React, { Suspense, useEffect } from 'react';
import { StatusBar } from 'expo-status-bar';
import { View, ActivityIndicator, Text, StyleSheet } from 'react-native';
import { AuthProvider } from './src/context/AuthContext';
import { OrderProvider } from './src/context/OrderContext';
import { LocationProvider } from './src/context/LocationContext';
import { EarningsProvider } from './src/context/EarningsContext';
import { DeliveryHistoryProvider } from './src/context/DeliveryHistoryContext';
import { ProfileProvider } from './src/context/ProfileContext';
import { SupportProvider } from './src/context/SupportContext';
import { SafetyProvider } from './src/context/SafetyContext';
import { AdvancedToolsProvider } from './src/contexts/AdvancedToolsContext';
import RootNavigator from './src/navigation/RootNavigator';
import { optimizationInitializer } from './src/services/optimizationInitializer';
import './global.css';

// Optimized context provider combiner to reduce nesting
const CombinedProviders: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  return (
    <AuthProvider>
      <LocationProvider>
        <EarningsProvider>
          <DeliveryHistoryProvider>
            <ProfileProvider>
              <SupportProvider>
                <SafetyProvider>
                  <AdvancedToolsProvider>
                    <OrderProvider>
                      {children}
                    </OrderProvider>
                  </AdvancedToolsProvider>
                </SafetyProvider>
              </SupportProvider>
            </ProfileProvider>
          </DeliveryHistoryProvider>
        </EarningsProvider>
      </LocationProvider>
    </AuthProvider>
  );
};

// Loading fallback component
const AppLoadingFallback: React.FC = () => (
  <View style={styles.loadingContainer}>
    <ActivityIndicator size="large" color="#ef4444" />
    <Text style={styles.loadingText}>Loading FoodWay Rider...</Text>
  </View>
);

export default function App() {
  useEffect(() => {
    // Initialize all optimizations
    const initializeOptimizations = async () => {
      try {
        await optimizationInitializer.initialize();

        if (__DEV__) {
          const status = optimizationInitializer.getOptimizationStatus();
          console.log('[Performance] App started with optimizations:', status);
        }
      } catch (error) {
        console.error('[Performance] Optimization initialization failed:', error);
      }
    };

    initializeOptimizations();
  }, []);

  return (
    <Suspense fallback={<AppLoadingFallback />}>
      <CombinedProviders>
        <RootNavigator />
        <StatusBar style="auto" />
      </CombinedProviders>
    </Suspense>
  );
}

const styles = StyleSheet.create({
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#ffffff',
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    color: '#666666',
    fontWeight: '500',
  },
});
