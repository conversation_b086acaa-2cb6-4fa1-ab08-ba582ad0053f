import { InteractionManager, Platform } from 'react-native';
import { useCallback, useEffect, useRef, useState } from 'react';

/**
 * Performance optimization utilities for React Native
 */

// Debounce function for performance optimization
export const debounce = <T extends (...args: any[]) => any>(
  func: T,
  wait: number
): ((...args: Parameters<T>) => void) => {
  let timeout: NodeJS.Timeout;
  return (...args: Parameters<T>) => {
    clearTimeout(timeout);
    timeout = setTimeout(() => func(...args), wait);
  };
};

// Throttle function for performance optimization
export const throttle = <T extends (...args: any[]) => any>(
  func: T,
  limit: number
): ((...args: Parameters<T>) => void) => {
  let inThrottle: boolean;
  return (...args: Parameters<T>) => {
    if (!inThrottle) {
      func(...args);
      inThrottle = true;
      setTimeout(() => (inThrottle = false), limit);
    }
  };
};

// Hook for lazy initialization
export const useLazyInit = <T>(initializer: () => T): T => {
  const [value] = useState(initializer);
  return value;
};

// Hook for interaction manager
export const useInteractionManager = () => {
  const [interactionComplete, setInteractionComplete] = useState(false);

  useEffect(() => {
    const handle = InteractionManager.runAfterInteractions(() => {
      setInteractionComplete(true);
    });

    return () => handle.cancel();
  }, []);

  return interactionComplete;
};

// Memory-efficient image loading
export const optimizeImageUri = (uri: string, width?: number, height?: number): string => {
  if (!uri) return '';
  
  // For local images, return as-is
  if (uri.startsWith('file://') || !uri.startsWith('http')) {
    return uri;
  }

  // Add image optimization parameters for remote images
  const separator = uri.includes('?') ? '&' : '?';
  let optimizedUri = uri;

  if (width) {
    optimizedUri += `${separator}w=${Math.round(width)}`;
  }
  if (height) {
    optimizedUri += `${width ? '&' : separator}h=${Math.round(height)}`;
  }

  // Add format optimization
  optimizedUri += `${width || height ? '&' : separator}format=webp&quality=80`;

  return optimizedUri;
};

// Efficient list rendering hook
export const useOptimizedList = <T>(
  data: T[],
  itemHeight: number,
  containerHeight: number
) => {
  const [visibleRange, setVisibleRange] = useState({ start: 0, end: 10 });
  
  const updateVisibleRange = useCallback(
    throttle((scrollOffset: number) => {
      const start = Math.max(0, Math.floor(scrollOffset / itemHeight) - 2);
      const visibleCount = Math.ceil(containerHeight / itemHeight) + 4;
      const end = Math.min(data.length, start + visibleCount);
      
      setVisibleRange({ start, end });
    }, 100),
    [data.length, itemHeight, containerHeight]
  );

  const visibleData = data.slice(visibleRange.start, visibleRange.end);

  return {
    visibleData,
    updateVisibleRange,
    visibleRange,
  };
};

// Memory cleanup hook
export const useMemoryCleanup = (cleanupFn: () => void, deps: any[] = []) => {
  const cleanupRef = useRef(cleanupFn);
  cleanupRef.current = cleanupFn;

  useEffect(() => {
    return () => {
      cleanupRef.current();
    };
  }, deps);
};

// Platform-specific optimization
export const getPlatformOptimizedValue = <T>(ios: T, android: T): T => {
  return Platform.select({ ios, android }) as T;
};

// Efficient state updates
export const useBatchedUpdates = () => {
  const pendingUpdates = useRef<(() => void)[]>([]);
  const timeoutRef = useRef<NodeJS.Timeout>();

  const batchUpdate = useCallback((updateFn: () => void) => {
    pendingUpdates.current.push(updateFn);
    
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }

    timeoutRef.current = setTimeout(() => {
      const updates = pendingUpdates.current;
      pendingUpdates.current = [];
      
      updates.forEach(update => update());
    }, 16); // Next frame
  }, []);

  useEffect(() => {
    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, []);

  return batchUpdate;
};

// Performance monitoring
export const measurePerformance = async <T>(
  name: string,
  fn: () => Promise<T> | T
): Promise<T> => {
  const start = Date.now();
  
  try {
    const result = await fn();
    const duration = Date.now() - start;
    
    if (__DEV__) {
      console.log(`[Performance] ${name}: ${duration}ms`);
    }
    
    return result;
  } catch (error) {
    const duration = Date.now() - start;
    
    if (__DEV__) {
      console.error(`[Performance] ${name} failed after ${duration}ms:`, error);
    }
    
    throw error;
  }
};

// Lazy component loader
export const createLazyComponent = <T extends React.ComponentType<any>>(
  importFn: () => Promise<{ default: T }>
) => {
  return React.lazy(() => 
    importFn().catch(error => {
      console.error('Failed to load component:', error);
      // Return a fallback component
      return { default: () => null as any };
    })
  );
};

// Memory usage monitoring (development only)
export const logMemoryUsage = (label: string) => {
  if (__DEV__ && (global as any).performance?.memory) {
    const memory = (global as any).performance.memory;
    console.log(`[Memory] ${label}:`, {
      used: `${Math.round(memory.usedJSHeapSize / 1024 / 1024)}MB`,
      total: `${Math.round(memory.totalJSHeapSize / 1024 / 1024)}MB`,
      limit: `${Math.round(memory.jsHeapSizeLimit / 1024 / 1024)}MB`,
    });
  }
};
