import React from 'react';
import { View, ViewProps, ViewStyle } from 'react-native';

interface CardProps extends ViewProps {
  variant?: 'default' | 'elevated' | 'outlined' | 'premium' | 'accent';
  padding?: 'none' | 'sm' | 'md' | 'lg' | 'xl' | 'xxl';
  margin?: 'none' | 'sm' | 'md' | 'lg' | 'xl';
  borderRadius?: 'sm' | 'md' | 'lg' | 'xl' | 'xxl';
  shadowColor?: string;
  children: React.ReactNode;
  style?: ViewStyle;
}

const Card: React.FC<CardProps> = ({
  variant = 'default',
  padding = 'md',
  margin = 'none',
  borderRadius = 'lg',
  shadowColor,
  style,
  children,
  ...props
}) => {
  const getCardStyles = (): ViewStyle => {
    // Border radius styles
    const borderRadiusStyles: Record<string, number> = {
      sm: 8,
      md: 12,
      lg: 16,
      xl: 20,
      xxl: 24,
    };

    const baseStyles: ViewStyle = {
      backgroundColor: '#ffffff',
      borderRadius: borderRadiusStyles[borderRadius],
    };

    // Enhanced variant styles with modern design
    const variantStyles: Record<string, ViewStyle> = {
      default: {
        backgroundColor: '#ffffff',
      },
      elevated: {
        backgroundColor: '#ffffff',
        shadowColor: shadowColor || '#000',
        shadowOffset: {
          width: 0,
          height: 6,
        },
        shadowOpacity: 0.1,
        shadowRadius: 16,
        elevation: 8,
        borderWidth: 1,
        borderColor: 'rgba(0, 0, 0, 0.05)',
      },
      outlined: {
        backgroundColor: '#ffffff',
        borderWidth: 2,
        borderColor: '#e2e8f0',
      },
      premium: {
        backgroundColor: '#ffffff',
        shadowColor: shadowColor || '#dc2626',
        shadowOffset: {
          width: 0,
          height: 8,
        },
        shadowOpacity: 0.15,
        shadowRadius: 20,
        elevation: 12,
        borderWidth: 1,
        borderColor: 'rgba(220, 38, 38, 0.1)',
      },
      accent: {
        backgroundColor: '#ffffff',
        shadowColor: shadowColor || '#dc2626',
        shadowOffset: {
          width: 0,
          height: 4,
        },
        shadowOpacity: 0.2,
        shadowRadius: 12,
        elevation: 6,
        borderWidth: 2,
        borderColor: '#dc2626',
      },
    };

    // Enhanced padding styles
    const paddingStyles: Record<string, ViewStyle> = {
      none: {},
      sm: { padding: 12 },
      md: { padding: 16 },
      lg: { padding: 20 },
      xl: { padding: 24 },
      xxl: { padding: 28 },
    };

    // Enhanced margin styles
    const marginStyles: Record<string, ViewStyle> = {
      none: {},
      sm: { margin: 8 },
      md: { margin: 16 },
      lg: { margin: 20 },
      xl: { margin: 24 },
    };

    return {
      ...baseStyles,
      ...variantStyles[variant],
      ...paddingStyles[padding],
      ...marginStyles[margin],
    };
  };

  return (
    <View style={[getCardStyles(), style]} {...props}>
      {children}
    </View>
  );
};

export default Card;
