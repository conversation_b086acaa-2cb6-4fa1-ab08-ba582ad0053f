import { NavigationContainerRef, CommonActions } from '@react-navigation/native';
import { performanceMonitor } from './performanceMonitoring';
import { FEATURE_FLAGS } from '../config/performanceConfig';

/**
 * Optimized navigation service for better performance and tracking
 */

class OptimizedNavigationService {
  private navigationRef: React.RefObject<NavigationContainerRef<any>>;
  private navigationHistory: string[] = [];
  private maxHistorySize = 50;
  private navigationStartTime: number = 0;

  constructor() {
    this.navigationRef = React.createRef();
  }

  // Set navigation reference
  setNavigationRef(ref: React.RefObject<NavigationContainerRef<any>>): void {
    this.navigationRef = ref;
  }

  // Get current navigation reference
  getNavigationRef(): NavigationContainerRef<any> | null {
    return this.navigationRef.current;
  }

  // Navigate with performance tracking
  navigate(name: string, params?: any): void {
    const startTime = Date.now();
    this.navigationStartTime = startTime;

    try {
      const navigation = this.getNavigationRef();
      if (navigation) {
        navigation.navigate(name, params);
        this.addToHistory(name);
        
        if (FEATURE_FLAGS.PERFORMANCE_MONITORING) {
          // Track navigation performance after a short delay
          setTimeout(() => {
            const duration = Date.now() - startTime;
            performanceMonitor.trackScreenNavigation(name, duration);
          }, 100);
        }
      }
    } catch (error) {
      console.error('Navigation error:', error);
    }
  }

  // Go back with performance tracking
  goBack(): void {
    const startTime = Date.now();

    try {
      const navigation = this.getNavigationRef();
      if (navigation && navigation.canGoBack()) {
        navigation.goBack();
        
        if (FEATURE_FLAGS.PERFORMANCE_MONITORING) {
          setTimeout(() => {
            const duration = Date.now() - startTime;
            performanceMonitor.trackMetric('navigation_back', duration);
          }, 100);
        }
      }
    } catch (error) {
      console.error('Go back error:', error);
    }
  }

  // Reset navigation stack
  reset(state: any): void {
    try {
      const navigation = this.getNavigationRef();
      if (navigation) {
        navigation.reset(state);
        this.navigationHistory = [];
      }
    } catch (error) {
      console.error('Reset navigation error:', error);
    }
  }

  // Replace current screen
  replace(name: string, params?: any): void {
    const startTime = Date.now();

    try {
      const navigation = this.getNavigationRef();
      if (navigation) {
        navigation.dispatch(
          CommonActions.replace(name, params)
        );
        
        // Replace last item in history
        if (this.navigationHistory.length > 0) {
          this.navigationHistory[this.navigationHistory.length - 1] = name;
        } else {
          this.addToHistory(name);
        }

        if (FEATURE_FLAGS.PERFORMANCE_MONITORING) {
          setTimeout(() => {
            const duration = Date.now() - startTime;
            performanceMonitor.trackMetric('navigation_replace', duration);
          }, 100);
        }
      }
    } catch (error) {
      console.error('Replace navigation error:', error);
    }
  }

  // Push to stack
  push(name: string, params?: any): void {
    const startTime = Date.now();

    try {
      const navigation = this.getNavigationRef();
      if (navigation) {
        navigation.dispatch(
          CommonActions.navigate({ name, params })
        );
        this.addToHistory(name);

        if (FEATURE_FLAGS.PERFORMANCE_MONITORING) {
          setTimeout(() => {
            const duration = Date.now() - startTime;
            performanceMonitor.trackMetric('navigation_push', duration);
          }, 100);
        }
      }
    } catch (error) {
      console.error('Push navigation error:', error);
    }
  }

  // Pop from stack
  pop(count: number = 1): void {
    const startTime = Date.now();

    try {
      const navigation = this.getNavigationRef();
      if (navigation) {
        navigation.dispatch(
          CommonActions.pop(count)
        );

        // Remove from history
        for (let i = 0; i < count && this.navigationHistory.length > 0; i++) {
          this.navigationHistory.pop();
        }

        if (FEATURE_FLAGS.PERFORMANCE_MONITORING) {
          setTimeout(() => {
            const duration = Date.now() - startTime;
            performanceMonitor.trackMetric('navigation_pop', duration);
          }, 100);
        }
      }
    } catch (error) {
      console.error('Pop navigation error:', error);
    }
  }

  // Get current route name
  getCurrentRouteName(): string | undefined {
    try {
      const navigation = this.getNavigationRef();
      return navigation?.getCurrentRoute()?.name;
    } catch (error) {
      console.error('Get current route error:', error);
      return undefined;
    }
  }

  // Get navigation state
  getState(): any {
    try {
      const navigation = this.getNavigationRef();
      return navigation?.getState();
    } catch (error) {
      console.error('Get navigation state error:', error);
      return null;
    }
  }

  // Check if can go back
  canGoBack(): boolean {
    try {
      const navigation = this.getNavigationRef();
      return navigation?.canGoBack() ?? false;
    } catch (error) {
      console.error('Can go back error:', error);
      return false;
    }
  }

  // Add to navigation history
  private addToHistory(routeName: string): void {
    this.navigationHistory.push(routeName);
    
    // Keep history size manageable
    if (this.navigationHistory.length > this.maxHistorySize) {
      this.navigationHistory = this.navigationHistory.slice(-this.maxHistorySize);
    }
  }

  // Get navigation history
  getNavigationHistory(): string[] {
    return [...this.navigationHistory];
  }

  // Clear navigation history
  clearHistory(): void {
    this.navigationHistory = [];
  }

  // Optimized navigation for deep linking
  navigateToDeepLink(url: string): void {
    try {
      // Parse deep link and navigate accordingly
      const navigation = this.getNavigationRef();
      if (navigation) {
        // Implementation depends on your deep linking structure
        console.log('Deep link navigation:', url);
        
        if (FEATURE_FLAGS.PERFORMANCE_MONITORING) {
          performanceMonitor.trackMetric('deep_link_navigation', 1, { url });
        }
      }
    } catch (error) {
      console.error('Deep link navigation error:', error);
    }
  }

  // Batch navigation operations
  batchNavigate(operations: Array<{ type: 'navigate' | 'replace' | 'push' | 'pop'; name?: string; params?: any; count?: number }>): void {
    const startTime = Date.now();

    try {
      const navigation = this.getNavigationRef();
      if (navigation) {
        operations.forEach(operation => {
          switch (operation.type) {
            case 'navigate':
              if (operation.name) this.navigate(operation.name, operation.params);
              break;
            case 'replace':
              if (operation.name) this.replace(operation.name, operation.params);
              break;
            case 'push':
              if (operation.name) this.push(operation.name, operation.params);
              break;
            case 'pop':
              this.pop(operation.count);
              break;
          }
        });

        if (FEATURE_FLAGS.PERFORMANCE_MONITORING) {
          const duration = Date.now() - startTime;
          performanceMonitor.trackMetric('batch_navigation', duration, { 
            operationCount: operations.length 
          });
        }
      }
    } catch (error) {
      console.error('Batch navigation error:', error);
    }
  }

  // Performance analytics
  getNavigationAnalytics(): {
    totalNavigations: number;
    averageNavigationTime: number;
    mostVisitedScreens: Record<string, number>;
  } {
    const screenCounts = this.navigationHistory.reduce((acc, screen) => {
      acc[screen] = (acc[screen] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    return {
      totalNavigations: this.navigationHistory.length,
      averageNavigationTime: 0, // Would need to track timing data
      mostVisitedScreens: screenCounts,
    };
  }
}

// Create singleton instance
export const optimizedNavigation = new OptimizedNavigationService();

// React hook for optimized navigation
export const useOptimizedNavigation = () => {
  const navigate = React.useCallback((name: string, params?: any) => {
    optimizedNavigation.navigate(name, params);
  }, []);

  const goBack = React.useCallback(() => {
    optimizedNavigation.goBack();
  }, []);

  const replace = React.useCallback((name: string, params?: any) => {
    optimizedNavigation.replace(name, params);
  }, []);

  const push = React.useCallback((name: string, params?: any) => {
    optimizedNavigation.push(name, params);
  }, []);

  const pop = React.useCallback((count?: number) => {
    optimizedNavigation.pop(count);
  }, []);

  return {
    navigate,
    goBack,
    replace,
    push,
    pop,
    canGoBack: optimizedNavigation.canGoBack(),
    currentRoute: optimizedNavigation.getCurrentRouteName(),
  };
};

export default optimizedNavigation;
