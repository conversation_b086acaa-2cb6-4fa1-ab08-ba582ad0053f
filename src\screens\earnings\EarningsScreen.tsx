import React, { useEffect, useState } from 'react';
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  RefreshControl,
  Dimensions,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import { useNavigation } from '@react-navigation/native';
import { LineChart, BarChart } from 'react-native-chart-kit';
import { Card, Button, Badge, LoadingSpinner } from '../../components/ui';
import { useEarnings } from '../../context/EarningsContext';
import { formatCurrency, formatDate, getWeekDates, getMonthDates } from '../../utils/helpers';
import { DeductionType, BonusType } from '../../types/earnings';

const { width: screenWidth } = Dimensions.get('window');

const EarningsScreen: React.FC = () => {
  const navigation = useNavigation();
  const {
    state,
    fetchEarningsSummary,
    fetchDailyEarnings,
    fetchWalletBalance,
    fetchDeductions,
    fetchBonuses,
    setSelectedPeriod
  } = useEarnings();
  const [refreshing, setRefreshing] = useState(false);
  const [chartData, setChartData] = useState<any>(null);

  useEffect(() => {
    loadEarningsData();
  }, [state.selectedPeriod]);

  const loadEarningsData = async () => {
    try {
      await Promise.all([
        fetchEarningsSummary(),
        fetchWalletBalance(),
        fetchDeductions(),
        fetchBonuses(),
      ]);
      await loadChartData();
    } catch (error) {
      console.error('Error loading earnings data:', error);
    }
  };

  const loadChartData = async () => {
    try {
      const now = new Date();
      let startDate: string;
      let endDate: string;

      switch (state.selectedPeriod) {
        case 'week':
          const weekDates = getWeekDates(now);
          startDate = weekDates.start;
          endDate = weekDates.end;
          break;
        case 'month':
          const monthDates = getMonthDates(now);
          startDate = monthDates.start;
          endDate = monthDates.end;
          break;
        default:
          // Last 7 days for today view
          startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000).toISOString().split('T')[0];
          endDate = now.toISOString().split('T')[0];
      }

      await fetchDailyEarnings(startDate, endDate);
    } catch (error) {
      console.error('Error loading chart data:', error);
    }
  };

  const onRefresh = async () => {
    setRefreshing(true);
    await loadEarningsData();
    setRefreshing(false);
  };

  const renderPeriodSelector = () => (
    <View style={{
      flexDirection: 'row',
      backgroundColor: '#f3f4f6',
      borderRadius: 8,
      padding: 4,
      marginHorizontal: 20,
      marginBottom: 20,
    }}>
      {(['today', 'week', 'month', 'all'] as const).map((period) => (
        <TouchableOpacity
          key={period}
          onPress={() => setSelectedPeriod(period)}
          style={{
            flex: 1,
            paddingVertical: 8,
            paddingHorizontal: 12,
            borderRadius: 6,
            backgroundColor: state.selectedPeriod === period ? '#ef4444' : 'transparent',
          }}
        >
          <Text style={{
            textAlign: 'center',
            fontSize: 14,
            fontWeight: '500',
            color: state.selectedPeriod === period ? 'white' : '#6b7280',
            textTransform: 'capitalize',
          }}>
            {period}
          </Text>
        </TouchableOpacity>
      ))}
    </View>
  );

  const renderEarningsSummary = () => {
    if (!state.summary) return null;

    const periodKey = state.selectedPeriod === 'all' ? 'allTime' : state.selectedPeriod;
    const currentPeriod = state.summary[periodKey as keyof typeof state.summary];

    return (
      <Card variant="elevated" margin="md" padding="lg">
        <View style={{ alignItems: 'center', marginBottom: 20 }}>
          <Text style={{ fontSize: 32, fontWeight: 'bold', color: '#111827' }}>
            {formatCurrency(currentPeriod.total)}
          </Text>
          <Text style={{ fontSize: 16, color: '#6b7280', textTransform: 'capitalize' }}>
            {state.selectedPeriod} Earnings
          </Text>
        </View>

        <View style={{
          flexDirection: 'row',
          justifyContent: 'space-between',
          paddingTop: 16,
          borderTopWidth: 1,
          borderTopColor: '#e5e7eb',
        }}>
          <View style={{ alignItems: 'center', flex: 1 }}>
            <Text style={{ fontSize: 18, fontWeight: 'bold', color: '#111827' }}>
              {currentPeriod.orders}
            </Text>
            <Text style={{ fontSize: 12, color: '#6b7280' }}>Orders</Text>
          </View>

          <View style={{ alignItems: 'center', flex: 1 }}>
            <Text style={{ fontSize: 18, fontWeight: 'bold', color: '#111827' }}>
              {formatCurrency(currentPeriod.tips)}
            </Text>
            <Text style={{ fontSize: 12, color: '#6b7280' }}>Tips</Text>
          </View>

          <View style={{ alignItems: 'center', flex: 1 }}>
            <Text style={{ fontSize: 18, fontWeight: 'bold', color: '#111827' }}>
              {currentPeriod.hours.toFixed(1)}h
            </Text>
            <Text style={{ fontSize: 12, color: '#6b7280' }}>Hours</Text>
          </View>

          <View style={{ alignItems: 'center', flex: 1 }}>
            <Text style={{ fontSize: 18, fontWeight: 'bold', color: '#111827' }}>
              {formatCurrency(currentPeriod.total / Math.max(currentPeriod.hours, 1))}
            </Text>
            <Text style={{ fontSize: 12, color: '#6b7280' }}>Per Hour</Text>
          </View>
        </View>
      </Card>
    );
  };

  const renderWalletBalance = () => {
    if (!state.walletBalance) return null;

    return (
      <Card variant="elevated" margin="md" padding="lg">
        <View style={{ flexDirection: 'row', justifyContent: 'space-between', alignItems: 'center', marginBottom: 16 }}>
          <Text style={{ fontSize: 18, fontWeight: 'bold', color: '#111827' }}>
            Wallet Balance
          </Text>
          <TouchableOpacity
            onPress={() => navigation.navigate('Wallet' as never)}
            style={{
              flexDirection: 'row',
              alignItems: 'center',
              backgroundColor: '#ef4444',
              paddingHorizontal: 12,
              paddingVertical: 6,
              borderRadius: 8,
            }}
          >
            <Ionicons name="wallet" size={16} color="white" />
            <Text style={{ color: 'white', fontWeight: '600', marginLeft: 4 }}>
              Manage
            </Text>
          </TouchableOpacity>
        </View>

        <View style={{ flexDirection: 'row', justifyContent: 'space-between' }}>
          <View style={{ alignItems: 'center', flex: 1 }}>
            <Text style={{ fontSize: 24, fontWeight: 'bold', color: '#10b981' }}>
              {formatCurrency(state.walletBalance.availableBalance)}
            </Text>
            <Text style={{ fontSize: 12, color: '#6b7280' }}>Available</Text>
          </View>

          <View style={{ alignItems: 'center', flex: 1 }}>
            <Text style={{ fontSize: 18, fontWeight: 'bold', color: '#f59e0b' }}>
              {formatCurrency(state.walletBalance.pendingBalance)}
            </Text>
            <Text style={{ fontSize: 12, color: '#6b7280' }}>Pending</Text>
          </View>

          <View style={{ alignItems: 'center', flex: 1 }}>
            <Text style={{ fontSize: 18, fontWeight: 'bold', color: '#111827' }}>
              {formatCurrency(state.walletBalance.totalBalance)}
            </Text>
            <Text style={{ fontSize: 12, color: '#6b7280' }}>Total</Text>
          </View>
        </View>
      </Card>
    );
  };

  const renderDeductionsAndBonuses = () => {
    const recentDeductions = state.deductions.slice(0, 3);
    const recentBonuses = state.bonuses.slice(0, 3);

    if (recentDeductions.length === 0 && recentBonuses.length === 0) return null;

    return (
      <Card variant="elevated" margin="md" padding="lg">
        <Text style={{ fontSize: 18, fontWeight: 'bold', color: '#111827', marginBottom: 16 }}>
          Recent Activity
        </Text>

        {recentBonuses.length > 0 && (
          <View style={{ marginBottom: 16 }}>
            <Text style={{ fontSize: 16, fontWeight: '600', color: '#10b981', marginBottom: 8 }}>
              Bonuses
            </Text>
            {recentBonuses.map((bonus) => (
              <View key={bonus.id} style={{
                flexDirection: 'row',
                justifyContent: 'space-between',
                alignItems: 'center',
                paddingVertical: 8,
                borderBottomWidth: 1,
                borderBottomColor: '#f3f4f6',
              }}>
                <View style={{ flex: 1 }}>
                  <Text style={{ fontSize: 14, fontWeight: '500', color: '#111827' }}>
                    {bonus.description}
                  </Text>
                  <Text style={{ fontSize: 12, color: '#6b7280' }}>
                    {formatDate(bonus.date)}
                  </Text>
                </View>
                <Text style={{ fontSize: 16, fontWeight: 'bold', color: '#10b981' }}>
                  +{formatCurrency(bonus.amount)}
                </Text>
              </View>
            ))}
          </View>
        )}

        {recentDeductions.length > 0 && (
          <View>
            <Text style={{ fontSize: 16, fontWeight: '600', color: '#ef4444', marginBottom: 8 }}>
              Deductions
            </Text>
            {recentDeductions.map((deduction) => (
              <View key={deduction.id} style={{
                flexDirection: 'row',
                justifyContent: 'space-between',
                alignItems: 'center',
                paddingVertical: 8,
                borderBottomWidth: 1,
                borderBottomColor: '#f3f4f6',
              }}>
                <View style={{ flex: 1 }}>
                  <Text style={{ fontSize: 14, fontWeight: '500', color: '#111827' }}>
                    {deduction.reason}
                  </Text>
                  <Text style={{ fontSize: 12, color: '#6b7280' }}>
                    {formatDate(deduction.date)}
                  </Text>
                </View>
                <Text style={{ fontSize: 16, fontWeight: 'bold', color: '#ef4444' }}>
                  -{formatCurrency(deduction.amount)}
                </Text>
              </View>
            ))}
          </View>
        )}

        <TouchableOpacity
          style={{
            marginTop: 12,
            paddingVertical: 8,
            alignItems: 'center',
          }}
          onPress={() => navigation.navigate('EarningsHistory' as never)}
        >
          <Text style={{ color: '#ef4444', fontWeight: '600' }}>
            View All Activity
          </Text>
        </TouchableOpacity>
      </Card>
    );
  };

  const renderEarningsChart = () => {
    if (state.dailyEarnings.length === 0) return null;

    // Temporarily disabled chart to fix Text component warnings
    return (
      <Card variant="elevated" margin="md" padding="lg">
        <Text style={{ fontSize: 18, fontWeight: 'bold', color: '#111827', marginBottom: 16 }}>
          Daily Earnings Trend
        </Text>

        <View style={{
          height: 220,
          alignItems: 'center',
          justifyContent: 'center',
          backgroundColor: '#f8fafc',
          borderRadius: 16,
          marginVertical: 8,
        }}>
          <Ionicons name="bar-chart" size={48} color="#6b7280" />
          <Text style={{
            fontSize: 16,
            color: '#6b7280',
            marginTop: 12,
            textAlign: 'center',
          }}>
            Chart temporarily disabled{'\n'}for debugging
          </Text>
        </View>
      </Card>
    );

    // Original chart code (commented out)
    /*
    const chartData = {
      labels: state.dailyEarnings.slice(-7).map(day =>
        formatDate(day.date, 'SHORT_DAY')
      ),
      datasets: [
        {
          data: state.dailyEarnings.slice(-7).map(day => day.total),
          color: (opacity = 1) => `rgba(249, 115, 22, ${opacity})`,
          strokeWidth: 3,
        },
      ],
    };

    const chartConfig = {
      backgroundColor: '#ffffff',
      backgroundGradientFrom: '#ffffff',
      backgroundGradientTo: '#ffffff',
      decimalPlaces: 0,
      color: (opacity = 1) => `rgba(107, 114, 128, ${opacity})`,
      labelColor: (opacity = 1) => `rgba(107, 114, 128, ${opacity})`,
      style: {
        borderRadius: 16,
      },
      propsForDots: {
        r: '6',
        strokeWidth: '2',
        stroke: '#ef4444',
      },
    };

    return (
      <Card variant="elevated" margin="md" padding="lg">
        <Text style={{ fontSize: 18, fontWeight: 'bold', color: '#111827', marginBottom: 16 }}>
          Daily Earnings Trend
        </Text>

        <LineChart
          data={chartData}
          width={screenWidth - 64}
          height={220}
          chartConfig={chartConfig}
          bezier
          style={{
            marginVertical: 8,
            borderRadius: 16,
          }}
        />
      </Card>
    );
    */
  };

  const renderQuickActions = () => (
    <Card variant="elevated" margin="md" padding="lg">
      <Text style={{ fontSize: 18, fontWeight: 'bold', color: '#111827', marginBottom: 16 }}>
        Quick Actions
      </Text>

      <View style={{ gap: 12 }}>
        {/* Expanded Wallet Button */}
        <TouchableOpacity
          style={{
            backgroundColor: '#ef4444',
            paddingVertical: 16,
            paddingHorizontal: 20,
            borderRadius: 12,
            flexDirection: 'row',
            alignItems: 'center',
            justifyContent: 'center',
            shadowColor: '#ef4444',
            shadowOffset: { width: 0, height: 4 },
            shadowOpacity: 0.3,
            shadowRadius: 8,
            elevation: 6,
          }}
          onPress={() => navigation.navigate('Wallet' as never)}
        >
          <Ionicons name="wallet" size={24} color="white" style={{ marginRight: 12 }} />
          <Text style={{ color: 'white', fontSize: 18, fontWeight: '700' }}>
            Manage Wallet
          </Text>
        </TouchableOpacity>

        <View style={{ flexDirection: 'row', gap: 12 }}>
          <TouchableOpacity
            style={{
              flex: 1,
              backgroundColor: 'transparent',
              borderWidth: 1,
              borderColor: '#ef4444',
              paddingVertical: 12,
              paddingHorizontal: 16,
              borderRadius: 8,
              flexDirection: 'row',
              alignItems: 'center',
              justifyContent: 'center',
            }}
            onPress={() => navigation.navigate('EarningsHistory' as never)}
          >
            <Ionicons name="time-outline" size={18} color="#ef4444" style={{ marginRight: 8 }} />
            <Text style={{ color: '#ef4444', fontSize: 16, fontWeight: '600' }}>
              History
            </Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={{
              flex: 1,
              backgroundColor: 'transparent',
              borderWidth: 1,
              borderColor: '#ef4444',
              paddingVertical: 12,
              paddingHorizontal: 16,
              borderRadius: 8,
              flexDirection: 'row',
              alignItems: 'center',
              justifyContent: 'center',
            }}
            onPress={() => navigation.navigate('PayoutSchedule' as never)}
          >
            <Ionicons name="calendar-outline" size={18} color="#ef4444" style={{ marginRight: 8 }} />
            <Text style={{ color: '#ef4444', fontSize: 16, fontWeight: '600' }}>
              Payouts
            </Text>
          </TouchableOpacity>
        </View>

        {/* Performance Analytics Button */}
        <TouchableOpacity
          style={{
            backgroundColor: '#10b981',
            paddingVertical: 12,
            paddingHorizontal: 16,
            borderRadius: 8,
            flexDirection: 'row',
            alignItems: 'center',
            justifyContent: 'center',
          }}
          onPress={() => navigation.navigate('PerformanceAnalytics' as never)}
        >
          <Ionicons name="analytics" size={18} color="white" style={{ marginRight: 8 }} />
          <Text style={{ color: 'white', fontSize: 16, fontWeight: '600' }}>
            Performance Analytics
          </Text>
        </TouchableOpacity>
      </View>
    </Card>
  );

  if (state.isLoading && !refreshing) {
    return (
      <SafeAreaView style={{ flex: 1, backgroundColor: '#f8fafc' }}>
        <LoadingSpinner message="Loading earnings data..." />
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={{ flex: 1, backgroundColor: '#f8fafc' }}>
      <View style={{
        paddingHorizontal: 20,
        paddingVertical: 16,
        backgroundColor: '#ffffff',
        borderBottomWidth: 1,
        borderBottomColor: '#e5e7eb',
      }}>
        <Text style={{ fontSize: 24, fontWeight: 'bold', color: '#111827' }}>
          Earnings
        </Text>
      </View>

      <ScrollView
        style={{ flex: 1 }}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
        showsVerticalScrollIndicator={false}
      >
        {renderPeriodSelector()}
        {renderEarningsSummary()}
        {renderWalletBalance()}
        {renderDeductionsAndBonuses()}
        {renderEarningsChart()}
        {renderQuickActions()}

        {/* Bottom spacing */}
        <View style={{ height: 20 }} />
      </ScrollView>
    </SafeAreaView>
  );
};

export default EarningsScreen;
