import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  FlatList,
  ScrollView,
  Alert,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import { useNavigation } from '@react-navigation/native';
import { FAQSection, FAQItem, FAQCategory } from '../../types/support';

const HelpCenterScreen: React.FC = () => {
  const navigation = useNavigation();
  
  // State
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState<FAQCategory | null>(null);
  const [expandedItems, setExpandedItems] = useState<Set<string>>(new Set());
  const [faqSections, setFaqSections] = useState<FAQSection[]>([]);
  const [filteredFAQs, setFilteredFAQs] = useState<FAQItem[]>([]);
  const [popularFAQs, setPopularFAQs] = useState<FAQItem[]>([]);

  // Mock FAQ data
  useEffect(() => {
    const mockFAQSections: FAQSection[] = [
      {
        category: FAQCategory.GETTING_STARTED,
        title: 'Getting Started',
        icon: 'rocket-outline',
        items: [
          {
            id: '1',
            category: FAQCategory.GETTING_STARTED,
            question: 'How do I start delivering with FoodWay?',
            answer: 'To start delivering with FoodWay, you need to: 1) Download the rider app, 2) Complete registration with your CNIC and documents, 3) Upload vehicle documents, 4) Wait for verification (usually 24-48 hours), 5) Go online and start accepting orders!',
            tags: ['registration', 'getting started', 'verification'],
            isPopular: true,
            helpfulCount: 245,
            notHelpfulCount: 12,
            createdAt: '2023-01-01',
            updatedAt: '2023-01-01',
          },
          {
            id: '2',
            category: FAQCategory.GETTING_STARTED,
            question: 'What documents do I need to provide?',
            answer: 'You need to provide: 1) Valid CNIC (front and back), 2) Driving license, 3) Vehicle registration certificate, 4) Vehicle insurance certificate, 5) Recent passport-size photo. All documents should be clear and valid.',
            tags: ['documents', 'verification', 'requirements'],
            isPopular: true,
            helpfulCount: 189,
            notHelpfulCount: 8,
            createdAt: '2023-01-01',
            updatedAt: '2023-01-01',
          },
        ],
      },
      {
        category: FAQCategory.ORDERS,
        title: 'Orders & Deliveries',
        icon: 'bicycle-outline',
        items: [
          {
            id: '3',
            category: FAQCategory.ORDERS,
            question: 'How do I accept an order?',
            answer: 'When you receive an order request, you\'ll see order details including pickup location, delivery address, and estimated earnings. Tap "Accept" to confirm. You have 30 seconds to respond before the order is offered to another rider.',
            tags: ['orders', 'accept', 'delivery'],
            isPopular: true,
            helpfulCount: 156,
            notHelpfulCount: 5,
            createdAt: '2023-01-01',
            updatedAt: '2023-01-01',
          },
          {
            id: '4',
            category: FAQCategory.ORDERS,
            question: 'What if the customer is not available?',
            answer: 'If the customer is not available: 1) Call the customer using the in-app calling feature, 2) Wait for 5 minutes at the delivery location, 3) If still no response, contact support through the app, 4) Follow support instructions for order completion.',
            tags: ['customer', 'unavailable', 'delivery'],
            isPopular: false,
            helpfulCount: 89,
            notHelpfulCount: 3,
            createdAt: '2023-01-01',
            updatedAt: '2023-01-01',
          },
        ],
      },
      {
        category: FAQCategory.PAYMENTS,
        title: 'Payments & Earnings',
        icon: 'wallet-outline',
        items: [
          {
            id: '5',
            category: FAQCategory.PAYMENTS,
            question: 'When do I get paid?',
            answer: 'Payments are processed daily. Your earnings from completed deliveries are transferred to your registered bank account or mobile wallet within 24 hours. You can track your earnings and payment history in the Earnings section.',
            tags: ['payment', 'earnings', 'transfer'],
            isPopular: true,
            helpfulCount: 234,
            notHelpfulCount: 7,
            createdAt: '2023-01-01',
            updatedAt: '2023-01-01',
          },
          {
            id: '6',
            category: FAQCategory.PAYMENTS,
            question: 'How are delivery fees calculated?',
            answer: 'Delivery fees are calculated based on: 1) Base delivery fee, 2) Distance between pickup and delivery, 3) Time of day (peak hours have higher rates), 4) Weather conditions, 5) Special promotions or bonuses. You can see the estimated earning before accepting each order.',
            tags: ['fees', 'calculation', 'earnings'],
            isPopular: false,
            helpfulCount: 167,
            notHelpfulCount: 12,
            createdAt: '2023-01-01',
            updatedAt: '2023-01-01',
          },
        ],
      },
      {
        category: FAQCategory.TECHNICAL,
        title: 'Technical Issues',
        icon: 'settings-outline',
        items: [
          {
            id: '7',
            category: FAQCategory.TECHNICAL,
            question: 'The app is not working properly, what should I do?',
            answer: 'If you\'re experiencing technical issues: 1) Force close and restart the app, 2) Check your internet connection, 3) Update the app to the latest version, 4) Restart your phone, 5) If the problem persists, contact support with details about the issue.',
            tags: ['technical', 'app', 'troubleshooting'],
            isPopular: false,
            helpfulCount: 98,
            notHelpfulCount: 15,
            createdAt: '2023-01-01',
            updatedAt: '2023-01-01',
          },
        ],
      },
      {
        category: FAQCategory.ACCOUNT,
        title: 'Account & Profile',
        icon: 'person-outline',
        items: [
          {
            id: '8',
            category: FAQCategory.ACCOUNT,
            question: 'How do I update my profile information?',
            answer: 'To update your profile: 1) Go to Profile section in the app, 2) Tap on the information you want to change, 3) Make your updates, 4) Save changes. Note: Some changes like bank details may require verification.',
            tags: ['profile', 'update', 'information'],
            isPopular: false,
            helpfulCount: 76,
            notHelpfulCount: 4,
            createdAt: '2023-01-01',
            updatedAt: '2023-01-01',
          },
        ],
      },
    ];

    setFaqSections(mockFAQSections);
    
    // Set popular FAQs
    const popular = mockFAQSections
      .flatMap(section => section.items)
      .filter(item => item.isPopular)
      .sort((a, b) => b.helpfulCount - a.helpfulCount);
    setPopularFAQs(popular);
  }, []);

  // Search functionality
  useEffect(() => {
    if (searchQuery.trim()) {
      const allFAQs = faqSections.flatMap(section => section.items);
      const filtered = allFAQs.filter(faq =>
        faq.question.toLowerCase().includes(searchQuery.toLowerCase()) ||
        faq.answer.toLowerCase().includes(searchQuery.toLowerCase()) ||
        faq.tags.some(tag => tag.toLowerCase().includes(searchQuery.toLowerCase()))
      );
      setFilteredFAQs(filtered);
    } else {
      setFilteredFAQs([]);
    }
  }, [searchQuery, faqSections]);

  const toggleExpanded = (itemId: string) => {
    const newExpanded = new Set(expandedItems);
    if (newExpanded.has(itemId)) {
      newExpanded.delete(itemId);
    } else {
      newExpanded.add(itemId);
    }
    setExpandedItems(newExpanded);
  };

  const markHelpful = (faqId: string, helpful: boolean) => {
    // In a real app, this would make an API call
    Alert.alert('Thank You!', 'Your feedback has been recorded.');
  };

  const renderFAQItem = (item: FAQItem) => {
    const isExpanded = expandedItems.has(item.id);
    
    return (
      <View
        key={item.id}
        style={{
          backgroundColor: 'white',
          marginHorizontal: 16,
          marginVertical: 4,
          borderRadius: 12,
          borderWidth: 1,
          borderColor: '#e5e7eb',
        }}
      >
        <TouchableOpacity
          onPress={() => toggleExpanded(item.id)}
          style={{
            flexDirection: 'row',
            alignItems: 'center',
            padding: 16,
          }}
        >
          <View style={{ flex: 1 }}>
            <Text style={{
              fontSize: 16,
              fontWeight: '600',
              color: '#1f2937',
              marginBottom: 4,
            }}>
              {item.question}
            </Text>
            {item.isPopular && (
              <View style={{
                flexDirection: 'row',
                alignItems: 'center',
              }}>
                <Ionicons name="star" size={12} color="#f59e0b" />
                <Text style={{
                  fontSize: 12,
                  color: '#f59e0b',
                  marginLeft: 4,
                }}>
                  Popular
                </Text>
              </View>
            )}
          </View>
          
          <Ionicons
            name={isExpanded ? 'chevron-up' : 'chevron-down'}
            size={20}
            color="#6b7280"
          />
        </TouchableOpacity>

        {isExpanded && (
          <View style={{
            paddingHorizontal: 16,
            paddingBottom: 16,
          }}>
            <Text style={{
              fontSize: 14,
              color: '#4b5563',
              lineHeight: 20,
              marginBottom: 16,
            }}>
              {item.answer}
            </Text>

            <View style={{
              flexDirection: 'row',
              alignItems: 'center',
              justifyContent: 'space-between',
              paddingTop: 12,
              borderTopWidth: 1,
              borderTopColor: '#f3f4f6',
            }}>
              <Text style={{
                fontSize: 12,
                color: '#6b7280',
              }}>
                Was this helpful?
              </Text>
              
              <View style={{ flexDirection: 'row', alignItems: 'center' }}>
                <TouchableOpacity
                  onPress={() => markHelpful(item.id, true)}
                  style={{
                    flexDirection: 'row',
                    alignItems: 'center',
                    paddingHorizontal: 8,
                    paddingVertical: 4,
                    marginRight: 8,
                  }}
                >
                  <Ionicons name="thumbs-up-outline" size={16} color="#10b981" />
                  <Text style={{
                    fontSize: 12,
                    color: '#10b981',
                    marginLeft: 4,
                  }}>
                    {item.helpfulCount}
                  </Text>
                </TouchableOpacity>
                
                <TouchableOpacity
                  onPress={() => markHelpful(item.id, false)}
                  style={{
                    flexDirection: 'row',
                    alignItems: 'center',
                    paddingHorizontal: 8,
                    paddingVertical: 4,
                  }}
                >
                  <Ionicons name="thumbs-down-outline" size={16} color="#ef4444" />
                  <Text style={{
                    fontSize: 12,
                    color: '#ef4444',
                    marginLeft: 4,
                  }}>
                    {item.notHelpfulCount}
                  </Text>
                </TouchableOpacity>
              </View>
            </View>
          </View>
        )}
      </View>
    );
  };

  const renderCategorySection = (section: FAQSection) => (
    <View key={section.category} style={{ marginBottom: 24 }}>
      <TouchableOpacity
        onPress={() => setSelectedCategory(section.category)}
        style={{
          flexDirection: 'row',
          alignItems: 'center',
          paddingHorizontal: 16,
          paddingVertical: 12,
          marginBottom: 8,
        }}
      >
        <View style={{
          width: 40,
          height: 40,
          backgroundColor: '#f3f4f6',
          borderRadius: 20,
          alignItems: 'center',
          justifyContent: 'center',
          marginRight: 12,
        }}>
          <Ionicons name={section.icon as any} size={20} color="#3b82f6" />
        </View>

        <View style={{ flex: 1 }}>
          <Text style={{
            fontSize: 16,
            fontWeight: '600',
            color: '#1f2937',
          }}>
            {section.title}
          </Text>
          <Text style={{
            fontSize: 12,
            color: '#6b7280',
          }}>
            {section.items.length} articles
          </Text>
        </View>

        <Ionicons name="chevron-forward" size={20} color="#6b7280" />
      </TouchableOpacity>

      {section.items.slice(0, 2).map(item => renderFAQItem(item))}

      {section.items.length > 2 && (
        <TouchableOpacity
          onPress={() => setSelectedCategory(section.category)}
          style={{
            alignItems: 'center',
            paddingVertical: 12,
            marginHorizontal: 16,
          }}
        >
          <Text style={{
            fontSize: 14,
            color: '#3b82f6',
            fontWeight: '500',
          }}>
            View all {section.items.length} articles
          </Text>
        </TouchableOpacity>
      )}
    </View>
  );

  return (
    <SafeAreaView style={{ flex: 1, backgroundColor: '#f9fafb' }}>
      {/* Header */}
      <View style={{
        flexDirection: 'row',
        alignItems: 'center',
        paddingHorizontal: 16,
        paddingVertical: 12,
        backgroundColor: 'white',
        borderBottomWidth: 1,
        borderBottomColor: '#e5e7eb',
      }}>
        <TouchableOpacity
          onPress={() => navigation.goBack()}
          style={{
            width: 40,
            height: 40,
            alignItems: 'center',
            justifyContent: 'center',
            marginRight: 8,
          }}
        >
          <Ionicons name="arrow-back" size={24} color="#374151" />
        </TouchableOpacity>

        <Text style={{
          fontSize: 18,
          fontWeight: '600',
          color: '#1f2937',
          flex: 1,
        }}>
          Help Center
        </Text>

        <TouchableOpacity
          onPress={() => navigation.navigate('LiveChat' as never)}
          style={{
            width: 40,
            height: 40,
            alignItems: 'center',
            justifyContent: 'center',
          }}
        >
          <Ionicons name="chatbubble-outline" size={20} color="#3b82f6" />
        </TouchableOpacity>
      </View>

      {/* Search */}
      <View style={{
        paddingHorizontal: 16,
        paddingVertical: 12,
        backgroundColor: 'white',
      }}>
        <View style={{
          flexDirection: 'row',
          alignItems: 'center',
          backgroundColor: '#f9fafb',
          borderRadius: 12,
          borderWidth: 1,
          borderColor: '#e5e7eb',
          paddingHorizontal: 12,
        }}>
          <Ionicons name="search" size={20} color="#6b7280" />
          <TextInput
            value={searchQuery}
            onChangeText={setSearchQuery}
            placeholder="Search for help..."
            placeholderTextColor="#9ca3af"
            style={{
              flex: 1,
              paddingVertical: 12,
              paddingHorizontal: 8,
              fontSize: 16,
              color: '#1f2937',
            }}
          />
          {searchQuery.length > 0 && (
            <TouchableOpacity onPress={() => setSearchQuery('')}>
              <Ionicons name="close-circle" size={20} color="#6b7280" />
            </TouchableOpacity>
          )}
        </View>
      </View>

      <ScrollView style={{ flex: 1 }}>
        {searchQuery.trim() ? (
          // Search Results
          <View style={{ paddingVertical: 16 }}>
            <Text style={{
              fontSize: 16,
              fontWeight: '600',
              color: '#1f2937',
              paddingHorizontal: 16,
              marginBottom: 12,
            }}>
              Search Results ({filteredFAQs.length})
            </Text>

            {filteredFAQs.length > 0 ? (
              filteredFAQs.map(item => renderFAQItem(item))
            ) : (
              <View style={{
                alignItems: 'center',
                paddingVertical: 32,
                paddingHorizontal: 16,
              }}>
                <Ionicons name="search" size={48} color="#d1d5db" />
                <Text style={{
                  fontSize: 16,
                  color: '#6b7280',
                  textAlign: 'center',
                  marginTop: 12,
                }}>
                  No results found for "{searchQuery}"
                </Text>
                <Text style={{
                  fontSize: 14,
                  color: '#9ca3af',
                  textAlign: 'center',
                  marginTop: 4,
                }}>
                  Try different keywords or browse categories below
                </Text>
              </View>
            )}
          </View>
        ) : selectedCategory ? (
          // Category View
          <View style={{ paddingVertical: 16 }}>
            <View style={{
              flexDirection: 'row',
              alignItems: 'center',
              paddingHorizontal: 16,
              marginBottom: 16,
            }}>
              <TouchableOpacity
                onPress={() => setSelectedCategory(null)}
                style={{ marginRight: 8 }}
              >
                <Ionicons name="arrow-back" size={20} color="#3b82f6" />
              </TouchableOpacity>
              <Text style={{
                fontSize: 18,
                fontWeight: '600',
                color: '#1f2937',
              }}>
                {faqSections.find(s => s.category === selectedCategory)?.title}
              </Text>
            </View>

            {faqSections
              .find(s => s.category === selectedCategory)
              ?.items.map(item => renderFAQItem(item))}
          </View>
        ) : (
          // Main View
          <View style={{ paddingVertical: 16 }}>
            {/* Popular FAQs */}
            {popularFAQs.length > 0 && (
              <View style={{ marginBottom: 32 }}>
                <Text style={{
                  fontSize: 18,
                  fontWeight: '600',
                  color: '#1f2937',
                  paddingHorizontal: 16,
                  marginBottom: 12,
                }}>
                  Popular Questions
                </Text>
                {popularFAQs.slice(0, 3).map(item => renderFAQItem(item))}
              </View>
            )}

            {/* Categories */}
            <Text style={{
              fontSize: 18,
              fontWeight: '600',
              color: '#1f2937',
              paddingHorizontal: 16,
              marginBottom: 12,
            }}>
              Browse by Category
            </Text>

            {faqSections.map(section => renderCategorySection(section))}

            {/* Contact Support */}
            <View style={{
              backgroundColor: 'white',
              marginHorizontal: 16,
              marginTop: 16,
              borderRadius: 12,
              padding: 16,
              borderWidth: 1,
              borderColor: '#e5e7eb',
            }}>
              <Text style={{
                fontSize: 16,
                fontWeight: '600',
                color: '#1f2937',
                marginBottom: 8,
              }}>
                Still need help?
              </Text>
              <Text style={{
                fontSize: 14,
                color: '#6b7280',
                marginBottom: 16,
              }}>
                Can't find what you're looking for? Our support team is here to help.
              </Text>

              <View style={{ flexDirection: 'row', gap: 12 }}>
                <TouchableOpacity
                  onPress={() => navigation.navigate('LiveChat' as never)}
                  style={{
                    flex: 1,
                    backgroundColor: '#3b82f6',
                    borderRadius: 8,
                    paddingVertical: 12,
                    alignItems: 'center',
                  }}
                >
                  <Text style={{
                    color: 'white',
                    fontSize: 14,
                    fontWeight: '600',
                  }}>
                    Live Chat
                  </Text>
                </TouchableOpacity>

                <TouchableOpacity
                  onPress={() => navigation.navigate('CallSupport' as never)}
                  style={{
                    flex: 1,
                    backgroundColor: '#f3f4f6',
                    borderRadius: 8,
                    paddingVertical: 12,
                    alignItems: 'center',
                  }}
                >
                  <Text style={{
                    color: '#374151',
                    fontSize: 14,
                    fontWeight: '600',
                  }}>
                    Call Support
                  </Text>
                </TouchableOpacity>
              </View>
            </View>
          </View>
        )}
      </ScrollView>
    </SafeAreaView>
  );
};

export default HelpCenterScreen;
