// Earnings and analytics types
export interface EarningsData {
  id: string;
  orderId: string;
  orderNumber: string;
  amount: number;
  tips: number;
  bonuses: number;
  total: number;
  date: string;
  status: EarningsStatus;
  paymentMethod: PaymentMethod;
  restaurant: {
    name: string;
    id: string;
  };
  customer: {
    name: string;
    id: string;
  };
  distance: number;
  duration: number;
}

export enum EarningsStatus {
  PENDING = 'pending',
  PAID = 'paid',
  PROCESSING = 'processing',
  FAILED = 'failed',
}

export enum PaymentMethod {
  BANK_TRANSFER = 'bank_transfer',
  JAZZCASH = 'jazzcash',
  EASYPAISA = 'easypaisa',
  PAYPAL = 'paypal',
  CASH = 'cash',
  DIGITAL_WALLET = 'digital_wallet',
}

export interface EarningsSummary {
  today: {
    total: number;
    orders: number;
    tips: number;
    bonuses: number;
    hours: number;
  };
  week: {
    total: number;
    orders: number;
    tips: number;
    bonuses: number;
    hours: number;
  };
  month: {
    total: number;
    orders: number;
    tips: number;
    bonuses: number;
    hours: number;
  };
  allTime: {
    total: number;
    orders: number;
    tips: number;
    bonuses: number;
    hours: number;
  };
}

export interface DailyEarnings {
  date: string;
  total: number;
  orders: number;
  tips: number;
  bonuses: number;
  hours: number;
}

export interface WeeklyEarnings {
  weekStart: string;
  weekEnd: string;
  total: number;
  orders: number;
  tips: number;
  bonuses: number;
  hours: number;
  dailyBreakdown: DailyEarnings[];
}

export interface MonthlyEarnings {
  month: string;
  year: number;
  total: number;
  orders: number;
  tips: number;
  bonuses: number;
  hours: number;
  weeklyBreakdown: WeeklyEarnings[];
}

export interface PerformanceMetrics {
  averageRating: number;
  totalRatings: number;
  completionRate: number;
  onTimeRate: number;
  averageDeliveryTime: number;
  totalDeliveries: number;
  totalDistance: number;
  averageEarningsPerOrder: number;
  averageEarningsPerHour: number;
  bestDay: {
    date: string;
    earnings: number;
    orders: number;
  };
  bestWeek: {
    weekStart: string;
    weekEnd: string;
    earnings: number;
    orders: number;
  };
}

export interface PayoutInfo {
  id: string;
  amount: number;
  status: PayoutStatus;
  method: PaymentMethod;
  scheduledDate: string;
  processedDate?: string;
  transactionId?: string;
  earningsIncluded: string[]; // Array of earnings IDs
}

export enum PayoutStatus {
  SCHEDULED = 'scheduled',
  PROCESSING = 'processing',
  COMPLETED = 'completed',
  FAILED = 'failed',
  CANCELLED = 'cancelled',
}

export interface EarningsFilter {
  startDate?: string;
  endDate?: string;
  status?: EarningsStatus;
  paymentMethod?: PaymentMethod;
  minAmount?: number;
  maxAmount?: number;
}

// Wallet and Withdrawal Types
export interface WalletBalance {
  totalBalance: number;
  availableBalance: number;
  pendingBalance: number;
  lastUpdated: string;
}

export interface WithdrawalMethod {
  id: string;
  type: PaymentMethod;
  name: string;
  accountNumber?: string;
  accountTitle?: string;
  bankName?: string;
  iban?: string;
  phoneNumber?: string; // For JazzCash/Easypaisa
  isVerified: boolean;
  isPrimary: boolean;
  createdAt: string;
}

export interface WithdrawalRequest {
  id: string;
  amount: number;
  method: WithdrawalMethod;
  status: WithdrawalStatus;
  requestedAt: string;
  processedAt?: string;
  completedAt?: string;
  transactionId?: string;
  fees: number;
  netAmount: number;
  notes?: string;
  rejectionReason?: string;
}

export enum WithdrawalStatus {
  PENDING = 'pending',
  PROCESSING = 'processing',
  COMPLETED = 'completed',
  FAILED = 'failed',
  CANCELLED = 'cancelled',
  REJECTED = 'rejected',
}

// Deductions and Bonuses
export interface Deduction {
  id: string;
  orderId?: string;
  type: DeductionType;
  amount: number;
  reason: string;
  date: string;
  status: DeductionStatus;
}

export enum DeductionType {
  CANCELLATION_PENALTY = 'cancellation_penalty',
  LATE_DELIVERY = 'late_delivery',
  CUSTOMER_COMPLAINT = 'customer_complaint',
  EQUIPMENT_DAMAGE = 'equipment_damage',
  FUEL_ADJUSTMENT = 'fuel_adjustment',
  OTHER = 'other',
}

export enum DeductionStatus {
  PENDING = 'pending',
  APPLIED = 'applied',
  DISPUTED = 'disputed',
  REVERSED = 'reversed',
}

export interface Bonus {
  id: string;
  type: BonusType;
  amount: number;
  description: string;
  date: string;
  orderId?: string;
  criteria?: string;
}

export enum BonusType {
  PEAK_HOUR = 'peak_hour',
  WEATHER_BONUS = 'weather_bonus',
  COMPLETION_BONUS = 'completion_bonus',
  RATING_BONUS = 'rating_bonus',
  REFERRAL_BONUS = 'referral_bonus',
  MILESTONE_BONUS = 'milestone_bonus',
  PROMOTIONAL = 'promotional',
}

// Payout Schedule Types
export interface PayoutSchedule {
  id: string;
  scheduledDate: string;
  amount: number;
  status: PayoutStatus;
  earningsIncluded: EarningsData[];
  deductions: Deduction[];
  bonuses: Bonus[];
  netAmount: number;
  paymentMethod: WithdrawalMethod;
}

export interface PayoutSettings {
  frequency: PayoutFrequency;
  minimumAmount: number;
  preferredMethod: WithdrawalMethod | null;
  autoWithdraw: boolean;
}

export enum PayoutFrequency {
  DAILY = 'daily',
  WEEKLY = 'weekly',
  BIWEEKLY = 'biweekly',
  MONTHLY = 'monthly',
}

export interface EarningsState {
  summary: EarningsSummary | null;
  recentEarnings: EarningsData[];
  dailyEarnings: DailyEarnings[];
  weeklyEarnings: WeeklyEarnings[];
  monthlyEarnings: MonthlyEarnings[];
  performanceMetrics: PerformanceMetrics | null;
  payouts: PayoutInfo[];
  walletBalance: WalletBalance | null;
  withdrawalMethods: WithdrawalMethod[];
  withdrawalHistory: WithdrawalRequest[];
  payoutSchedule: PayoutSchedule[];
  payoutSettings: PayoutSettings | null;
  deductions: Deduction[];
  bonuses: Bonus[];
  isLoading: boolean;
  error: string | null;
  selectedPeriod: 'today' | 'week' | 'month' | 'all';
}

export interface EarningsContextType {
  state: EarningsState;
  fetchEarningsSummary: () => Promise<void>;
  fetchRecentEarnings: (limit?: number) => Promise<void>;
  fetchDailyEarnings: (startDate: string, endDate: string) => Promise<void>;
  fetchWeeklyEarnings: (startDate: string, endDate: string) => Promise<void>;
  fetchMonthlyEarnings: (year: number) => Promise<void>;
  fetchPerformanceMetrics: () => Promise<void>;
  fetchPayouts: () => Promise<void>;

  // Wallet functions
  fetchWalletBalance: () => Promise<void>;
  fetchWithdrawalMethods: () => Promise<void>;
  addWithdrawalMethod: (method: Omit<WithdrawalMethod, 'id' | 'createdAt'>) => Promise<void>;
  updateWithdrawalMethod: (id: string, updates: Partial<WithdrawalMethod>) => Promise<void>;
  deleteWithdrawalMethod: (id: string) => Promise<void>;

  // Withdrawal functions
  requestWithdrawal: (amount: number, methodId: string) => Promise<void>;
  fetchWithdrawalHistory: () => Promise<void>;
  cancelWithdrawal: (id: string) => Promise<void>;

  // Payout functions
  fetchPayoutSchedule: () => Promise<void>;
  fetchPayoutSettings: () => Promise<void>;
  updatePayoutSettings: (settings: Partial<PayoutSettings>) => Promise<void>;

  // Deductions and bonuses
  fetchDeductions: (startDate?: string, endDate?: string) => Promise<void>;
  fetchBonuses: (startDate?: string, endDate?: string) => Promise<void>;

  setSelectedPeriod: (period: 'today' | 'week' | 'month' | 'all') => void;
  clearError: () => void;
}
