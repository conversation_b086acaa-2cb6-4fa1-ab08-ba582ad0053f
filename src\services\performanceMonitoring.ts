import { InteractionManager, Platform } from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';

interface PerformanceMetric {
  name: string;
  value: number;
  timestamp: number;
  metadata?: Record<string, any>;
}

interface MemoryInfo {
  used: number;
  total: number;
  limit: number;
}

interface NetworkMetric {
  url: string;
  method: string;
  duration: number;
  status: number;
  size?: number;
  timestamp: number;
}

interface RenderMetric {
  component: string;
  renderTime: number;
  timestamp: number;
}

class PerformanceMonitoringService {
  private metrics: PerformanceMetric[] = [];
  private networkMetrics: NetworkMetric[] = [];
  private renderMetrics: RenderMetric[] = [];
  private maxMetricsCount = 1000;
  private isEnabled = __DEV__; // Only enable in development by default

  constructor() {
    this.setupPerformanceObserver();
    this.startMemoryMonitoring();
  }

  private setupPerformanceObserver(): void {
    if (!this.isEnabled) return;

    // Monitor React Native bridge performance
    if (global.nativePerformanceNow) {
      this.trackMetric('bridge_startup', global.nativePerformanceNow());
    }

    // Monitor JavaScript thread performance
    this.monitorJavaScriptThread();
  }

  private monitorJavaScriptThread(): void {
    let lastTime = Date.now();
    
    const checkJSThread = () => {
      const currentTime = Date.now();
      const delay = currentTime - lastTime - 16; // Expected 60fps = 16ms per frame
      
      if (delay > 16) {
        this.trackMetric('js_thread_delay', delay, {
          expected: 16,
          actual: currentTime - lastTime,
        });
      }
      
      lastTime = currentTime;
      setTimeout(checkJSThread, 16);
    };

    setTimeout(checkJSThread, 16);
  }

  private startMemoryMonitoring(): void {
    if (!this.isEnabled) return;

    const monitorMemory = () => {
      const memoryInfo = this.getMemoryInfo();
      if (memoryInfo) {
        this.trackMetric('memory_usage', memoryInfo.used, {
          total: memoryInfo.total,
          limit: memoryInfo.limit,
          percentage: (memoryInfo.used / memoryInfo.total) * 100,
        });
      }
    };

    // Monitor memory every 30 seconds
    setInterval(monitorMemory, 30000);
    monitorMemory(); // Initial check
  }

  // Track custom performance metrics
  trackMetric(name: string, value: number, metadata?: Record<string, any>): void {
    if (!this.isEnabled) return;

    const metric: PerformanceMetric = {
      name,
      value,
      timestamp: Date.now(),
      metadata,
    };

    this.metrics.push(metric);

    // Keep only recent metrics
    if (this.metrics.length > this.maxMetricsCount) {
      this.metrics = this.metrics.slice(-this.maxMetricsCount);
    }

    // Log critical performance issues
    this.checkCriticalMetrics(metric);
  }

  // Track network performance
  trackNetworkRequest(
    url: string,
    method: string,
    duration: number,
    status: number,
    size?: number
  ): void {
    if (!this.isEnabled) return;

    const networkMetric: NetworkMetric = {
      url,
      method,
      duration,
      status,
      size,
      timestamp: Date.now(),
    };

    this.networkMetrics.push(networkMetric);

    // Keep only recent network metrics
    if (this.networkMetrics.length > this.maxMetricsCount) {
      this.networkMetrics = this.networkMetrics.slice(-this.maxMetricsCount);
    }

    // Track as general metric
    this.trackMetric('network_request', duration, {
      url,
      method,
      status,
      size,
    });
  }

  // Track component render performance
  trackRender(component: string, renderTime: number): void {
    if (!this.isEnabled) return;

    const renderMetric: RenderMetric = {
      component,
      renderTime,
      timestamp: Date.now(),
    };

    this.renderMetrics.push(renderMetric);

    // Keep only recent render metrics
    if (this.renderMetrics.length > this.maxMetricsCount) {
      this.renderMetrics = this.renderMetrics.slice(-this.maxMetricsCount);
    }

    // Track as general metric
    this.trackMetric('component_render', renderTime, { component });
  }

  // Measure function execution time
  async measureAsync<T>(name: string, fn: () => Promise<T>): Promise<T> {
    const start = Date.now();
    
    try {
      const result = await fn();
      const duration = Date.now() - start;
      this.trackMetric(name, duration);
      return result;
    } catch (error) {
      const duration = Date.now() - start;
      this.trackMetric(`${name}_error`, duration, { error: error.message });
      throw error;
    }
  }

  // Measure synchronous function execution time
  measure<T>(name: string, fn: () => T): T {
    const start = Date.now();
    
    try {
      const result = fn();
      const duration = Date.now() - start;
      this.trackMetric(name, duration);
      return result;
    } catch (error) {
      const duration = Date.now() - start;
      this.trackMetric(`${name}_error`, duration, { error: error.message });
      throw error;
    }
  }

  // Get memory information
  private getMemoryInfo(): MemoryInfo | null {
    if (Platform.OS === 'web' && (global as any).performance?.memory) {
      const memory = (global as any).performance.memory;
      return {
        used: Math.round(memory.usedJSHeapSize / 1024 / 1024), // MB
        total: Math.round(memory.totalJSHeapSize / 1024 / 1024), // MB
        limit: Math.round(memory.jsHeapSizeLimit / 1024 / 1024), // MB
      };
    }
    return null;
  }

  // Check for critical performance issues
  private checkCriticalMetrics(metric: PerformanceMetric): void {
    const criticalThresholds = {
      js_thread_delay: 100, // 100ms delay is critical
      component_render: 100, // 100ms render time is slow
      network_request: 5000, // 5s network request is slow
      memory_usage: 100, // 100MB memory usage warning
    };

    const threshold = criticalThresholds[metric.name];
    if (threshold && metric.value > threshold) {
      console.warn(`[Performance] Critical metric: ${metric.name} = ${metric.value}`, metric.metadata);
    }
  }

  // Get performance summary
  getPerformanceSummary(): {
    metrics: PerformanceMetric[];
    networkMetrics: NetworkMetric[];
    renderMetrics: RenderMetric[];
    averages: Record<string, number>;
  } {
    const averages: Record<string, number> = {};
    
    // Calculate averages for each metric type
    const metricGroups = this.metrics.reduce((groups, metric) => {
      if (!groups[metric.name]) {
        groups[metric.name] = [];
      }
      groups[metric.name].push(metric.value);
      return groups;
    }, {} as Record<string, number[]>);

    Object.keys(metricGroups).forEach(name => {
      const values = metricGroups[name];
      averages[name] = values.reduce((sum, value) => sum + value, 0) / values.length;
    });

    return {
      metrics: this.metrics.slice(-100), // Last 100 metrics
      networkMetrics: this.networkMetrics.slice(-50), // Last 50 network requests
      renderMetrics: this.renderMetrics.slice(-50), // Last 50 renders
      averages,
    };
  }

  // Export performance data
  async exportPerformanceData(): Promise<string> {
    const data = {
      timestamp: Date.now(),
      platform: Platform.OS,
      summary: this.getPerformanceSummary(),
      deviceInfo: {
        version: Platform.Version,
        constants: Platform.constants,
      },
    };

    const jsonData = JSON.stringify(data, null, 2);
    
    try {
      await AsyncStorage.setItem('performance_export', jsonData);
      return jsonData;
    } catch (error) {
      console.error('Error exporting performance data:', error);
      return jsonData;
    }
  }

  // Clear all metrics
  clearMetrics(): void {
    this.metrics = [];
    this.networkMetrics = [];
    this.renderMetrics = [];
  }

  // Enable/disable monitoring
  setEnabled(enabled: boolean): void {
    this.isEnabled = enabled;
  }

  // Get current status
  isMonitoringEnabled(): boolean {
    return this.isEnabled;
  }

  // Track app startup performance
  trackAppStartup(): void {
    InteractionManager.runAfterInteractions(() => {
      this.trackMetric('app_startup_complete', Date.now());
    });
  }

  // Track screen navigation performance
  trackScreenNavigation(screenName: string, duration: number): void {
    this.trackMetric('screen_navigation', duration, { screen: screenName });
  }

  // Track list scroll performance
  trackListScroll(listName: string, itemCount: number, scrollDuration: number): void {
    this.trackMetric('list_scroll', scrollDuration, {
      list: listName,
      itemCount,
      itemsPerSecond: itemCount / (scrollDuration / 1000),
    });
  }
}

// Create singleton instance
export const performanceMonitor = new PerformanceMonitoringService();

// Higher-order component for tracking render performance
export function withPerformanceTracking<P extends object>(
  WrappedComponent: React.ComponentType<P>,
  componentName?: string
) {
  const displayName = componentName || WrappedComponent.displayName || WrappedComponent.name || 'Component';
  
  return React.forwardRef<any, P>((props, ref) => {
    const renderStart = Date.now();
    
    React.useEffect(() => {
      const renderTime = Date.now() - renderStart;
      performanceMonitor.trackRender(displayName, renderTime);
    });

    return React.createElement(WrappedComponent, { ...props, ref });
  });
}

export default performanceMonitor;
