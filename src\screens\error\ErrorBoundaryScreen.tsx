import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  ScrollView,
  StatusBar,
  Animated,
  Alert,
  Clipboard,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import { useNavigation } from '@react-navigation/native';
import { LinearGradient } from 'expo-linear-gradient';

interface ErrorBoundaryScreenProps {
  error?: Error;
  errorInfo?: any;
  onRetry?: () => void;
  onRestart?: () => void;
  errorType?: 'crash' | 'network' | 'permission' | 'maintenance' | 'not_found' | 'server_error';
}

const ErrorBoundaryScreen: React.FC<ErrorBoundaryScreenProps> = ({
  error,
  errorInfo,
  onRetry,
  onRestart,
  errorType = 'crash',
}) => {
  const navigation = useNavigation();
  const [fadeAnimation] = useState(new Animated.Value(0));
  const [showDetails, setShowDetails] = useState(false);

  useEffect(() => {
    Animated.timing(fadeAnimation, {
      toValue: 1,
      duration: 800,
      useNativeDriver: true,
    }).start();
  }, []);

  const getErrorConfig = () => {
    switch (errorType) {
      case 'network':
        return {
          icon: 'wifi-off',
          title: 'Connection Problem',
          subtitle: 'Unable to connect to our servers',
          description: 'Please check your internet connection and try again. If the problem persists, our servers might be temporarily unavailable.',
          color: '#f59e0b',
          actions: [
            { label: 'Retry', action: onRetry, primary: true },
            { label: 'Go Back', action: () => navigation.goBack(), primary: false },
          ],
        };
      case 'permission':
        return {
          icon: 'lock-closed',
          title: 'Permission Required',
          subtitle: 'Access denied',
          description: 'This feature requires additional permissions to work properly. Please grant the necessary permissions in your device settings.',
          color: '#dc2626',
          actions: [
            { label: 'Open Settings', action: onRetry, primary: true },
            { label: 'Go Back', action: () => navigation.goBack(), primary: false },
          ],
        };
      case 'maintenance':
        return {
          icon: 'construct',
          title: 'Under Maintenance',
          subtitle: 'We\'ll be back soon',
          description: 'We\'re currently performing scheduled maintenance to improve your experience. Please check back in a few minutes.',
          color: '#3b82f6',
          actions: [
            { label: 'Retry', action: onRetry, primary: true },
            { label: 'Check Status', action: () => {}, primary: false },
          ],
        };
      case 'not_found':
        return {
          icon: 'search',
          title: 'Page Not Found',
          subtitle: 'This page doesn\'t exist',
          description: 'The page you\'re looking for might have been moved, deleted, or you entered the wrong URL.',
          color: '#6b7280',
          actions: [
            { label: 'Go Home', action: () => navigation.navigate('Dashboard' as never), primary: true },
            { label: 'Go Back', action: () => navigation.goBack(), primary: false },
          ],
        };
      case 'server_error':
        return {
          icon: 'server',
          title: 'Server Error',
          subtitle: 'Something went wrong on our end',
          description: 'We\'re experiencing technical difficulties. Our team has been notified and is working to fix this issue.',
          color: '#dc2626',
          actions: [
            { label: 'Retry', action: onRetry, primary: true },
            { label: 'Report Issue', action: () => {}, primary: false },
          ],
        };
      default: // crash
        return {
          icon: 'warning',
          title: 'Oops! Something Went Wrong',
          subtitle: 'The app encountered an unexpected error',
          description: 'Don\'t worry, this happens sometimes. You can try restarting the app or contact our support team if the problem persists.',
          color: '#dc2626',
          actions: [
            { label: 'Restart App', action: onRestart, primary: true },
            { label: 'Report Bug', action: () => handleReportBug(), primary: false },
          ],
        };
    }
  };

  const handleReportBug = () => {
    const errorDetails = {
      error: error?.message || 'Unknown error',
      stack: error?.stack || 'No stack trace',
      timestamp: new Date().toISOString(),
      userAgent: 'FoodWay Rider App',
    };

    Alert.alert(
      'Report Bug',
      'Would you like to copy the error details to share with our support team?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Copy Details',
          onPress: () => {
            Clipboard.setString(JSON.stringify(errorDetails, null, 2));
            Alert.alert('Copied!', 'Error details copied to clipboard');
          },
        },
      ]
    );
  };

  const config = getErrorConfig();

  return (
    <View style={{ flex: 1, backgroundColor: '#f1f5f9' }}>
      <SafeAreaView style={{ flex: 1 }}>
        <StatusBar barStyle="light-content" backgroundColor={config.color} />
        
        {/* Enhanced Header */}
        <View style={{
          backgroundColor: config.color,
          paddingHorizontal: 20,
          paddingTop: 20,
          paddingBottom: 28,
          borderBottomLeftRadius: 28,
          borderBottomRightRadius: 28,
          shadowColor: config.color,
          shadowOffset: { width: 0, height: 12 },
          shadowOpacity: 0.3,
          shadowRadius: 20,
          elevation: 16,
        }}>
          <View style={{
            alignItems: 'center',
            marginBottom: 20,
          }}>
            <View style={{
              width: 80,
              height: 80,
              backgroundColor: 'rgba(255,255,255,0.2)',
              borderRadius: 40,
              alignItems: 'center',
              justifyContent: 'center',
              marginBottom: 16,
              borderWidth: 3,
              borderColor: 'rgba(255,255,255,0.3)',
            }}>
              <Ionicons name={config.icon as any} size={40} color="white" />
            </View>
            
            <Text style={{
              fontSize: 24,
              fontWeight: 'bold',
              color: 'white',
              marginBottom: 8,
              textAlign: 'center',
            }}>
              {config.title}
            </Text>
            
            <Text style={{
              fontSize: 16,
              color: 'rgba(255,255,255,0.9)',
              textAlign: 'center',
              fontWeight: '500',
            }}>
              {config.subtitle}
            </Text>
          </View>
        </View>

        <ScrollView style={{ flex: 1 }} showsVerticalScrollIndicator={false}>
          <Animated.View style={{ opacity: fadeAnimation }}>
            {/* Main Content */}
            <View style={{
              backgroundColor: 'white',
              marginHorizontal: 20,
              marginTop: -12,
              borderRadius: 20,
              padding: 24,
              shadowColor: config.color,
              shadowOffset: { width: 0, height: 8 },
              shadowOpacity: 0.1,
              shadowRadius: 16,
              elevation: 8,
              borderWidth: 1,
              borderColor: `${config.color}20`,
            }}>
              <Text style={{
                fontSize: 16,
                color: '#6b7280',
                lineHeight: 24,
                textAlign: 'center',
                marginBottom: 24,
              }}>
                {config.description}
              </Text>

              {/* Action Buttons */}
              <View style={{ gap: 12 }}>
                {config.actions.map((action, index) => (
                  <TouchableOpacity
                    key={index}
                    onPress={action.action}
                    style={{
                      backgroundColor: action.primary ? config.color : 'transparent',
                      borderWidth: action.primary ? 0 : 2,
                      borderColor: action.primary ? 'transparent' : config.color,
                      borderRadius: 16,
                      paddingVertical: 16,
                      paddingHorizontal: 24,
                      alignItems: 'center',
                    }}
                  >
                    <Text style={{
                      fontSize: 16,
                      fontWeight: 'bold',
                      color: action.primary ? 'white' : config.color,
                    }}>
                      {action.label}
                    </Text>
                  </TouchableOpacity>
                ))}
              </View>
            </View>

            {/* Error Details (for crash type) */}
            {errorType === 'crash' && error && (
              <View style={{
                backgroundColor: 'white',
                marginHorizontal: 20,
                marginTop: 16,
                borderRadius: 20,
                shadowColor: '#000',
                shadowOffset: { width: 0, height: 4 },
                shadowOpacity: 0.1,
                shadowRadius: 8,
                elevation: 4,
              }}>
                <TouchableOpacity
                  onPress={() => setShowDetails(!showDetails)}
                  style={{
                    flexDirection: 'row',
                    alignItems: 'center',
                    justifyContent: 'space-between',
                    padding: 20,
                  }}
                >
                  <Text style={{
                    fontSize: 16,
                    fontWeight: '600',
                    color: '#111827',
                  }}>
                    Technical Details
                  </Text>
                  <Ionicons
                    name={showDetails ? 'chevron-up' : 'chevron-down'}
                    size={20}
                    color="#6b7280"
                  />
                </TouchableOpacity>

                {showDetails && (
                  <View style={{
                    paddingHorizontal: 20,
                    paddingBottom: 20,
                    borderTopWidth: 1,
                    borderTopColor: '#f3f4f6',
                  }}>
                    <Text style={{
                      fontSize: 12,
                      color: '#6b7280',
                      fontFamily: 'monospace',
                      backgroundColor: '#f8fafc',
                      padding: 12,
                      borderRadius: 8,
                      marginTop: 12,
                    }}>
                      {error.message}
                      {'\n\n'}
                      {error.stack}
                    </Text>
                  </View>
                )}
              </View>
            )}

            {/* Help Section */}
            <View style={{
              backgroundColor: '#f8fafc',
              marginHorizontal: 20,
              marginTop: 16,
              borderRadius: 20,
              padding: 20,
              borderWidth: 1,
              borderColor: '#e2e8f0',
            }}>
              <View style={{
                flexDirection: 'row',
                alignItems: 'center',
                marginBottom: 12,
              }}>
                <Ionicons name="help-circle" size={20} color="#6b7280" />
                <Text style={{
                  fontSize: 16,
                  fontWeight: '600',
                  color: '#111827',
                  marginLeft: 8,
                }}>
                  Need Help?
                </Text>
              </View>
              
              <Text style={{
                fontSize: 14,
                color: '#6b7280',
                lineHeight: 20,
                marginBottom: 16,
              }}>
                If you continue to experience issues, please contact our support team. We're here to help!
              </Text>

              <TouchableOpacity
                onPress={() => navigation.navigate('Support' as never)}
                style={{
                  backgroundColor: '#3b82f6',
                  borderRadius: 12,
                  paddingVertical: 12,
                  paddingHorizontal: 16,
                  alignItems: 'center',
                }}
              >
                <Text style={{
                  fontSize: 14,
                  fontWeight: '600',
                  color: 'white',
                }}>
                  Contact Support
                </Text>
              </TouchableOpacity>
            </View>

            <View style={{ height: 32 }} />
          </Animated.View>
        </ScrollView>
      </SafeAreaView>
    </View>
  );
};

export default ErrorBoundaryScreen;

// Export a React Error Boundary component
export class AppErrorBoundary extends React.Component<
  { children: React.ReactNode; fallback?: React.ComponentType<any> },
  { hasError: boolean; error?: Error; errorInfo?: any }
> {
  constructor(props: any) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error: Error) {
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, errorInfo: any) {
    console.error('Error Boundary caught an error:', error, errorInfo);
    this.setState({ error, errorInfo });

    // Log to crash reporting service
    // crashlytics().recordError(error);
  }

  render() {
    if (this.state.hasError) {
      const FallbackComponent = this.props.fallback || ErrorBoundaryScreen;
      return (
        <FallbackComponent
          error={this.state.error}
          errorInfo={this.state.errorInfo}
          onRetry={() => this.setState({ hasError: false, error: undefined, errorInfo: undefined })}
          onRestart={() => {
            // Restart app logic
            this.setState({ hasError: false, error: undefined, errorInfo: undefined });
          }}
        />
      );
    }

    return this.props.children;
  }
}

// Export error handling utilities
export const ErrorUtils = {
  showNetworkError: () => <ErrorBoundaryScreen errorType="network" />,
  showPermissionError: () => <ErrorBoundaryScreen errorType="permission" />,
  showMaintenanceError: () => <ErrorBoundaryScreen errorType="maintenance" />,
  showNotFoundError: () => <ErrorBoundaryScreen errorType="not_found" />,
  showServerError: () => <ErrorBoundaryScreen errorType="server_error" />,
};
