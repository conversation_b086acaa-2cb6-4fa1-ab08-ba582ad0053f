import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  RefreshControl,
  Modal,
  Animated,
  Dimensions,
  StatusBar,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import { useNavigation } from '@react-navigation/native';
import { LinearGradient } from 'expo-linear-gradient';
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>hart } from 'react-native-chart-kit';
import { useDeliveryHistory } from '../../context/DeliveryHistoryContext';
import { formatCurrency, formatDate, formatTime } from '../../utils/helpers';
import { getStatusColor, getStatusText, getOrderTypeIcon } from '../../utils/orderHelpers';
import { OrderStatus } from '../../types/deliveryHistory';

const { width: screenWidth } = Dimensions.get('window');

const OrderHistoryAnalyticsScreen: React.FC = () => {
  const navigation = useNavigation();
  const { state, fetchOrderHistory, fetchPerformanceMetrics } = useDeliveryHistory();
  
  const [activeTab, setActiveTab] = useState<'history' | 'performance'>('history');
  const [refreshing, setRefreshing] = useState(false);
  const [showFilters, setShowFilters] = useState(false);
  const [selectedPeriod, setSelectedPeriod] = useState<'today' | 'week' | 'month' | 'all'>('month');
  const [statusFilter, setStatusFilter] = useState<OrderStatus | 'all'>('all');
  const [dateFilter, setDateFilter] = useState<'today' | 'week' | 'month' | 'custom'>('month');

  // Animation refs
  const fadeAnimation = useRef(new Animated.Value(0)).current;
  const slideAnimation = useRef(new Animated.Value(50)).current;
  const tabAnimation = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    loadData();
    
    // Start entrance animations
    Animated.parallel([
      Animated.timing(fadeAnimation, {
        toValue: 1,
        duration: 800,
        useNativeDriver: true,
      }),
      Animated.timing(slideAnimation, {
        toValue: 0,
        duration: 800,
        useNativeDriver: true,
      }),
    ]).start();
  }, []);

  const loadData = async () => {
    try {
      await Promise.all([
        fetchOrderHistory(),
        fetchPerformanceMetrics(selectedPeriod),
      ]);
    } catch (error) {
      console.error('Failed to load data:', error);
    }
  };

  const onRefresh = async () => {
    setRefreshing(true);
    await loadData();
    setRefreshing(false);
  };

  const handleTabChange = (tab: 'history' | 'performance') => {
    setActiveTab(tab);
    Animated.timing(tabAnimation, {
      toValue: 1,
      duration: 300,
      useNativeDriver: true,
    }).start(() => {
      tabAnimation.setValue(0);
    });
  };

  const getStatusColorWithIcon = (status: OrderStatus) => {
    const color = getStatusColor(status);
    switch (status) {
      case OrderStatus.DELIVERED:
        return { color, bg: '#d1fae5', icon: 'checkmark-circle' };
      case OrderStatus.CANCELLED:
        return { color, bg: '#fee2e2', icon: 'close-circle' };
      case OrderStatus.IN_PROGRESS:
        return { color, bg: '#fef3c7', icon: 'time' };
      default:
        return { color, bg: '#f3f4f6', icon: 'help-circle' };
    }
  };

  const renderHeader = () => (
    <LinearGradient
      colors={['#ef4444', '#dc2626']}
      style={{
        paddingTop: StatusBar.currentHeight || 0,
        paddingBottom: 20,
      }}
    >
      <SafeAreaView>
        <View style={{
          flexDirection: 'row',
          alignItems: 'center',
          justifyContent: 'space-between',
          paddingHorizontal: 20,
          paddingTop: 16,
        }}>
          <TouchableOpacity
            onPress={() => navigation.goBack()}
            style={{
              width: 40,
              height: 40,
              borderRadius: 20,
              backgroundColor: 'rgba(255,255,255,0.2)',
              alignItems: 'center',
              justifyContent: 'center',
            }}
          >
            <Ionicons name="arrow-back" size={24} color="white" />
          </TouchableOpacity>

          <Text style={{
            fontSize: 20,
            fontWeight: 'bold',
            color: 'white',
          }}>
            Order Analytics
          </Text>

          <TouchableOpacity
            onPress={() => setShowFilters(true)}
            style={{
              width: 40,
              height: 40,
              borderRadius: 20,
              backgroundColor: 'rgba(255,255,255,0.2)',
              alignItems: 'center',
              justifyContent: 'center',
            }}
          >
            <Ionicons name="filter" size={20} color="white" />
          </TouchableOpacity>
        </View>
      </SafeAreaView>
    </LinearGradient>
  );

  const renderTabSection = () => {
    const tabs = [
      { key: 'history', label: 'Order History', icon: 'list' },
      { key: 'performance', label: 'Performance', icon: 'analytics' },
    ] as const;

    return (
      <View style={{
        marginHorizontal: 20,
        marginVertical: 16,
      }}>
        <View style={{
          flexDirection: 'row',
          backgroundColor: 'white',
          borderRadius: 16,
          padding: 4,
          shadowColor: '#000',
          shadowOffset: { width: 0, height: 2 },
          shadowOpacity: 0.1,
          shadowRadius: 8,
          elevation: 3,
        }}>
          {tabs.map((tab) => (
            <TouchableOpacity
              key={tab.key}
              onPress={() => handleTabChange(tab.key)}
              style={{
                flex: 1,
                paddingVertical: 12,
                paddingHorizontal: 16,
                borderRadius: 12,
                backgroundColor: activeTab === tab.key ? '#f97316' : 'transparent',
                flexDirection: 'row',
                alignItems: 'center',
                justifyContent: 'center',
              }}
            >
              <Ionicons 
                name={tab.icon as any} 
                size={16} 
                color={activeTab === tab.key ? 'white' : '#6b7280'} 
                style={{ marginRight: 6 }}
              />
              <Text style={{
                fontSize: 14,
                fontWeight: activeTab === tab.key ? '600' : '500',
                color: activeTab === tab.key ? 'white' : '#6b7280',
              }}>
                {tab.label}
              </Text>
            </TouchableOpacity>
          ))}
        </View>
      </View>
    );
  };

  return (
    <View style={{ flex: 1, backgroundColor: '#f8fafc' }}>
      <StatusBar barStyle="light-content" backgroundColor="#f97316" />
      
      {renderHeader()}

      <Animated.View
        style={{
          flex: 1,
          opacity: fadeAnimation,
          transform: [{ translateY: slideAnimation }],
        }}
      >
        {renderTabSection()}

        <ScrollView
          style={{ flex: 1 }}
          refreshControl={
            <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
          }
          showsVerticalScrollIndicator={false}
          contentContainerStyle={{ paddingBottom: 100 }}
        >
          <Animated.View
            style={{
              opacity: tabAnimation.interpolate({
                inputRange: [0, 1],
                outputRange: [1, 0.5],
              }),
              transform: [{
                scale: tabAnimation.interpolate({
                  inputRange: [0, 1],
                  outputRange: [1, 0.95],
                }),
              }],
            }}
          >
            {activeTab === 'history' ? renderOrderHistory() : renderPerformanceAnalytics()}
          </Animated.View>
        </ScrollView>
      </Animated.View>

      {renderFiltersModal()}
    </View>
  );
};

  const renderOrderHistory = () => {
    // Mock order data
    const mockOrders = [
      {
        id: '1',
        orderNumber: 'FW-2024-001',
        status: OrderStatus.DELIVERED,
        totalAmount: 1250,
        deliveryFee: 150,
        tip: 100,
        timeline: {
          orderPlaced: '2024-01-22T14:30:00Z',
          orderDelivered: '2024-01-22T15:15:00Z',
        },
        restaurant: { name: 'KFC DHA', category: 'Fast Food' },
        customer: { name: 'Ahmed Ali' },
        rating: { overall: 5 },
        metrics: { deliveryTime: 25, totalDistance: 3.2 },
      },
      {
        id: '2',
        orderNumber: 'FW-2024-002',
        status: OrderStatus.DELIVERED,
        totalAmount: 890,
        deliveryFee: 120,
        tip: 50,
        timeline: {
          orderPlaced: '2024-01-22T12:15:00Z',
          orderDelivered: '2024-01-22T12:55:00Z',
        },
        restaurant: { name: 'McDonald\'s Gulberg', category: 'Fast Food' },
        customer: { name: 'Sara Khan' },
        rating: { overall: 4 },
        metrics: { deliveryTime: 28, totalDistance: 4.1 },
      },
      {
        id: '3',
        orderNumber: 'FW-2024-003',
        status: OrderStatus.CANCELLED,
        totalAmount: 650,
        deliveryFee: 100,
        tip: 0,
        timeline: {
          orderPlaced: '2024-01-21T19:20:00Z',
          orderCancelled: '2024-01-21T19:35:00Z',
        },
        restaurant: { name: 'Pizza Hut MM Alam', category: 'Pizza' },
        customer: { name: 'Hassan Ahmed' },
        rating: null,
        metrics: { deliveryTime: 0, totalDistance: 0 },
      },
    ];

    return (
      <View style={{ marginHorizontal: 20 }}>
        {/* Summary Stats */}
        <View style={{
          flexDirection: 'row',
          marginBottom: 20,
          gap: 12,
        }}>
          <View style={{
            flex: 1,
            backgroundColor: 'white',
            borderRadius: 16,
            padding: 16,
            alignItems: 'center',
            shadowColor: '#000',
            shadowOffset: { width: 0, height: 2 },
            shadowOpacity: 0.1,
            shadowRadius: 8,
            elevation: 3,
          }}>
            <Ionicons name="checkmark-circle" size={24} color="#10b981" />
            <Text style={{
              fontSize: 24,
              fontWeight: 'bold',
              color: '#111827',
              marginTop: 8,
            }}>
              142
            </Text>
            <Text style={{
              fontSize: 12,
              color: '#6b7280',
              textAlign: 'center',
            }}>
              Completed
            </Text>
          </View>

          <View style={{
            flex: 1,
            backgroundColor: 'white',
            borderRadius: 16,
            padding: 16,
            alignItems: 'center',
            shadowColor: '#000',
            shadowOffset: { width: 0, height: 2 },
            shadowOpacity: 0.1,
            shadowRadius: 8,
            elevation: 3,
          }}>
            <Ionicons name="time" size={24} color="#f59e0b" />
            <Text style={{
              fontSize: 24,
              fontWeight: 'bold',
              color: '#111827',
              marginTop: 8,
            }}>
              22.5
            </Text>
            <Text style={{
              fontSize: 12,
              color: '#6b7280',
              textAlign: 'center',
            }}>
              Avg Time (min)
            </Text>
          </View>

          <View style={{
            flex: 1,
            backgroundColor: 'white',
            borderRadius: 16,
            padding: 16,
            alignItems: 'center',
            shadowColor: '#000',
            shadowOffset: { width: 0, height: 2 },
            shadowOpacity: 0.1,
            shadowRadius: 8,
            elevation: 3,
          }}>
            <Ionicons name="star" size={24} color="#f97316" />
            <Text style={{
              fontSize: 24,
              fontWeight: 'bold',
              color: '#111827',
              marginTop: 8,
            }}>
              4.6
            </Text>
            <Text style={{
              fontSize: 12,
              color: '#6b7280',
              textAlign: 'center',
            }}>
              Avg Rating
            </Text>
          </View>
        </View>

        {/* Order Cards */}
        <View style={{
          backgroundColor: 'white',
          borderRadius: 16,
          padding: 20,
          shadowColor: '#000',
          shadowOffset: { width: 0, height: 2 },
          shadowOpacity: 0.1,
          shadowRadius: 8,
          elevation: 3,
        }}>
          <Text style={{
            fontSize: 18,
            fontWeight: 'bold',
            color: '#111827',
            marginBottom: 16,
          }}>
            Recent Orders
          </Text>

          {mockOrders.map((order, index) => {
            const statusStyle = getStatusColorWithIcon(order.status);

            return (
              <TouchableOpacity
                key={order.id}
                style={{
                  paddingVertical: 16,
                  borderBottomWidth: index < mockOrders.length - 1 ? 1 : 0,
                  borderBottomColor: '#f3f4f6',
                }}
              >
                {/* Order Header */}
                <View style={{
                  flexDirection: 'row',
                  alignItems: 'center',
                  justifyContent: 'space-between',
                  marginBottom: 8,
                }}>
                  <View style={{
                    flexDirection: 'row',
                    alignItems: 'center',
                  }}>
                    <Text style={{
                      fontSize: 16,
                      fontWeight: '600',
                      color: '#111827',
                      marginRight: 8,
                    }}>
                      {order.orderNumber}
                    </Text>
                    <View style={{
                      paddingHorizontal: 8,
                      paddingVertical: 2,
                      borderRadius: 12,
                      backgroundColor: statusStyle.bg,
                      flexDirection: 'row',
                      alignItems: 'center',
                    }}>
                      <Ionicons
                        name={statusStyle.icon as any}
                        size={12}
                        color={statusStyle.color}
                        style={{ marginRight: 4 }}
                      />
                      <Text style={{
                        fontSize: 12,
                        fontWeight: '600',
                        color: statusStyle.color,
                        textTransform: 'capitalize',
                      }}>
                        {order.status.replace('_', ' ')}
                      </Text>
                    </View>
                  </View>

                  <Text style={{
                    fontSize: 18,
                    fontWeight: 'bold',
                    color: '#111827',
                  }}>
                    {formatCurrency(order.totalAmount)}
                  </Text>
                </View>

                {/* Order Details */}
                <View style={{
                  flexDirection: 'row',
                  justifyContent: 'space-between',
                  marginBottom: 8,
                }}>
                  <View style={{ flex: 1 }}>
                    <Text style={{
                      fontSize: 14,
                      fontWeight: '500',
                      color: '#111827',
                      marginBottom: 2,
                    }}>
                      {order.restaurant.name}
                    </Text>
                    <Text style={{
                      fontSize: 12,
                      color: '#6b7280',
                    }}>
                      {order.customer.name}
                    </Text>
                  </View>

                  <View style={{ alignItems: 'flex-end' }}>
                    <Text style={{
                      fontSize: 12,
                      color: '#6b7280',
                      marginBottom: 2,
                    }}>
                      {formatTime(order.timeline.orderPlaced)}
                    </Text>
                    {order.rating && (
                      <View style={{
                        flexDirection: 'row',
                        alignItems: 'center',
                      }}>
                        <Ionicons name="star" size={12} color="#f59e0b" />
                        <Text style={{
                          fontSize: 12,
                          color: '#6b7280',
                          marginLeft: 2,
                        }}>
                          {order.rating.overall}
                        </Text>
                      </View>
                    )}
                  </View>
                </View>

                {/* Metrics */}
                {order.status === OrderStatus.DELIVERED && (
                  <View style={{
                    flexDirection: 'row',
                    alignItems: 'center',
                    gap: 16,
                  }}>
                    <View style={{
                      flexDirection: 'row',
                      alignItems: 'center',
                    }}>
                      <Ionicons name="time" size={14} color="#6b7280" />
                      <Text style={{
                        fontSize: 12,
                        color: '#6b7280',
                        marginLeft: 4,
                      }}>
                        {order.metrics.deliveryTime} min
                      </Text>
                    </View>

                    <View style={{
                      flexDirection: 'row',
                      alignItems: 'center',
                    }}>
                      <Ionicons name="location" size={14} color="#6b7280" />
                      <Text style={{
                        fontSize: 12,
                        color: '#6b7280',
                        marginLeft: 4,
                      }}>
                        {order.metrics.totalDistance} km
                      </Text>
                    </View>

                    <View style={{
                      flexDirection: 'row',
                      alignItems: 'center',
                    }}>
                      <Ionicons name="cash" size={14} color="#10b981" />
                      <Text style={{
                        fontSize: 12,
                        color: '#10b981',
                        marginLeft: 4,
                      }}>
                        +{formatCurrency(order.tip)} tip
                      </Text>
                    </View>
                  </View>
                )}
              </TouchableOpacity>
            );
          })}

          {/* View All Button */}
          <TouchableOpacity
            style={{
              marginTop: 16,
              paddingVertical: 12,
              alignItems: 'center',
              borderTopWidth: 1,
              borderTopColor: '#f3f4f6',
            }}
          >
            <Text style={{
              fontSize: 16,
              fontWeight: '600',
              color: '#f97316',
            }}>
              View All Orders
            </Text>
          </TouchableOpacity>
        </View>
      </View>
    );
  };

  const renderPerformanceAnalytics = () => {
    // Mock chart data
    const ratingTrendData = {
      labels: ['Week 1', 'Week 2', 'Week 3', 'Week 4'],
      datasets: [{
        data: [4.2, 4.5, 4.6, 4.8],
        color: (opacity = 1) => `rgba(249, 115, 22, ${opacity})`,
        strokeWidth: 3,
      }],
    };

    const deliveryTimeData = {
      labels: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'],
      datasets: [{
        data: [25, 22, 28, 20, 24, 30, 26],
        color: (opacity = 1) => `rgba(16, 185, 129, ${opacity})`,
        strokeWidth: 2,
      }],
    };

    const earningsData = {
      labels: ['Week 1', 'Week 2', 'Week 3', 'Week 4'],
      datasets: [{
        data: [1250, 1480, 1320, 1650],
      }],
    };

    const performanceDistribution = [
      { name: '5 Star', population: 65, color: '#10b981', legendFontColor: '#111827' },
      { name: '4 Star', population: 25, color: '#f59e0b', legendFontColor: '#111827' },
      { name: '3 Star', population: 8, color: '#ef4444', legendFontColor: '#111827' },
      { name: '2 Star', population: 2, color: '#dc2626', legendFontColor: '#111827' },
    ];

    const chartConfig = {
      backgroundColor: '#ffffff',
      backgroundGradientFrom: '#ffffff',
      backgroundGradientTo: '#ffffff',
      decimalPlaces: 1,
      color: (opacity = 1) => `rgba(0, 0, 0, ${opacity})`,
      labelColor: (opacity = 1) => `rgba(107, 114, 128, ${opacity})`,
      style: { borderRadius: 16 },
      propsForDots: {
        r: '6',
        strokeWidth: '2',
        stroke: '#ef4444',
      },
    };

    return (
      <View style={{ marginHorizontal: 20 }}>
        {/* Performance Overview */}
        <View style={{
          backgroundColor: 'white',
          borderRadius: 16,
          padding: 20,
          marginBottom: 20,
          shadowColor: '#000',
          shadowOffset: { width: 0, height: 2 },
          shadowOpacity: 0.1,
          shadowRadius: 8,
          elevation: 3,
        }}>
          <View style={{
            flexDirection: 'row',
            alignItems: 'center',
            marginBottom: 16,
          }}>
            <Ionicons name="trending-up" size={24} color="#f97316" />
            <Text style={{
              fontSize: 18,
              fontWeight: 'bold',
              color: '#111827',
              marginLeft: 8,
            }}>
              Performance Overview
            </Text>
          </View>

          <View style={{
            flexDirection: 'row',
            flexWrap: 'wrap',
            gap: 12,
          }}>
            <View style={{
              flex: 1,
              minWidth: '45%',
              backgroundColor: '#f8fafc',
              borderRadius: 12,
              padding: 16,
              alignItems: 'center',
            }}>
              <Ionicons name="star" size={20} color="#f59e0b" />
              <Text style={{
                fontSize: 20,
                fontWeight: 'bold',
                color: '#111827',
                marginTop: 8,
              }}>
                4.6
              </Text>
              <Text style={{
                fontSize: 12,
                color: '#6b7280',
                textAlign: 'center',
              }}>
                Average Rating
              </Text>
            </View>

            <View style={{
              flex: 1,
              minWidth: '45%',
              backgroundColor: '#f8fafc',
              borderRadius: 12,
              padding: 16,
              alignItems: 'center',
            }}>
              <Ionicons name="checkmark-circle" size={20} color="#10b981" />
              <Text style={{
                fontSize: 20,
                fontWeight: 'bold',
                color: '#111827',
                marginTop: 8,
              }}>
                91%
              </Text>
              <Text style={{
                fontSize: 12,
                color: '#6b7280',
                textAlign: 'center',
              }}>
                Completion Rate
              </Text>
            </View>

            <View style={{
              flex: 1,
              minWidth: '45%',
              backgroundColor: '#f8fafc',
              borderRadius: 12,
              padding: 16,
              alignItems: 'center',
            }}>
              <Ionicons name="time" size={20} color="#3b82f6" />
              <Text style={{
                fontSize: 20,
                fontWeight: 'bold',
                color: '#111827',
                marginTop: 8,
              }}>
                22.5
              </Text>
              <Text style={{
                fontSize: 12,
                color: '#6b7280',
                textAlign: 'center',
              }}>
                Avg Time (min)
              </Text>
            </View>

            <View style={{
              flex: 1,
              minWidth: '45%',
              backgroundColor: '#f8fafc',
              borderRadius: 12,
              padding: 16,
              alignItems: 'center',
            }}>
              <Ionicons name="cash" size={20} color="#10b981" />
              <Text style={{
                fontSize: 20,
                fontWeight: 'bold',
                color: '#111827',
                marginTop: 8,
              }}>
                {formatCurrency(293)}
              </Text>
              <Text style={{
                fontSize: 12,
                color: '#6b7280',
                textAlign: 'center',
              }}>
                Avg Earnings
              </Text>
            </View>
          </View>
        </View>

        {/* Rating Trends Chart */}
        <View style={{
          backgroundColor: 'white',
          borderRadius: 16,
          padding: 20,
          marginBottom: 20,
          shadowColor: '#000',
          shadowOffset: { width: 0, height: 2 },
          shadowOpacity: 0.1,
          shadowRadius: 8,
          elevation: 3,
        }}>
          <View style={{
            flexDirection: 'row',
            alignItems: 'center',
            marginBottom: 16,
          }}>
            <Ionicons name="star" size={20} color="#f59e0b" />
            <Text style={{
              fontSize: 16,
              fontWeight: '600',
              color: '#111827',
              marginLeft: 8,
            }}>
              Rating Trends
            </Text>
          </View>

          <LineChart
            data={ratingTrendData}
            width={screenWidth - 80}
            height={200}
            chartConfig={chartConfig}
            bezier
            style={{
              marginVertical: 8,
              borderRadius: 16,
            }}
          />
        </View>

        {/* Delivery Time Analysis */}
        <View style={{
          backgroundColor: 'white',
          borderRadius: 16,
          padding: 20,
          marginBottom: 20,
          shadowColor: '#000',
          shadowOffset: { width: 0, height: 2 },
          shadowOpacity: 0.1,
          shadowRadius: 8,
          elevation: 3,
        }}>
          <View style={{
            flexDirection: 'row',
            alignItems: 'center',
            marginBottom: 16,
          }}>
            <Ionicons name="time" size={20} color="#3b82f6" />
            <Text style={{
              fontSize: 16,
              fontWeight: '600',
              color: '#111827',
              marginLeft: 8,
            }}>
              Delivery Time by Day
            </Text>
          </View>

          <LineChart
            data={deliveryTimeData}
            width={screenWidth - 80}
            height={200}
            chartConfig={{
              ...chartConfig,
              color: (opacity = 1) => `rgba(16, 185, 129, ${opacity})`,
              propsForDots: {
                r: '4',
                strokeWidth: '2',
                stroke: '#10b981',
              },
            }}
            style={{
              marginVertical: 8,
              borderRadius: 16,
            }}
          />
        </View>

        {/* Weekly Earnings Chart */}
        <View style={{
          backgroundColor: 'white',
          borderRadius: 16,
          padding: 20,
          marginBottom: 20,
          shadowColor: '#000',
          shadowOffset: { width: 0, height: 2 },
          shadowOpacity: 0.1,
          shadowRadius: 8,
          elevation: 3,
        }}>
          <View style={{
            flexDirection: 'row',
            alignItems: 'center',
            marginBottom: 16,
          }}>
            <Ionicons name="cash" size={20} color="#10b981" />
            <Text style={{
              fontSize: 16,
              fontWeight: '600',
              color: '#111827',
              marginLeft: 8,
            }}>
              Weekly Earnings
            </Text>
          </View>

          <BarChart
            data={earningsData}
            width={screenWidth - 80}
            height={200}
            chartConfig={{
              ...chartConfig,
              color: (opacity = 1) => `rgba(16, 185, 129, ${opacity})`,
            }}
            style={{
              marginVertical: 8,
              borderRadius: 16,
            }}
          />
        </View>

        {/* Rating Distribution */}
        <View style={{
          backgroundColor: 'white',
          borderRadius: 16,
          padding: 20,
          marginBottom: 20,
          shadowColor: '#000',
          shadowOffset: { width: 0, height: 2 },
          shadowOpacity: 0.1,
          shadowRadius: 8,
          elevation: 3,
        }}>
          <View style={{
            flexDirection: 'row',
            alignItems: 'center',
            marginBottom: 16,
          }}>
            <Ionicons name="pie-chart" size={20} color="#f97316" />
            <Text style={{
              fontSize: 16,
              fontWeight: '600',
              color: '#111827',
              marginLeft: 8,
            }}>
              Rating Distribution
            </Text>
          </View>

          <PieChart
            data={performanceDistribution}
            width={screenWidth - 80}
            height={200}
            chartConfig={chartConfig}
            accessor="population"
            backgroundColor="transparent"
            paddingLeft="15"
            style={{
              marginVertical: 8,
              borderRadius: 16,
            }}
          />
        </View>
      </View>
    );
  };

  const renderFiltersModal = () => (
    <Modal
      visible={showFilters}
      animationType="slide"
      transparent={true}
      onRequestClose={() => setShowFilters(false)}
    >
      <View style={{
        flex: 1,
        backgroundColor: 'rgba(0,0,0,0.5)',
        justifyContent: 'flex-end',
      }}>
        <View style={{
          backgroundColor: 'white',
          borderTopLeftRadius: 24,
          borderTopRightRadius: 24,
          padding: 24,
          maxHeight: '80%',
        }}>
          {/* Header */}
          <View style={{
            flexDirection: 'row',
            alignItems: 'center',
            justifyContent: 'space-between',
            marginBottom: 24,
          }}>
            <Text style={{
              fontSize: 20,
              fontWeight: 'bold',
              color: '#111827',
            }}>
              Filters
            </Text>
            <TouchableOpacity
              onPress={() => setShowFilters(false)}
              style={{
                width: 32,
                height: 32,
                borderRadius: 16,
                backgroundColor: '#f3f4f6',
                alignItems: 'center',
                justifyContent: 'center',
              }}
            >
              <Ionicons name="close" size={20} color="#6b7280" />
            </TouchableOpacity>
          </View>

          {/* Date Filter */}
          <View style={{ marginBottom: 24 }}>
            <Text style={{
              fontSize: 16,
              fontWeight: '600',
              color: '#111827',
              marginBottom: 12,
            }}>
              Date Range
            </Text>
            <View style={{
              flexDirection: 'row',
              flexWrap: 'wrap',
              gap: 8,
            }}>
              {['today', 'week', 'month', 'custom'].map((period) => (
                <TouchableOpacity
                  key={period}
                  onPress={() => setDateFilter(period as any)}
                  style={{
                    paddingHorizontal: 16,
                    paddingVertical: 8,
                    borderRadius: 20,
                    backgroundColor: dateFilter === period ? '#f97316' : '#f3f4f6',
                    borderWidth: 1,
                    borderColor: dateFilter === period ? '#f97316' : '#e5e7eb',
                  }}
                >
                  <Text style={{
                    fontSize: 14,
                    fontWeight: '500',
                    color: dateFilter === period ? 'white' : '#6b7280',
                    textTransform: 'capitalize',
                  }}>
                    {period}
                  </Text>
                </TouchableOpacity>
              ))}
            </View>
          </View>

          {/* Status Filter */}
          <View style={{ marginBottom: 24 }}>
            <Text style={{
              fontSize: 16,
              fontWeight: '600',
              color: '#111827',
              marginBottom: 12,
            }}>
              Order Status
            </Text>
            <View style={{
              flexDirection: 'row',
              flexWrap: 'wrap',
              gap: 8,
            }}>
              {['all', OrderStatus.DELIVERED, OrderStatus.CANCELLED, OrderStatus.IN_PROGRESS].map((status) => (
                <TouchableOpacity
                  key={status}
                  onPress={() => setStatusFilter(status as any)}
                  style={{
                    paddingHorizontal: 16,
                    paddingVertical: 8,
                    borderRadius: 20,
                    backgroundColor: statusFilter === status ? '#ef4444' : '#f3f4f6',
                    borderWidth: 1,
                    borderColor: statusFilter === status ? '#ef4444' : '#e5e7eb',
                  }}
                >
                  <Text style={{
                    fontSize: 14,
                    fontWeight: '500',
                    color: statusFilter === status ? 'white' : '#6b7280',
                    textTransform: 'capitalize',
                  }}>
                    {status === 'all' ? 'All' : status.replace('_', ' ')}
                  </Text>
                </TouchableOpacity>
              ))}
            </View>
          </View>

          {/* Apply Button */}
          <TouchableOpacity
            onPress={() => {
              setShowFilters(false);
              // Apply filters logic here
            }}
            style={{
              backgroundColor: '#f97316',
              borderRadius: 16,
              padding: 16,
              alignItems: 'center',
              marginTop: 16,
            }}
          >
            <Text style={{
              fontSize: 16,
              fontWeight: '600',
              color: 'white',
            }}>
              Apply Filters
            </Text>
          </TouchableOpacity>
        </View>
      </View>
    </Modal>
  );

export default OrderHistoryAnalyticsScreen;
