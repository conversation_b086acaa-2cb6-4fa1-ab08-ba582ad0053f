import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StatusBar,
  Animated,
  Alert,
  Vibration,
  Linking,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import { useNavigation } from '@react-navigation/native';
import { LinearGradient } from 'expo-linear-gradient';
import * as Location from 'expo-location';

interface LocationData {
  latitude: number;
  longitude: number;
  address?: string;
  timestamp: string;
}

interface EmergencyContact {
  id: string;
  name: string;
  phoneNumber: string;
  type: 'emergency' | 'company' | 'personal';
}

const EmergencySOSMainScreen: React.FC = () => {
  const navigation = useNavigation();
  
  const [currentLocation, setCurrentLocation] = useState<LocationData | null>(null);
  const [isSOSActive, setIsSOSActive] = useState(false);
  const [countdown, setCountdown] = useState(0);
  const [locationSharing, setLocationSharing] = useState(false);
  const [loading, setLoading] = useState(false);

  // Animation refs
  const pulseAnimation = useRef(new Animated.Value(1)).current;
  const sirenAnimation = useRef(new Animated.Value(0)).current;
  const fadeAnimation = useRef(new Animated.Value(0)).current;
  const slideAnimation = useRef(new Animated.Value(50)).current;
  const countdownRef = useRef<NodeJS.Timeout | null>(null);

  // Emergency contacts
  const emergencyContacts: EmergencyContact[] = [
    {
      id: 'police',
      name: 'Police Emergency',
      phoneNumber: '15',
      type: 'emergency',
    },
    {
      id: 'ambulance',
      name: 'Ambulance Service',
      phoneNumber: '1122',
      type: 'emergency',
    },
    {
      id: 'company',
      name: 'FoodWay Emergency',
      phoneNumber: '+92-21-111-FOOD',
      type: 'company',
    },
  ];

  useEffect(() => {
    // Start entrance animations
    Animated.parallel([
      Animated.timing(fadeAnimation, {
        toValue: 1,
        duration: 800,
        useNativeDriver: true,
      }),
      Animated.timing(slideAnimation, {
        toValue: 0,
        duration: 800,
        useNativeDriver: true,
      }),
    ]).start();

    // Start pulse animation for SOS button
    startPulseAnimation();
    
    // Get current location
    getCurrentLocation();

    return () => {
      if (countdownRef.current) {
        clearTimeout(countdownRef.current);
      }
    };
  }, []);

  useEffect(() => {
    if (countdown > 0) {
      countdownRef.current = setTimeout(() => {
        setCountdown(countdown - 1);
        Vibration.vibrate(200);
      }, 1000);
    } else if (countdown === 0 && isSOSActive) {
      activateEmergencySOS();
    }
  }, [countdown, isSOSActive]);

  const startPulseAnimation = () => {
    const pulse = () => {
      Animated.sequence([
        Animated.timing(pulseAnimation, {
          toValue: 1.2,
          duration: 800,
          useNativeDriver: true,
        }),
        Animated.timing(pulseAnimation, {
          toValue: 1,
          duration: 800,
          useNativeDriver: true,
        }),
      ]).start(() => pulse());
    };
    pulse();

    // Siren rotation animation
    const rotateSiren = () => {
      Animated.timing(sirenAnimation, {
        toValue: 1,
        duration: 2000,
        useNativeDriver: true,
      }).start(() => {
        sirenAnimation.setValue(0);
        rotateSiren();
      });
    };
    rotateSiren();
  };

  const getCurrentLocation = async () => {
    try {
      const { status } = await Location.requestForegroundPermissionsAsync();
      if (status !== 'granted') {
        Alert.alert('Permission Denied', 'Location permission is required for emergency services.');
        return;
      }

      const location = await Location.getCurrentPositionAsync({
        accuracy: Location.Accuracy.High,
      });

      const address = await Location.reverseGeocodeAsync({
        latitude: location.coords.latitude,
        longitude: location.coords.longitude,
      });

      const locationData: LocationData = {
        latitude: location.coords.latitude,
        longitude: location.coords.longitude,
        address: address[0] ? `${address[0].street || ''} ${address[0].city || ''} ${address[0].region || ''}`.trim() : 'Unknown location',
        timestamp: new Date().toISOString(),
      };

      setCurrentLocation(locationData);
    } catch (error) {
      console.error('Error getting location:', error);
      Alert.alert('Location Error', 'Unable to get current location. Please enable location services.');
    }
  };

  const handleSOSPress = () => {
    Alert.alert(
      'Emergency SOS',
      'This will immediately alert emergency services and share your location. Are you sure you want to continue?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Start SOS',
          style: 'destructive',
          onPress: () => startSOSCountdown(),
        },
      ]
    );
  };

  const startSOSCountdown = () => {
    setIsSOSActive(true);
    setCountdown(5); // 5 second countdown
    Vibration.vibrate([0, 500, 200, 500]);
  };

  const cancelSOS = () => {
    setIsSOSActive(false);
    setCountdown(0);
    if (countdownRef.current) {
      clearTimeout(countdownRef.current);
    }
  };

  const activateEmergencySOS = async () => {
    try {
      setLoading(true);
      
      // Start location sharing
      setLocationSharing(true);

      // Show confirmation dialog
      Alert.alert(
        'Emergency SOS Activated',
        `Emergency services and FoodWay support have been notified.\n\nYour location is being shared:\n${currentLocation?.address || 'Unknown location'}`,
        [
          {
            text: 'Call Police (15)',
            onPress: () => callEmergencyNumber('15', 'Police'),
            style: 'default',
          },
          {
            text: 'Call Ambulance (1122)',
            onPress: () => callEmergencyNumber('1122', 'Ambulance'),
            style: 'default',
          },
          {
            text: 'End SOS',
            onPress: () => endSOS(),
            style: 'cancel',
          },
        ]
      );

      // Simulate notifying emergency contacts
      setTimeout(() => {
        Alert.alert(
          'Emergency Contacts Notified',
          'Your emergency contacts and FoodWay support have been notified of your situation.'
        );
      }, 2000);

    } catch (error) {
      console.error('Error activating SOS:', error);
      Alert.alert('Error', 'Failed to activate emergency SOS. Please call emergency services directly.');
    } finally {
      setLoading(false);
      setIsSOSActive(false);
      setCountdown(0);
    }
  };

  const endSOS = () => {
    setLocationSharing(false);
    setIsSOSActive(false);
    setCountdown(0);
    Alert.alert('SOS Ended', 'Emergency SOS has been deactivated.');
  };

  const callEmergencyNumber = (phoneNumber: string, serviceName: string) => {
    const url = `tel:${phoneNumber}`;
    Linking.canOpenURL(url)
      .then((supported) => {
        if (supported) {
          Linking.openURL(url);
        } else {
          Alert.alert('Error', 'Unable to make phone call');
        }
      })
      .catch((error) => console.error('Error making call:', error));
  };

  const handleCallEmergencyContact = (contact: EmergencyContact) => {
    Alert.alert(
      `Call ${contact.name}`,
      `Do you want to call ${contact.phoneNumber}?`,
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Call',
          onPress: () => callEmergencyNumber(contact.phoneNumber, contact.name),
        },
      ]
    );
  };

  const handleSendLocationToAdmin = () => {
    if (!currentLocation) {
      Alert.alert('Location Error', 'Unable to get current location. Please try again.');
      return;
    }

    Alert.alert(
      'Send Location to Admin',
      `This will share your current location with FoodWay support team.\n\nLocation: ${currentLocation.address}`,
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Send Location',
          onPress: () => sendLocationToAdmin(),
        },
      ]
    );
  };

  const sendLocationToAdmin = async () => {
    try {
      setLoading(true);
      
      // Simulate sending location to admin
      await new Promise(resolve => setTimeout(resolve, 1500));
      
      Alert.alert(
        'Location Sent',
        'Your location has been shared with FoodWay support team. They will contact you shortly if needed.'
      );
    } catch (error) {
      Alert.alert('Error', 'Failed to send location. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const renderHeader = () => (
    <LinearGradient
      colors={['#dc2626', '#b91c1c']}
      style={{
        paddingTop: StatusBar.currentHeight || 0,
        paddingBottom: 20,
      }}
    >
      <SafeAreaView>
        <View style={{
          flexDirection: 'row',
          alignItems: 'center',
          justifyContent: 'space-between',
          paddingHorizontal: 20,
          paddingTop: 16,
        }}>
          <TouchableOpacity
            onPress={() => navigation.goBack()}
            style={{
              width: 40,
              height: 40,
              borderRadius: 20,
              backgroundColor: 'rgba(255,255,255,0.2)',
              alignItems: 'center',
              justifyContent: 'center',
            }}
          >
            <Ionicons name="arrow-back" size={24} color="white" />
          </TouchableOpacity>

          <Text style={{
            fontSize: 24,
            fontWeight: 'bold',
            color: 'white',
          }}>
            Emergency SOS
          </Text>

          <View style={{ width: 40 }} />
        </View>
      </SafeAreaView>
    </LinearGradient>
  );

  return (
    <View style={{ flex: 1, backgroundColor: '#f8fafc' }}>
      <StatusBar barStyle="light-content" backgroundColor="#dc2626" />
      
      {renderHeader()}

      <Animated.View
        style={{
          flex: 1,
          opacity: fadeAnimation,
          transform: [{ translateY: slideAnimation }],
        }}
      >
        {/* SOS Countdown Overlay */}
        {isSOSActive && (
          <View style={{
            position: 'absolute',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            backgroundColor: 'rgba(220, 38, 38, 0.95)',
            alignItems: 'center',
            justifyContent: 'center',
            zIndex: 1000,
          }}>
            <Text style={{
              fontSize: 32,
              fontWeight: 'bold',
              color: 'white',
              marginBottom: 20,
            }}>
              Emergency SOS
            </Text>

            <Text style={{
              fontSize: 72,
              fontWeight: 'bold',
              color: 'white',
              marginBottom: 20,
            }}>
              {countdown}
            </Text>

            <Text style={{
              fontSize: 18,
              color: 'white',
              textAlign: 'center',
              marginBottom: 40,
              paddingHorizontal: 40,
            }}>
              Emergency services will be contacted automatically
            </Text>

            <TouchableOpacity
              onPress={cancelSOS}
              style={{
                backgroundColor: 'white',
                borderRadius: 25,
                paddingHorizontal: 40,
                paddingVertical: 15,
              }}
            >
              <Text style={{
                fontSize: 18,
                fontWeight: '600',
                color: '#dc2626',
              }}>
                Cancel SOS
              </Text>
            </TouchableOpacity>
          </View>
        )}

        {/* Current Location Display */}
        <View style={{
          backgroundColor: 'white',
          marginHorizontal: 20,
          marginTop: 20,
          borderRadius: 16,
          padding: 20,
          shadowColor: '#000',
          shadowOffset: { width: 0, height: 2 },
          shadowOpacity: 0.1,
          shadowRadius: 8,
          elevation: 3,
        }}>
          <View style={{
            flexDirection: 'row',
            alignItems: 'center',
            marginBottom: 12,
          }}>
            <Ionicons name="location" size={24} color="#dc2626" />
            <Text style={{
              fontSize: 18,
              fontWeight: '600',
              color: '#111827',
              marginLeft: 12,
            }}>
              Your Current Location
            </Text>
          </View>

          {currentLocation ? (
            <>
              <Text style={{
                fontSize: 16,
                color: '#374151',
                marginBottom: 8,
              }}>
                {currentLocation.address}
              </Text>

              <Text style={{
                fontSize: 14,
                color: '#6b7280',
              }}>
                Lat: {currentLocation.latitude.toFixed(6)}, Lng: {currentLocation.longitude.toFixed(6)}
              </Text>

              {locationSharing && (
                <View style={{
                  flexDirection: 'row',
                  alignItems: 'center',
                  marginTop: 12,
                  backgroundColor: '#dcfdf7',
                  padding: 8,
                  borderRadius: 8,
                }}>
                  <Ionicons name="radio" size={16} color="#059669" />
                  <Text style={{
                    fontSize: 14,
                    color: '#059669',
                    fontWeight: '600',
                    marginLeft: 8,
                  }}>
                    Location sharing active
                  </Text>
                </View>
              )}
            </>
          ) : (
            <View style={{
              flexDirection: 'row',
              alignItems: 'center',
            }}>
              <Ionicons name="refresh" size={16} color="#6b7280" />
              <Text style={{
                fontSize: 16,
                color: '#6b7280',
                marginLeft: 8,
              }}>
                Getting location...
              </Text>
            </View>
          )}
        </View>

        {/* Main SOS Button */}
        <View style={{
          flex: 1,
          alignItems: 'center',
          justifyContent: 'center',
          paddingHorizontal: 40,
        }}>
          <Animated.View
            style={{
              transform: [{ scale: pulseAnimation }],
            }}
          >
            <TouchableOpacity
              onPress={handleSOSPress}
              disabled={loading || isSOSActive}
              style={{
                width: 200,
                height: 200,
                borderRadius: 100,
                backgroundColor: '#dc2626',
                alignItems: 'center',
                justifyContent: 'center',
                shadowColor: '#dc2626',
                shadowOffset: { width: 0, height: 8 },
                shadowOpacity: 0.4,
                shadowRadius: 20,
                elevation: 12,
                borderWidth: 8,
                borderColor: 'white',
              }}
            >
              <Animated.View
                style={{
                  transform: [{
                    rotate: sirenAnimation.interpolate({
                      inputRange: [0, 1],
                      outputRange: ['0deg', '360deg'],
                    }),
                  }],
                }}
              >
                <Ionicons name="warning" size={60} color="white" />
              </Animated.View>

              <Text style={{
                fontSize: 24,
                fontWeight: 'bold',
                color: 'white',
                marginTop: 12,
              }}>
                SOS
              </Text>
            </TouchableOpacity>
          </Animated.View>

          <Text style={{
            fontSize: 16,
            color: '#6b7280',
            textAlign: 'center',
            marginTop: 24,
            lineHeight: 24,
          }}>
            Press and hold for emergency assistance.{'\n'}
            Your location will be shared with emergency services.
          </Text>
        </View>

        {/* Emergency Actions */}
        <View style={{
          backgroundColor: 'white',
          marginHorizontal: 20,
          marginBottom: 20,
          borderRadius: 16,
          padding: 20,
          shadowColor: '#000',
          shadowOffset: { width: 0, height: 2 },
          shadowOpacity: 0.1,
          shadowRadius: 8,
          elevation: 3,
        }}>
          <Text style={{
            fontSize: 18,
            fontWeight: '600',
            color: '#111827',
            marginBottom: 16,
          }}>
            Emergency Actions
          </Text>

          {/* Emergency Contacts */}
          <View style={{
            flexDirection: 'row',
            justifyContent: 'space-between',
            marginBottom: 16,
          }}>
            {emergencyContacts.map((contact) => (
              <TouchableOpacity
                key={contact.id}
                onPress={() => handleCallEmergencyContact(contact)}
                style={{
                  flex: 1,
                  backgroundColor: contact.type === 'emergency' ? '#fef2f2' : '#f0f9ff',
                  borderRadius: 12,
                  padding: 12,
                  alignItems: 'center',
                  marginHorizontal: 4,
                  borderWidth: 1,
                  borderColor: contact.type === 'emergency' ? '#fecaca' : '#bae6fd',
                }}
              >
                <Ionicons
                  name={contact.type === 'emergency' ? 'call' : 'headset'}
                  size={20}
                  color={contact.type === 'emergency' ? '#dc2626' : '#0284c7'}
                />
                <Text style={{
                  fontSize: 12,
                  fontWeight: '600',
                  color: contact.type === 'emergency' ? '#dc2626' : '#0284c7',
                  marginTop: 4,
                  textAlign: 'center',
                }}>
                  {contact.name.split(' ')[0]}
                </Text>
              </TouchableOpacity>
            ))}
          </View>

          {/* Send Location to Admin */}
          <TouchableOpacity
            onPress={handleSendLocationToAdmin}
            disabled={loading || !currentLocation}
            style={{
              backgroundColor: '#f97316',
              borderRadius: 12,
              padding: 16,
              flexDirection: 'row',
              alignItems: 'center',
              justifyContent: 'center',
              opacity: (!currentLocation || loading) ? 0.6 : 1,
            }}
          >
            <Ionicons name="share" size={20} color="white" />
            <Text style={{
              fontSize: 16,
              fontWeight: '600',
              color: 'white',
              marginLeft: 8,
            }}>
              {loading ? 'Sending...' : 'Send Location to Admin'}
            </Text>
          </TouchableOpacity>
        </View>
      </Animated.View>
    </View>
  );
};

export default EmergencySOSMainScreen;
