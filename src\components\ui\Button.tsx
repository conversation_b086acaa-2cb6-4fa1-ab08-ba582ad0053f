import React from 'react';
import {
  TouchableOpacity,
  Text,
  ActivityIndicator,
  TouchableOpacityProps,
  ViewStyle,
  TextStyle,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';

interface ButtonProps extends TouchableOpacityProps {
  title: string;
  variant?: 'primary' | 'secondary' | 'outline' | 'ghost' | 'danger' | 'premium' | 'success';
  size?: 'sm' | 'md' | 'lg' | 'xl';
  loading?: boolean;
  disabled?: boolean;
  fullWidth?: boolean;
  leftIcon?: React.ReactNode | string;
  rightIcon?: React.ReactNode | string;
  onPress?: () => void;
  style?: ViewStyle;
}

const Button: React.FC<ButtonProps> = ({
  title,
  variant = 'primary',
  size = 'md',
  loading = false,
  disabled = false,
  fullWidth = false,
  leftIcon,
  rightIcon,
  style,
  ...props
}) => {
  const getButtonStyles = (): ViewStyle => {
    const baseStyles: ViewStyle = {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'center',
      borderRadius: 16, // Increased for modern look
    };

    // Enhanced size styles with better proportions
    const sizeStyles: Record<string, ViewStyle> = {
      sm: { paddingHorizontal: 16, paddingVertical: 12, minHeight: 44 },
      md: { paddingHorizontal: 20, paddingVertical: 16, minHeight: 52 },
      lg: { paddingHorizontal: 24, paddingVertical: 18, minHeight: 60 },
      xl: { paddingHorizontal: 28, paddingVertical: 20, minHeight: 68 },
    };

    // Enhanced variant styles with modern design
    const variantStyles: Record<string, ViewStyle> = {
      primary: {
        backgroundColor: disabled ? '#94a3b8' : '#dc2626',
        shadowColor: '#dc2626',
        shadowOffset: { width: 0, height: 6 },
        shadowOpacity: 0.3,
        shadowRadius: 12,
        elevation: 8,
      },
      secondary: {
        backgroundColor: disabled ? '#f1f5f9' : '#64748b',
        shadowColor: '#64748b',
        shadowOffset: { width: 0, height: 4 },
        shadowOpacity: 0.2,
        shadowRadius: 8,
        elevation: 6,
      },
      outline: {
        backgroundColor: 'white',
        borderWidth: 2,
        borderColor: disabled ? '#cbd5e1' : '#dc2626',
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 4 },
        shadowOpacity: 0.1,
        shadowRadius: 8,
        elevation: 4,
      },
      ghost: {
        backgroundColor: 'transparent',
      },
      danger: {
        backgroundColor: disabled ? '#fca5a5' : '#b91c1c',
        shadowColor: '#b91c1c',
        shadowOffset: { width: 0, height: 6 },
        shadowOpacity: 0.3,
        shadowRadius: 12,
        elevation: 8,
      },
      premium: {
        backgroundColor: disabled ? '#94a3b8' : '#dc2626',
        shadowColor: '#dc2626',
        shadowOffset: { width: 0, height: 8 },
        shadowOpacity: 0.4,
        shadowRadius: 16,
        elevation: 12,
        borderWidth: 1,
        borderColor: 'rgba(220, 38, 38, 0.2)',
      },
      success: {
        backgroundColor: disabled ? '#94a3b8' : '#10b981',
        shadowColor: '#10b981',
        shadowOffset: { width: 0, height: 6 },
        shadowOpacity: 0.3,
        shadowRadius: 12,
        elevation: 8,
      },
    };

    return {
      ...baseStyles,
      ...sizeStyles[size],
      ...variantStyles[variant],
      width: fullWidth ? '100%' : 'auto',
      opacity: disabled ? 0.6 : 1,
    };
  };

  const getTextStyles = (): TextStyle => {
    const baseStyles: TextStyle = {
      fontWeight: 'bold', // Changed to bold for better hierarchy
      textAlign: 'center',
    };

    // Enhanced size styles
    const sizeStyles: Record<string, TextStyle> = {
      sm: { fontSize: 14 },
      md: { fontSize: 16 },
      lg: { fontSize: 18 },
      xl: { fontSize: 20 },
    };

    // Enhanced variant styles
    const variantStyles: Record<string, TextStyle> = {
      primary: { color: '#ffffff' },
      secondary: { color: '#ffffff' },
      outline: { color: disabled ? '#cbd5e1' : '#dc2626' },
      ghost: { color: disabled ? '#cbd5e1' : '#dc2626' },
      danger: { color: '#ffffff' },
      premium: { color: '#ffffff' },
      success: { color: '#ffffff' },
    };

    return {
      ...baseStyles,
      ...sizeStyles[size],
      ...variantStyles[variant],
    };
  };

  const renderIcon = (icon: React.ReactNode | string, isLeft: boolean = true) => {
    if (typeof icon === 'string') {
      const iconColor = variant === 'outline' || variant === 'ghost'
        ? (disabled ? '#cbd5e1' : '#dc2626')
        : '#ffffff';

      return (
        <Ionicons
          name={icon as any}
          size={size === 'sm' ? 16 : size === 'lg' ? 20 : 18}
          color={iconColor}
          style={isLeft ? { marginRight: 8 } : { marginLeft: 8 }}
        />
      );
    }
    return icon;
  };

  return (
    <TouchableOpacity
      style={[getButtonStyles(), style]}
      disabled={disabled || loading}
      {...props}
    >
      {loading ? (
        <ActivityIndicator
          size="small"
          color={variant === 'outline' || variant === 'ghost' ? '#dc2626' : '#ffffff'}
        />
      ) : (
        <>
          {leftIcon && renderIcon(leftIcon, true)}
          <Text style={getTextStyles()}>
            {title}
          </Text>
          {rightIcon && renderIcon(rightIcon, false)}
        </>
      )}
    </TouchableOpacity>
  );
};

export default Button;
