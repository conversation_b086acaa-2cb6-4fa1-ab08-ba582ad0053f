import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  Alert,
  RefreshControl,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import { useNavigation } from '@react-navigation/native';
import { useAuth } from '../../context/AuthContext';
import { sessionManager } from '../../services/auth/sessionManager';
import { Card } from '../../components/ui';

const SessionDebugScreen: React.FC = () => {
  const navigation = useNavigation();
  const { state, logout } = useAuth();
  const [sessionInfo, setSessionInfo] = useState<any>(null);
  const [refreshing, setRefreshing] = useState(false);

  const loadSessionInfo = async () => {
    try {
      const info = await sessionManager.getSessionInfo();
      setSessionInfo(info);
    } catch (error) {
      console.error('Failed to load session info:', error);
    }
  };

  useEffect(() => {
    loadSessionInfo();
  }, []);

  const handleRefresh = async () => {
    setRefreshing(true);
    await loadSessionInfo();
    setRefreshing(false);
  };

  const handleClearSession = async () => {
    Alert.alert(
      'Clear Session',
      'Are you sure you want to clear the session? This will log you out.',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Clear',
          style: 'destructive',
          onPress: async () => {
            await sessionManager.clearSession();
            await logout();
          },
        },
      ]
    );
  };

  const formatTime = (ms: number) => {
    const hours = Math.floor(ms / (1000 * 60 * 60));
    const minutes = Math.floor((ms % (1000 * 60 * 60)) / (1000 * 60));
    return `${hours}h ${minutes}m`;
  };

  return (
    <SafeAreaView style={{ flex: 1, backgroundColor: '#f8fafc' }}>
      {/* Header */}
      <View style={{
        paddingHorizontal: 20,
        paddingVertical: 16,
        backgroundColor: '#ffffff',
        borderBottomWidth: 1,
        borderBottomColor: '#e5e7eb',
        flexDirection: 'row',
        alignItems: 'center',
      }}>
        <TouchableOpacity
          onPress={() => navigation.goBack()}
          style={{
            padding: 8,
            borderRadius: 8,
            backgroundColor: '#f3f4f6',
            marginRight: 12,
          }}
        >
          <Ionicons name="arrow-back" size={20} color="#6b7280" />
        </TouchableOpacity>
        <Text style={{ fontSize: 24, fontWeight: 'bold', color: '#111827' }}>
          Session Debug
        </Text>
      </View>

      <ScrollView
        style={{ flex: 1 }}
        contentContainerStyle={{ padding: 20 }}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={handleRefresh} />
        }
      >
        {/* Auth State */}
        <Card variant="elevated" margin="none" padding="lg" style={{ marginBottom: 16 }}>
          <Text style={{ fontSize: 18, fontWeight: 'bold', color: '#111827', marginBottom: 12 }}>
            Authentication State
          </Text>
          
          <View style={{ gap: 8 }}>
            <View style={{ flexDirection: 'row', justifyContent: 'space-between' }}>
              <Text style={{ color: '#6b7280' }}>Is Authenticated:</Text>
              <Text style={{ 
                color: state.isAuthenticated ? '#10b981' : '#ef4444',
                fontWeight: '600' 
              }}>
                {state.isAuthenticated ? 'Yes' : 'No'}
              </Text>
            </View>
            
            <View style={{ flexDirection: 'row', justifyContent: 'space-between' }}>
              <Text style={{ color: '#6b7280' }}>Is Loading:</Text>
              <Text style={{ 
                color: state.isLoading ? '#f59e0b' : '#6b7280',
                fontWeight: '600' 
              }}>
                {state.isLoading ? 'Yes' : 'No'}
              </Text>
            </View>

            {state.user && (
              <>
                <View style={{ flexDirection: 'row', justifyContent: 'space-between' }}>
                  <Text style={{ color: '#6b7280' }}>User Email:</Text>
                  <Text style={{ color: '#111827', fontWeight: '600' }}>
                    {state.user.email || 'N/A'}
                  </Text>
                </View>
                
                <View style={{ flexDirection: 'row', justifyContent: 'space-between' }}>
                  <Text style={{ color: '#6b7280' }}>User Phone:</Text>
                  <Text style={{ color: '#111827', fontWeight: '600' }}>
                    {state.user.phone || 'N/A'}
                  </Text>
                </View>

                <View style={{ flexDirection: 'row', justifyContent: 'space-between' }}>
                  <Text style={{ color: '#6b7280' }}>Demo Account:</Text>
                  <Text style={{ 
                    color: state.user.isDemoAccount ? '#f59e0b' : '#6b7280',
                    fontWeight: '600' 
                  }}>
                    {state.user.isDemoAccount ? 'Yes' : 'No'}
                  </Text>
                </View>
              </>
            )}

            {state.error && (
              <View style={{ marginTop: 8 }}>
                <Text style={{ color: '#ef4444', fontSize: 12 }}>
                  Error: {state.error}
                </Text>
              </View>
            )}
          </View>
        </Card>

        {/* Session Info */}
        <Card variant="elevated" margin="none" padding="lg" style={{ marginBottom: 16 }}>
          <Text style={{ fontSize: 18, fontWeight: 'bold', color: '#111827', marginBottom: 12 }}>
            Session Information
          </Text>
          
          {sessionInfo ? (
            <View style={{ gap: 8 }}>
              <View style={{ flexDirection: 'row', justifyContent: 'space-between' }}>
                <Text style={{ color: '#6b7280' }}>Has Active Session:</Text>
                <Text style={{ 
                  color: sessionInfo.hasSession ? '#10b981' : '#ef4444',
                  fontWeight: '600' 
                }}>
                  {sessionInfo.hasSession ? 'Yes' : 'No'}
                </Text>
              </View>

              {sessionInfo.hasSession && (
                <>
                  {sessionInfo.lastActivity && (
                    <View style={{ flexDirection: 'row', justifyContent: 'space-between' }}>
                      <Text style={{ color: '#6b7280' }}>Last Activity:</Text>
                      <Text style={{ color: '#111827', fontWeight: '600' }}>
                        {sessionInfo.lastActivity.toLocaleString()}
                      </Text>
                    </View>
                  )}

                  {sessionInfo.timeUntilExpiry !== undefined && (
                    <View style={{ flexDirection: 'row', justifyContent: 'space-between' }}>
                      <Text style={{ color: '#6b7280' }}>Time Until Expiry:</Text>
                      <Text style={{ 
                        color: sessionInfo.timeUntilExpiry > 0 ? '#10b981' : '#ef4444',
                        fontWeight: '600' 
                      }}>
                        {sessionInfo.timeUntilExpiry > 0 
                          ? formatTime(sessionInfo.timeUntilExpiry)
                          : 'Expired'
                        }
                      </Text>
                    </View>
                  )}

                  {sessionInfo.deviceId && (
                    <View style={{ flexDirection: 'row', justifyContent: 'space-between' }}>
                      <Text style={{ color: '#6b7280' }}>Device ID:</Text>
                      <Text style={{ 
                        color: '#111827', 
                        fontWeight: '600',
                        fontSize: 12,
                        flex: 1,
                        textAlign: 'right'
                      }}>
                        {sessionInfo.deviceId}
                      </Text>
                    </View>
                  )}
                </>
              )}
            </View>
          ) : (
            <Text style={{ color: '#6b7280', fontStyle: 'italic' }}>
              Loading session information...
            </Text>
          )}
        </Card>

        {/* Actions */}
        <Card variant="elevated" margin="none" padding="lg">
          <Text style={{ fontSize: 18, fontWeight: 'bold', color: '#111827', marginBottom: 12 }}>
            Actions
          </Text>
          
          <View style={{ gap: 12 }}>
            <TouchableOpacity
              onPress={handleRefresh}
              style={{
                backgroundColor: '#3b82f6',
                paddingVertical: 12,
                paddingHorizontal: 16,
                borderRadius: 8,
                flexDirection: 'row',
                alignItems: 'center',
                justifyContent: 'center',
              }}
            >
              <Ionicons name="refresh" size={16} color="white" style={{ marginRight: 8 }} />
              <Text style={{ color: 'white', fontWeight: '600' }}>
                Refresh Session Info
              </Text>
            </TouchableOpacity>

            <TouchableOpacity
              onPress={handleClearSession}
              style={{
                backgroundColor: '#ef4444',
                paddingVertical: 12,
                paddingHorizontal: 16,
                borderRadius: 8,
                flexDirection: 'row',
                alignItems: 'center',
                justifyContent: 'center',
              }}
            >
              <Ionicons name="trash" size={16} color="white" style={{ marginRight: 8 }} />
              <Text style={{ color: 'white', fontWeight: '600' }}>
                Clear Session & Logout
              </Text>
            </TouchableOpacity>
          </View>
        </Card>
      </ScrollView>
    </SafeAreaView>
  );
};

export default SessionDebugScreen;
