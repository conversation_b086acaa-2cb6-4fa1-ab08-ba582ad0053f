import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  ScrollView,
  Switch,
  Alert,
  Dimensions,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import { useNavigation } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import { AdvancedToolsStackParamList } from '../../types';
import {
  LocationTrackingConfig,
  LocationData,
  TrackingStatus,
  LocationAccuracy,
  BatteryOptimization,
} from '../../types/advancedTools';

const { width } = Dimensions.get('window');

type LocationTrackingScreenNavigationProp = StackNavigationProp<AdvancedToolsStackParamList, 'LocationTracking'>;

const LocationTrackingScreen: React.FC = () => {
  const navigation = useNavigation<LocationTrackingScreenNavigationProp>();
  const [config, setConfig] = useState<LocationTrackingConfig | null>(null);
  const [currentLocation, setCurrentLocation] = useState<LocationData | null>(null);
  const [trackingStatus, setTrackingStatus] = useState<TrackingStatus>(TrackingStatus.STOPPED);
  const [locationHistory, setLocationHistory] = useState<LocationData[]>([]);
  const [loading, setLoading] = useState(true);

  // Mock location tracking configuration
  const mockConfig: LocationTrackingConfig = {
    id: 'config-1',
    riderId: 'rider-1',
    isEnabled: true,
    accuracy: LocationAccuracy.HIGH,
    updateInterval: 30, // seconds
    distanceFilter: 10, // meters
    batteryOptimization: BatteryOptimization.BALANCED,
    backgroundTracking: true,
    shareWithCustomers: true,
    shareWithSupport: true,
    emergencySharing: true,
    dataRetentionDays: 30,
    privacySettings: {
      shareLocation: true,
      shareSpeed: false,
      shareRoute: true,
      anonymizeData: false,
    },
    geofencing: {
      enabled: true,
      restaurantRadius: 100, // meters
      customerRadius: 50, // meters
      alertOnEntry: true,
      alertOnExit: true,
    },
    updatedAt: new Date().toISOString(),
  };

  // Mock current location
  const mockCurrentLocation: LocationData = {
    id: 'loc-current',
    riderId: 'rider-1',
    latitude: 24.8607,
    longitude: 67.0011,
    accuracy: 5.2,
    altitude: 12.5,
    speed: 25.5, // km/h
    heading: 45.0,
    timestamp: new Date().toISOString(),
    address: 'Gulshan-e-Iqbal, Block 13-D, Karachi',
    isOnline: true,
    batteryLevel: 78,
    networkType: '4G',
  };

  // Mock location history
  const mockLocationHistory: LocationData[] = [
    {
      id: 'loc-1',
      riderId: 'rider-1',
      latitude: 24.8607,
      longitude: 67.0011,
      accuracy: 5.2,
      altitude: 12.5,
      speed: 25.5,
      heading: 45.0,
      timestamp: new Date(Date.now() - 5 * 60 * 1000).toISOString(),
      address: 'Gulshan-e-Iqbal, Block 13-D, Karachi',
      isOnline: true,
      batteryLevel: 78,
      networkType: '4G',
    },
    {
      id: 'loc-2',
      riderId: 'rider-1',
      latitude: 24.8590,
      longitude: 67.0025,
      accuracy: 4.8,
      altitude: 11.2,
      speed: 30.2,
      heading: 52.0,
      timestamp: new Date(Date.now() - 10 * 60 * 1000).toISOString(),
      address: 'University Road, Gulshan-e-Iqbal, Karachi',
      isOnline: true,
      batteryLevel: 79,
      networkType: '4G',
    },
    {
      id: 'loc-3',
      riderId: 'rider-1',
      latitude: 24.8575,
      longitude: 67.0040,
      accuracy: 6.1,
      altitude: 10.8,
      speed: 22.8,
      heading: 38.0,
      timestamp: new Date(Date.now() - 15 * 60 * 1000).toISOString(),
      address: 'Rashid Minhas Road, Gulshan-e-Iqbal, Karachi',
      isOnline: true,
      batteryLevel: 80,
      networkType: '4G',
    },
    {
      id: 'loc-4',
      riderId: 'rider-1',
      latitude: 24.8560,
      longitude: 67.0055,
      accuracy: 5.5,
      altitude: 9.5,
      speed: 18.5,
      heading: 42.0,
      timestamp: new Date(Date.now() - 20 * 60 * 1000).toISOString(),
      address: 'Samama Shopping Mall, Gulshan-e-Iqbal, Karachi',
      isOnline: true,
      batteryLevel: 81,
      networkType: '4G',
    },
    {
      id: 'loc-5',
      riderId: 'rider-1',
      latitude: 24.8545,
      longitude: 67.0070,
      accuracy: 4.2,
      altitude: 8.8,
      speed: 35.0,
      heading: 55.0,
      timestamp: new Date(Date.now() - 25 * 60 * 1000).toISOString(),
      address: 'Millennium Mall, Gulshan-e-Iqbal, Karachi',
      isOnline: true,
      batteryLevel: 82,
      networkType: '4G',
    },
  ];

  useEffect(() => {
    loadLocationData();
    // Simulate real-time location updates
    const interval = setInterval(() => {
      if (trackingStatus === TrackingStatus.ACTIVE) {
        updateCurrentLocation();
      }
    }, 30000); // Update every 30 seconds

    return () => clearInterval(interval);
  }, [trackingStatus]);

  const loadLocationData = async () => {
    setLoading(true);
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      setConfig(mockConfig);
      setCurrentLocation(mockCurrentLocation);
      setLocationHistory(mockLocationHistory);
      setTrackingStatus(mockConfig.isEnabled ? TrackingStatus.ACTIVE : TrackingStatus.STOPPED);
    } catch (error) {
      console.error('Error loading location data:', error);
    } finally {
      setLoading(false);
    }
  };

  const updateCurrentLocation = () => {
    if (!currentLocation) return;

    // Simulate slight location changes
    const newLocation: LocationData = {
      ...currentLocation,
      id: `loc-${Date.now()}`,
      latitude: currentLocation.latitude + (Math.random() - 0.5) * 0.001,
      longitude: currentLocation.longitude + (Math.random() - 0.5) * 0.001,
      speed: Math.max(0, currentLocation.speed + (Math.random() - 0.5) * 10),
      heading: (currentLocation.heading + (Math.random() - 0.5) * 20) % 360,
      timestamp: new Date().toISOString(),
      batteryLevel: Math.max(1, currentLocation.batteryLevel - Math.random() * 2),
    };

    setCurrentLocation(newLocation);
    
    // Add to history (keep last 20 locations)
    setLocationHistory(prev => [newLocation, ...prev.slice(0, 19)]);
  };

  const toggleTracking = async () => {
    if (!config) return;

    try {
      if (trackingStatus === TrackingStatus.ACTIVE) {
        setTrackingStatus(TrackingStatus.STOPPED);
        Alert.alert(
          'Location Tracking Stopped',
          'Background location tracking has been disabled. You can re-enable it anytime from settings.'
        );
      } else {
        // Request location permissions
        Alert.alert(
          'Enable Location Tracking',
          'FoodWay needs location access to track your deliveries and ensure rider safety. This helps customers track their orders and provides emergency assistance if needed.',
          [
            { text: 'Cancel', style: 'cancel' },
            {
              text: 'Enable',
              onPress: () => {
                setTrackingStatus(TrackingStatus.ACTIVE);
                updateCurrentLocation();
              },
            },
          ]
        );
      }
    } catch (error) {
      console.error('Error toggling tracking:', error);
      Alert.alert('Error', 'Failed to update location tracking settings.');
    }
  };

  const updateConfig = async (newConfig: LocationTrackingConfig) => {
    try {
      setConfig(newConfig);
      // Here you would typically save to API
    } catch (error) {
      console.error('Error updating config:', error);
    }
  };

  const getTrackingStatusColor = (): string => {
    switch (trackingStatus) {
      case TrackingStatus.ACTIVE:
        return '#10b981';
      case TrackingStatus.PAUSED:
        return '#f59e0b';
      case TrackingStatus.STOPPED:
        return '#ef4444';
      default:
        return '#6b7280';
    }
  };

  const getTrackingStatusText = (): string => {
    switch (trackingStatus) {
      case TrackingStatus.ACTIVE:
        return 'Active';
      case TrackingStatus.PAUSED:
        return 'Paused';
      case TrackingStatus.STOPPED:
        return 'Stopped';
      default:
        return 'Unknown';
    }
  };

  const getAccuracyText = (accuracy: LocationAccuracy): string => {
    switch (accuracy) {
      case LocationAccuracy.HIGH:
        return 'High (GPS)';
      case LocationAccuracy.MEDIUM:
        return 'Medium (Network)';
      case LocationAccuracy.LOW:
        return 'Low (Passive)';
      default:
        return 'Unknown';
    }
  };

  const getBatteryOptimizationText = (optimization: BatteryOptimization): string => {
    switch (optimization) {
      case BatteryOptimization.AGGRESSIVE:
        return 'Aggressive (Longer battery)';
      case BatteryOptimization.BALANCED:
        return 'Balanced (Recommended)';
      case BatteryOptimization.PERFORMANCE:
        return 'Performance (More accurate)';
      default:
        return 'Unknown';
    }
  };

  const formatSpeed = (speed: number): string => {
    return `${speed.toFixed(1)} km/h`;
  };

  const formatCoordinate = (coord: number): string => {
    return coord.toFixed(6);
  };

  const formatTimeAgo = (dateString: string): string => {
    if (!dateString) return 'N/A';

    const now = new Date();
    const date = new Date(dateString);

    // Check if date is valid
    if (isNaN(date.getTime())) return 'Invalid Date';

    const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60));

    if (diffInMinutes < 1) return 'Just now';
    if (diffInMinutes < 60) return `${diffInMinutes}m ago`;

    const diffInHours = Math.floor(diffInMinutes / 60);
    if (diffInHours < 24) return `${diffInHours}h ago`;

    return date.toLocaleDateString();
  };

  const renderCurrentLocationCard = () => {
    if (!currentLocation) return null;

    return (
      <View style={{
        backgroundColor: 'white',
        borderRadius: 20,
        marginHorizontal: 20,
        marginTop: -12,
        marginBottom: 24,
        padding: 24,
        shadowColor: '#dc2626',
        shadowOffset: { width: 0, height: 8 },
        shadowOpacity: 0.1,
        shadowRadius: 16,
        elevation: 8,
        borderWidth: 1,
        borderColor: 'rgba(220, 38, 38, 0.05)',
      }}>
        <View style={{
          flexDirection: 'row',
          alignItems: 'center',
          justifyContent: 'space-between',
          marginBottom: 20,
        }}>
          <View style={{ flexDirection: 'row', alignItems: 'center' }}>
            <View style={{
              width: 48,
              height: 48,
              backgroundColor: '#fef2f2',
              borderRadius: 24,
              justifyContent: 'center',
              alignItems: 'center',
              marginRight: 12,
              borderWidth: 2,
              borderColor: '#dc2626',
            }}>
              <Ionicons name="location" size={24} color="#dc2626" />
            </View>
            <Text style={{
              fontSize: 20,
              fontWeight: 'bold',
              color: '#111827',
            }}>
              Current Location
            </Text>
          </View>

          <View style={{
            flexDirection: 'row',
            alignItems: 'center',
            backgroundColor: getTrackingStatusColor() + '20',
            paddingHorizontal: 12,
            paddingVertical: 8,
            borderRadius: 16,
            borderWidth: 2,
            borderColor: getTrackingStatusColor(),
          }}>
            <View style={{
              width: 12,
              height: 12,
              borderRadius: 4,
              backgroundColor: getTrackingStatusColor(),
              marginRight: 6,
            }} />
            <Text style={{
              fontSize: 12,
              fontWeight: '600',
              color: getTrackingStatusColor(),
            }}>
              {getTrackingStatusText()}
            </Text>
          </View>
        </View>

        {/* Location Details */}
        <View style={{ marginBottom: 16 }}>
          <Text style={{
            fontSize: 14,
            color: '#374151',
            marginBottom: 8,
          }}>
            📍 {currentLocation.address}
          </Text>

          <View style={{
            flexDirection: 'row',
            flexWrap: 'wrap',
            gap: 12,
          }}>
            <View style={{ flex: 1, minWidth: '45%' }}>
              <Text style={{
                fontSize: 12,
                color: '#6b7280',
                marginBottom: 2,
              }}>
                Coordinates
              </Text>
              <Text style={{
                fontSize: 14,
                fontWeight: '600',
                color: '#1f2937',
              }}>
                {formatCoordinate(currentLocation.latitude)}, {formatCoordinate(currentLocation.longitude)}
              </Text>
            </View>

            <View style={{ flex: 1, minWidth: '45%' }}>
              <Text style={{
                fontSize: 12,
                color: '#6b7280',
                marginBottom: 2,
              }}>
                Speed
              </Text>
              <Text style={{
                fontSize: 14,
                fontWeight: '600',
                color: '#1f2937',
              }}>
                {formatSpeed(currentLocation.speed)}
              </Text>
            </View>

            <View style={{ flex: 1, minWidth: '45%' }}>
              <Text style={{
                fontSize: 12,
                color: '#6b7280',
                marginBottom: 2,
              }}>
                Accuracy
              </Text>
              <Text style={{
                fontSize: 14,
                fontWeight: '600',
                color: '#1f2937',
              }}>
                ±{currentLocation.accuracy.toFixed(1)}m
              </Text>
            </View>

            <View style={{ flex: 1, minWidth: '45%' }}>
              <Text style={{
                fontSize: 12,
                color: '#6b7280',
                marginBottom: 2,
              }}>
                Battery
              </Text>
              <Text style={{
                fontSize: 14,
                fontWeight: '600',
                color: currentLocation.batteryLevel > 20 ? '#10b981' : '#ef4444',
              }}>
                {Math.round(currentLocation.batteryLevel)}%
              </Text>
            </View>
          </View>
        </View>

        {/* Quick Actions */}
        <View style={{
          flexDirection: 'row',
          gap: 8,
        }}>
          <TouchableOpacity
            onPress={toggleTracking}
            style={{
              flex: 1,
              backgroundColor: trackingStatus === TrackingStatus.ACTIVE ? '#ef4444' : '#10b981',
              paddingVertical: 10,
              borderRadius: 8,
              alignItems: 'center',
            }}
          >
            <Text style={{
              fontSize: 14,
              fontWeight: '600',
              color: 'white',
            }}>
              {trackingStatus === TrackingStatus.ACTIVE ? 'Stop Tracking' : 'Start Tracking'}
            </Text>
          </TouchableOpacity>

          <TouchableOpacity
            onPress={() => updateCurrentLocation()}
            style={{
              backgroundColor: '#f97316',
              paddingHorizontal: 16,
              paddingVertical: 10,
              borderRadius: 8,
              alignItems: 'center',
            }}
          >
            <Ionicons name="refresh" size={16} color="white" />
          </TouchableOpacity>
        </View>
      </View>
    );
  };

  const renderTrackingSettings = () => {
    if (!config) return null;

    return (
      <View style={{
        backgroundColor: 'white',
        borderRadius: 12,
        marginHorizontal: 16,
        marginBottom: 16,
        padding: 16,
      }}>
        <Text style={{
          fontSize: 18,
          fontWeight: '600',
          color: '#1f2937',
          marginBottom: 16,
        }}>
          Tracking Settings
        </Text>

        {/* Master Toggle */}
        <View style={{
          flexDirection: 'row',
          alignItems: 'center',
          justifyContent: 'space-between',
          marginBottom: 16,
        }}>
          <View style={{ flex: 1 }}>
            <Text style={{
              fontSize: 16,
              fontWeight: '600',
              color: '#374151',
            }}>
              Background Tracking
            </Text>
            <Text style={{
              fontSize: 12,
              color: '#6b7280',
              marginTop: 2,
            }}>
              Track location even when app is closed
            </Text>
          </View>
          <Switch
            value={config.backgroundTracking}
            onValueChange={(backgroundTracking) =>
              updateConfig({ ...config, backgroundTracking })
            }
            trackColor={{ false: '#f3f4f6', true: '#f97316' }}
            thumbColor={config.backgroundTracking ? 'white' : '#f4f3f4'}
          />
        </View>

        {/* Accuracy Setting */}
        <View style={{ marginBottom: 16 }}>
          <Text style={{
            fontSize: 14,
            fontWeight: '600',
            color: '#374151',
            marginBottom: 8,
          }}>
            Location Accuracy
          </Text>
          <View style={{
            flexDirection: 'row',
            backgroundColor: '#f3f4f6',
            borderRadius: 8,
            padding: 2,
          }}>
            {([LocationAccuracy.LOW, LocationAccuracy.MEDIUM, LocationAccuracy.HIGH] as const).map((accuracy) => (
              <TouchableOpacity
                key={accuracy}
                onPress={() => updateConfig({ ...config, accuracy })}
                style={{
                  flex: 1,
                  paddingVertical: 8,
                  borderRadius: 6,
                  backgroundColor: config.accuracy === accuracy ? '#f97316' : 'transparent',
                  alignItems: 'center',
                }}
              >
                <Text style={{
                  fontSize: 12,
                  fontWeight: '600',
                  color: config.accuracy === accuracy ? 'white' : '#6b7280',
                }}>
                  {getAccuracyText(accuracy).split(' ')[0]}
                </Text>
              </TouchableOpacity>
            ))}
          </View>
        </View>

        {/* Battery Optimization */}
        <View style={{ marginBottom: 16 }}>
          <Text style={{
            fontSize: 14,
            fontWeight: '600',
            color: '#374151',
            marginBottom: 8,
          }}>
            Battery Optimization
          </Text>
          <View style={{
            flexDirection: 'row',
            backgroundColor: '#f3f4f6',
            borderRadius: 8,
            padding: 2,
          }}>
            {([BatteryOptimization.AGGRESSIVE, BatteryOptimization.BALANCED, BatteryOptimization.PERFORMANCE] as const).map((optimization) => (
              <TouchableOpacity
                key={optimization}
                onPress={() => updateConfig({ ...config, batteryOptimization: optimization })}
                style={{
                  flex: 1,
                  paddingVertical: 8,
                  borderRadius: 6,
                  backgroundColor: config.batteryOptimization === optimization ? '#f97316' : 'transparent',
                  alignItems: 'center',
                }}
              >
                <Text style={{
                  fontSize: 10,
                  fontWeight: '600',
                  color: config.batteryOptimization === optimization ? 'white' : '#6b7280',
                  textAlign: 'center',
                }}>
                  {getBatteryOptimizationText(optimization).split(' ')[0]}
                </Text>
              </TouchableOpacity>
            ))}
          </View>
        </View>

        {/* Update Interval */}
        <View style={{ marginBottom: 16 }}>
          <Text style={{
            fontSize: 14,
            fontWeight: '600',
            color: '#374151',
            marginBottom: 8,
          }}>
            Update Interval: {config.updateInterval}s
          </Text>
          <View style={{
            flexDirection: 'row',
            backgroundColor: '#f3f4f6',
            borderRadius: 8,
            padding: 2,
          }}>
            {([15, 30, 60, 120] as const).map((interval) => (
              <TouchableOpacity
                key={interval}
                onPress={() => updateConfig({ ...config, updateInterval: interval })}
                style={{
                  flex: 1,
                  paddingVertical: 8,
                  borderRadius: 6,
                  backgroundColor: config.updateInterval === interval ? '#f97316' : 'transparent',
                  alignItems: 'center',
                }}
              >
                <Text style={{
                  fontSize: 12,
                  fontWeight: '600',
                  color: config.updateInterval === interval ? 'white' : '#6b7280',
                }}>
                  {interval}s
                </Text>
              </TouchableOpacity>
            ))}
          </View>
        </View>
      </View>
    );
  };

  const renderPrivacySettings = () => {
    if (!config) return null;

    return (
      <View style={{
        backgroundColor: 'white',
        borderRadius: 12,
        marginHorizontal: 16,
        marginBottom: 16,
        padding: 16,
      }}>
        <Text style={{
          fontSize: 18,
          fontWeight: '600',
          color: '#1f2937',
          marginBottom: 16,
        }}>
          Privacy & Sharing
        </Text>

        {/* Share with Customers */}
        <View style={{
          flexDirection: 'row',
          alignItems: 'center',
          justifyContent: 'space-between',
          marginBottom: 12,
        }}>
          <View style={{ flex: 1 }}>
            <Text style={{
              fontSize: 14,
              fontWeight: '600',
              color: '#374151',
            }}>
              Share with Customers
            </Text>
            <Text style={{
              fontSize: 12,
              color: '#6b7280',
              marginTop: 2,
            }}>
              Allow customers to track your location during delivery
            </Text>
          </View>
          <Switch
            value={config.shareWithCustomers}
            onValueChange={(shareWithCustomers) =>
              updateConfig({ ...config, shareWithCustomers })
            }
            trackColor={{ false: '#f3f4f6', true: '#f97316' }}
            thumbColor={config.shareWithCustomers ? 'white' : '#f4f3f4'}
          />
        </View>

        {/* Share with Support */}
        <View style={{
          flexDirection: 'row',
          alignItems: 'center',
          justifyContent: 'space-between',
          marginBottom: 12,
        }}>
          <View style={{ flex: 1 }}>
            <Text style={{
              fontSize: 14,
              fontWeight: '600',
              color: '#374151',
            }}>
              Share with Support
            </Text>
            <Text style={{
              fontSize: 12,
              color: '#6b7280',
              marginTop: 2,
            }}>
              Allow FoodWay support to access your location for assistance
            </Text>
          </View>
          <Switch
            value={config.shareWithSupport}
            onValueChange={(shareWithSupport) =>
              updateConfig({ ...config, shareWithSupport })
            }
            trackColor={{ false: '#f3f4f6', true: '#f97316' }}
            thumbColor={config.shareWithSupport ? 'white' : '#f4f3f4'}
          />
        </View>

        {/* Emergency Sharing */}
        <View style={{
          flexDirection: 'row',
          alignItems: 'center',
          justifyContent: 'space-between',
          marginBottom: 16,
        }}>
          <View style={{ flex: 1 }}>
            <Text style={{
              fontSize: 14,
              fontWeight: '600',
              color: '#374151',
            }}>
              Emergency Sharing
            </Text>
            <Text style={{
              fontSize: 12,
              color: '#6b7280',
              marginTop: 2,
            }}>
              Share location with emergency contacts during SOS
            </Text>
          </View>
          <Switch
            value={config.emergencySharing}
            onValueChange={(emergencySharing) =>
              updateConfig({ ...config, emergencySharing })
            }
            trackColor={{ false: '#f3f4f6', true: '#f97316' }}
            thumbColor={config.emergencySharing ? 'white' : '#f4f3f4'}
          />
        </View>

        {/* Data Retention */}
        <View>
          <Text style={{
            fontSize: 14,
            fontWeight: '600',
            color: '#374151',
            marginBottom: 8,
          }}>
            Data Retention: {config.dataRetentionDays} days
          </Text>
          <View style={{
            flexDirection: 'row',
            backgroundColor: '#f3f4f6',
            borderRadius: 8,
            padding: 2,
          }}>
            {([7, 15, 30, 90] as const).map((days) => (
              <TouchableOpacity
                key={days}
                onPress={() => updateConfig({ ...config, dataRetentionDays: days })}
                style={{
                  flex: 1,
                  paddingVertical: 8,
                  borderRadius: 6,
                  backgroundColor: config.dataRetentionDays === days ? '#f97316' : 'transparent',
                  alignItems: 'center',
                }}
              >
                <Text style={{
                  fontSize: 12,
                  fontWeight: '600',
                  color: config.dataRetentionDays === days ? 'white' : '#6b7280',
                }}>
                  {days}d
                </Text>
              </TouchableOpacity>
            ))}
          </View>
        </View>
      </View>
    );
  };

  const renderLocationHistory = () => {
    return (
      <View style={{
        backgroundColor: 'white',
        borderRadius: 12,
        marginHorizontal: 16,
        marginBottom: 16,
        padding: 16,
      }}>
        <Text style={{
          fontSize: 18,
          fontWeight: '600',
          color: '#1f2937',
          marginBottom: 16,
        }}>
          Location History
        </Text>

        {locationHistory.length === 0 ? (
          <View style={{
            alignItems: 'center',
            paddingVertical: 32,
          }}>
            <Ionicons name="location-outline" size={48} color="#d1d5db" />
            <Text style={{
              fontSize: 16,
              color: '#6b7280',
              marginTop: 8,
            }}>
              No location history
            </Text>
          </View>
        ) : (
          locationHistory.slice(0, 5).map((location, index) => (
            <View
              key={location.id}
              style={{
                flexDirection: 'row',
                alignItems: 'center',
                paddingVertical: 12,
                borderBottomWidth: index < 4 ? 1 : 0,
                borderBottomColor: '#f3f4f6',
              }}
            >
              <View style={{
                width: 40,
                height: 40,
                borderRadius: 20,
                backgroundColor: '#f97316' + '20',
                alignItems: 'center',
                justifyContent: 'center',
                marginRight: 12,
              }}>
                <Ionicons name="location" size={20} color="#f97316" />
              </View>

              <View style={{ flex: 1 }}>
                <Text style={{
                  fontSize: 14,
                  fontWeight: '600',
                  color: '#1f2937',
                  marginBottom: 2,
                }}>
                  {location.address}
                </Text>

                <View style={{
                  flexDirection: 'row',
                  alignItems: 'center',
                  gap: 12,
                }}>
                  <Text style={{
                    fontSize: 12,
                    color: '#6b7280',
                  }}>
                    {formatSpeed(location.speed)}
                  </Text>
                  <Text style={{
                    fontSize: 12,
                    color: '#6b7280',
                  }}>
                    ±{location.accuracy.toFixed(1)}m
                  </Text>
                  <Text style={{
                    fontSize: 12,
                    color: '#6b7280',
                  }}>
                    {formatTimeAgo(location.timestamp)}
                  </Text>
                </View>
              </View>
            </View>
          ))
        )}

        {locationHistory.length > 5 && (
          <TouchableOpacity
            onPress={() => navigation.navigate('LocationHistory' as never)}
            style={{
              alignItems: 'center',
              paddingVertical: 12,
              marginTop: 8,
            }}
          >
            <Text style={{
              fontSize: 14,
              color: '#f97316',
              fontWeight: '600',
            }}>
              View All History ({locationHistory.length} locations)
            </Text>
          </TouchableOpacity>
        )}
      </View>
    );
  };

  return (
    <View style={{ flex: 1, backgroundColor: '#f1f5f9' }}>
      <SafeAreaView style={{ flex: 1 }}>
        {/* Enhanced Header */}
        <View style={{
          backgroundColor: '#dc2626',
          paddingHorizontal: 20,
          paddingTop: 20,
          paddingBottom: 28,
          borderBottomLeftRadius: 28,
          borderBottomRightRadius: 28,
          shadowColor: '#dc2626',
          shadowOffset: { width: 0, height: 12 },
          shadowOpacity: 0.3,
          shadowRadius: 20,
          elevation: 16,
        }}>
          <View style={{
            flexDirection: 'row',
            alignItems: 'center',
            marginBottom: 20,
          }}>
            <TouchableOpacity
              onPress={() => navigation.goBack()}
              style={{
                width: 48,
                height: 48,
                borderRadius: 24,
                backgroundColor: 'rgba(255,255,255,0.2)',
                justifyContent: 'center',
                alignItems: 'center',
                marginRight: 16,
                borderWidth: 2,
                borderColor: 'rgba(255,255,255,0.3)',
              }}
            >
              <Ionicons name="arrow-back" size={24} color="white" />
            </TouchableOpacity>
            <View style={{ flex: 1 }}>
              <Text style={{
                fontSize: 24,
                fontWeight: 'bold',
                color: 'white',
                marginBottom: 4,
              }}>
                Location Tracking
              </Text>
              <Text style={{
                fontSize: 14,
                color: 'rgba(255,255,255,0.8)',
              }}>
                Manage your location settings
              </Text>
            </View>
          </View>
        </View>

      {/* Content */}
      {loading ? (
        <View style={{
          flex: 1,
          alignItems: 'center',
          justifyContent: 'center',
        }}>
          <Text style={{
            fontSize: 16,
            color: '#6b7280',
          }}>
            Loading location data...
          </Text>
        </View>
      ) : (
        <ScrollView
          style={{ flex: 1 }}
          contentContainerStyle={{ paddingVertical: 16 }}
          showsVerticalScrollIndicator={false}
        >
          {renderCurrentLocationCard()}
          {renderTrackingSettings()}
          {renderPrivacySettings()}
          {renderLocationHistory()}

          {/* Safety Notice */}
          <View style={{
            backgroundColor: '#fef3c7',
            borderRadius: 12,
            marginHorizontal: 16,
            marginBottom: 16,
            padding: 16,
            borderLeftWidth: 4,
            borderLeftColor: '#f59e0b',
          }}>
            <View style={{
              flexDirection: 'row',
              alignItems: 'center',
              marginBottom: 8,
            }}>
              <Ionicons name="shield-checkmark" size={20} color="#f59e0b" />
              <Text style={{
                fontSize: 16,
                fontWeight: '600',
                color: '#92400e',
                marginLeft: 8,
              }}>
                Safety & Privacy
              </Text>
            </View>

            <Text style={{
              fontSize: 14,
              color: '#92400e',
              lineHeight: 20,
            }}>
              Your location data is encrypted and only shared with authorized parties as per your privacy settings.
              Location tracking helps ensure your safety during deliveries and provides better customer service.
            </Text>
          </View>
        </ScrollView>
      )}
      </SafeAreaView>
    </View>
  );
};

export default LocationTrackingScreen;
