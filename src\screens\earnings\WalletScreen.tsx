import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  Text,
  ScrollView,
  RefreshControl,
  TouchableOpacity,
  Alert,
  Modal,
  TextInput,
  Animated,
  Dimensions,
  StatusBar,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import { useNavigation } from '@react-navigation/native';
import { LinearGradient } from 'expo-linear-gradient';
import { BlurView } from 'expo-blur';
import { Card, Button, Badge, LoadingSpinner } from '../../components/ui';
import { useEarnings } from '../../context/EarningsContext';
import { formatCurrency, formatDate } from '../../utils/helpers';
import { WithdrawalStatus, PaymentMethod } from '../../types/earnings';

const { width: screenWidth } = Dimensions.get('window');

const WalletScreen: React.FC = () => {
  const navigation = useNavigation();
  const {
    state,
    fetchWalletBalance,
    fetchWithdrawalMethods,
    fetchWithdrawalHistory,
    requestWithdrawal,
    addWithdrawalMethod,
  } = useEarnings();

  const [refreshing, setRefreshing] = useState(false);
  const [showWithdrawModal, setShowWithdrawModal] = useState(false);
  const [showAddMethodModal, setShowAddMethodModal] = useState(false);
  const [withdrawAmount, setWithdrawAmount] = useState('');
  const [selectedMethodId, setSelectedMethodId] = useState<string>('');
  const [activeTab, setActiveTab] = useState<'daily' | 'weekly' | 'bonuses'>('daily');
  const [newMethod, setNewMethod] = useState({
    type: PaymentMethod.BANK_TRANSFER,
    name: '',
    accountNumber: '',
    accountTitle: '',
    bankName: '',
    phoneNumber: '',
  });

  // Animation refs
  const fadeAnimation = useRef(new Animated.Value(0)).current;
  const slideAnimation = useRef(new Animated.Value(50)).current;
  const balanceAnimation = useRef(new Animated.Value(0)).current;
  const tabAnimation = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    loadWalletData();

    // Start entrance animations
    Animated.parallel([
      Animated.timing(fadeAnimation, {
        toValue: 1,
        duration: 800,
        useNativeDriver: true,
      }),
      Animated.timing(slideAnimation, {
        toValue: 0,
        duration: 800,
        useNativeDriver: true,
      }),
    ]).start();

    // Delayed balance animation
    setTimeout(() => {
      Animated.spring(balanceAnimation, {
        toValue: 1,
        tension: 50,
        friction: 7,
        useNativeDriver: true,
      }).start();
    }, 300);
  }, []);

  const loadWalletData = async () => {
    try {
      await Promise.all([
        fetchWalletBalance(),
        fetchWithdrawalMethods(),
        fetchWithdrawalHistory(),
      ]);
    } catch (error) {
      console.error('Error loading wallet data:', error);
    }
  };

  const onRefresh = async () => {
    setRefreshing(true);
    await loadWalletData();
    setRefreshing(false);
  };

  const handleWithdraw = async () => {
    if (!withdrawAmount || !selectedMethodId) {
      Alert.alert('Error', 'Please enter amount and select withdrawal method');
      return;
    }

    const amount = parseFloat(withdrawAmount);
    if (amount <= 0 || amount > (state.walletBalance?.availableBalance || 0)) {
      Alert.alert('Error', 'Invalid withdrawal amount');
      return;
    }

    try {
      await requestWithdrawal(amount, selectedMethodId);
      setShowWithdrawModal(false);
      setWithdrawAmount('');
      setSelectedMethodId('');
      Alert.alert('Success', 'Withdrawal request submitted successfully');
      await loadWalletData();
    } catch (error: any) {
      Alert.alert('Error', error.message || 'Failed to submit withdrawal request');
    }
  };

  const handleAddMethod = async () => {
    if (!newMethod.name || (!newMethod.accountNumber && !newMethod.phoneNumber)) {
      Alert.alert('Error', 'Please fill in all required fields');
      return;
    }

    try {
      await addWithdrawalMethod({
        type: newMethod.type,
        name: newMethod.name,
        accountNumber: newMethod.accountNumber,
        accountTitle: newMethod.accountTitle,
        bankName: newMethod.bankName,
        phoneNumber: newMethod.phoneNumber,
        isVerified: false,
        isPrimary: state.withdrawalMethods.length === 0,
      });
      
      setShowAddMethodModal(false);
      setNewMethod({
        type: PaymentMethod.BANK_TRANSFER,
        name: '',
        accountNumber: '',
        accountTitle: '',
        bankName: '',
        phoneNumber: '',
      });
      Alert.alert('Success', 'Withdrawal method added successfully');
      await loadWalletData();
    } catch (error: any) {
      Alert.alert('Error', error.message || 'Failed to add withdrawal method');
    }
  };

  const getStatusColor = (status: WithdrawalStatus) => {
    switch (status) {
      case WithdrawalStatus.COMPLETED:
        return '#10b981';
      case WithdrawalStatus.PENDING:
        return '#f59e0b';
      case WithdrawalStatus.PROCESSING:
        return '#3b82f6';
      case WithdrawalStatus.FAILED:
      case WithdrawalStatus.REJECTED:
        return '#ef4444';
      case WithdrawalStatus.CANCELLED:
        return '#6b7280';
      default:
        return '#6b7280';
    }
  };

  const getStatusText = (status: WithdrawalStatus) => {
    return status.charAt(0).toUpperCase() + status.slice(1).replace('_', ' ');
  };

  const getMethodIcon = (type: PaymentMethod) => {
    switch (type) {
      case PaymentMethod.BANK_TRANSFER:
        return { icon: 'card-outline', color: '#3b82f6', bg: '#dbeafe' };
      case PaymentMethod.JAZZCASH:
        return { icon: 'phone-portrait-outline', color: '#dc2626', bg: '#fee2e2' };
      case PaymentMethod.EASYPAISA:
        return { icon: 'phone-portrait-outline', color: '#059669', bg: '#d1fae5' };
      default:
        return { icon: 'wallet-outline', color: '#6b7280', bg: '#f3f4f6' };
    }
  };

  const getMethodBrandName = (type: PaymentMethod) => {
    switch (type) {
      case PaymentMethod.BANK_TRANSFER:
        return 'Bank Transfer';
      case PaymentMethod.JAZZCASH:
        return 'JazzCash';
      case PaymentMethod.EASYPAISA:
        return 'Easypaisa';
      default:
        return 'Digital Wallet';
    }
  };

  const renderWalletBalance = () => {
    if (!state.walletBalance) return null;

    return (
      <Animated.View
        style={{
          marginHorizontal: 20,
          marginBottom: 20,
          opacity: balanceAnimation,
          transform: [{ scale: balanceAnimation }],
        }}
      >
        <LinearGradient
          colors={['#10b981', '#059669']}
          style={{
            borderRadius: 20,
            padding: 24,
            shadowColor: '#10b981',
            shadowOffset: { width: 0, height: 8 },
            shadowOpacity: 0.3,
            shadowRadius: 16,
            elevation: 8,
          }}
        >
          {/* Header with Money Icon */}
          <View style={{
            flexDirection: 'row',
            alignItems: 'center',
            marginBottom: 20,
          }}>
            <View style={{
              width: 48,
              height: 48,
              borderRadius: 24,
              backgroundColor: 'rgba(255,255,255,0.2)',
              alignItems: 'center',
              justifyContent: 'center',
              marginRight: 12,
            }}>
              <Ionicons name="wallet" size={24} color="white" />
            </View>
            <View>
              <Text style={{
                fontSize: 18,
                fontWeight: 'bold',
                color: 'white',
              }}>
                Wallet Balance
              </Text>
              <Text style={{
                fontSize: 14,
                color: 'rgba(255,255,255,0.8)',
              }}>
                Available for withdrawal
              </Text>
            </View>
          </View>

          {/* Balance Amount */}
          <View style={{ alignItems: 'center', marginBottom: 20 }}>
            <Text style={{
              fontSize: 48,
              fontWeight: 'bold',
              color: 'white',
              marginBottom: 8,
            }}>
              {formatCurrency(state.walletBalance.availableBalance)}
            </Text>

            {/* Pending Balance */}
            {state.walletBalance.pendingBalance > 0 && (
              <View style={{
                flexDirection: 'row',
                alignItems: 'center',
                backgroundColor: 'rgba(255,255,255,0.2)',
                paddingHorizontal: 12,
                paddingVertical: 6,
                borderRadius: 20,
              }}>
                <Ionicons name="time" size={14} color="rgba(255,255,255,0.9)" />
                <Text style={{
                  fontSize: 14,
                  color: 'rgba(255,255,255,0.9)',
                  marginLeft: 6,
                }}>
                  {formatCurrency(state.walletBalance.pendingBalance)} pending
                </Text>
              </View>
            )}
          </View>

          {/* Withdraw Button */}
          <TouchableOpacity
            onPress={() => setShowWithdrawModal(true)}
            style={{
              backgroundColor: 'white',
              borderRadius: 16,
              padding: 16,
              flexDirection: 'row',
              alignItems: 'center',
              justifyContent: 'center',
              shadowColor: '#000',
              shadowOffset: { width: 0, height: 4 },
              shadowOpacity: 0.1,
              shadowRadius: 8,
              elevation: 4,
            }}
          >
            <Ionicons name="card" size={20} color="#10b981" style={{ marginRight: 8 }} />
            <Text style={{
              color: '#10b981',
              fontSize: 16,
              fontWeight: '600',
            }}>
              Withdraw Funds
            </Text>
          </TouchableOpacity>
        </LinearGradient>
      </Animated.View>
    );
  };

  const renderTabSection = () => {
    const tabs = [
      { key: 'daily', label: 'Daily', icon: 'calendar' },
      { key: 'weekly', label: 'Weekly', icon: 'calendar-outline' },
      { key: 'bonuses', label: 'Bonuses', icon: 'gift' },
    ] as const;

    return (
      <View style={{
        marginHorizontal: 20,
        marginBottom: 20,
      }}>
        {/* Tab Headers */}
        <View style={{
          flexDirection: 'row',
          backgroundColor: 'white',
          borderRadius: 16,
          padding: 4,
          shadowColor: '#000',
          shadowOffset: { width: 0, height: 2 },
          shadowOpacity: 0.1,
          shadowRadius: 8,
          elevation: 3,
          marginBottom: 16,
        }}>
          {tabs.map((tab) => (
            <TouchableOpacity
              key={tab.key}
              onPress={() => {
                setActiveTab(tab.key);
                Animated.timing(tabAnimation, {
                  toValue: 1,
                  duration: 300,
                  useNativeDriver: true,
                }).start(() => {
                  tabAnimation.setValue(0);
                });
              }}
              style={{
                flex: 1,
                paddingVertical: 12,
                paddingHorizontal: 16,
                borderRadius: 12,
                backgroundColor: activeTab === tab.key ? '#f97316' : 'transparent',
                flexDirection: 'row',
                alignItems: 'center',
                justifyContent: 'center',
              }}
            >
              <Ionicons
                name={tab.icon as any}
                size={16}
                color={activeTab === tab.key ? 'white' : '#6b7280'}
                style={{ marginRight: 6 }}
              />
              <Text style={{
                fontSize: 14,
                fontWeight: activeTab === tab.key ? '600' : '500',
                color: activeTab === tab.key ? 'white' : '#6b7280',
              }}>
                {tab.label}
              </Text>
            </TouchableOpacity>
          ))}
        </View>

        {/* Tab Content */}
        <Animated.View
          style={{
            opacity: tabAnimation.interpolate({
              inputRange: [0, 1],
              outputRange: [1, 0.5],
            }),
            transform: [{
              scale: tabAnimation.interpolate({
                inputRange: [0, 1],
                outputRange: [1, 0.95],
              }),
            }],
          }}
        >
          {renderTabContent()}
        </Animated.View>
      </View>
    );
  };

  const renderTabContent = () => {
    switch (activeTab) {
      case 'daily':
        return renderDailyEarnings();
      case 'weekly':
        return renderWeeklyEarnings();
      case 'bonuses':
        return renderBonuses();
      default:
        return null;
    }
  };

  const renderDailyEarnings = () => {
    const mockDailyData = [
      { date: 'Today', amount: 125.50, orders: 8, tips: 25.50 },
      { date: 'Yesterday', amount: 98.75, orders: 6, tips: 18.75 },
      { date: 'Jan 20', amount: 142.25, orders: 9, tips: 32.25 },
      { date: 'Jan 19', amount: 87.50, orders: 5, tips: 12.50 },
      { date: 'Jan 18', amount: 156.75, orders: 10, tips: 41.75 },
      { date: 'Jan 17', amount: 89.25, orders: 6, tips: 14.25 },
      { date: 'Jan 16', amount: 134.50, orders: 8, tips: 29.50 },
    ];

    return (
      <View>
        {/* Daily Earnings Trend Chart */}
        <View style={{
          backgroundColor: 'white',
          borderRadius: 16,
          padding: 20,
          shadowColor: '#000',
          shadowOffset: { width: 0, height: 2 },
          shadowOpacity: 0.1,
          shadowRadius: 8,
          elevation: 3,
          marginBottom: 16,
        }}>
          <View style={{
            flexDirection: 'row',
            justifyContent: 'space-between',
            alignItems: 'center',
            marginBottom: 16,
          }}>
            <Text style={{
              fontSize: 18,
              fontWeight: 'bold',
              color: '#111827',
            }}>
              Daily Earnings Trend
            </Text>
            <TouchableOpacity
              onPress={() => navigation.navigate('PerformanceAnalytics' as never)}
              style={{
                flexDirection: 'row',
                alignItems: 'center',
                backgroundColor: '#f97316',
                paddingHorizontal: 8,
                paddingVertical: 4,
                borderRadius: 6,
              }}
            >
              <Ionicons name="analytics" size={14} color="white" />
              <Text style={{ color: 'white', fontWeight: '600', marginLeft: 4, fontSize: 12 }}>
                View Details
              </Text>
            </TouchableOpacity>
          </View>

          {/* Simple Chart Visualization */}
          <View style={{
            height: 180,
            backgroundColor: '#f8fafc',
            borderRadius: 12,
            padding: 16,
            marginBottom: 16,
          }}>
            <View style={{
              flexDirection: 'row',
              justifyContent: 'space-between',
              alignItems: 'flex-end',
              height: '100%',
              paddingBottom: 20,
            }}>
              {mockDailyData.slice(-7).map((day, index) => {
                const maxAmount = Math.max(...mockDailyData.map(d => d.amount));
                const barHeight = (day.amount / maxAmount) * 120;
                return (
                  <View key={index} style={{ alignItems: 'center', flex: 1 }}>
                    <View style={{
                      width: 20,
                      height: barHeight,
                      backgroundColor: '#f97316',
                      borderRadius: 4,
                      marginBottom: 8,
                    }} />
                    <Text style={{
                      fontSize: 10,
                      color: '#6b7280',
                      textAlign: 'center',
                      transform: [{ rotate: '-45deg' }],
                    }}>
                      {day.date.split(' ')[1] || day.date.slice(0, 3)}
                    </Text>
                  </View>
                );
              })}
            </View>
          </View>

          {/* Chart Summary */}
          <View style={{
            flexDirection: 'row',
            justifyContent: 'space-between',
            backgroundColor: '#f8fafc',
            padding: 12,
            borderRadius: 8,
          }}>
            <View style={{ alignItems: 'center' }}>
              <Text style={{ fontSize: 12, color: '#6b7280' }}>Avg Daily</Text>
              <Text style={{ fontSize: 16, fontWeight: 'bold', color: '#111827' }}>
                {formatCurrency(mockDailyData.reduce((sum, day) => sum + day.amount, 0) / mockDailyData.length)}
              </Text>
            </View>
            <View style={{ alignItems: 'center' }}>
              <Text style={{ fontSize: 12, color: '#6b7280' }}>Best Day</Text>
              <Text style={{ fontSize: 16, fontWeight: 'bold', color: '#10b981' }}>
                {formatCurrency(Math.max(...mockDailyData.map(d => d.amount)))}
              </Text>
            </View>
            <View style={{ alignItems: 'center' }}>
              <Text style={{ fontSize: 12, color: '#6b7280' }}>Total Week</Text>
              <Text style={{ fontSize: 16, fontWeight: 'bold', color: '#f97316' }}>
                {formatCurrency(mockDailyData.slice(-7).reduce((sum, day) => sum + day.amount, 0))}
              </Text>
            </View>
          </View>
        </View>

        {/* Daily Earnings List */}
        <View style={{
          backgroundColor: 'white',
          borderRadius: 16,
          padding: 20,
          shadowColor: '#000',
          shadowOffset: { width: 0, height: 2 },
          shadowOpacity: 0.1,
          shadowRadius: 8,
          elevation: 3,
        }}>
          <Text style={{
            fontSize: 18,
            fontWeight: 'bold',
            color: '#111827',
            marginBottom: 16,
          }}>
            Daily Earnings
          </Text>

          {mockDailyData.slice(0, 4).map((day, index) => (
            <View
              key={index}
              style={{
                flexDirection: 'row',
                alignItems: 'center',
                justifyContent: 'space-between',
                paddingVertical: 12,
                borderBottomWidth: index < 3 ? 1 : 0,
                borderBottomColor: '#f3f4f6',
              }}
            >
              <View>
                <Text style={{
                  fontSize: 16,
                  fontWeight: '600',
                  color: '#111827',
                }}>
                  {day.date}
                </Text>
                <Text style={{
                  fontSize: 14,
                  color: '#6b7280',
                }}>
                  {day.orders} orders • {formatCurrency(day.tips)} tips
                </Text>
              </View>
              <Text style={{
                fontSize: 18,
                fontWeight: 'bold',
                color: '#10b981',
              }}>
                {formatCurrency(day.amount)}
              </Text>
            </View>
          ))}
        </View>
      </View>
    );
  };

  const renderWeeklyEarnings = () => {
    const mockWeeklyData = [
      { week: 'This Week', amount: 567.25, days: 5, average: 113.45 },
      { week: 'Last Week', amount: 642.80, days: 6, average: 107.13 },
      { week: 'Jan 8-14', amount: 598.50, days: 6, average: 99.75 },
      { week: 'Jan 1-7', amount: 523.75, days: 5, average: 104.75 },
    ];

    return (
      <View style={{
        backgroundColor: 'white',
        borderRadius: 16,
        padding: 20,
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.1,
        shadowRadius: 8,
        elevation: 3,
      }}>
        <Text style={{
          fontSize: 18,
          fontWeight: 'bold',
          color: '#111827',
          marginBottom: 16,
        }}>
          Weekly Earnings
        </Text>

        {mockWeeklyData.map((week, index) => (
          <View
            key={index}
            style={{
              flexDirection: 'row',
              alignItems: 'center',
              justifyContent: 'space-between',
              paddingVertical: 12,
              borderBottomWidth: index < mockWeeklyData.length - 1 ? 1 : 0,
              borderBottomColor: '#f3f4f6',
            }}
          >
            <View>
              <Text style={{
                fontSize: 16,
                fontWeight: '600',
                color: '#111827',
              }}>
                {week.week}
              </Text>
              <Text style={{
                fontSize: 14,
                color: '#6b7280',
              }}>
                {week.days} days • {formatCurrency(week.average)} avg/day
              </Text>
            </View>
            <Text style={{
              fontSize: 18,
              fontWeight: 'bold',
              color: '#10b981',
            }}>
              {formatCurrency(week.amount)}
            </Text>
          </View>
        ))}
      </View>
    );
  };

  const renderBonuses = () => {
    const mockBonusData = [
      {
        type: 'Peak Hour Bonus',
        amount: 45.00,
        date: 'Today',
        icon: 'flash',
        color: '#f59e0b'
      },
      {
        type: 'Weather Bonus',
        amount: 25.00,
        date: 'Yesterday',
        icon: 'rainy',
        color: '#3b82f6'
      },
      {
        type: 'Completion Bonus',
        amount: 50.00,
        date: 'Jan 20',
        icon: 'trophy',
        color: '#10b981'
      },
      {
        type: 'Rating Bonus',
        amount: 30.00,
        date: 'Jan 19',
        icon: 'star',
        color: '#f97316'
      },
    ];

    return (
      <View style={{
        backgroundColor: 'white',
        borderRadius: 16,
        padding: 20,
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.1,
        shadowRadius: 8,
        elevation: 3,
      }}>
        <Text style={{
          fontSize: 18,
          fontWeight: 'bold',
          color: '#111827',
          marginBottom: 16,
        }}>
          Recent Bonuses
        </Text>

        {mockBonusData.map((bonus, index) => (
          <View
            key={index}
            style={{
              flexDirection: 'row',
              alignItems: 'center',
              paddingVertical: 12,
              borderBottomWidth: index < mockBonusData.length - 1 ? 1 : 0,
              borderBottomColor: '#f3f4f6',
            }}
          >
            <View style={{
              width: 40,
              height: 40,
              borderRadius: 20,
              backgroundColor: `${bonus.color}20`,
              alignItems: 'center',
              justifyContent: 'center',
              marginRight: 12,
            }}>
              <Ionicons name={bonus.icon as any} size={20} color={bonus.color} />
            </View>

            <View style={{ flex: 1 }}>
              <Text style={{
                fontSize: 16,
                fontWeight: '600',
                color: '#111827',
              }}>
                {bonus.type}
              </Text>
              <Text style={{
                fontSize: 14,
                color: '#6b7280',
              }}>
                {bonus.date}
              </Text>
            </View>

            <Text style={{
              fontSize: 18,
              fontWeight: 'bold',
              color: bonus.color,
            }}>
              +{formatCurrency(bonus.amount)}
            </Text>
          </View>
        ))}
      </View>
    );
  };

  const renderWithdrawalMethods = () => (
    <Card variant="elevated" margin="md" padding="lg">
      <View style={{ flexDirection: 'row', justifyContent: 'space-between', alignItems: 'center', marginBottom: 16 }}>
        <Text style={{ fontSize: 18, fontWeight: 'bold', color: '#111827' }}>
          Withdrawal Methods
        </Text>
        <TouchableOpacity
          onPress={() => setShowAddMethodModal(true)}
          style={{
            flexDirection: 'row',
            alignItems: 'center',
            backgroundColor: '#f97316',
            paddingHorizontal: 12,
            paddingVertical: 6,
            borderRadius: 8,
          }}
        >
          <Ionicons name="add" size={16} color="white" />
          <Text style={{ color: 'white', fontWeight: '600', marginLeft: 4 }}>
            Add
          </Text>
        </TouchableOpacity>
      </View>

      {state.withdrawalMethods.length === 0 ? (
        <View style={{ alignItems: 'center', paddingVertical: 20 }}>
          <Ionicons name="card-outline" size={48} color="#d1d5db" />
          <Text style={{ fontSize: 16, color: '#6b7280', marginTop: 8 }}>
            No withdrawal methods added
          </Text>
          <Text style={{ fontSize: 14, color: '#9ca3af', textAlign: 'center', marginTop: 4 }}>
            Add a bank account or mobile wallet to withdraw your earnings
          </Text>
        </View>
      ) : (
        <View style={{ gap: 12 }}>
          {state.withdrawalMethods.map((method) => (
            <View
              key={method.id}
              style={{
                flexDirection: 'row',
                alignItems: 'center',
                padding: 16,
                backgroundColor: '#f9fafb',
                borderRadius: 12,
                borderWidth: method.isPrimary ? 2 : 1,
                borderColor: method.isPrimary ? '#f97316' : '#e5e7eb',
              }}
            >
              <View style={{
                width: 40,
                height: 40,
                borderRadius: 20,
                backgroundColor: '#f97316',
                alignItems: 'center',
                justifyContent: 'center',
                marginRight: 12,
              }}>
                <Ionicons name={getMethodIcon(method.type)} size={20} color="white" />
              </View>

              <View style={{ flex: 1 }}>
                <Text style={{ fontSize: 16, fontWeight: '600', color: '#111827' }}>
                  {method.name}
                </Text>
                <Text style={{ fontSize: 14, color: '#6b7280' }}>
                  {method.accountNumber || method.phoneNumber}
                </Text>
              </View>

              <View style={{ alignItems: 'flex-end' }}>
                {method.isPrimary && (
                  <Badge text="Primary" style={{ backgroundColor: '#f97316', marginBottom: 4 }} />
                )}
                <Badge
                  text={method.isVerified ? 'Verified' : 'Pending'}
                  style={{
                    backgroundColor: method.isVerified ? '#10b981' : '#f59e0b',
                  }}
                />
              </View>
            </View>
          ))}
        </View>
      )}
    </Card>
  );

  const renderWithdrawalHistory = () => {
    const mockWithdrawals = [
      {
        id: '1',
        amount: 500.00,
        method: PaymentMethod.BANK_TRANSFER,
        status: 'completed',
        date: '2024-01-22',
        transactionId: 'TXN123456789',
        accountInfo: 'HBL ****1234'
      },
      {
        id: '2',
        amount: 250.00,
        method: PaymentMethod.JAZZCASH,
        status: 'processing',
        date: '2024-01-21',
        transactionId: 'TXN987654321',
        accountInfo: '0300-*******'
      },
      {
        id: '3',
        amount: 350.00,
        method: PaymentMethod.EASYPAISA,
        status: 'completed',
        date: '2024-01-20',
        transactionId: 'TXN456789123',
        accountInfo: '0321-*******'
      },
      {
        id: '4',
        amount: 150.00,
        method: PaymentMethod.BANK_TRANSFER,
        status: 'failed',
        date: '2024-01-19',
        transactionId: 'TXN789123456',
        accountInfo: 'UBL ****5678'
      },
    ];

    const getStatusColor = (status: string) => {
      switch (status) {
        case 'completed':
          return { color: '#10b981', bg: '#d1fae5' };
        case 'processing':
          return { color: '#f59e0b', bg: '#fef3c7' };
        case 'failed':
          return { color: '#ef4444', bg: '#fee2e2' };
        default:
          return { color: '#6b7280', bg: '#f3f4f6' };
      }
    };

    return (
      <View style={{
        marginHorizontal: 20,
        marginBottom: 20,
      }}>
        <View style={{
          backgroundColor: 'white',
          borderRadius: 16,
          padding: 20,
          shadowColor: '#000',
          shadowOffset: { width: 0, height: 2 },
          shadowOpacity: 0.1,
          shadowRadius: 8,
          elevation: 3,
        }}>
          <Text style={{
            fontSize: 18,
            fontWeight: 'bold',
            color: '#111827',
            marginBottom: 16,
          }}>
            Withdrawal History
          </Text>

          {mockWithdrawals.map((withdrawal, index) => {
            const methodStyle = getMethodIcon(withdrawal.method);
            const statusStyle = getStatusColor(withdrawal.status);

            return (
              <View
                key={withdrawal.id}
                style={{
                  flexDirection: 'row',
                  alignItems: 'center',
                  paddingVertical: 16,
                  borderBottomWidth: index < mockWithdrawals.length - 1 ? 1 : 0,
                  borderBottomColor: '#f3f4f6',
                }}
              >
                {/* Payment Method Icon */}
                <View style={{
                  width: 48,
                  height: 48,
                  borderRadius: 24,
                  backgroundColor: methodStyle.bg,
                  alignItems: 'center',
                  justifyContent: 'center',
                  marginRight: 12,
                }}>
                  <Ionicons name={methodStyle.icon as any} size={24} color={methodStyle.color} />
                </View>

                {/* Transaction Details */}
                <View style={{ flex: 1 }}>
                  <View style={{
                    flexDirection: 'row',
                    alignItems: 'center',
                    marginBottom: 4,
                  }}>
                    <Text style={{
                      fontSize: 16,
                      fontWeight: '600',
                      color: '#111827',
                      marginRight: 8,
                    }}>
                      {getMethodBrandName(withdrawal.method)}
                    </Text>
                    <View style={{
                      paddingHorizontal: 8,
                      paddingVertical: 2,
                      borderRadius: 12,
                      backgroundColor: statusStyle.bg,
                    }}>
                      <Text style={{
                        fontSize: 12,
                        fontWeight: '600',
                        color: statusStyle.color,
                        textTransform: 'capitalize',
                      }}>
                        {withdrawal.status}
                      </Text>
                    </View>
                  </View>

                  <Text style={{
                    fontSize: 14,
                    color: '#6b7280',
                    marginBottom: 2,
                  }}>
                    {withdrawal.accountInfo}
                  </Text>

                  <Text style={{
                    fontSize: 12,
                    color: '#9ca3af',
                  }}>
                    {formatDate(withdrawal.date)} • {withdrawal.transactionId}
                  </Text>
                </View>

                {/* Amount */}
                <Text style={{
                  fontSize: 18,
                  fontWeight: 'bold',
                  color: withdrawal.status === 'failed' ? '#ef4444' : '#111827',
                }}>
                  {withdrawal.status === 'failed' ? '-' : ''}{formatCurrency(withdrawal.amount)}
                </Text>
              </View>
            );
          })}

          {/* View All Button */}
          <TouchableOpacity
            onPress={() => navigation.navigate('WithdrawalHistory' as never)}
            style={{
              marginTop: 16,
              paddingVertical: 12,
              alignItems: 'center',
              borderTopWidth: 1,
              borderTopColor: '#f3f4f6',
            }}
          >
            <Text style={{
              fontSize: 16,
              fontWeight: '600',
              color: '#f97316',
            }}>
              View All Withdrawals
            </Text>
          </TouchableOpacity>
        </View>
      </View>
    );
  };

  if (state.isLoading && !refreshing) {
    return (
      <SafeAreaView style={{ flex: 1, backgroundColor: '#f8fafc' }}>
        <LoadingSpinner message="Loading wallet data..." />
      </SafeAreaView>
    );
  }

  return (
    <View style={{ flex: 1, backgroundColor: '#f8fafc' }}>
      <StatusBar barStyle="light-content" backgroundColor="#10b981" />

      {/* Header */}
      <LinearGradient
        colors={['#10b981', '#059669']}
        style={{
          paddingTop: StatusBar.currentHeight || 0,
          paddingBottom: 20,
        }}
      >
        <SafeAreaView>
          <View style={{
            flexDirection: 'row',
            alignItems: 'center',
            justifyContent: 'space-between',
            paddingHorizontal: 20,
            paddingTop: 16,
          }}>
            <TouchableOpacity
              onPress={() => navigation.goBack()}
              style={{
                width: 40,
                height: 40,
                borderRadius: 20,
                backgroundColor: 'rgba(255,255,255,0.2)',
                alignItems: 'center',
                justifyContent: 'center',
              }}
            >
              <Ionicons name="arrow-back" size={24} color="white" />
            </TouchableOpacity>

            <Text style={{
              fontSize: 20,
              fontWeight: 'bold',
              color: 'white',
            }}>
              Digital Wallet
            </Text>

            <TouchableOpacity
              style={{
                width: 40,
                height: 40,
                borderRadius: 20,
                backgroundColor: 'rgba(255,255,255,0.2)',
                alignItems: 'center',
                justifyContent: 'center',
              }}
            >
              <Ionicons name="notifications" size={20} color="white" />
            </TouchableOpacity>
          </View>
        </SafeAreaView>
      </LinearGradient>

      <Animated.View
        style={{
          flex: 1,
          opacity: fadeAnimation,
          transform: [{ translateY: slideAnimation }],
        }}
      >
        <ScrollView
          style={{ flex: 1 }}
          refreshControl={
            <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
          }
          showsVerticalScrollIndicator={false}
          contentContainerStyle={{ paddingBottom: 100 }}
        >
          {renderWalletBalance()}
          {renderTabSection()}
          {renderWithdrawalHistory()}
        </ScrollView>
      </Animated.View>

      {/* Withdraw Modal */}
      <Modal
        visible={showWithdrawModal}
        animationType="slide"
        presentationStyle="pageSheet"
        onRequestClose={() => setShowWithdrawModal(false)}
      >
        <SafeAreaView style={{ flex: 1, backgroundColor: '#f8fafc' }}>
          <View style={{
            paddingHorizontal: 20,
            paddingVertical: 16,
            backgroundColor: '#ffffff',
            borderBottomWidth: 1,
            borderBottomColor: '#e5e7eb',
            flexDirection: 'row',
            alignItems: 'center',
            justifyContent: 'space-between',
          }}>
            <Text style={{ fontSize: 20, fontWeight: 'bold', color: '#111827' }}>
              Withdraw Money
            </Text>
            <TouchableOpacity onPress={() => setShowWithdrawModal(false)}>
              <Ionicons name="close" size={24} color="#111827" />
            </TouchableOpacity>
          </View>

          <ScrollView style={{ flex: 1, padding: 20 }}>
            <Card variant="elevated" padding="lg" style={{ marginBottom: 20 }}>
              <Text style={{ fontSize: 16, fontWeight: '600', color: '#111827', marginBottom: 8 }}>
                Available Balance
              </Text>
              <Text style={{ fontSize: 28, fontWeight: 'bold', color: '#10b981' }}>
                {formatCurrency(state.walletBalance?.availableBalance || 0)}
              </Text>
            </Card>

            <Card variant="elevated" padding="lg" style={{ marginBottom: 20 }}>
              <Text style={{ fontSize: 16, fontWeight: '600', color: '#111827', marginBottom: 12 }}>
                Withdrawal Amount
              </Text>
              <TextInput
                style={{
                  borderWidth: 1,
                  borderColor: '#d1d5db',
                  borderRadius: 8,
                  padding: 12,
                  fontSize: 16,
                  backgroundColor: '#ffffff',
                }}
                placeholder="Enter amount"
                value={withdrawAmount}
                onChangeText={setWithdrawAmount}
                keyboardType="numeric"
              />
            </Card>

            <Card variant="elevated" padding="lg" style={{ marginBottom: 20 }}>
              <Text style={{ fontSize: 16, fontWeight: '600', color: '#111827', marginBottom: 12 }}>
                Withdrawal Method
              </Text>
              {state.withdrawalMethods.map((method) => (
                <TouchableOpacity
                  key={method.id}
                  onPress={() => setSelectedMethodId(method.id)}
                  style={{
                    flexDirection: 'row',
                    alignItems: 'center',
                    padding: 12,
                    borderRadius: 8,
                    borderWidth: 1,
                    borderColor: selectedMethodId === method.id ? '#f97316' : '#e5e7eb',
                    backgroundColor: selectedMethodId === method.id ? '#fef3e2' : '#ffffff',
                    marginBottom: 8,
                  }}
                >
                  <View style={{
                    width: 32,
                    height: 32,
                    borderRadius: 16,
                    backgroundColor: '#f97316',
                    alignItems: 'center',
                    justifyContent: 'center',
                    marginRight: 12,
                  }}>
                    <Ionicons name={getMethodIcon(method.type)} size={16} color="white" />
                  </View>
                  <View style={{ flex: 1 }}>
                    <Text style={{ fontSize: 14, fontWeight: '600', color: '#111827' }}>
                      {method.name}
                    </Text>
                    <Text style={{ fontSize: 12, color: '#6b7280' }}>
                      {method.accountNumber || method.phoneNumber}
                    </Text>
                  </View>
                  {selectedMethodId === method.id && (
                    <Ionicons name="checkmark-circle" size={20} color="#f97316" />
                  )}
                </TouchableOpacity>
              ))}
            </Card>

            <Button
              title="Submit Withdrawal Request"
              onPress={handleWithdraw}
              disabled={!withdrawAmount || !selectedMethodId}
              fullWidth
              size="lg"
            />
          </ScrollView>
        </SafeAreaView>
      </Modal>

      {/* Add Method Modal */}
      <Modal
        visible={showAddMethodModal}
        animationType="slide"
        presentationStyle="pageSheet"
        onRequestClose={() => setShowAddMethodModal(false)}
      >
        <SafeAreaView style={{ flex: 1, backgroundColor: '#f8fafc' }}>
          <View style={{
            paddingHorizontal: 20,
            paddingVertical: 16,
            backgroundColor: '#ffffff',
            borderBottomWidth: 1,
            borderBottomColor: '#e5e7eb',
            flexDirection: 'row',
            alignItems: 'center',
            justifyContent: 'space-between',
          }}>
            <Text style={{ fontSize: 20, fontWeight: 'bold', color: '#111827' }}>
              Add Withdrawal Method
            </Text>
            <TouchableOpacity onPress={() => setShowAddMethodModal(false)}>
              <Ionicons name="close" size={24} color="#111827" />
            </TouchableOpacity>
          </View>

          <ScrollView style={{ flex: 1, padding: 20 }}>
            <Card variant="elevated" padding="lg" style={{ marginBottom: 20 }}>
              <Text style={{ fontSize: 16, fontWeight: '600', color: '#111827', marginBottom: 12 }}>
                Payment Method Type
              </Text>
              <View style={{ flexDirection: 'row', gap: 8, marginBottom: 16 }}>
                {[
                  { type: PaymentMethod.BANK_TRANSFER, label: 'Bank' },
                  { type: PaymentMethod.JAZZCASH, label: 'JazzCash' },
                  { type: PaymentMethod.EASYPAISA, label: 'Easypaisa' },
                ].map((option) => (
                  <TouchableOpacity
                    key={option.type}
                    onPress={() => setNewMethod({ ...newMethod, type: option.type })}
                    style={{
                      flex: 1,
                      padding: 12,
                      borderRadius: 8,
                      borderWidth: 1,
                      borderColor: newMethod.type === option.type ? '#f97316' : '#e5e7eb',
                      backgroundColor: newMethod.type === option.type ? '#fef3e2' : '#ffffff',
                      alignItems: 'center',
                    }}
                  >
                    <Text style={{
                      fontSize: 14,
                      fontWeight: '600',
                      color: newMethod.type === option.type ? '#f97316' : '#6b7280',
                    }}>
                      {option.label}
                    </Text>
                  </TouchableOpacity>
                ))}
              </View>

              <TextInput
                style={{
                  borderWidth: 1,
                  borderColor: '#d1d5db',
                  borderRadius: 8,
                  padding: 12,
                  fontSize: 16,
                  backgroundColor: '#ffffff',
                  marginBottom: 12,
                }}
                placeholder="Method Name (e.g., My Bank Account)"
                value={newMethod.name}
                onChangeText={(text) => setNewMethod({ ...newMethod, name: text })}
              />

              {newMethod.type === PaymentMethod.BANK_TRANSFER ? (
                <>
                  <TextInput
                    style={{
                      borderWidth: 1,
                      borderColor: '#d1d5db',
                      borderRadius: 8,
                      padding: 12,
                      fontSize: 16,
                      backgroundColor: '#ffffff',
                      marginBottom: 12,
                    }}
                    placeholder="Bank Name"
                    value={newMethod.bankName}
                    onChangeText={(text) => setNewMethod({ ...newMethod, bankName: text })}
                  />
                  <TextInput
                    style={{
                      borderWidth: 1,
                      borderColor: '#d1d5db',
                      borderRadius: 8,
                      padding: 12,
                      fontSize: 16,
                      backgroundColor: '#ffffff',
                      marginBottom: 12,
                    }}
                    placeholder="Account Number"
                    value={newMethod.accountNumber}
                    onChangeText={(text) => setNewMethod({ ...newMethod, accountNumber: text })}
                  />
                  <TextInput
                    style={{
                      borderWidth: 1,
                      borderColor: '#d1d5db',
                      borderRadius: 8,
                      padding: 12,
                      fontSize: 16,
                      backgroundColor: '#ffffff',
                    }}
                    placeholder="Account Title"
                    value={newMethod.accountTitle}
                    onChangeText={(text) => setNewMethod({ ...newMethod, accountTitle: text })}
                  />
                </>
              ) : (
                <TextInput
                  style={{
                    borderWidth: 1,
                    borderColor: '#d1d5db',
                    borderRadius: 8,
                    padding: 12,
                    fontSize: 16,
                    backgroundColor: '#ffffff',
                  }}
                  placeholder="Phone Number"
                  value={newMethod.phoneNumber}
                  onChangeText={(text) => setNewMethod({ ...newMethod, phoneNumber: text })}
                  keyboardType="phone-pad"
                />
              )}
            </Card>

            <Button
              title="Add Method"
              onPress={handleAddMethod}
              fullWidth
              size="lg"
            />
          </ScrollView>
        </SafeAreaView>
      </Modal>
    </View>
  );
};

export default WalletScreen;
