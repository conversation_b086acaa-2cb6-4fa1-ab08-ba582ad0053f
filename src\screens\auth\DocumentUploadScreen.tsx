import React, { useState } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  Image,
  Alert,
  ScrollView,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import * as ImagePicker from 'expo-image-picker';
import { <PERSON><PERSON>, <PERSON> } from '../../components/ui';

interface DocumentUploadScreenProps {
  documentType: string;
  documentTitle: string;
  onUpload: (uri: string) => void;
  onBack: () => void;
  existingImage?: string;
}

const DocumentUploadScreen: React.FC<DocumentUploadScreenProps> = ({
  documentType,
  documentTitle,
  onUpload,
  onBack,
  existingImage,
}) => {
  const [selectedImage, setSelectedImage] = useState<string | null>(existingImage || null);
  const [isUploading, setIsUploading] = useState(false);

  const requestPermissions = async () => {
    const { status } = await ImagePicker.requestMediaLibraryPermissionsAsync();
    if (status !== 'granted') {
      Alert.alert(
        'Permission Required',
        'Please grant camera roll permissions to upload documents.',
        [{ text: 'OK' }]
      );
      return false;
    }
    return true;
  };

  const pickImage = async (source: 'camera' | 'library') => {
    const hasPermission = await requestPermissions();
    if (!hasPermission) return;

    try {
      let result;
      
      if (source === 'camera') {
        const { status } = await ImagePicker.requestCameraPermissionsAsync();
        if (status !== 'granted') {
          Alert.alert('Permission Required', 'Camera permission is required to take photos.');
          return;
        }
        
        result = await ImagePicker.launchCameraAsync({
          mediaTypes: ImagePicker.MediaTypeOptions.Images,
          allowsEditing: true,
          aspect: [4, 3],
          quality: 0.8,
        });
      } else {
        result = await ImagePicker.launchImageLibraryAsync({
          mediaTypes: ImagePicker.MediaTypeOptions.Images,
          allowsEditing: true,
          aspect: [4, 3],
          quality: 0.8,
        });
      }

      if (!result.canceled && result.assets[0]) {
        setSelectedImage(result.assets[0].uri);
      }
    } catch (error) {
      Alert.alert('Error', 'Failed to pick image. Please try again.');
    }
  };

  const handleUpload = async () => {
    if (!selectedImage) {
      Alert.alert('No Image Selected', 'Please select an image to upload.');
      return;
    }

    setIsUploading(true);
    try {
      // Simulate upload delay
      await new Promise(resolve => setTimeout(resolve, 2000));
      onUpload(selectedImage);
    } catch (error) {
      Alert.alert('Upload Failed', 'Failed to upload document. Please try again.');
    } finally {
      setIsUploading(false);
    }
  };

  const showImageSourceOptions = () => {
    Alert.alert(
      'Select Image Source',
      'Choose how you want to add the document',
      [
        { text: 'Camera', onPress: () => pickImage('camera') },
        { text: 'Photo Library', onPress: () => pickImage('library') },
        { text: 'Cancel', style: 'cancel' },
      ]
    );
  };

  const getDocumentInstructions = (type: string) => {
    switch (type) {
      case 'cnic':
        return [
          'Take clear photos of both front and back sides',
          'Ensure all text is readable',
          'Avoid glare and shadows',
          'CNIC should be valid and not expired',
        ];
      case 'drivingLicense':
        return [
          'Upload a clear photo of your driving license',
          'License should be valid for your vehicle type',
          'Ensure expiry date is visible and valid',
          'All text should be clearly readable',
        ];
      case 'vehicleRegistration':
        return [
          'Upload vehicle registration certificate',
          'Document should match your vehicle details',
          'Ensure all information is clearly visible',
          'Registration should be current and valid',
        ];
      case 'profilePhoto':
        return [
          'Take a clear photo of yourself',
          'Face should be clearly visible',
          'Good lighting with no shadows',
          'Look directly at the camera',
        ];
      case 'passport':
        return [
          'Upload a clear photo of passport main page',
          'All text should be readable',
          'Passport should be valid and not expired',
          'Ensure good lighting without glare',
        ];
      default:
        return ['Upload a clear, readable photo of the document'];
    }
  };

  return (
    <SafeAreaView className="flex-1 bg-gray-50">
      {/* Header */}
      <View className="flex-row items-center p-4 bg-white border-b border-gray-200">
        <TouchableOpacity onPress={onBack} className="mr-4">
          <Ionicons name="arrow-back" size={24} color="#374151" />
        </TouchableOpacity>
        <Text className="text-lg font-semibold text-gray-800">Upload {documentTitle}</Text>
      </View>

      <ScrollView className="flex-1 p-4">
        {/* Instructions */}
        <Card className="p-4 mb-6">
          <View className="flex-row items-start mb-3">
            <Ionicons name="information-circle" size={20} color="#3B82F6" />
            <Text className="text-blue-800 font-semibold ml-2">Upload Instructions</Text>
          </View>
          {getDocumentInstructions(documentType).map((instruction, index) => (
            <Text key={index} className="text-gray-700 text-sm leading-5 mb-1">
              • {instruction}
            </Text>
          ))}
        </Card>

        {/* Image Preview */}
        <Card className="p-4 mb-6">
          <Text className="text-lg font-semibold text-gray-800 mb-4">Document Preview</Text>
          
          {selectedImage ? (
            <View>
              <Image
                source={{ uri: selectedImage }}
                className="w-full h-64 rounded-lg mb-4"
                resizeMode="contain"
              />
              <TouchableOpacity
                onPress={showImageSourceOptions}
                className="bg-gray-100 border-2 border-dashed border-gray-300 rounded-lg p-4 items-center"
              >
                <Ionicons name="camera" size={24} color="#6B7280" />
                <Text className="text-gray-600 font-medium mt-2">Change Image</Text>
              </TouchableOpacity>
            </View>
          ) : (
            <TouchableOpacity
              onPress={showImageSourceOptions}
              className="bg-gray-100 border-2 border-dashed border-gray-300 rounded-lg p-8 items-center"
            >
              <Ionicons name="camera" size={48} color="#6B7280" />
              <Text className="text-gray-600 font-medium mt-4 mb-2">Add Document Photo</Text>
              <Text className="text-gray-500 text-sm text-center">
                Tap to take a photo or select from gallery
              </Text>
            </TouchableOpacity>
          )}
        </Card>

        {/* Tips */}
        <Card className="p-4">
          <View className="flex-row items-start mb-3">
            <Ionicons name="bulb" size={20} color="#F59E0B" />
            <Text className="text-yellow-800 font-semibold ml-2">Tips for Best Results</Text>
          </View>
          <Text className="text-gray-700 text-sm leading-5 mb-2">
            📱 Use good lighting - natural light works best
          </Text>
          <Text className="text-gray-700 text-sm leading-5 mb-2">
            📐 Keep the document flat and straight
          </Text>
          <Text className="text-gray-700 text-sm leading-5 mb-2">
            🔍 Make sure all text is clearly readable
          </Text>
          <Text className="text-gray-700 text-sm leading-5">
            ✨ Avoid shadows, glare, and blurry images
          </Text>
        </Card>
      </ScrollView>

      {/* Bottom Actions */}
      <View className="p-4 bg-white border-t border-gray-200">
        <Button
          title="Upload Document"
          onPress={handleUpload}
          disabled={!selectedImage}
          loading={isUploading}
          className="mb-2"
        />
        <TouchableOpacity onPress={onBack}>
          <Text className="text-center text-gray-600 font-medium">Cancel</Text>
        </TouchableOpacity>
      </View>
    </SafeAreaView>
  );
};

export default DocumentUploadScreen;
