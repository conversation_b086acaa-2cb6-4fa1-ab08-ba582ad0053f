// Re-export all types for easy importing
export * from './auth';
export * from './orders';
export * from './user';
export * from './location';
export * from './earnings';
export * from './deliveryHistory';
export * from './profile';
export * from './support';
export * from './safety';
export * from './advancedTools';

// Navigation types
export type RootStackParamList = {
  Splash: undefined;
  Auth: undefined;
  Main: undefined;
};

export type AuthStackParamList = {
  Splash: undefined;
  Onboarding: undefined;
  Login: undefined;
  OTPVerification: { phoneNumber: string };
  Registration: undefined;
  DocumentStatus: undefined;
  DocumentUpload: { documentType: string };
  DemoCredentials: undefined;
  ForgotPassword: undefined;
  ResetPassword: { token: string };
};

export type MainTabParamList = {
  Dashboard: undefined;
  Orders: undefined;
  Earnings: undefined;
  History: undefined;
  Profile: undefined;
  Support: undefined;
  Safety: undefined;
  AdvancedTools: undefined;
};

export type DashboardStackParamList = {
  DashboardMain: undefined;
  OrderRequests: undefined;
};

export type OrderStackParamList = {
  OrderList: undefined;
  OrderDetails: { orderId: string };
  OrderHistory: undefined;
  PickupFlow: { orderId: string };
  DeliveryFlow: { orderId: string };
  TripSummary: { tripData: any };
  MapNavigation: { orderId: string; destination: 'pickup' | 'delivery' };
};

export type ProfileStackParamList = {
  ProfileMain: undefined;
  EditProfile: undefined;
  VehicleInfo: undefined;
  PaymentMethods: undefined;
  BankInfo: undefined;
  Settings: undefined;
  DocumentStatus: undefined;
  ProfileLegacy: undefined;
  SettingsLegacy: undefined;
};

export type SupportStackParamList = {
  SupportMain: undefined;
  LiveChat: undefined;
  HelpCenter: undefined;
  CallSupport: undefined;
  ReportIssue: undefined;
  AppFeedback: undefined;
};

export type SafetyStackParamList = {
  SafetyMain: undefined;
  EmergencySOS: undefined;
  EmergencySOSLegacy: undefined;
  IncidentReport: undefined;
  TermsPolicies: undefined;
};

export type AdvancedToolsStackParamList = {
  AdvancedToolsHub: undefined;
  Heatmap: undefined;
  Performance: undefined;
  Training: undefined;
  Notifications: undefined;
  LocationTracking: undefined;
  LocationHistory: undefined;
};

// Common utility types
export interface ApiResponse<T> {
  data: T;
  message: string;
  success: boolean;
  timestamp: string;
}

export interface PaginatedResponse<T> {
  data: T[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
}

export interface LoadingState {
  isLoading: boolean;
  error: string | null;
}

export interface FormValidation {
  isValid: boolean;
  errors: Record<string, string>;
}

export interface SelectOption {
  label: string;
  value: string | number;
}

export interface ToastMessage {
  id: string;
  type: 'success' | 'error' | 'warning' | 'info';
  title: string;
  message?: string;
  duration?: number;
  action?: {
    label: string;
    onPress: () => void;
  };
}

export interface BottomSheetRef {
  open: () => void;
  close: () => void;
  snapTo: (index: number) => void;
}

export interface ModalProps {
  visible: boolean;
  onClose: () => void;
  title?: string;
  children: React.ReactNode;
}


