import React, { createContext, useContext, useReducer } from 'react';
import {
  EarningsState,
  EarningsContextType,
  EarningsSummary,
  EarningsData,
  DailyEarnings,
  WeeklyEarnings,
  MonthlyEarnings,
  PerformanceMetrics,
  PayoutInfo,
} from '../types/earnings';
import { mockEarningsService as earningsService } from '../services/api/mockEarningsService';

// Initial state
const initialState: EarningsState = {
  summary: null,
  recentEarnings: [],
  dailyEarnings: [],
  weeklyEarnings: [],
  monthlyEarnings: [],
  performanceMetrics: null,
  payouts: [],
  walletBalance: null,
  withdrawalMethods: [],
  withdrawalHistory: [],
  payoutSchedule: [],
  payoutSettings: null,
  deductions: [],
  bonuses: [],
  isLoading: false,
  error: null,
  selectedPeriod: 'today',
};

// Action types
type EarningsAction =
  | { type: 'SET_LOADING'; payload: boolean }
  | { type: 'SET_ERROR'; payload: string | null }
  | { type: 'SET_SUMMARY'; payload: EarningsSummary }
  | { type: 'SET_RECENT_EARNINGS'; payload: EarningsData[] }
  | { type: 'SET_DAILY_EARNINGS'; payload: DailyEarnings[] }
  | { type: 'SET_WEEKLY_EARNINGS'; payload: WeeklyEarnings[] }
  | { type: 'SET_MONTHLY_EARNINGS'; payload: MonthlyEarnings[] }
  | { type: 'SET_PERFORMANCE_METRICS'; payload: PerformanceMetrics }
  | { type: 'SET_PAYOUTS'; payload: PayoutInfo[] }
  | { type: 'SET_WALLET_BALANCE'; payload: any }
  | { type: 'SET_WITHDRAWAL_METHODS'; payload: any[] }
  | { type: 'SET_WITHDRAWAL_HISTORY'; payload: any[] }
  | { type: 'SET_PAYOUT_SCHEDULE'; payload: any[] }
  | { type: 'SET_PAYOUT_SETTINGS'; payload: any }
  | { type: 'SET_DEDUCTIONS'; payload: any[] }
  | { type: 'SET_BONUSES'; payload: any[] }
  | { type: 'SET_SELECTED_PERIOD'; payload: 'today' | 'week' | 'month' | 'all' }
  | { type: 'CLEAR_ERROR' };

// Reducer
const earningsReducer = (state: EarningsState, action: EarningsAction): EarningsState => {
  switch (action.type) {
    case 'SET_LOADING':
      return {
        ...state,
        isLoading: action.payload,
      };
    case 'SET_ERROR':
      return {
        ...state,
        error: action.payload,
        isLoading: false,
      };
    case 'SET_SUMMARY':
      return {
        ...state,
        summary: action.payload,
        error: null,
      };
    case 'SET_RECENT_EARNINGS':
      return {
        ...state,
        recentEarnings: action.payload,
        error: null,
      };
    case 'SET_DAILY_EARNINGS':
      return {
        ...state,
        dailyEarnings: action.payload,
        error: null,
      };
    case 'SET_WEEKLY_EARNINGS':
      return {
        ...state,
        weeklyEarnings: action.payload,
        error: null,
      };
    case 'SET_MONTHLY_EARNINGS':
      return {
        ...state,
        monthlyEarnings: action.payload,
        error: null,
      };
    case 'SET_PERFORMANCE_METRICS':
      return {
        ...state,
        performanceMetrics: action.payload,
        error: null,
      };
    case 'SET_PAYOUTS':
      return {
        ...state,
        payouts: action.payload,
        error: null,
      };
    case 'SET_WALLET_BALANCE':
      return {
        ...state,
        walletBalance: action.payload,
        error: null,
      };
    case 'SET_WITHDRAWAL_METHODS':
      return {
        ...state,
        withdrawalMethods: action.payload,
        error: null,
      };
    case 'SET_WITHDRAWAL_HISTORY':
      return {
        ...state,
        withdrawalHistory: action.payload,
        error: null,
      };
    case 'SET_PAYOUT_SCHEDULE':
      return {
        ...state,
        payoutSchedule: action.payload,
        error: null,
      };
    case 'SET_PAYOUT_SETTINGS':
      return {
        ...state,
        payoutSettings: action.payload,
        error: null,
      };
    case 'SET_DEDUCTIONS':
      return {
        ...state,
        deductions: action.payload,
        error: null,
      };
    case 'SET_BONUSES':
      return {
        ...state,
        bonuses: action.payload,
        error: null,
      };
    case 'SET_SELECTED_PERIOD':
      return {
        ...state,
        selectedPeriod: action.payload,
      };
    case 'CLEAR_ERROR':
      return {
        ...state,
        error: null,
      };
    default:
      return state;
  }
};

// Create context
const EarningsContext = createContext<EarningsContextType | undefined>(undefined);

// Provider component
interface EarningsProviderProps {
  children: React.ReactNode;
}

export const EarningsProvider: React.FC<EarningsProviderProps> = ({ children }) => {
  const [state, dispatch] = useReducer(earningsReducer, initialState);

  const fetchEarningsSummary = async () => {
    try {
      dispatch({ type: 'SET_LOADING', payload: true });
      dispatch({ type: 'CLEAR_ERROR' });
      
      const summary = await earningsService.getEarningsSummary();
      dispatch({ type: 'SET_SUMMARY', payload: summary });
    } catch (error: any) {
      dispatch({ type: 'SET_ERROR', payload: error.message });
      throw error;
    } finally {
      dispatch({ type: 'SET_LOADING', payload: false });
    }
  };

  const fetchRecentEarnings = async (limit: number = 20) => {
    try {
      dispatch({ type: 'SET_LOADING', payload: true });
      dispatch({ type: 'CLEAR_ERROR' });
      
      const earnings = await earningsService.getRecentEarnings(limit);
      dispatch({ type: 'SET_RECENT_EARNINGS', payload: earnings });
    } catch (error: any) {
      dispatch({ type: 'SET_ERROR', payload: error.message });
      throw error;
    } finally {
      dispatch({ type: 'SET_LOADING', payload: false });
    }
  };

  const fetchDailyEarnings = async (startDate: string, endDate: string) => {
    try {
      dispatch({ type: 'SET_LOADING', payload: true });
      dispatch({ type: 'CLEAR_ERROR' });
      
      const earnings = await earningsService.getDailyEarnings(startDate, endDate);
      dispatch({ type: 'SET_DAILY_EARNINGS', payload: earnings });
    } catch (error: any) {
      dispatch({ type: 'SET_ERROR', payload: error.message });
      throw error;
    } finally {
      dispatch({ type: 'SET_LOADING', payload: false });
    }
  };

  const fetchWeeklyEarnings = async (startDate: string, endDate: string) => {
    try {
      dispatch({ type: 'SET_LOADING', payload: true });
      dispatch({ type: 'CLEAR_ERROR' });
      
      const earnings = await earningsService.getWeeklyEarnings(startDate, endDate);
      dispatch({ type: 'SET_WEEKLY_EARNINGS', payload: earnings });
    } catch (error: any) {
      dispatch({ type: 'SET_ERROR', payload: error.message });
      throw error;
    } finally {
      dispatch({ type: 'SET_LOADING', payload: false });
    }
  };

  const fetchMonthlyEarnings = async (year: number) => {
    try {
      dispatch({ type: 'SET_LOADING', payload: true });
      dispatch({ type: 'CLEAR_ERROR' });
      
      const earnings = await earningsService.getMonthlyEarnings(year);
      dispatch({ type: 'SET_MONTHLY_EARNINGS', payload: earnings });
    } catch (error: any) {
      dispatch({ type: 'SET_ERROR', payload: error.message });
      throw error;
    } finally {
      dispatch({ type: 'SET_LOADING', payload: false });
    }
  };

  const fetchPerformanceMetrics = async () => {
    try {
      dispatch({ type: 'SET_LOADING', payload: true });
      dispatch({ type: 'CLEAR_ERROR' });
      
      const metrics = await earningsService.getPerformanceMetrics();
      dispatch({ type: 'SET_PERFORMANCE_METRICS', payload: metrics });
    } catch (error: any) {
      dispatch({ type: 'SET_ERROR', payload: error.message });
      throw error;
    } finally {
      dispatch({ type: 'SET_LOADING', payload: false });
    }
  };

  const fetchPayouts = async () => {
    try {
      dispatch({ type: 'SET_LOADING', payload: true });
      dispatch({ type: 'CLEAR_ERROR' });
      
      const payouts = await earningsService.getPayouts();
      dispatch({ type: 'SET_PAYOUTS', payload: payouts });
    } catch (error: any) {
      dispatch({ type: 'SET_ERROR', payload: error.message });
      throw error;
    } finally {
      dispatch({ type: 'SET_LOADING', payload: false });
    }
  };

  // Wallet functions
  const fetchWalletBalance = async () => {
    try {
      dispatch({ type: 'SET_LOADING', payload: true });
      dispatch({ type: 'CLEAR_ERROR' });

      // Mock wallet balance for now
      const mockBalance = {
        totalBalance: 15750.50,
        availableBalance: 12500.00,
        pendingBalance: 3250.50,
        lastUpdated: new Date().toISOString(),
      };
      dispatch({ type: 'SET_WALLET_BALANCE', payload: mockBalance });
    } catch (error: any) {
      dispatch({ type: 'SET_ERROR', payload: error.message });
      throw error;
    } finally {
      dispatch({ type: 'SET_LOADING', payload: false });
    }
  };

  const fetchWithdrawalMethods = async () => {
    try {
      dispatch({ type: 'SET_LOADING', payload: true });
      dispatch({ type: 'CLEAR_ERROR' });

      // Mock withdrawal methods for now
      const mockMethods = [
        {
          id: '1',
          type: 'bank_transfer',
          name: 'HBL Main Account',
          accountNumber: '****1234',
          accountTitle: 'Muhammad Ali',
          bankName: 'Habib Bank Limited',
          isVerified: true,
          isPrimary: true,
          createdAt: '2024-01-15T10:00:00Z',
        },
        {
          id: '2',
          type: 'jazzcash',
          name: 'JazzCash Wallet',
          phoneNumber: '***********',
          isVerified: true,
          isPrimary: false,
          createdAt: '2024-02-01T10:00:00Z',
        },
      ];
      dispatch({ type: 'SET_WITHDRAWAL_METHODS', payload: mockMethods });
    } catch (error: any) {
      dispatch({ type: 'SET_ERROR', payload: error.message });
      throw error;
    } finally {
      dispatch({ type: 'SET_LOADING', payload: false });
    }
  };

  const addWithdrawalMethod = async (method: any) => {
    try {
      dispatch({ type: 'SET_LOADING', payload: true });
      dispatch({ type: 'CLEAR_ERROR' });

      // Mock adding method
      console.log('Adding withdrawal method:', method);
      await new Promise(resolve => setTimeout(resolve, 1000));

      // Refresh methods
      await fetchWithdrawalMethods();
    } catch (error: any) {
      dispatch({ type: 'SET_ERROR', payload: error.message });
      throw error;
    } finally {
      dispatch({ type: 'SET_LOADING', payload: false });
    }
  };

  const updateWithdrawalMethod = async (id: string, updates: any) => {
    try {
      dispatch({ type: 'SET_LOADING', payload: true });
      dispatch({ type: 'CLEAR_ERROR' });

      console.log('Updating withdrawal method:', id, updates);
      await new Promise(resolve => setTimeout(resolve, 1000));

      await fetchWithdrawalMethods();
    } catch (error: any) {
      dispatch({ type: 'SET_ERROR', payload: error.message });
      throw error;
    } finally {
      dispatch({ type: 'SET_LOADING', payload: false });
    }
  };

  const deleteWithdrawalMethod = async (id: string) => {
    try {
      dispatch({ type: 'SET_LOADING', payload: true });
      dispatch({ type: 'CLEAR_ERROR' });

      console.log('Deleting withdrawal method:', id);
      await new Promise(resolve => setTimeout(resolve, 1000));

      await fetchWithdrawalMethods();
    } catch (error: any) {
      dispatch({ type: 'SET_ERROR', payload: error.message });
      throw error;
    } finally {
      dispatch({ type: 'SET_LOADING', payload: false });
    }
  };

  const requestWithdrawal = async (amount: number, methodId: string) => {
    try {
      dispatch({ type: 'SET_LOADING', payload: true });
      dispatch({ type: 'CLEAR_ERROR' });

      console.log('Requesting withdrawal:', amount, methodId);
      await new Promise(resolve => setTimeout(resolve, 1000));

      // Refresh wallet balance and history
      await Promise.all([fetchWalletBalance(), fetchWithdrawalHistory()]);
    } catch (error: any) {
      dispatch({ type: 'SET_ERROR', payload: error.message });
      throw error;
    } finally {
      dispatch({ type: 'SET_LOADING', payload: false });
    }
  };

  const fetchWithdrawalHistory = async () => {
    try {
      dispatch({ type: 'SET_LOADING', payload: true });
      dispatch({ type: 'CLEAR_ERROR' });

      // Mock withdrawal history
      const mockHistory = [
        {
          id: '1',
          amount: 5000,
          method: { id: '1', name: 'HBL Main Account', type: 'bank_transfer' },
          status: 'completed',
          requestedAt: '2024-01-20T10:00:00Z',
          completedAt: '2024-01-21T14:30:00Z',
          fees: 50,
          netAmount: 4950,
        },
        {
          id: '2',
          amount: 2500,
          method: { id: '2', name: 'JazzCash Wallet', type: 'jazzcash' },
          status: 'processing',
          requestedAt: '2024-01-22T15:00:00Z',
          fees: 25,
          netAmount: 2475,
        },
      ];
      dispatch({ type: 'SET_WITHDRAWAL_HISTORY', payload: mockHistory });
    } catch (error: any) {
      dispatch({ type: 'SET_ERROR', payload: error.message });
      throw error;
    } finally {
      dispatch({ type: 'SET_LOADING', payload: false });
    }
  };

  const cancelWithdrawal = async (id: string) => {
    try {
      dispatch({ type: 'SET_LOADING', payload: true });
      dispatch({ type: 'CLEAR_ERROR' });

      console.log('Cancelling withdrawal:', id);
      await new Promise(resolve => setTimeout(resolve, 1000));

      await fetchWithdrawalHistory();
    } catch (error: any) {
      dispatch({ type: 'SET_ERROR', payload: error.message });
      throw error;
    } finally {
      dispatch({ type: 'SET_LOADING', payload: false });
    }
  };

  const setSelectedPeriod = (period: 'today' | 'week' | 'month' | 'all') => {
    dispatch({ type: 'SET_SELECTED_PERIOD', payload: period });
  };

  // Payout functions
  const fetchPayoutSchedule = async () => {
    try {
      dispatch({ type: 'SET_LOADING', payload: true });
      dispatch({ type: 'CLEAR_ERROR' });

      // Mock payout schedule
      const mockSchedule = [
        {
          id: '1',
          scheduledDate: '2024-01-25T00:00:00Z',
          amount: 8500,
          status: 'scheduled',
          earningsIncluded: [],
          deductions: [],
          bonuses: [],
          netAmount: 8500,
          paymentMethod: { id: '1', name: 'HBL Main Account', type: 'bank_transfer' },
        },
      ];
      dispatch({ type: 'SET_PAYOUT_SCHEDULE', payload: mockSchedule });
    } catch (error: any) {
      dispatch({ type: 'SET_ERROR', payload: error.message });
      throw error;
    } finally {
      dispatch({ type: 'SET_LOADING', payload: false });
    }
  };

  const fetchPayoutSettings = async () => {
    try {
      dispatch({ type: 'SET_LOADING', payload: true });
      dispatch({ type: 'CLEAR_ERROR' });

      // Mock payout settings
      const mockSettings = {
        frequency: 'weekly',
        minimumAmount: 1000,
        preferredMethod: { id: '1', name: 'HBL Main Account', type: 'bank_transfer' },
        autoWithdraw: true,
      };
      dispatch({ type: 'SET_PAYOUT_SETTINGS', payload: mockSettings });
    } catch (error: any) {
      dispatch({ type: 'SET_ERROR', payload: error.message });
      throw error;
    } finally {
      dispatch({ type: 'SET_LOADING', payload: false });
    }
  };

  const updatePayoutSettings = async (settings: any) => {
    try {
      dispatch({ type: 'SET_LOADING', payload: true });
      dispatch({ type: 'CLEAR_ERROR' });

      console.log('Updating payout settings:', settings);
      await new Promise(resolve => setTimeout(resolve, 1000));

      await fetchPayoutSettings();
    } catch (error: any) {
      dispatch({ type: 'SET_ERROR', payload: error.message });
      throw error;
    } finally {
      dispatch({ type: 'SET_LOADING', payload: false });
    }
  };

  // Deductions and bonuses
  const fetchDeductions = async (startDate?: string, endDate?: string) => {
    try {
      dispatch({ type: 'SET_LOADING', payload: true });
      dispatch({ type: 'CLEAR_ERROR' });

      // Mock deductions
      const mockDeductions = [
        {
          id: '1',
          orderId: 'ORD-001',
          type: 'cancellation_penalty',
          amount: 150,
          reason: 'Order cancelled after pickup',
          date: '2024-01-20T10:00:00Z',
          status: 'applied',
        },
        {
          id: '2',
          type: 'late_delivery',
          amount: 100,
          reason: 'Delivery was 15 minutes late',
          date: '2024-01-19T18:30:00Z',
          status: 'applied',
        },
      ];
      dispatch({ type: 'SET_DEDUCTIONS', payload: mockDeductions });
    } catch (error: any) {
      dispatch({ type: 'SET_ERROR', payload: error.message });
      throw error;
    } finally {
      dispatch({ type: 'SET_LOADING', payload: false });
    }
  };

  const fetchBonuses = async (startDate?: string, endDate?: string) => {
    try {
      dispatch({ type: 'SET_LOADING', payload: true });
      dispatch({ type: 'CLEAR_ERROR' });

      // Mock bonuses
      const mockBonuses = [
        {
          id: '1',
          type: 'peak_hour',
          amount: 200,
          description: 'Peak hour bonus (7-9 PM)',
          date: '2024-01-21T20:00:00Z',
          orderId: 'ORD-123',
          criteria: 'Delivered during peak hours',
        },
        {
          id: '2',
          type: 'weather_bonus',
          amount: 150,
          description: 'Rain weather bonus',
          date: '2024-01-20T14:00:00Z',
          orderId: 'ORD-122',
          criteria: 'Delivered during heavy rain',
        },
        {
          id: '3',
          type: 'completion_bonus',
          amount: 500,
          description: '50 orders milestone bonus',
          date: '2024-01-18T22:00:00Z',
          criteria: 'Completed 50 orders this month',
        },
      ];
      dispatch({ type: 'SET_BONUSES', payload: mockBonuses });
    } catch (error: any) {
      dispatch({ type: 'SET_ERROR', payload: error.message });
      throw error;
    } finally {
      dispatch({ type: 'SET_LOADING', payload: false });
    }
  };

  const clearError = () => {
    dispatch({ type: 'CLEAR_ERROR' });
  };

  const contextValue: EarningsContextType = {
    state,
    fetchEarningsSummary,
    fetchRecentEarnings,
    fetchDailyEarnings,
    fetchWeeklyEarnings,
    fetchMonthlyEarnings,
    fetchPerformanceMetrics,
    fetchPayouts,

    // Wallet functions
    fetchWalletBalance,
    fetchWithdrawalMethods,
    addWithdrawalMethod,
    updateWithdrawalMethod,
    deleteWithdrawalMethod,

    // Withdrawal functions
    requestWithdrawal,
    fetchWithdrawalHistory,
    cancelWithdrawal,

    // Payout functions
    fetchPayoutSchedule,
    fetchPayoutSettings,
    updatePayoutSettings,

    // Deductions and bonuses
    fetchDeductions,
    fetchBonuses,

    setSelectedPeriod,
    clearError,
  };

  return (
    <EarningsContext.Provider value={contextValue}>
      {children}
    </EarningsContext.Provider>
  );
};

// Hook to use earnings context
export const useEarnings = (): EarningsContextType => {
  const context = useContext(EarningsContext);
  if (context === undefined) {
    throw new Error('useEarnings must be used within an EarningsProvider');
  }
  return context;
};
