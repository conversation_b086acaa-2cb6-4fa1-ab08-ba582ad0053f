import React from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  ScrollView,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import { useNavigation } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import { SupportStackParamList } from '../../types/navigation';

type SupportMainScreenNavigationProp = StackNavigationProp<SupportStackParamList, 'SupportMain'>;

const SupportMainScreen: React.FC = () => {
  const navigation = useNavigation<SupportMainScreenNavigationProp>();

  const supportOptions = [
    {
      id: 'live-chat',
      title: 'Live Chat with Support',
      description: 'Real-time messaging with our support team',
      icon: 'chatbubbles',
      color: '#3b82f6',
      onPress: () => navigation.navigate('LiveChat'),
    },
    {
      id: 'help-center',
      title: 'Help Center & FAQ',
      description: 'Find answers to common questions',
      icon: 'help-circle',
      color: '#10b981',
      onPress: () => navigation.navigate('HelpCenter'),
    },
    {
      id: 'call-support',
      title: 'Call Support',
      description: 'Speak directly with our support team',
      icon: 'call',
      color: '#f59e0b',
      onPress: () => navigation.navigate('CallSupport'),
    },
    {
      id: 'report-issue',
      title: 'Report Issue with Order',
      description: 'Report problems with deliveries or customers',
      icon: 'warning',
      color: '#ef4444',
      onPress: () => navigation.navigate('ReportIssue'),
    },
    {
      id: 'app-feedback',
      title: 'App Feedback',
      description: 'Suggest features or report bugs',
      icon: 'star',
      color: '#8b5cf6',
      onPress: () => navigation.navigate('AppFeedback'),
    },
  ];

  const quickActions = [
    {
      id: 'emergency',
      title: 'Emergency Support',
      description: '24/7 emergency assistance',
      icon: 'medical',
      color: '#dc2626',
      onPress: () => navigation.navigate('CallSupport'),
    },
    {
      id: 'earnings',
      title: 'Earnings Issue',
      description: 'Payment and earnings problems',
      icon: 'wallet',
      color: '#059669',
      onPress: () => navigation.navigate('ReportIssue'),
    },
    {
      id: 'app-bug',
      title: 'App Not Working',
      description: 'Technical issues with the app',
      icon: 'bug',
      color: '#dc2626',
      onPress: () => navigation.navigate('AppFeedback'),
    },
  ];

  const renderSupportOption = (option: typeof supportOptions[0]) => (
    <TouchableOpacity
      key={option.id}
      onPress={option.onPress}
      style={{
        backgroundColor: 'white',
        marginHorizontal: 20,
        marginVertical: 8,
        borderRadius: 20,
        padding: 20,
        flexDirection: 'row',
        alignItems: 'center',
        shadowColor: option.color,
        shadowOffset: { width: 0, height: 8 },
        shadowOpacity: 0.1,
        shadowRadius: 16,
        elevation: 8,
        borderWidth: 1,
        borderColor: `${option.color}20`,
      }}
    >
      <View style={{
        width: 64,
        height: 64,
        backgroundColor: `${option.color}15`,
        borderRadius: 32,
        alignItems: 'center',
        justifyContent: 'center',
        marginRight: 16,
        borderWidth: 3,
        borderColor: option.color,
        shadowColor: option.color,
        shadowOffset: { width: 0, height: 4 },
        shadowOpacity: 0.3,
        shadowRadius: 8,
        elevation: 6,
      }}>
        <Ionicons
          name={option.icon as any}
          size={28}
          color={option.color}
        />
      </View>

      <View style={{ flex: 1 }}>
        <Text style={{
          fontSize: 18,
          fontWeight: 'bold',
          color: '#111827',
          marginBottom: 6,
        }}>
          {option.title}
        </Text>
        <Text style={{
          fontSize: 14,
          color: '#6b7280',
          lineHeight: 20,
          fontWeight: '500',
        }}>
          {option.description}
        </Text>
      </View>

      <View style={{
        width: 32,
        height: 32,
        backgroundColor: '#f3f4f6',
        borderRadius: 16,
        alignItems: 'center',
        justifyContent: 'center',
      }}>
        <Ionicons
          name="chevron-forward"
          size={18}
          color="#6b7280"
        />
      </View>
    </TouchableOpacity>
  );

  const renderQuickAction = (action: typeof quickActions[0]) => (
    <TouchableOpacity
      key={action.id}
      onPress={action.onPress}
      style={{
        backgroundColor: 'white',
        borderRadius: 12,
        padding: 16,
        alignItems: 'center',
        borderWidth: 1,
        borderColor: '#e5e7eb',
        minWidth: 120,
        marginRight: 12,
      }}
    >
      <View style={{
        width: 40,
        height: 40,
        backgroundColor: `${action.color}15`,
        borderRadius: 20,
        alignItems: 'center',
        justifyContent: 'center',
        marginBottom: 8,
      }}>
        <Ionicons
          name={action.icon as any}
          size={20}
          color={action.color}
        />
      </View>
      
      <Text style={{
        fontSize: 14,
        fontWeight: '600',
        color: '#1f2937',
        textAlign: 'center',
        marginBottom: 4,
      }}>
        {action.title}
      </Text>
      
      <Text style={{
        fontSize: 12,
        color: '#6b7280',
        textAlign: 'center',
        lineHeight: 16,
      }}>
        {action.description}
      </Text>
    </TouchableOpacity>
  );

  return (
    <View style={{ flex: 1, backgroundColor: '#f1f5f9' }}>
      <SafeAreaView style={{ flex: 1 }}>
        {/* Enhanced Header */}
        <View style={{
          backgroundColor: '#dc2626',
          paddingHorizontal: 20,
          paddingTop: 20,
          paddingBottom: 28,
          borderBottomLeftRadius: 28,
          borderBottomRightRadius: 28,
          shadowColor: '#dc2626',
          shadowOffset: { width: 0, height: 12 },
          shadowOpacity: 0.3,
          shadowRadius: 20,
          elevation: 16,
        }}>
          <View style={{
            flexDirection: 'row',
            alignItems: 'center',
            marginBottom: 20,
          }}>
            <TouchableOpacity
              onPress={() => navigation.goBack()}
              style={{
                width: 48,
                height: 48,
                borderRadius: 24,
                backgroundColor: 'rgba(255,255,255,0.2)',
                justifyContent: 'center',
                alignItems: 'center',
                marginRight: 16,
                borderWidth: 2,
                borderColor: 'rgba(255,255,255,0.3)',
              }}
            >
              <Ionicons name="arrow-back" size={24} color="white" />
            </TouchableOpacity>

            <View style={{ flex: 1 }}>
              <Text style={{
                fontSize: 24,
                fontWeight: 'bold',
                color: 'white',
                marginBottom: 4,
              }}>
                🆘 Support Center
              </Text>
              <Text style={{
                fontSize: 14,
                color: 'rgba(255,255,255,0.8)',
                fontWeight: '500',
              }}>
                We're here to help 24/7
              </Text>
            </View>

            <View style={{
              flexDirection: 'row',
              alignItems: 'center',
              backgroundColor: 'rgba(255,255,255,0.2)',
              paddingHorizontal: 12,
              paddingVertical: 6,
              borderRadius: 16,
              borderWidth: 2,
              borderColor: 'rgba(255,255,255,0.3)',
            }}>
              <View style={{
                width: 12,
                height: 12,
                backgroundColor: '#10b981',
                borderRadius: 6,
                marginRight: 8,
                borderWidth: 2,
                borderColor: 'white',
              }} />
              <Text style={{
                fontSize: 14,
                color: 'white',
                fontWeight: 'bold',
              }}>
                Online
              </Text>
            </View>
          </View>
        </View>

      <ScrollView style={{ flex: 1 }}>
        {/* Welcome Message */}
        <View style={{
          backgroundColor: '#f0f9ff',
          marginHorizontal: 16,
          marginTop: 16,
          borderRadius: 12,
          padding: 16,
          borderWidth: 1,
          borderColor: '#bae6fd',
        }}>
          <View style={{
            flexDirection: 'row',
            alignItems: 'center',
            marginBottom: 8,
          }}>
            <Ionicons name="information-circle" size={20} color="#0284c7" />
            <Text style={{
              fontSize: 16,
              fontWeight: '600',
              color: '#0284c7',
              marginLeft: 8,
            }}>
              We're here to help!
            </Text>
          </View>
          <Text style={{
            fontSize: 14,
            color: '#0369a1',
            lineHeight: 20,
          }}>
            Our support team is available 24/7 to assist you with any questions or issues you may have.
          </Text>
        </View>

        {/* Quick Actions */}
        <View style={{ marginTop: 24 }}>
          <Text style={{
            fontSize: 18,
            fontWeight: '600',
            color: '#1f2937',
            paddingHorizontal: 16,
            marginBottom: 12,
          }}>
            Quick Actions
          </Text>
          
          <ScrollView
            horizontal
            showsHorizontalScrollIndicator={false}
            style={{ paddingLeft: 16 }}
          >
            {quickActions.map(action => renderQuickAction(action))}
          </ScrollView>
        </View>

        {/* Support Options */}
        <View style={{ marginTop: 32 }}>
          <Text style={{
            fontSize: 18,
            fontWeight: '600',
            color: '#1f2937',
            paddingHorizontal: 16,
            marginBottom: 12,
          }}>
            Support Options
          </Text>
          
          {supportOptions.map(option => renderSupportOption(option))}
        </View>

        {/* Contact Info */}
        <View style={{
          backgroundColor: 'white',
          marginHorizontal: 16,
          marginTop: 24,
          marginBottom: 32,
          borderRadius: 12,
          padding: 16,
          borderWidth: 1,
          borderColor: '#e5e7eb',
        }}>
          <Text style={{
            fontSize: 16,
            fontWeight: '600',
            color: '#1f2937',
            marginBottom: 12,
          }}>
            Contact Information
          </Text>
          
          <View style={{
            flexDirection: 'row',
            alignItems: 'center',
            marginBottom: 8,
          }}>
            <Ionicons name="call" size={16} color="#6b7280" />
            <Text style={{
              fontSize: 14,
              color: '#374151',
              marginLeft: 8,
            }}>
              Emergency: +92-21-111-HELP (4357)
            </Text>
          </View>
          
          <View style={{
            flexDirection: 'row',
            alignItems: 'center',
            marginBottom: 8,
          }}>
            <Ionicons name="mail" size={16} color="#6b7280" />
            <Text style={{
              fontSize: 14,
              color: '#374151',
              marginLeft: 8,
            }}>
              <EMAIL>
            </Text>
          </View>
          
          <View style={{
            flexDirection: 'row',
            alignItems: 'center',
          }}>
            <Ionicons name="time" size={16} color="#6b7280" />
            <Text style={{
              fontSize: 14,
              color: '#374151',
              marginLeft: 8,
            }}>
              Support Hours: 24/7 for emergencies, 9 AM - 9 PM for general support
            </Text>
          </View>
        </View>
      </ScrollView>
      </SafeAreaView>
    </View>
  );
};

export default SupportMainScreen;
