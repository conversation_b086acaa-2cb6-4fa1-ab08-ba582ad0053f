import React, { useState } from 'react';
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  Alert,
  Switch,
  Modal,
  Linking,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import { useNavigation } from '@react-navigation/native';
import { Card, Button } from '../../components/ui';
import { useAuth } from '../../context/AuthContext';
import { Language, NotificationType, PrivacySetting, UserSettings } from '../../types/profile';

// Mock settings data
const mockSettings: UserSettings = {
  language: Language.ENGLISH,
  notifications: {
    orderRequests: true,
    orderUpdates: true,
    earnings: true,
    promotions: false,
    systemUpdates: true,
    marketing: false,
  },
  privacy: {
    shareLocation: PrivacySetting.WHILE_WORKING,
    shareProfile: PrivacySetting.PRIVATE,
    shareEarnings: PrivacySetting.PRIVATE,
    shareRating: PrivacySetting.PUBLIC,
  },
  preferences: {
    autoAcceptOrders: false,
    soundEnabled: true,
    vibrationEnabled: true,
    darkMode: false,
    dataUsage: 'normal',
  },
  appVersion: '1.0.0',
  buildNumber: '100',
};

const SettingsScreen: React.FC = () => {
  const navigation = useNavigation();
  const { logout } = useAuth();
  const [settings, setSettings] = useState(mockSettings);
  const [showLanguageModal, setShowLanguageModal] = useState(false);
  const [showPrivacyModal, setShowPrivacyModal] = useState(false);
  const [selectedPrivacySetting, setSelectedPrivacySetting] = useState<keyof typeof settings.privacy>('shareLocation');

  const languages = [
    { code: Language.ENGLISH, name: 'English', nativeName: 'English', available: true },
    { code: Language.URDU, name: 'Urdu', nativeName: 'اردو', available: false },
    { code: Language.PUNJABI, name: 'Punjabi', nativeName: 'ਪੰਜਾਬੀ', available: false },
  ];

  const privacyOptions = [
    { value: PrivacySetting.PUBLIC, label: 'Public', description: 'Visible to everyone' },
    { value: PrivacySetting.PRIVATE, label: 'Private', description: 'Only visible to you' },
    { value: PrivacySetting.WHILE_WORKING, label: 'While Working', description: 'Visible only when online' },
  ];

  const handleNotificationToggle = (type: keyof typeof settings.notifications) => {
    setSettings(prev => ({
      ...prev,
      notifications: {
        ...prev.notifications,
        [type]: !prev.notifications[type],
      },
    }));
  };

  const handlePreferenceToggle = (type: keyof typeof settings.preferences) => {
    setSettings(prev => ({
      ...prev,
      preferences: {
        ...prev.preferences,
        [type]: !prev.preferences[type],
      },
    }));
  };

  const handleLanguageChange = (language: Language, available: boolean) => {
    if (!available) {
      Alert.alert(
        'Coming Soon',
        'This language will be available in a future update. Stay tuned!',
        [{ text: 'OK' }]
      );
      return;
    }
    setSettings(prev => ({ ...prev, language }));
    setShowLanguageModal(false);
    Alert.alert('Language Changed', 'App language has been updated successfully!');
  };

  const handlePrivacyChange = (setting: keyof typeof settings.privacy, value: PrivacySetting) => {
    setSettings(prev => ({
      ...prev,
      privacy: {
        ...prev.privacy,
        [setting]: value,
      },
    }));
    setShowPrivacyModal(false);
    Alert.alert('Privacy Updated', 'Privacy setting has been updated successfully!');
  };

  const handleLogout = () => {
    Alert.alert(
      'Logout',
      'Are you sure you want to logout?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Logout',
          style: 'destructive',
          onPress: async () => {
            try {
              await logout();
            } catch (error: any) {
              Alert.alert('Error', error.message);
            }
          },
        },
      ]
    );
  };

  const handleDeactivateAccount = () => {
    Alert.alert(
      'Deactivate Account',
      'Are you sure you want to deactivate your account? This action will temporarily disable your account and you won\'t be able to receive orders.',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Deactivate',
          style: 'destructive',
          onPress: () => {
            Alert.alert('Account Deactivated', 'Your account has been deactivated. Contact support to reactivate.');
          },
        },
      ]
    );
  };

  const handleContactSupport = () => {
    Alert.alert(
      'Contact Support',
      'Choose how you want to contact support:',
      [
        { text: 'Cancel', style: 'cancel' },
        { text: 'Call', onPress: () => Linking.openURL('tel:+92-21-111-222-333') },
        { text: 'Email', onPress: () => Linking.openURL('mailto:<EMAIL>') },
        { text: 'WhatsApp', onPress: () => Linking.openURL('whatsapp://send?phone=************') },
      ]
    );
  };

  const renderLanguageModal = () => (
    <Modal
      visible={showLanguageModal}
      animationType="slide"
      presentationStyle="pageSheet"
      onRequestClose={() => setShowLanguageModal(false)}
    >
      <SafeAreaView style={{ flex: 1, backgroundColor: '#f8fafc' }}>
        <View style={{
          paddingHorizontal: 20,
          paddingVertical: 16,
          backgroundColor: '#ffffff',
          borderBottomWidth: 1,
          borderBottomColor: '#e5e7eb',
          flexDirection: 'row',
          alignItems: 'center',
          justifyContent: 'space-between',
        }}>
          <TouchableOpacity onPress={() => setShowLanguageModal(false)}>
            <Ionicons name="close" size={24} color="#6b7280" />
          </TouchableOpacity>
          <Text style={{ fontSize: 18, fontWeight: 'bold', color: '#111827' }}>
            Select Language
          </Text>
          <View style={{ width: 24 }} />
        </View>

        <View style={{ padding: 20 }}>
          <Card variant="elevated" padding="none">
            {languages.map((lang, index) => (
              <TouchableOpacity
                key={lang.code}
                onPress={() => handleLanguageChange(lang.code, lang.available)}
                style={{
                  flexDirection: 'row',
                  alignItems: 'center',
                  justifyContent: 'space-between',
                  paddingVertical: 16,
                  paddingHorizontal: 20,
                  borderBottomWidth: index < languages.length - 1 ? 1 : 0,
                  borderBottomColor: '#e5e7eb',
                  opacity: lang.available ? 1 : 0.6,
                }}
              >
                <View style={{ flex: 1 }}>
                  <View style={{ flexDirection: 'row', alignItems: 'center' }}>
                    <Text style={{
                      fontSize: 16,
                      fontWeight: '600',
                      color: lang.available ? '#111827' : '#9ca3af'
                    }}>
                      {lang.name}
                    </Text>
                    {!lang.available && (
                      <View style={{
                        backgroundColor: '#fbbf24',
                        paddingHorizontal: 6,
                        paddingVertical: 2,
                        borderRadius: 10,
                        marginLeft: 8,
                      }}>
                        <Text style={{
                          fontSize: 9,
                          fontWeight: '600',
                          color: 'white',
                        }}>
                          Coming Soon
                        </Text>
                      </View>
                    )}
                  </View>
                  <Text style={{
                    fontSize: 14,
                    color: lang.available ? '#6b7280' : '#9ca3af',
                    marginTop: 2
                  }}>
                    {lang.nativeName}
                  </Text>
                </View>

                {settings.language === lang.code && lang.available && (
                  <Ionicons name="checkmark-circle" size={20} color="#f97316" />
                )}

                {!lang.available && (
                  <Ionicons name="lock-closed" size={18} color="#9ca3af" />
                )}
              </TouchableOpacity>
            ))}
          </Card>
        </View>
      </SafeAreaView>
    </Modal>
  );

  const renderPrivacyModal = () => (
    <Modal
      visible={showPrivacyModal}
      animationType="slide"
      presentationStyle="pageSheet"
      onRequestClose={() => setShowPrivacyModal(false)}
    >
      <SafeAreaView style={{ flex: 1, backgroundColor: '#f8fafc' }}>
        <View style={{
          paddingHorizontal: 20,
          paddingVertical: 16,
          backgroundColor: '#ffffff',
          borderBottomWidth: 1,
          borderBottomColor: '#e5e7eb',
          flexDirection: 'row',
          alignItems: 'center',
          justifyContent: 'space-between',
        }}>
          <TouchableOpacity onPress={() => setShowPrivacyModal(false)}>
            <Ionicons name="close" size={24} color="#6b7280" />
          </TouchableOpacity>
          <Text style={{ fontSize: 18, fontWeight: 'bold', color: '#111827' }}>
            Privacy Setting
          </Text>
          <View style={{ width: 24 }} />
        </View>

        <View style={{ padding: 20 }}>
          <Card variant="elevated" padding="lg">
            <Text style={{ fontSize: 16, fontWeight: 'bold', color: '#111827', marginBottom: 16 }}>
              {selectedPrivacySetting.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase())}
            </Text>
            
            <View style={{ gap: 12 }}>
              {privacyOptions.map((option) => (
                <TouchableOpacity
                  key={option.value}
                  onPress={() => handlePrivacyChange(selectedPrivacySetting, option.value)}
                  style={{
                    flexDirection: 'row',
                    alignItems: 'center',
                    justifyContent: 'space-between',
                    backgroundColor: settings.privacy[selectedPrivacySetting] === option.value ? '#fef3e2' : '#ffffff',
                    borderColor: settings.privacy[selectedPrivacySetting] === option.value ? '#f97316' : '#e5e7eb',
                    borderWidth: 1,
                    paddingVertical: 16,
                    paddingHorizontal: 16,
                    borderRadius: 8,
                  }}
                >
                  <View style={{ flex: 1 }}>
                    <Text style={{
                      fontSize: 16,
                      fontWeight: '600',
                      color: settings.privacy[selectedPrivacySetting] === option.value ? '#f97316' : '#111827',
                    }}>
                      {option.label}
                    </Text>
                    <Text style={{
                      fontSize: 14,
                      color: settings.privacy[selectedPrivacySetting] === option.value ? '#92400e' : '#6b7280',
                      marginTop: 2,
                    }}>
                      {option.description}
                    </Text>
                  </View>
                  
                  {settings.privacy[selectedPrivacySetting] === option.value && (
                    <Ionicons name="checkmark-circle" size={20} color="#f97316" />
                  )}
                </TouchableOpacity>
              ))}
            </View>
          </Card>
        </View>
      </SafeAreaView>
    </Modal>
  );

  const renderSettingItem = (
    icon: string,
    title: string,
    subtitle?: string,
    onPress?: () => void,
    rightComponent?: React.ReactNode,
    showChevron: boolean = true
  ) => (
    <TouchableOpacity
      onPress={onPress}
      disabled={!onPress}
      style={{
        flexDirection: 'row',
        alignItems: 'center',
        paddingVertical: 16,
        paddingHorizontal: 20,
        borderBottomWidth: 1,
        borderBottomColor: '#e5e7eb',
      }}
    >
      <View style={{
        width: 40,
        height: 40,
        backgroundColor: '#f3f4f6',
        borderRadius: 20,
        alignItems: 'center',
        justifyContent: 'center',
        marginRight: 12,
      }}>
        <Ionicons name={icon as any} size={20} color="#374151" />
      </View>
      
      <View style={{ flex: 1 }}>
        <Text style={{ fontSize: 16, fontWeight: '500', color: '#111827' }}>
          {title}
        </Text>
        {subtitle && (
          <Text style={{ fontSize: 14, color: '#6b7280', marginTop: 2 }}>
            {subtitle}
          </Text>
        )}
      </View>
      
      {rightComponent || (showChevron && onPress && (
        <Ionicons name="chevron-forward" size={20} color="#9ca3af" />
      ))}
    </TouchableOpacity>
  );

  return (
    <SafeAreaView style={{ flex: 1, backgroundColor: '#f8fafc' }}>
      <View style={{
        paddingHorizontal: 20,
        paddingVertical: 16,
        backgroundColor: '#ffffff',
        borderBottomWidth: 1,
        borderBottomColor: '#e5e7eb',
        flexDirection: 'row',
        alignItems: 'center',
      }}>
        <TouchableOpacity
          onPress={() => navigation.goBack()}
          style={{ marginRight: 16 }}
        >
          <Ionicons name="arrow-back" size={24} color="#111827" />
        </TouchableOpacity>
        <Text style={{ fontSize: 20, fontWeight: 'bold', color: '#111827' }}>
          Settings
        </Text>
      </View>

      <ScrollView style={{ flex: 1 }} showsVerticalScrollIndicator={false}>
        {/* Language Settings */}
        <Card variant="elevated" margin="md" padding="none">
          <View style={{ paddingHorizontal: 20, paddingVertical: 16, borderBottomWidth: 1, borderBottomColor: '#e5e7eb' }}>
            <Text style={{ fontSize: 18, fontWeight: 'bold', color: '#111827' }}>
              Language & Region
            </Text>
          </View>

          {renderSettingItem(
            'language',
            'Language',
            languages.find(l => l.code === settings.language)?.name,
            () => setShowLanguageModal(true)
          )}
        </Card>

        {/* Notification Settings */}
        <Card variant="elevated" margin="md" padding="none">
          <View style={{ paddingHorizontal: 20, paddingVertical: 16, borderBottomWidth: 1, borderBottomColor: '#e5e7eb' }}>
            <Text style={{ fontSize: 18, fontWeight: 'bold', color: '#111827' }}>
              Notifications
            </Text>
          </View>

          {renderSettingItem(
            'notifications',
            'Order Requests',
            'Get notified about new delivery requests',
            undefined,
            <Switch
              value={settings.notifications.orderRequests}
              onValueChange={() => handleNotificationToggle('orderRequests')}
              trackColor={{ false: '#d1d5db', true: '#fed7aa' }}
              thumbColor={settings.notifications.orderRequests ? '#f97316' : '#f4f3f4'}
            />,
            false
          )}

          {renderSettingItem(
            'refresh',
            'Order Updates',
            'Get notified about order status changes',
            undefined,
            <Switch
              value={settings.notifications.orderUpdates}
              onValueChange={() => handleNotificationToggle('orderUpdates')}
              trackColor={{ false: '#d1d5db', true: '#fed7aa' }}
              thumbColor={settings.notifications.orderUpdates ? '#f97316' : '#f4f3f4'}
            />,
            false
          )}

          {renderSettingItem(
            'cash',
            'Earnings',
            'Get notified about earnings and payments',
            undefined,
            <Switch
              value={settings.notifications.earnings}
              onValueChange={() => handleNotificationToggle('earnings')}
              trackColor={{ false: '#d1d5db', true: '#fed7aa' }}
              thumbColor={settings.notifications.earnings ? '#f97316' : '#f4f3f4'}
            />,
            false
          )}

          {renderSettingItem(
            'megaphone',
            'Promotions',
            'Get notified about special offers and bonuses',
            undefined,
            <Switch
              value={settings.notifications.promotions}
              onValueChange={() => handleNotificationToggle('promotions')}
              trackColor={{ false: '#d1d5db', true: '#fed7aa' }}
              thumbColor={settings.notifications.promotions ? '#f97316' : '#f4f3f4'}
            />,
            false
          )}
        </Card>

        {/* Privacy Settings */}
        <Card variant="elevated" margin="md" padding="none">
          <View style={{ paddingHorizontal: 20, paddingVertical: 16, borderBottomWidth: 1, borderBottomColor: '#e5e7eb' }}>
            <Text style={{ fontSize: 18, fontWeight: 'bold', color: '#111827' }}>
              Privacy
            </Text>
          </View>

          {renderSettingItem(
            'location',
            'Share Location',
            privacyOptions.find(p => p.value === settings.privacy.shareLocation)?.label,
            () => {
              setSelectedPrivacySetting('shareLocation');
              setShowPrivacyModal(true);
            }
          )}

          {renderSettingItem(
            'person',
            'Share Profile',
            privacyOptions.find(p => p.value === settings.privacy.shareProfile)?.label,
            () => {
              setSelectedPrivacySetting('shareProfile');
              setShowPrivacyModal(true);
            }
          )}

          {renderSettingItem(
            'star',
            'Share Rating',
            privacyOptions.find(p => p.value === settings.privacy.shareRating)?.label,
            () => {
              setSelectedPrivacySetting('shareRating');
              setShowPrivacyModal(true);
            }
          )}
        </Card>

        {/* App Preferences */}
        <Card variant="elevated" margin="md" padding="none">
          <View style={{ paddingHorizontal: 20, paddingVertical: 16, borderBottomWidth: 1, borderBottomColor: '#e5e7eb' }}>
            <Text style={{ fontSize: 18, fontWeight: 'bold', color: '#111827' }}>
              App Preferences
            </Text>
          </View>

          {renderSettingItem(
            'checkmark-circle',
            'Auto Accept Orders',
            'Automatically accept orders when online',
            undefined,
            <Switch
              value={settings.preferences.autoAcceptOrders}
              onValueChange={() => handlePreferenceToggle('autoAcceptOrders')}
              trackColor={{ false: '#d1d5db', true: '#fed7aa' }}
              thumbColor={settings.preferences.autoAcceptOrders ? '#f97316' : '#f4f3f4'}
            />,
            false
          )}

          {renderSettingItem(
            'volume-high',
            'Sound',
            'Enable notification sounds',
            undefined,
            <Switch
              value={settings.preferences.soundEnabled}
              onValueChange={() => handlePreferenceToggle('soundEnabled')}
              trackColor={{ false: '#d1d5db', true: '#fed7aa' }}
              thumbColor={settings.preferences.soundEnabled ? '#f97316' : '#f4f3f4'}
            />,
            false
          )}

          {renderSettingItem(
            'phone-portrait',
            'Vibration',
            'Enable vibration for notifications',
            undefined,
            <Switch
              value={settings.preferences.vibrationEnabled}
              onValueChange={() => handlePreferenceToggle('vibrationEnabled')}
              trackColor={{ false: '#d1d5db', true: '#fed7aa' }}
              thumbColor={settings.preferences.vibrationEnabled ? '#f97316' : '#f4f3f4'}
            />,
            false
          )}
        </Card>

        {/* Support & Legal */}
        <Card variant="elevated" margin="md" padding="none">
          <View style={{ paddingHorizontal: 20, paddingVertical: 16, borderBottomWidth: 1, borderBottomColor: '#e5e7eb' }}>
            <Text style={{ fontSize: 18, fontWeight: 'bold', color: '#111827' }}>
              Support & Legal
            </Text>
          </View>

          {renderSettingItem(
            'help-circle',
            'Help & Support',
            'Get help or contact support',
            handleContactSupport
          )}

          {renderSettingItem(
            'document-text',
            'Terms of Service',
            'Read our terms and conditions',
            () => Linking.openURL('https://foodway.pk/terms')
          )}

          {renderSettingItem(
            'shield-checkmark',
            'Privacy Policy',
            'Read our privacy policy',
            () => Linking.openURL('https://foodway.pk/privacy')
          )}
        </Card>

        {/* App Information */}
        <Card variant="elevated" margin="md" padding="none">
          <View style={{ paddingHorizontal: 20, paddingVertical: 16, borderBottomWidth: 1, borderBottomColor: '#e5e7eb' }}>
            <Text style={{ fontSize: 18, fontWeight: 'bold', color: '#111827' }}>
              App Information
            </Text>
          </View>

          {renderSettingItem(
            'information-circle',
            'App Version',
            `Version ${settings.appVersion} (${settings.buildNumber})`,
            undefined,
            undefined,
            false
          )}
        </Card>

        {/* Account Actions */}
        <View style={{ padding: 20, gap: 12 }}>
          <Button
            title="Logout"
            variant="outline"
            leftIcon="log-out-outline"
            onPress={handleLogout}
            style={{ borderColor: '#f59e0b' }}
            textStyle={{ color: '#f59e0b' }}
          />

          <Button
            title="Deactivate Account"
            variant="outline"
            leftIcon="close-circle-outline"
            onPress={handleDeactivateAccount}
            style={{ borderColor: '#ef4444' }}
            textStyle={{ color: '#ef4444' }}
          />
        </View>

        {/* Bottom spacing */}
        <View style={{ height: 20 }} />
      </ScrollView>

      {renderLanguageModal()}
      {renderPrivacyModal()}
    </SafeAreaView>
  );
};

export default SettingsScreen;
