import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  TextInput,
  Alert,
  KeyboardAvoidingView,
  Platform,
  Image,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import { useNavigation, useRoute } from '@react-navigation/native';
import * as ImagePicker from 'expo-image-picker';
import { Card, Button, LoadingSpinner } from '../../components/ui';
import { useAuth } from '../../context/AuthContext';
import { useProfile } from '../../context/ProfileContext';

interface EditProfileFormData {
  firstName: string;
  lastName: string;
  email: string;
  phone: string;
  cnic: string;
  dateOfBirth: string;
  address: string;
  city: string;
  profilePicture: string;
}

interface FormErrors {
  firstName?: string;
  lastName?: string;
  email?: string;
  phone?: string;
  cnic?: string;
  dateOfBirth?: string;
  address?: string;
  city?: string;
}

const EditProfileScreen: React.FC = () => {
  const navigation = useNavigation();
  const { state } = useAuth();
  const { state: profileState } = useProfile();
  
  // Mock profile data (in real app, this would come from context/API)
  const mockProfile = {
    personalInfo: {
      firstName: 'Muhammad',
      lastName: 'Ahmed',
      email: '<EMAIL>',
      phone: '+92 300 1234567',
      cnic: '35202-1234567-1',
      dateOfBirth: '1995-06-15',
      address: 'House 123, Street 5, DHA Phase 2',
      city: 'Lahore',
      profilePicture: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face',
    },
  };

  const [formData, setFormData] = useState<EditProfileFormData>({
    firstName: mockProfile.personalInfo.firstName,
    lastName: mockProfile.personalInfo.lastName,
    email: mockProfile.personalInfo.email,
    phone: mockProfile.personalInfo.phone,
    cnic: mockProfile.personalInfo.cnic,
    dateOfBirth: mockProfile.personalInfo.dateOfBirth,
    address: mockProfile.personalInfo.address,
    city: mockProfile.personalInfo.city,
    profilePicture: mockProfile.personalInfo.profilePicture,
  });

  const [errors, setErrors] = useState<FormErrors>({});
  const [loading, setLoading] = useState(false);
  const [uploading, setUploading] = useState(false);

  const validateForm = (): boolean => {
    const newErrors: FormErrors = {};

    // First Name validation
    if (!formData.firstName.trim()) {
      newErrors.firstName = 'First name is required';
    } else if (formData.firstName.trim().length < 2) {
      newErrors.firstName = 'First name must be at least 2 characters';
    }

    // Last Name validation
    if (!formData.lastName.trim()) {
      newErrors.lastName = 'Last name is required';
    } else if (formData.lastName.trim().length < 2) {
      newErrors.lastName = 'Last name must be at least 2 characters';
    }

    // Email validation
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!formData.email.trim()) {
      newErrors.email = 'Email is required';
    } else if (!emailRegex.test(formData.email)) {
      newErrors.email = 'Please enter a valid email address';
    }

    // Phone validation
    const phoneRegex = /^\+92\s?[0-9]{3}\s?[0-9]{7}$/;
    if (!formData.phone.trim()) {
      newErrors.phone = 'Phone number is required';
    } else if (!phoneRegex.test(formData.phone)) {
      newErrors.phone = 'Please enter a valid Pakistani phone number (+92 XXX XXXXXXX)';
    }

    // CNIC validation
    const cnicRegex = /^[0-9]{5}-[0-9]{7}-[0-9]$/;
    if (!formData.cnic.trim()) {
      newErrors.cnic = 'CNIC is required';
    } else if (!cnicRegex.test(formData.cnic)) {
      newErrors.cnic = 'Please enter a valid CNIC (XXXXX-XXXXXXX-X)';
    }

    // Address validation
    if (!formData.address.trim()) {
      newErrors.address = 'Address is required';
    } else if (formData.address.trim().length < 10) {
      newErrors.address = 'Please enter a complete address';
    }

    // City validation
    if (!formData.city.trim()) {
      newErrors.city = 'City is required';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSave = async () => {
    if (!validateForm()) {
      Alert.alert('Validation Error', 'Please fix the errors in the form before saving.');
      return;
    }

    setLoading(true);
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      Alert.alert(
        'Success',
        'Profile updated successfully!',
        [
          {
            text: 'OK',
            onPress: () => navigation.goBack(),
          },
        ]
      );
    } catch (error) {
      Alert.alert('Error', 'Failed to update profile. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const handleProfilePicturePress = async () => {
    const { status } = await ImagePicker.requestMediaLibraryPermissionsAsync();
    if (status !== 'granted') {
      Alert.alert('Permission Required', 'Please grant camera roll permissions to upload profile picture.');
      return;
    }

    Alert.alert(
      'Update Profile Picture',
      'Choose an option',
      [
        { text: 'Camera', onPress: () => openCamera() },
        { text: 'Gallery', onPress: () => openGallery() },
        { text: 'Cancel', style: 'cancel' },
      ]
    );
  };

  const openCamera = async () => {
    const { status } = await ImagePicker.requestCameraPermissionsAsync();
    if (status !== 'granted') {
      Alert.alert('Permission Required', 'Please grant camera permissions to take a photo.');
      return;
    }

    const result = await ImagePicker.launchCameraAsync({
      mediaTypes: ImagePicker.MediaTypeOptions.Images,
      allowsEditing: true,
      aspect: [1, 1],
      quality: 0.8,
    });

    if (!result.canceled) {
      uploadProfilePicture(result.assets[0].uri);
    }
  };

  const openGallery = async () => {
    const result = await ImagePicker.launchImageLibraryAsync({
      mediaTypes: ImagePicker.MediaTypeOptions.Images,
      allowsEditing: true,
      aspect: [1, 1],
      quality: 0.8,
    });

    if (!result.canceled) {
      uploadProfilePicture(result.assets[0].uri);
    }
  };

  const uploadProfilePicture = async (uri: string) => {
    setUploading(true);
    try {
      // Simulate upload
      await new Promise(resolve => setTimeout(resolve, 2000));
      setFormData(prev => ({
        ...prev,
        profilePicture: uri,
      }));
      Alert.alert('Success', 'Profile picture updated successfully!');
    } catch (error) {
      Alert.alert('Error', 'Failed to upload profile picture. Please try again.');
    } finally {
      setUploading(false);
    }
  };

  const updateFormData = (field: keyof EditProfileFormData, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: value,
    }));
    
    // Clear error when user starts typing
    if (errors[field as keyof FormErrors]) {
      setErrors(prev => ({
        ...prev,
        [field]: undefined,
      }));
    }
  };

  const renderHeader = () => (
    <View style={{
      backgroundColor: '#dc2626',
      paddingHorizontal: 20,
      paddingTop: 20,
      paddingBottom: 28,
      borderBottomLeftRadius: 28,
      borderBottomRightRadius: 28,
      shadowColor: '#dc2626',
      shadowOffset: { width: 0, height: 12 },
      shadowOpacity: 0.3,
      shadowRadius: 20,
      elevation: 16,
    }}>
      <View style={{
        flexDirection: 'row',
        alignItems: 'center',
        marginBottom: 20,
      }}>
        <TouchableOpacity
          onPress={() => navigation.goBack()}
          style={{
            width: 48,
            height: 48,
            borderRadius: 24,
            backgroundColor: 'rgba(255,255,255,0.2)',
            justifyContent: 'center',
            alignItems: 'center',
            marginRight: 16,
            borderWidth: 2,
            borderColor: 'rgba(255,255,255,0.3)',
          }}
        >
          <Ionicons name="arrow-back" size={24} color="white" />
        </TouchableOpacity>

        <View style={{ flex: 1 }}>
          <Text style={{
            fontSize: 24,
            fontWeight: 'bold',
            color: 'white',
            marginBottom: 4,
          }}>
            ✏️ Edit Profile
          </Text>
          <Text style={{
            fontSize: 14,
            color: 'rgba(255,255,255,0.8)',
            fontWeight: '500',
          }}>
            Update your information
          </Text>
        </View>

        <TouchableOpacity
          onPress={handleSave}
          disabled={loading}
          style={{
            backgroundColor: loading ? 'rgba(255,255,255,0.1)' : 'rgba(255,255,255,0.2)',
            paddingHorizontal: 16,
            paddingVertical: 10,
            borderRadius: 16,
            borderWidth: 2,
            borderColor: 'rgba(255,255,255,0.3)',
            opacity: loading ? 0.5 : 1,
            flexDirection: 'row',
            alignItems: 'center',
          }}
        >
          {loading && <LoadingSpinner size="small" color="white" style={{ marginRight: 8 }} />}
          <Text style={{
            fontSize: 16,
            fontWeight: 'bold',
            color: 'white',
          }}>
            {loading ? 'Saving...' : 'Save'}
          </Text>
        </TouchableOpacity>
      </View>
    </View>
  );

  return (
    <View style={{ flex: 1, backgroundColor: '#f1f5f9' }}>
      <SafeAreaView style={{ flex: 1 }}>
        {renderHeader()}
      
      <KeyboardAvoidingView
        style={{ flex: 1 }}
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        keyboardVerticalOffset={Platform.OS === 'ios' ? 0 : 20}
      >
        <ScrollView
          style={{ flex: 1 }}
          showsVerticalScrollIndicator={false}
          contentContainerStyle={{ paddingBottom: 40 }}
        >
          {/* Profile Picture Section */}
          <Card variant="elevated" margin="md" padding="lg">
            <Text style={{ fontSize: 18, fontWeight: 'bold', color: '#111827', marginBottom: 16 }}>
              Profile Picture
            </Text>
            
            <View style={{ alignItems: 'center' }}>
              <TouchableOpacity onPress={handleProfilePicturePress} disabled={uploading}>
                <View style={{ position: 'relative' }}>
                  <Image
                    source={{
                      uri: formData.profilePicture || 'https://via.placeholder.com/120x120/e5e7eb/6b7280?text=No+Image'
                    }}
                    style={{
                      width: 120,
                      height: 120,
                      borderRadius: 60,
                      backgroundColor: '#f3f4f6',
                    }}
                  />
                  {uploading && (
                    <View style={{
                      position: 'absolute',
                      top: 0,
                      left: 0,
                      right: 0,
                      bottom: 0,
                      backgroundColor: 'rgba(0,0,0,0.5)',
                      borderRadius: 60,
                      alignItems: 'center',
                      justifyContent: 'center',
                    }}>
                      <LoadingSpinner size="small" color="white" />
                    </View>
                  )}
                  <View style={{
                    position: 'absolute',
                    bottom: 0,
                    right: 0,
                    backgroundColor: '#f97316',
                    borderRadius: 16,
                    width: 32,
                    height: 32,
                    alignItems: 'center',
                    justifyContent: 'center',
                    borderWidth: 2,
                    borderColor: 'white',
                  }}>
                    <Ionicons name="camera" size={16} color="white" />
                  </View>
                </View>
              </TouchableOpacity>
              
              <Text style={{ fontSize: 14, color: '#6b7280', marginTop: 12, textAlign: 'center' }}>
                Tap to change profile picture
              </Text>
            </View>
          </Card>

          {/* Personal Information Form */}
          <Card variant="elevated" margin="md" padding="lg">
            <Text style={{ fontSize: 18, fontWeight: 'bold', color: '#111827', marginBottom: 16 }}>
              Personal Information
            </Text>

            <View style={{ gap: 16 }}>
              {/* First Name */}
              <View>
                <Text style={{ fontSize: 14, fontWeight: '600', color: '#374151', marginBottom: 8 }}>
                  First Name *
                </Text>
                <TextInput
                  value={formData.firstName}
                  onChangeText={(value) => updateFormData('firstName', value)}
                  placeholder="Enter your first name"
                  style={{
                    borderWidth: 1,
                    borderColor: errors.firstName ? '#ef4444' : '#d1d5db',
                    borderRadius: 8,
                    paddingHorizontal: 12,
                    paddingVertical: 12,
                    fontSize: 16,
                    backgroundColor: '#ffffff',
                  }}
                />
                {errors.firstName && (
                  <Text style={{ fontSize: 12, color: '#ef4444', marginTop: 4 }}>
                    {errors.firstName}
                  </Text>
                )}
              </View>

              {/* Last Name */}
              <View>
                <Text style={{ fontSize: 14, fontWeight: '600', color: '#374151', marginBottom: 8 }}>
                  Last Name *
                </Text>
                <TextInput
                  value={formData.lastName}
                  onChangeText={(value) => updateFormData('lastName', value)}
                  placeholder="Enter your last name"
                  style={{
                    borderWidth: 1,
                    borderColor: errors.lastName ? '#ef4444' : '#d1d5db',
                    borderRadius: 8,
                    paddingHorizontal: 12,
                    paddingVertical: 12,
                    fontSize: 16,
                    backgroundColor: '#ffffff',
                  }}
                />
                {errors.lastName && (
                  <Text style={{ fontSize: 12, color: '#ef4444', marginTop: 4 }}>
                    {errors.lastName}
                  </Text>
                )}
              </View>

              {/* Email */}
              <View>
                <Text style={{ fontSize: 14, fontWeight: '600', color: '#374151', marginBottom: 8 }}>
                  Email Address *
                </Text>
                <TextInput
                  value={formData.email}
                  onChangeText={(value) => updateFormData('email', value)}
                  placeholder="Enter your email address"
                  keyboardType="email-address"
                  autoCapitalize="none"
                  style={{
                    borderWidth: 1,
                    borderColor: errors.email ? '#ef4444' : '#d1d5db',
                    borderRadius: 8,
                    paddingHorizontal: 12,
                    paddingVertical: 12,
                    fontSize: 16,
                    backgroundColor: '#ffffff',
                  }}
                />
                {errors.email && (
                  <Text style={{ fontSize: 12, color: '#ef4444', marginTop: 4 }}>
                    {errors.email}
                  </Text>
                )}
              </View>

              {/* Phone */}
              <View>
                <Text style={{ fontSize: 14, fontWeight: '600', color: '#374151', marginBottom: 8 }}>
                  Phone Number *
                </Text>
                <TextInput
                  value={formData.phone}
                  onChangeText={(value) => updateFormData('phone', value)}
                  placeholder="+92 300 1234567"
                  keyboardType="phone-pad"
                  style={{
                    borderWidth: 1,
                    borderColor: errors.phone ? '#ef4444' : '#d1d5db',
                    borderRadius: 8,
                    paddingHorizontal: 12,
                    paddingVertical: 12,
                    fontSize: 16,
                    backgroundColor: '#ffffff',
                  }}
                />
                {errors.phone && (
                  <Text style={{ fontSize: 12, color: '#ef4444', marginTop: 4 }}>
                    {errors.phone}
                  </Text>
                )}
              </View>

              {/* CNIC */}
              <View>
                <Text style={{ fontSize: 14, fontWeight: '600', color: '#374151', marginBottom: 8 }}>
                  CNIC *
                </Text>
                <TextInput
                  value={formData.cnic}
                  onChangeText={(value) => updateFormData('cnic', value)}
                  placeholder="35202-1234567-1"
                  keyboardType="numeric"
                  style={{
                    borderWidth: 1,
                    borderColor: errors.cnic ? '#ef4444' : '#d1d5db',
                    borderRadius: 8,
                    paddingHorizontal: 12,
                    paddingVertical: 12,
                    fontSize: 16,
                    backgroundColor: '#ffffff',
                  }}
                />
                {errors.cnic && (
                  <Text style={{ fontSize: 12, color: '#ef4444', marginTop: 4 }}>
                    {errors.cnic}
                  </Text>
                )}
              </View>

              {/* Date of Birth */}
              <View>
                <Text style={{ fontSize: 14, fontWeight: '600', color: '#374151', marginBottom: 8 }}>
                  Date of Birth
                </Text>
                <TextInput
                  value={formData.dateOfBirth}
                  onChangeText={(value) => updateFormData('dateOfBirth', value)}
                  placeholder="YYYY-MM-DD"
                  style={{
                    borderWidth: 1,
                    borderColor: errors.dateOfBirth ? '#ef4444' : '#d1d5db',
                    borderRadius: 8,
                    paddingHorizontal: 12,
                    paddingVertical: 12,
                    fontSize: 16,
                    backgroundColor: '#ffffff',
                  }}
                />
                {errors.dateOfBirth && (
                  <Text style={{ fontSize: 12, color: '#ef4444', marginTop: 4 }}>
                    {errors.dateOfBirth}
                  </Text>
                )}
              </View>

              {/* Address */}
              <View>
                <Text style={{ fontSize: 14, fontWeight: '600', color: '#374151', marginBottom: 8 }}>
                  Address *
                </Text>
                <TextInput
                  value={formData.address}
                  onChangeText={(value) => updateFormData('address', value)}
                  placeholder="Enter your complete address"
                  multiline
                  numberOfLines={3}
                  style={{
                    borderWidth: 1,
                    borderColor: errors.address ? '#ef4444' : '#d1d5db',
                    borderRadius: 8,
                    paddingHorizontal: 12,
                    paddingVertical: 12,
                    fontSize: 16,
                    backgroundColor: '#ffffff',
                    textAlignVertical: 'top',
                  }}
                />
                {errors.address && (
                  <Text style={{ fontSize: 12, color: '#ef4444', marginTop: 4 }}>
                    {errors.address}
                  </Text>
                )}
              </View>

              {/* City */}
              <View>
                <Text style={{ fontSize: 14, fontWeight: '600', color: '#374151', marginBottom: 8 }}>
                  City *
                </Text>
                <TextInput
                  value={formData.city}
                  onChangeText={(value) => updateFormData('city', value)}
                  placeholder="Enter your city"
                  style={{
                    borderWidth: 1,
                    borderColor: errors.city ? '#ef4444' : '#d1d5db',
                    borderRadius: 8,
                    paddingHorizontal: 12,
                    paddingVertical: 12,
                    fontSize: 16,
                    backgroundColor: '#ffffff',
                  }}
                />
                {errors.city && (
                  <Text style={{ fontSize: 12, color: '#ef4444', marginTop: 4 }}>
                    {errors.city}
                  </Text>
                )}
              </View>
            </View>
          </Card>

          {/* Action Buttons */}
          <View style={{ paddingHorizontal: 20, paddingVertical: 20, gap: 12 }}>
            <Button
              title={loading ? 'Saving...' : 'Save Changes'}
              onPress={handleSave}
              disabled={loading}
              leftIcon={loading ? undefined : 'checkmark'}
              style={{
                backgroundColor: loading ? '#9ca3af' : '#f97316',
              }}
            />

            <Button
              title="Cancel"
              variant="outline"
              onPress={() => navigation.goBack()}
              disabled={loading}
              style={{
                borderColor: '#6b7280',
              }}
              textStyle={{
                color: '#6b7280',
              }}
            />
          </View>
        </ScrollView>
      </KeyboardAvoidingView>
      </SafeAreaView>
    </View>
  );
};

export default EditProfileScreen;
