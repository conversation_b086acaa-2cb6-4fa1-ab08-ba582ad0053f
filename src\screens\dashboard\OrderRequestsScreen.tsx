import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  ScrollView,
  RefreshControl,
  TouchableOpacity,
  Alert,
  Dimensions,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import { useNavigation } from '@react-navigation/native';
import { Card, Button, Badge, LoadingSpinner } from '../../components/ui';
import { useOrders } from '../../context/OrderContext';
import { Order } from '../../types/orders';
import { formatCurrency, formatDistance, formatDuration } from '../../utils/helpers';

const { width } = Dimensions.get('window');

const OrderRequestsScreen: React.FC = () => {
  const navigation = useNavigation();
  const { state, fetchAvailableOrders, acceptOrder, declineOrder } = useOrders();
  const [refreshing, setRefreshing] = useState(false);
  const [selectedOrder, setSelectedOrder] = useState<string | null>(null);
  const [showHeatmap, setShowHeatmap] = useState(false);

  useEffect(() => {
    fetchAvailableOrders();
  }, []);

  const onRefresh = async () => {
    setRefreshing(true);
    await fetchAvailableOrders();
    setRefreshing(false);
  };

  const handleAcceptOrder = async (orderId: string) => {
    try {
      await acceptOrder(orderId);
      Alert.alert(
        'Order Accepted!',
        'Navigate to the restaurant to pick up the order.',
        [{ text: 'OK' }]
      );
    } catch (error: any) {
      Alert.alert('Error', error.message);
    }
  };

  const handleDeclineOrder = async (orderId: string) => {
    try {
      await declineOrder(orderId);
    } catch (error: any) {
      Alert.alert('Error', error.message);
    }
  };

  const renderHeader = () => (
    <View style={{
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      paddingHorizontal: 20,
      paddingVertical: 16,
      backgroundColor: '#ffffff',
      borderBottomWidth: 1,
      borderBottomColor: '#e5e7eb',
    }}>
      <View>
        <Text style={{
          fontSize: 24,
          fontWeight: 'bold',
          color: '#111827',
        }}>
          Order Requests
        </Text>
        <Text style={{
          fontSize: 14,
          color: '#6b7280',
        }}>
          {state.availableOrders.length} orders available
        </Text>
      </View>

      <TouchableOpacity
        onPress={() => setShowHeatmap(!showHeatmap)}
        style={{
          flexDirection: 'row',
          alignItems: 'center',
          backgroundColor: showHeatmap ? '#f97316' : '#f3f4f6',
          paddingHorizontal: 12,
          paddingVertical: 8,
          borderRadius: 20,
        }}
      >
        <Ionicons 
          name="map" 
          size={16} 
          color={showHeatmap ? 'white' : '#6b7280'} 
        />
        <Text style={{
          fontSize: 12,
          fontWeight: '500',
          color: showHeatmap ? 'white' : '#6b7280',
          marginLeft: 4,
        }}>
          Heatmap
        </Text>
      </TouchableOpacity>
    </View>
  );

  const renderHeatmapView = () => (
    <Card variant="elevated" margin="md" padding="lg">
      <Text style={{
        fontSize: 18,
        fontWeight: 'bold',
        color: '#111827',
        marginBottom: 16,
      }}>
        Busy Areas
      </Text>

      <View style={{
        backgroundColor: '#f8fafc',
        borderRadius: 12,
        padding: 16,
        marginBottom: 16,
      }}>
        {/* Simulated heatmap areas */}
        {[
          { area: 'Downtown', orders: 12, color: '#dc2626' },
          { area: 'University District', orders: 8, color: '#f59e0b' },
          { area: 'Shopping Mall', orders: 5, color: '#eab308' },
          { area: 'Residential Area', orders: 2, color: '#22c55e' },
        ].map((zone, index) => (
          <View key={index} style={{
            flexDirection: 'row',
            justifyContent: 'space-between',
            alignItems: 'center',
            paddingVertical: 8,
            borderBottomWidth: index < 3 ? 1 : 0,
            borderBottomColor: '#e5e7eb',
          }}>
            <View style={{ flexDirection: 'row', alignItems: 'center' }}>
              <View style={{
                width: 12,
                height: 12,
                borderRadius: 6,
                backgroundColor: zone.color,
                marginRight: 12,
              }} />
              <Text style={{
                fontSize: 14,
                fontWeight: '500',
                color: '#111827',
              }}>
                {zone.area}
              </Text>
            </View>
            
            <Text style={{
              fontSize: 14,
              color: '#6b7280',
            }}>
              {zone.orders} orders
            </Text>
          </View>
        ))}
      </View>

      <Text style={{
        fontSize: 12,
        color: '#6b7280',
        textAlign: 'center',
      }}>
        Red areas have the highest demand and potential earnings
      </Text>
    </Card>
  );

  const renderOrderCard = (order: Order) => {
    const isSelected = selectedOrder === order.id;
    const isBusyArea = Math.random() > 0.5; // Simulate busy area detection

    return (
      <Card 
        key={order.id}
        variant="elevated" 
        margin="md" 
        padding="lg"
        style={{
          borderWidth: isSelected ? 2 : 0,
          borderColor: isSelected ? '#ef4444' : 'transparent',
        }}
      >
        {/* Order Header */}
        <View style={{
          flexDirection: 'row',
          justifyContent: 'space-between',
          alignItems: 'flex-start',
          marginBottom: 12,
        }}>
          <View style={{ flex: 1 }}>
            <Text style={{
              fontSize: 18,
              fontWeight: 'bold',
              color: '#111827',
              marginBottom: 4,
            }}>
              {order.restaurant.name}
            </Text>
            <Text style={{
              fontSize: 14,
              color: '#6b7280',
            }}>
              Order #{order.orderNumber}
            </Text>
          </View>

          <View style={{ alignItems: 'flex-end' }}>
            <Text style={{
              fontSize: 20,
              fontWeight: 'bold',
              color: '#059669',
              marginBottom: 4,
            }}>
              {formatCurrency(order.estimatedEarnings)}
            </Text>
            {isBusyArea && (
              <Badge text="High Demand" variant="warning" />
            )}
          </View>
        </View>

        {/* Order Details */}
        <View style={{
          backgroundColor: '#f8fafc',
          padding: 12,
          borderRadius: 8,
          marginBottom: 12,
        }}>
          <View style={{
            flexDirection: 'row',
            justifyContent: 'space-between',
            marginBottom: 8,
          }}>
            <View style={{ flexDirection: 'row', alignItems: 'center' }}>
              <Ionicons name="location" size={16} color="#f97316" />
              <Text style={{
                fontSize: 12,
                color: '#6b7280',
                marginLeft: 4,
              }}>
                Distance
              </Text>
            </View>
            <Text style={{
              fontSize: 12,
              fontWeight: '500',
              color: '#111827',
            }}>
              {formatDistance(order.estimatedDistance)}
            </Text>
          </View>

          <View style={{
            flexDirection: 'row',
            justifyContent: 'space-between',
            marginBottom: 8,
          }}>
            <View style={{ flexDirection: 'row', alignItems: 'center' }}>
              <Ionicons name="time" size={16} color="#f97316" />
              <Text style={{
                fontSize: 12,
                color: '#6b7280',
                marginLeft: 4,
              }}>
                Duration
              </Text>
            </View>
            <Text style={{
              fontSize: 12,
              fontWeight: '500',
              color: '#111827',
            }}>
              {formatDuration(order.estimatedDuration)}
            </Text>
          </View>

          <View style={{
            flexDirection: 'row',
            justifyContent: 'space-between',
          }}>
            <View style={{ flexDirection: 'row', alignItems: 'center' }}>
              <Ionicons name="card" size={16} color="#f97316" />
              <Text style={{
                fontSize: 12,
                color: '#6b7280',
                marginLeft: 4,
              }}>
                Order Total
              </Text>
            </View>
            <Text style={{
              fontSize: 12,
              fontWeight: '500',
              color: '#111827',
            }}>
              {formatCurrency(order.total)}
            </Text>
          </View>
        </View>

        {/* Delivery Address */}
        <View style={{
          flexDirection: 'row',
          alignItems: 'center',
          marginBottom: 16,
        }}>
          <Ionicons name="navigate" size={16} color="#6b7280" />
          <Text style={{
            fontSize: 14,
            color: '#6b7280',
            marginLeft: 8,
            flex: 1,
          }}>
            {order.deliveryAddress.street}, {order.deliveryAddress.city}
          </Text>
        </View>

        {/* Action Buttons */}
        <View style={{
          flexDirection: 'row',
          gap: 12,
        }}>
          <Button
            title="Decline"
            variant="outline"
            onPress={() => handleDeclineOrder(order.id)}
            style={{ flex: 1 }}
          />
          
          <Button
            title="Accept"
            variant="primary"
            onPress={() => handleAcceptOrder(order.id)}
            style={{ flex: 1 }}
          />
        </View>
      </Card>
    );
  };

  if (state.isLoading && !refreshing) {
    return (
      <SafeAreaView style={{ flex: 1, backgroundColor: '#f8fafc' }}>
        {renderHeader()}
        <LoadingSpinner />
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={{ flex: 1, backgroundColor: '#f8fafc' }}>
      {renderHeader()}
      
      <ScrollView
        style={{ flex: 1 }}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
        showsVerticalScrollIndicator={false}
      >
        {showHeatmap && renderHeatmapView()}
        
        {state.availableOrders.length === 0 ? (
          <Card variant="elevated" margin="md" padding="lg">
            <View style={{ alignItems: 'center', paddingVertical: 40 }}>
              <Ionicons name="hourglass-outline" size={48} color="#6b7280" />
              <Text style={{
                fontSize: 18,
                fontWeight: '600',
                color: '#6b7280',
                marginTop: 16,
                marginBottom: 8,
              }}>
                No orders available
              </Text>
              <Text style={{
                fontSize: 14,
                color: '#9ca3af',
                textAlign: 'center',
              }}>
                New orders will appear here when customers place them
              </Text>
            </View>
          </Card>
        ) : (
          state.availableOrders.map(renderOrderCard)
        )}
        
        {/* Bottom spacing */}
        <View style={{ height: 20 }} />
      </ScrollView>
    </SafeAreaView>
  );
};

export default OrderRequestsScreen;
