import React, { createContext, useContext, useReducer, ReactNode } from 'react';
import {
  RiderProfile,
  Vehicle,
  PaymentMethod,
  UserSettings,
  Document,
  DocumentStatus,
  VehicleType,
  PaymentMethodType,
  Language,
  PrivacySetting,
} from '../types/profile';

// Profile state interface
interface ProfileState {
  profile: RiderProfile | null;
  vehicles: Vehicle[];
  paymentMethods: PaymentMethod[];
  settings: UserSettings;
  loading: boolean;
  error: string | null;
}

// Profile actions
type ProfileAction =
  | { type: 'SET_LOADING'; payload: boolean }
  | { type: 'SET_ERROR'; payload: string | null }
  | { type: 'SET_PROFILE'; payload: RiderProfile }
  | { type: 'UPDATE_PROFILE'; payload: Partial<RiderProfile> }
  | { type: 'SET_VEHICLES'; payload: Vehicle[] }
  | { type: 'ADD_VEHICLE'; payload: Vehicle }
  | { type: 'UPDATE_VEHICLE'; payload: { id: string; updates: Partial<Vehicle> } }
  | { type: 'DELETE_VEHICLE'; payload: string }
  | { type: 'SET_PAYMENT_METHODS'; payload: PaymentMethod[] }
  | { type: 'ADD_PAYMENT_METHOD'; payload: PaymentMethod }
  | { type: 'UPDATE_PAYMENT_METHOD'; payload: { id: string; updates: Partial<PaymentMethod> } }
  | { type: 'DELETE_PAYMENT_METHOD'; payload: string }
  | { type: 'SET_SETTINGS'; payload: UserSettings }
  | { type: 'UPDATE_SETTINGS'; payload: Partial<UserSettings> }
  | { type: 'UPDATE_DOCUMENT_STATUS'; payload: { documentId: string; status: DocumentStatus } };

// Initial state
const initialState: ProfileState = {
  profile: null,
  vehicles: [],
  paymentMethods: [],
  settings: {
    language: Language.ENGLISH,
    notifications: {
      orderRequests: true,
      orderUpdates: true,
      earnings: true,
      promotions: false,
      systemUpdates: true,
      marketing: false,
    },
    privacy: {
      shareLocation: PrivacySetting.WHILE_WORKING,
      shareProfile: PrivacySetting.PRIVATE,
      shareEarnings: PrivacySetting.PRIVATE,
      shareRating: PrivacySetting.PUBLIC,
    },
    preferences: {
      autoAcceptOrders: false,
      soundEnabled: true,
      vibrationEnabled: true,
      darkMode: false,
      dataUsage: 'normal',
    },
    appVersion: '1.0.0',
    buildNumber: '100',
  },
  loading: false,
  error: null,
};

// Profile reducer
const profileReducer = (state: ProfileState, action: ProfileAction): ProfileState => {
  switch (action.type) {
    case 'SET_LOADING':
      return { ...state, loading: action.payload };
    
    case 'SET_ERROR':
      return { ...state, error: action.payload, loading: false };
    
    case 'SET_PROFILE':
      return { ...state, profile: action.payload, loading: false, error: null };
    
    case 'UPDATE_PROFILE':
      return {
        ...state,
        profile: state.profile ? { ...state.profile, ...action.payload } : null,
        loading: false,
        error: null,
      };
    
    case 'SET_VEHICLES':
      return { ...state, vehicles: action.payload, loading: false, error: null };
    
    case 'ADD_VEHICLE':
      return {
        ...state,
        vehicles: [...state.vehicles, action.payload],
        loading: false,
        error: null,
      };
    
    case 'UPDATE_VEHICLE':
      return {
        ...state,
        vehicles: state.vehicles.map(vehicle =>
          vehicle.id === action.payload.id
            ? { ...vehicle, ...action.payload.updates }
            : vehicle
        ),
        loading: false,
        error: null,
      };
    
    case 'DELETE_VEHICLE':
      return {
        ...state,
        vehicles: state.vehicles.filter(vehicle => vehicle.id !== action.payload),
        loading: false,
        error: null,
      };
    
    case 'SET_PAYMENT_METHODS':
      return { ...state, paymentMethods: action.payload, loading: false, error: null };
    
    case 'ADD_PAYMENT_METHOD':
      return {
        ...state,
        paymentMethods: [...state.paymentMethods, action.payload],
        loading: false,
        error: null,
      };
    
    case 'UPDATE_PAYMENT_METHOD':
      return {
        ...state,
        paymentMethods: state.paymentMethods.map(method =>
          method.id === action.payload.id
            ? { ...method, ...action.payload.updates }
            : method
        ),
        loading: false,
        error: null,
      };
    
    case 'DELETE_PAYMENT_METHOD':
      return {
        ...state,
        paymentMethods: state.paymentMethods.filter(method => method.id !== action.payload),
        loading: false,
        error: null,
      };
    
    case 'SET_SETTINGS':
      return { ...state, settings: action.payload, loading: false, error: null };
    
    case 'UPDATE_SETTINGS':
      return {
        ...state,
        settings: { ...state.settings, ...action.payload },
        loading: false,
        error: null,
      };
    
    case 'UPDATE_DOCUMENT_STATUS':
      return {
        ...state,
        vehicles: state.vehicles.map(vehicle => ({
          ...vehicle,
          documents: vehicle.documents.map(doc =>
            doc.id === action.payload.documentId
              ? { ...doc, status: action.payload.status }
              : doc
          ),
        })),
        profile: state.profile ? {
          ...state.profile,
          documents: state.profile.documents.map(doc =>
            doc.id === action.payload.documentId
              ? { ...doc, status: action.payload.status }
              : doc
          ),
        } : null,
        loading: false,
        error: null,
      };
    
    default:
      return state;
  }
};

// Profile context interface
interface ProfileContextType {
  state: ProfileState;
  
  // Profile actions
  loadProfile: () => Promise<void>;
  updateProfile: (updates: Partial<RiderProfile>) => Promise<void>;
  uploadProfilePicture: (imageUri: string) => Promise<void>;
  
  // Vehicle actions
  loadVehicles: () => Promise<void>;
  addVehicle: (vehicle: Omit<Vehicle, 'id' | 'createdAt' | 'updatedAt'>) => Promise<void>;
  updateVehicle: (id: string, updates: Partial<Vehicle>) => Promise<void>;
  deleteVehicle: (id: string) => Promise<void>;
  setActiveVehicle: (id: string) => Promise<void>;
  uploadVehicleDocument: (vehicleId: string, documentType: string, imageUri: string) => Promise<void>;
  
  // Payment method actions
  loadPaymentMethods: () => Promise<void>;
  addPaymentMethod: (method: Omit<PaymentMethod, 'id' | 'createdAt' | 'updatedAt'>) => Promise<void>;
  updatePaymentMethod: (id: string, updates: Partial<PaymentMethod>) => Promise<void>;
  deletePaymentMethod: (id: string) => Promise<void>;
  setDefaultPaymentMethod: (id: string) => Promise<void>;
  
  // Settings actions
  updateSettings: (updates: Partial<UserSettings>) => Promise<void>;
  updateNotificationSettings: (notifications: Partial<UserSettings['notifications']>) => Promise<void>;
  updatePrivacySettings: (privacy: Partial<UserSettings['privacy']>) => Promise<void>;
  updatePreferences: (preferences: Partial<UserSettings['preferences']>) => Promise<void>;
  
  // Document actions
  updateDocumentStatus: (documentId: string, status: DocumentStatus) => Promise<void>;
}

// Create context
const ProfileContext = createContext<ProfileContextType | undefined>(undefined);

// Profile provider component
interface ProfileProviderProps {
  children: ReactNode;
}

export const ProfileProvider: React.FC<ProfileProviderProps> = ({ children }) => {
  const [state, dispatch] = useReducer(profileReducer, initialState);

  // Profile actions
  const loadProfile = async (): Promise<void> => {
    try {
      dispatch({ type: 'SET_LOADING', payload: true });
      
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // Mock profile data would be loaded here
      const mockProfile: RiderProfile = {
        id: '1',
        firstName: 'Muhammad',
        lastName: 'Ahmed',
        email: '<EMAIL>',
        phone: '+92 300 1234567',
        profilePicture: null,
        cnic: '12345-6789012-3',
        dateOfBirth: '1995-06-15',
        address: {
          street: 'Street 123, Block A',
          city: 'Lahore',
          state: 'Punjab',
          postalCode: '54000',
          country: 'Pakistan',
        },
        emergencyContact: {
          name: 'Ali Ahmed',
          phone: '+92 301 2345678',
          relationship: 'Brother',
        },
        documents: [],
        preferredZones: ['DHA', 'Gulberg', 'Johar Town'],
        rating: 4.8,
        totalDeliveries: 1250,
        isActive: true,
        isVerified: true,
        joinedAt: '2023-01-15',
        lastActiveAt: new Date().toISOString(),
      };
      
      dispatch({ type: 'SET_PROFILE', payload: mockProfile });
    } catch (error: any) {
      dispatch({ type: 'SET_ERROR', payload: error.message });
    }
  };

  const updateProfile = async (updates: Partial<RiderProfile>): Promise<void> => {
    try {
      dispatch({ type: 'SET_LOADING', payload: true });
      
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 500));
      
      dispatch({ type: 'UPDATE_PROFILE', payload: updates });
    } catch (error: any) {
      dispatch({ type: 'SET_ERROR', payload: error.message });
    }
  };

  const uploadProfilePicture = async (imageUri: string): Promise<void> => {
    try {
      dispatch({ type: 'SET_LOADING', payload: true });

      // Simulate upload
      await new Promise(resolve => setTimeout(resolve, 2000));

      dispatch({ type: 'UPDATE_PROFILE', payload: { profilePicture: imageUri } });
    } catch (error: any) {
      dispatch({ type: 'SET_ERROR', payload: error.message });
    }
  };

  // Vehicle actions
  const loadVehicles = async (): Promise<void> => {
    try {
      dispatch({ type: 'SET_LOADING', payload: true });

      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));

      // Mock vehicles would be loaded here
      dispatch({ type: 'SET_VEHICLES', payload: [] });
    } catch (error: any) {
      dispatch({ type: 'SET_ERROR', payload: error.message });
    }
  };

  const addVehicle = async (vehicle: Omit<Vehicle, 'id' | 'createdAt' | 'updatedAt'>): Promise<void> => {
    try {
      dispatch({ type: 'SET_LOADING', payload: true });

      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));

      const newVehicle: Vehicle = {
        ...vehicle,
        id: Date.now().toString(),
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      };

      dispatch({ type: 'ADD_VEHICLE', payload: newVehicle });
    } catch (error: any) {
      dispatch({ type: 'SET_ERROR', payload: error.message });
    }
  };

  const updateVehicle = async (id: string, updates: Partial<Vehicle>): Promise<void> => {
    try {
      dispatch({ type: 'SET_LOADING', payload: true });

      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 500));

      dispatch({ type: 'UPDATE_VEHICLE', payload: { id, updates: { ...updates, updatedAt: new Date().toISOString() } } });
    } catch (error: any) {
      dispatch({ type: 'SET_ERROR', payload: error.message });
    }
  };

  const deleteVehicle = async (id: string): Promise<void> => {
    try {
      dispatch({ type: 'SET_LOADING', payload: true });

      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 500));

      dispatch({ type: 'DELETE_VEHICLE', payload: id });
    } catch (error: any) {
      dispatch({ type: 'SET_ERROR', payload: error.message });
    }
  };

  const setActiveVehicle = async (id: string): Promise<void> => {
    try {
      dispatch({ type: 'SET_LOADING', payload: true });

      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 500));

      // Set all vehicles to inactive, then set the selected one to active
      state.vehicles.forEach(vehicle => {
        dispatch({ type: 'UPDATE_VEHICLE', payload: { id: vehicle.id, updates: { isActive: vehicle.id === id } } });
      });
    } catch (error: any) {
      dispatch({ type: 'SET_ERROR', payload: error.message });
    }
  };

  const uploadVehicleDocument = async (vehicleId: string, documentType: string, imageUri: string): Promise<void> => {
    try {
      dispatch({ type: 'SET_LOADING', payload: true });

      // Simulate upload
      await new Promise(resolve => setTimeout(resolve, 2000));

      // Update vehicle with new document
      const vehicle = state.vehicles.find(v => v.id === vehicleId);
      if (vehicle) {
        const updatedDocuments = vehicle.documents.map(doc =>
          doc.type === documentType
            ? { ...doc, status: DocumentStatus.PENDING, uploadedAt: new Date().toISOString() }
            : doc
        );

        dispatch({ type: 'UPDATE_VEHICLE', payload: { id: vehicleId, updates: { documents: updatedDocuments } } });
      }
    } catch (error: any) {
      dispatch({ type: 'SET_ERROR', payload: error.message });
    }
  };

  // Payment method actions
  const loadPaymentMethods = async (): Promise<void> => {
    try {
      dispatch({ type: 'SET_LOADING', payload: true });

      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));

      // Mock payment methods would be loaded here
      dispatch({ type: 'SET_PAYMENT_METHODS', payload: [] });
    } catch (error: any) {
      dispatch({ type: 'SET_ERROR', payload: error.message });
    }
  };

  const addPaymentMethod = async (method: Omit<PaymentMethod, 'id' | 'createdAt' | 'updatedAt'>): Promise<void> => {
    try {
      dispatch({ type: 'SET_LOADING', payload: true });

      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));

      const newMethod: PaymentMethod = {
        ...method,
        id: Date.now().toString(),
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      };

      dispatch({ type: 'ADD_PAYMENT_METHOD', payload: newMethod });
    } catch (error: any) {
      dispatch({ type: 'SET_ERROR', payload: error.message });
    }
  };

  const updatePaymentMethod = async (id: string, updates: Partial<PaymentMethod>): Promise<void> => {
    try {
      dispatch({ type: 'SET_LOADING', payload: true });

      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 500));

      dispatch({ type: 'UPDATE_PAYMENT_METHOD', payload: { id, updates: { ...updates, updatedAt: new Date().toISOString() } } });
    } catch (error: any) {
      dispatch({ type: 'SET_ERROR', payload: error.message });
    }
  };

  const deletePaymentMethod = async (id: string): Promise<void> => {
    try {
      dispatch({ type: 'SET_LOADING', payload: true });

      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 500));

      dispatch({ type: 'DELETE_PAYMENT_METHOD', payload: id });
    } catch (error: any) {
      dispatch({ type: 'SET_ERROR', payload: error.message });
    }
  };

  const setDefaultPaymentMethod = async (id: string): Promise<void> => {
    try {
      dispatch({ type: 'SET_LOADING', payload: true });

      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 500));

      // Set all payment methods to non-default, then set the selected one to default
      state.paymentMethods.forEach(method => {
        dispatch({ type: 'UPDATE_PAYMENT_METHOD', payload: { id: method.id, updates: { isDefault: method.id === id } } });
      });
    } catch (error: any) {
      dispatch({ type: 'SET_ERROR', payload: error.message });
    }
  };

  // Settings actions
  const updateSettings = async (updates: Partial<UserSettings>): Promise<void> => {
    try {
      dispatch({ type: 'SET_LOADING', payload: true });

      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 500));

      dispatch({ type: 'UPDATE_SETTINGS', payload: updates });
    } catch (error: any) {
      dispatch({ type: 'SET_ERROR', payload: error.message });
    }
  };

  const updateNotificationSettings = async (notifications: Partial<UserSettings['notifications']>): Promise<void> => {
    try {
      dispatch({ type: 'SET_LOADING', payload: true });

      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 500));

      dispatch({ type: 'UPDATE_SETTINGS', payload: { notifications: { ...state.settings.notifications, ...notifications } } });
    } catch (error: any) {
      dispatch({ type: 'SET_ERROR', payload: error.message });
    }
  };

  const updatePrivacySettings = async (privacy: Partial<UserSettings['privacy']>): Promise<void> => {
    try {
      dispatch({ type: 'SET_LOADING', payload: true });

      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 500));

      dispatch({ type: 'UPDATE_SETTINGS', payload: { privacy: { ...state.settings.privacy, ...privacy } } });
    } catch (error: any) {
      dispatch({ type: 'SET_ERROR', payload: error.message });
    }
  };

  const updatePreferences = async (preferences: Partial<UserSettings['preferences']>): Promise<void> => {
    try {
      dispatch({ type: 'SET_LOADING', payload: true });

      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 500));

      dispatch({ type: 'UPDATE_SETTINGS', payload: { preferences: { ...state.settings.preferences, ...preferences } } });
    } catch (error: any) {
      dispatch({ type: 'SET_ERROR', payload: error.message });
    }
  };

  // Document actions
  const updateDocumentStatus = async (documentId: string, status: DocumentStatus): Promise<void> => {
    try {
      dispatch({ type: 'SET_LOADING', payload: true });

      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 500));

      dispatch({ type: 'UPDATE_DOCUMENT_STATUS', payload: { documentId, status } });
    } catch (error: any) {
      dispatch({ type: 'SET_ERROR', payload: error.message });
    }
  };

  const contextValue: ProfileContextType = {
    state,

    // Profile actions
    loadProfile,
    updateProfile,
    uploadProfilePicture,

    // Vehicle actions
    loadVehicles,
    addVehicle,
    updateVehicle,
    deleteVehicle,
    setActiveVehicle,
    uploadVehicleDocument,

    // Payment method actions
    loadPaymentMethods,
    addPaymentMethod,
    updatePaymentMethod,
    deletePaymentMethod,
    setDefaultPaymentMethod,

    // Settings actions
    updateSettings,
    updateNotificationSettings,
    updatePrivacySettings,
    updatePreferences,

    // Document actions
    updateDocumentStatus,
  };

  return (
    <ProfileContext.Provider value={contextValue}>
      {children}
    </ProfileContext.Provider>
  );
};

// Custom hook to use profile context
export const useProfile = (): ProfileContextType => {
  const context = useContext(ProfileContext);
  if (context === undefined) {
    throw new Error('useProfile must be used within a ProfileProvider');
  }
  return context;
};

export default ProfileContext;
