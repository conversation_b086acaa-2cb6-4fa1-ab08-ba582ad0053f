import React, { useState } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  Image,
  ScrollView,
  Modal,
  Alert,
  Dimensions,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { LinearGradient } from 'expo-linear-gradient';

interface Photo {
  id: string;
  uri: string;
  type: 'profile' | 'document' | 'order_proof' | 'delivery_proof';
  timestamp: string;
  description?: string;
}

interface PhotoGalleryProps {
  photos: Photo[];
  onAddPhoto: () => void;
  onDeletePhoto: (photoId: string) => void;
  onViewPhoto: (photo: Photo) => void;
  title?: string;
  maxPhotos?: number;
  allowDelete?: boolean;
  gridColumns?: number;
}

const { width: screenWidth } = Dimensions.get('window');

const PhotoGallery: React.FC<PhotoGalleryProps> = ({
  photos,
  onAddPhoto,
  onDeletePhoto,
  onViewPhoto,
  title = 'Photos',
  maxPhotos = 10,
  allowDelete = true,
  gridColumns = 2,
}) => {
  const [selectedPhoto, setSelectedPhoto] = useState<Photo | null>(null);
  const [showFullScreen, setShowFullScreen] = useState(false);

  const itemWidth = (screenWidth - 60 - (gridColumns - 1) * 12) / gridColumns;

  const handlePhotoPress = (photo: Photo) => {
    setSelectedPhoto(photo);
    setShowFullScreen(true);
    onViewPhoto(photo);
  };

  const handleDeletePhoto = (photo: Photo) => {
    Alert.alert(
      'Delete Photo',
      'Are you sure you want to delete this photo?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Delete',
          style: 'destructive',
          onPress: () => onDeletePhoto(photo.id),
        },
      ]
    );
  };

  const getPhotoTypeIcon = (type: Photo['type']) => {
    switch (type) {
      case 'profile': return 'person-circle';
      case 'document': return 'document-text';
      case 'order_proof': return 'restaurant';
      case 'delivery_proof': return 'checkmark-circle';
      default: return 'image';
    }
  };

  const getPhotoTypeColor = (type: Photo['type']) => {
    switch (type) {
      case 'profile': return '#3b82f6';
      case 'document': return '#f59e0b';
      case 'order_proof': return '#10b981';
      case 'delivery_proof': return '#dc2626';
      default: return '#6b7280';
    }
  };

  const renderPhotoItem = (photo: Photo) => (
    <TouchableOpacity
      key={photo.id}
      onPress={() => handlePhotoPress(photo)}
      style={{
        width: itemWidth,
        aspectRatio: 1,
        borderRadius: 16,
        overflow: 'hidden',
        marginBottom: 12,
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 4 },
        shadowOpacity: 0.1,
        shadowRadius: 8,
        elevation: 4,
      }}
    >
      <Image
        source={{ uri: photo.uri }}
        style={{
          width: '100%',
          height: '100%',
        }}
        resizeMode="cover"
      />
      
      {/* Photo Type Badge */}
      <View style={{
        position: 'absolute',
        top: 8,
        left: 8,
        backgroundColor: getPhotoTypeColor(photo.type),
        borderRadius: 12,
        paddingHorizontal: 8,
        paddingVertical: 4,
      }}>
        <Ionicons
          name={getPhotoTypeIcon(photo.type) as any}
          size={12}
          color="white"
        />
      </View>

      {/* Delete Button */}
      {allowDelete && (
        <TouchableOpacity
          onPress={() => handleDeletePhoto(photo)}
          style={{
            position: 'absolute',
            top: 8,
            right: 8,
            backgroundColor: 'rgba(0,0,0,0.6)',
            borderRadius: 12,
            width: 24,
            height: 24,
            alignItems: 'center',
            justifyContent: 'center',
          }}
        >
          <Ionicons name="trash" size={12} color="white" />
        </TouchableOpacity>
      )}

      {/* Timestamp */}
      <LinearGradient
        colors={['transparent', 'rgba(0,0,0,0.6)']}
        style={{
          position: 'absolute',
          bottom: 0,
          left: 0,
          right: 0,
          paddingHorizontal: 8,
          paddingVertical: 6,
        }}
      >
        <Text style={{
          fontSize: 10,
          color: 'white',
          fontWeight: '500',
        }}>
          {new Date(photo.timestamp).toLocaleDateString()}
        </Text>
      </LinearGradient>
    </TouchableOpacity>
  );

  const renderAddPhotoButton = () => (
    <TouchableOpacity
      onPress={onAddPhoto}
      style={{
        width: itemWidth,
        aspectRatio: 1,
        borderRadius: 16,
        backgroundColor: 'white',
        borderWidth: 2,
        borderColor: '#dc2626',
        borderStyle: 'dashed',
        alignItems: 'center',
        justifyContent: 'center',
        marginBottom: 12,
      }}
    >
      <View style={{
        width: 48,
        height: 48,
        backgroundColor: '#dc262620',
        borderRadius: 24,
        alignItems: 'center',
        justifyContent: 'center',
        marginBottom: 8,
      }}>
        <Ionicons name="camera" size={24} color="#dc2626" />
      </View>
      <Text style={{
        fontSize: 12,
        fontWeight: 'bold',
        color: '#dc2626',
        textAlign: 'center',
      }}>
        Add Photo
      </Text>
    </TouchableOpacity>
  );

  const renderFullScreenModal = () => (
    <Modal
      visible={showFullScreen}
      animationType="fade"
      presentationStyle="fullScreen"
      onRequestClose={() => setShowFullScreen(false)}
    >
      <View style={{ flex: 1, backgroundColor: 'black' }}>
        {/* Header */}
        <View style={{
          position: 'absolute',
          top: 0,
          left: 0,
          right: 0,
          zIndex: 1,
          paddingTop: 50,
          paddingHorizontal: 20,
          paddingBottom: 20,
        }}>
          <LinearGradient
            colors={['rgba(0,0,0,0.8)', 'transparent']}
            style={{
              position: 'absolute',
              top: 0,
              left: 0,
              right: 0,
              height: 100,
            }}
          />
          
          <View style={{
            flexDirection: 'row',
            alignItems: 'center',
            justifyContent: 'space-between',
          }}>
            <TouchableOpacity
              onPress={() => setShowFullScreen(false)}
              style={{
                width: 40,
                height: 40,
                borderRadius: 20,
                backgroundColor: 'rgba(255,255,255,0.2)',
                alignItems: 'center',
                justifyContent: 'center',
              }}
            >
              <Ionicons name="close" size={24} color="white" />
            </TouchableOpacity>

            {selectedPhoto && (
              <View style={{
                backgroundColor: 'rgba(255,255,255,0.2)',
                paddingHorizontal: 12,
                paddingVertical: 6,
                borderRadius: 16,
              }}>
                <Text style={{
                  color: 'white',
                  fontSize: 14,
                  fontWeight: '600',
                }}>
                  {new Date(selectedPhoto.timestamp).toLocaleDateString()}
                </Text>
              </View>
            )}

            {allowDelete && selectedPhoto && (
              <TouchableOpacity
                onPress={() => {
                  setShowFullScreen(false);
                  handleDeletePhoto(selectedPhoto);
                }}
                style={{
                  width: 40,
                  height: 40,
                  borderRadius: 20,
                  backgroundColor: 'rgba(220, 38, 38, 0.8)',
                  alignItems: 'center',
                  justifyContent: 'center',
                }}
              >
                <Ionicons name="trash" size={20} color="white" />
              </TouchableOpacity>
            )}
          </View>
        </View>

        {/* Full Screen Image */}
        {selectedPhoto && (
          <Image
            source={{ uri: selectedPhoto.uri }}
            style={{
              flex: 1,
              width: '100%',
              height: '100%',
            }}
            resizeMode="contain"
          />
        )}
      </View>
    </Modal>
  );

  return (
    <View style={{
      backgroundColor: 'white',
      borderRadius: 20,
      padding: 20,
      shadowColor: '#000',
      shadowOffset: { width: 0, height: 8 },
      shadowOpacity: 0.1,
      shadowRadius: 16,
      elevation: 8,
      borderWidth: 1,
      borderColor: 'rgba(0, 0, 0, 0.05)',
    }}>
      {/* Header */}
      <View style={{
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between',
        marginBottom: 16,
      }}>
        <Text style={{
          fontSize: 18,
          fontWeight: 'bold',
          color: '#111827',
        }}>
          📸 {title}
        </Text>
        
        <View style={{
          backgroundColor: '#dc262620',
          paddingHorizontal: 8,
          paddingVertical: 4,
          borderRadius: 12,
        }}>
          <Text style={{
            fontSize: 12,
            fontWeight: 'bold',
            color: '#dc2626',
          }}>
            {photos.length}/{maxPhotos}
          </Text>
        </View>
      </View>

      {/* Photo Grid */}
      <ScrollView
        showsVerticalScrollIndicator={false}
        contentContainerStyle={{
          flexDirection: 'row',
          flexWrap: 'wrap',
          justifyContent: 'space-between',
        }}
      >
        {photos.map(renderPhotoItem)}
        
        {photos.length < maxPhotos && renderAddPhotoButton()}
      </ScrollView>

      {photos.length === 0 && (
        <View style={{
          alignItems: 'center',
          paddingVertical: 40,
        }}>
          <View style={{
            width: 80,
            height: 80,
            backgroundColor: '#f3f4f6',
            borderRadius: 40,
            alignItems: 'center',
            justifyContent: 'center',
            marginBottom: 16,
          }}>
            <Ionicons name="images" size={40} color="#9ca3af" />
          </View>
          <Text style={{
            fontSize: 16,
            fontWeight: '600',
            color: '#6b7280',
            marginBottom: 8,
          }}>
            No photos yet
          </Text>
          <Text style={{
            fontSize: 14,
            color: '#9ca3af',
            textAlign: 'center',
          }}>
            Tap "Add Photo" to get started
          </Text>
        </View>
      )}

      {renderFullScreenModal()}
    </View>
  );
};

export default PhotoGallery;
