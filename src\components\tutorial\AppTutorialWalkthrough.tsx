import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  Modal,
  Animated,
  Dimensions,
  StatusBar,
  BackHandler,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import { LinearGradient } from 'expo-linear-gradient';

const { width: screenWidth, height: screenHeight } = Dimensions.get('window');

interface TutorialStep {
  id: string;
  title: string;
  description: string;
  icon: keyof typeof Ionicons.glyphMap;
  targetElement?: string;
  position?: 'top' | 'bottom' | 'center';
  action?: 'tap' | 'swipe' | 'scroll' | 'none';
  highlight?: {
    x: number;
    y: number;
    width: number;
    height: number;
  };
}

interface AppTutorialWalkthroughProps {
  isVisible: boolean;
  onClose: () => void;
  onComplete: () => void;
  steps: TutorialStep[];
  showSkip?: boolean;
  autoAdvance?: boolean;
  autoAdvanceDelay?: number;
}

const AppTutorialWalkthrough: React.FC<AppTutorialWalkthroughProps> = ({
  isVisible,
  onClose,
  onComplete,
  steps,
  showSkip = true,
  autoAdvance = false,
  autoAdvanceDelay = 3000,
}) => {
  const [currentStep, setCurrentStep] = useState(0);
  const [fadeAnimation] = useState(new Animated.Value(0));
  const [slideAnimation] = useState(new Animated.Value(50));
  const [pulseAnimation] = useState(new Animated.Value(1));
  const [highlightAnimation] = useState(new Animated.Value(0));
  
  const autoAdvanceTimer = useRef<NodeJS.Timeout | null>(null);

  useEffect(() => {
    if (isVisible) {
      startAnimations();
      
      // Handle back button on Android
      const backHandler = BackHandler.addEventListener('hardwareBackPress', () => {
        if (showSkip) {
          handleSkip();
          return true;
        }
        return false;
      });

      return () => backHandler.remove();
    }
  }, [isVisible]);

  useEffect(() => {
    if (isVisible && autoAdvance) {
      startAutoAdvanceTimer();
    }

    return () => {
      if (autoAdvanceTimer.current) {
        clearTimeout(autoAdvanceTimer.current);
      }
    };
  }, [currentStep, isVisible, autoAdvance]);

  const startAnimations = () => {
    Animated.parallel([
      Animated.timing(fadeAnimation, {
        toValue: 1,
        duration: 500,
        useNativeDriver: true,
      }),
      Animated.timing(slideAnimation, {
        toValue: 0,
        duration: 500,
        useNativeDriver: true,
      }),
      Animated.timing(highlightAnimation, {
        toValue: 1,
        duration: 800,
        useNativeDriver: true,
      }),
    ]).start();

    // Start pulse animation for highlights
    Animated.loop(
      Animated.sequence([
        Animated.timing(pulseAnimation, {
          toValue: 1.1,
          duration: 1000,
          useNativeDriver: true,
        }),
        Animated.timing(pulseAnimation, {
          toValue: 1,
          duration: 1000,
          useNativeDriver: true,
        }),
      ])
    ).start();
  };

  const startAutoAdvanceTimer = () => {
    if (autoAdvanceTimer.current) {
      clearTimeout(autoAdvanceTimer.current);
    }

    autoAdvanceTimer.current = setTimeout(() => {
      handleNext();
    }, autoAdvanceDelay);
  };

  const handleNext = () => {
    if (currentStep < steps.length - 1) {
      setCurrentStep(currentStep + 1);
      
      // Reset animations for next step
      slideAnimation.setValue(50);
      Animated.timing(slideAnimation, {
        toValue: 0,
        duration: 300,
        useNativeDriver: true,
      }).start();
    } else {
      handleComplete();
    }
  };

  const handlePrevious = () => {
    if (currentStep > 0) {
      setCurrentStep(currentStep - 1);
      
      // Reset animations for previous step
      slideAnimation.setValue(-50);
      Animated.timing(slideAnimation, {
        toValue: 0,
        duration: 300,
        useNativeDriver: true,
      }).start();
    }
  };

  const handleSkip = () => {
    onClose();
  };

  const handleComplete = () => {
    onComplete();
    onClose();
  };

  const currentStepData = steps[currentStep];
  const progress = ((currentStep + 1) / steps.length) * 100;

  const renderHighlight = () => {
    if (!currentStepData?.highlight) return null;

    const { x, y, width, height } = currentStepData.highlight;

    return (
      <Animated.View
        style={{
          position: 'absolute',
          left: x - 10,
          top: y - 10,
          width: width + 20,
          height: height + 20,
          borderRadius: 12,
          borderWidth: 3,
          borderColor: '#dc2626',
          backgroundColor: 'transparent',
          opacity: highlightAnimation,
          transform: [{ scale: pulseAnimation }],
        }}
      />
    );
  };

  const renderTooltip = () => {
    if (!currentStepData) return null;

    const isBottom = currentStepData.position === 'bottom';
    const isTop = currentStepData.position === 'top';
    const isCenter = currentStepData.position === 'center';

    let tooltipStyle: any = {
      position: 'absolute',
      left: 20,
      right: 20,
      backgroundColor: 'white',
      borderRadius: 20,
      padding: 24,
      shadowColor: '#dc2626',
      shadowOffset: { width: 0, height: 8 },
      shadowOpacity: 0.2,
      shadowRadius: 16,
      elevation: 16,
      borderWidth: 1,
      borderColor: 'rgba(220, 38, 38, 0.1)',
    };

    if (isBottom) {
      tooltipStyle.bottom = 120;
    } else if (isTop) {
      tooltipStyle.top = 120;
    } else {
      tooltipStyle.top = '50%';
      tooltipStyle.marginTop = -100;
    }

    return (
      <Animated.View
        style={[
          tooltipStyle,
          {
            opacity: fadeAnimation,
            transform: [{ translateY: slideAnimation }],
          },
        ]}
      >
        {/* Icon */}
        <View style={{
          width: 64,
          height: 64,
          backgroundColor: '#dc262620',
          borderRadius: 32,
          alignItems: 'center',
          justifyContent: 'center',
          alignSelf: 'center',
          marginBottom: 16,
          borderWidth: 2,
          borderColor: '#dc2626',
        }}>
          <Ionicons name={currentStepData.icon} size={32} color="#dc2626" />
        </View>

        {/* Title */}
        <Text style={{
          fontSize: 20,
          fontWeight: 'bold',
          color: '#111827',
          textAlign: 'center',
          marginBottom: 8,
        }}>
          {currentStepData.title}
        </Text>

        {/* Description */}
        <Text style={{
          fontSize: 16,
          color: '#6b7280',
          textAlign: 'center',
          lineHeight: 24,
          marginBottom: 24,
        }}>
          {currentStepData.description}
        </Text>

        {/* Action Hint */}
        {currentStepData.action && currentStepData.action !== 'none' && (
          <View style={{
            backgroundColor: '#f8fafc',
            borderRadius: 12,
            padding: 12,
            marginBottom: 20,
            borderWidth: 1,
            borderColor: '#e2e8f0',
          }}>
            <Text style={{
              fontSize: 14,
              color: '#374151',
              textAlign: 'center',
              fontWeight: '500',
            }}>
              {currentStepData.action === 'tap' && '👆 Tap to continue'}
              {currentStepData.action === 'swipe' && '👈 Swipe to navigate'}
              {currentStepData.action === 'scroll' && '📜 Scroll to explore'}
            </Text>
          </View>
        )}

        {/* Progress Bar */}
        <View style={{
          backgroundColor: '#f3f4f6',
          height: 6,
          borderRadius: 3,
          marginBottom: 20,
        }}>
          <Animated.View style={{
            backgroundColor: '#dc2626',
            height: '100%',
            borderRadius: 3,
            width: `${progress}%`,
          }} />
        </View>

        {/* Step Counter */}
        <Text style={{
          fontSize: 12,
          color: '#9ca3af',
          textAlign: 'center',
          marginBottom: 20,
        }}>
          Step {currentStep + 1} of {steps.length}
        </Text>

        {/* Navigation Buttons */}
        <View style={{
          flexDirection: 'row',
          justifyContent: 'space-between',
          alignItems: 'center',
        }}>
          {/* Previous Button */}
          <TouchableOpacity
            onPress={handlePrevious}
            disabled={currentStep === 0}
            style={{
              paddingHorizontal: 16,
              paddingVertical: 8,
              borderRadius: 12,
              backgroundColor: currentStep === 0 ? '#f3f4f6' : '#e5e7eb',
              opacity: currentStep === 0 ? 0.5 : 1,
            }}
          >
            <Text style={{
              fontSize: 14,
              fontWeight: '600',
              color: currentStep === 0 ? '#9ca3af' : '#374151',
            }}>
              Previous
            </Text>
          </TouchableOpacity>

          {/* Skip Button */}
          {showSkip && (
            <TouchableOpacity
              onPress={handleSkip}
              style={{
                paddingHorizontal: 16,
                paddingVertical: 8,
                borderRadius: 12,
              }}
            >
              <Text style={{
                fontSize: 14,
                fontWeight: '600',
                color: '#6b7280',
              }}>
                Skip Tutorial
              </Text>
            </TouchableOpacity>
          )}

          {/* Next/Complete Button */}
          <TouchableOpacity
            onPress={handleNext}
            style={{
              backgroundColor: '#dc2626',
              paddingHorizontal: 20,
              paddingVertical: 12,
              borderRadius: 12,
              shadowColor: '#dc2626',
              shadowOffset: { width: 0, height: 4 },
              shadowOpacity: 0.3,
              shadowRadius: 8,
              elevation: 6,
            }}
          >
            <Text style={{
              fontSize: 14,
              fontWeight: 'bold',
              color: 'white',
            }}>
              {currentStep === steps.length - 1 ? 'Complete' : 'Next'}
            </Text>
          </TouchableOpacity>
        </View>
      </Animated.View>
    );
  };

  if (!isVisible) return null;

  return (
    <Modal
      visible={isVisible}
      transparent
      animationType="none"
      onRequestClose={showSkip ? handleSkip : undefined}
    >
      <StatusBar barStyle="light-content" backgroundColor="rgba(0,0,0,0.8)" />
      
      {/* Overlay */}
      <View style={{
        flex: 1,
        backgroundColor: 'rgba(0, 0, 0, 0.8)',
      }}>
        <SafeAreaView style={{ flex: 1 }}>
          {/* Highlight */}
          {renderHighlight()}
          
          {/* Tooltip */}
          {renderTooltip()}
        </SafeAreaView>
      </View>
    </Modal>
  );
};

export default AppTutorialWalkthrough;

// Predefined tutorial steps for different app sections
export const DashboardTutorialSteps: TutorialStep[] = [
  {
    id: 'welcome',
    title: 'Welcome to FoodWay Rider! 🚴‍♂️',
    description: 'Let\'s take a quick tour of your rider dashboard and show you how to start earning money!',
    icon: 'home',
    position: 'center',
    action: 'none',
  },
  {
    id: 'online-toggle',
    title: 'Go Online to Start Earning',
    description: 'Toggle this switch to go online and start receiving order requests. You\'ll only get orders when you\'re online!',
    icon: 'power',
    position: 'top',
    action: 'tap',
    highlight: { x: 20, y: 120, width: screenWidth - 40, height: 80 },
  },
  {
    id: 'earnings-card',
    title: 'Track Your Earnings 💰',
    description: 'See your daily, weekly, and monthly earnings here. Tap to view detailed breakdowns and payment history.',
    icon: 'cash',
    position: 'top',
    action: 'tap',
    highlight: { x: 20, y: 220, width: screenWidth - 40, height: 120 },
  },
  {
    id: 'active-orders',
    title: 'Your Active Orders',
    description: 'All your current orders appear here. Tap on any order to view details, navigate, or update status.',
    icon: 'list',
    position: 'bottom',
    action: 'tap',
    highlight: { x: 20, y: 360, width: screenWidth - 40, height: 200 },
  },
  {
    id: 'bottom-navigation',
    title: 'Navigate the App',
    description: 'Use the bottom navigation to access orders, earnings, wallet, and your profile. Everything you need is just a tap away!',
    icon: 'apps',
    position: 'top',
    action: 'tap',
    highlight: { x: 0, y: screenHeight - 100, width: screenWidth, height: 80 },
  },
];

export const OrderFlowTutorialSteps: TutorialStep[] = [
  {
    id: 'order-request',
    title: 'New Order Request! 🔔',
    description: 'When you receive an order request, you\'ll see all the important details like pickup location, delivery address, and earnings.',
    icon: 'notifications',
    position: 'center',
    action: 'none',
  },
  {
    id: 'accept-order',
    title: 'Accept or Decline',
    description: 'You have 30 seconds to accept or decline each order. Choose wisely based on distance and earnings!',
    icon: 'checkmark-circle',
    position: 'bottom',
    action: 'tap',
  },
  {
    id: 'navigate-pickup',
    title: 'Navigate to Restaurant',
    description: 'Once accepted, use the built-in navigation to reach the restaurant. The app will guide you with turn-by-turn directions.',
    icon: 'navigate',
    position: 'center',
    action: 'tap',
  },
  {
    id: 'pickup-confirmation',
    title: 'Confirm Pickup',
    description: 'At the restaurant, confirm pickup and take a photo of the food for quality assurance.',
    icon: 'camera',
    position: 'center',
    action: 'tap',
  },
  {
    id: 'delivery-completion',
    title: 'Complete Delivery',
    description: 'After delivering to the customer, take a photo as proof of delivery and collect your payment!',
    icon: 'checkmark-done',
    position: 'center',
    action: 'tap',
  },
];

export const WalletTutorialSteps: TutorialStep[] = [
  {
    id: 'wallet-overview',
    title: 'Your Digital Wallet 💳',
    description: 'Manage all your earnings, payments, and financial information in one secure place.',
    icon: 'wallet',
    position: 'center',
    action: 'none',
  },
  {
    id: 'balance-card',
    title: 'Current Balance',
    description: 'See your available balance and pending payments. Money from completed orders appears here instantly!',
    icon: 'cash',
    position: 'top',
    action: 'none',
  },
  {
    id: 'withdraw-money',
    title: 'Withdraw Your Earnings',
    description: 'Tap here to withdraw money to your bank account. Withdrawals are processed within 24 hours.',
    icon: 'card',
    position: 'center',
    action: 'tap',
  },
  {
    id: 'transaction-history',
    title: 'Transaction History',
    description: 'View all your earnings, withdrawals, and bonuses. Keep track of every rupee you\'ve earned!',
    icon: 'list-circle',
    position: 'bottom',
    action: 'scroll',
  },
];

// Hook for managing tutorial state
export const useTutorial = () => {
  const [activeTutorial, setActiveTutorial] = useState<string | null>(null);
  const [completedTutorials, setCompletedTutorials] = useState<string[]>([]);

  const startTutorial = (tutorialId: string) => {
    setActiveTutorial(tutorialId);
  };

  const completeTutorial = (tutorialId: string) => {
    setCompletedTutorials(prev => [...prev, tutorialId]);
    setActiveTutorial(null);
  };

  const isTutorialCompleted = (tutorialId: string) => {
    return completedTutorials.includes(tutorialId);
  };

  const closeTutorial = () => {
    setActiveTutorial(null);
  };

  return {
    activeTutorial,
    completedTutorials,
    startTutorial,
    completeTutorial,
    isTutorialCompleted,
    closeTutorial,
  };
};
