import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  Animated,
  Vibration,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import Modal from './Modal';

interface ConfirmationModalProps {
  isVisible: boolean;
  onClose: () => void;
  onConfirm: () => void;
  title: string;
  message: string;
  confirmText?: string;
  cancelText?: string;
  variant?: 'default' | 'danger' | 'warning' | 'success';
  icon?: keyof typeof Ionicons.glyphMap;
  showIcon?: boolean;
  destructive?: boolean;
  loading?: boolean;
}

const ConfirmationModal: React.FC<ConfirmationModalProps> = ({
  isVisible,
  onClose,
  onConfirm,
  title,
  message,
  confirmText = 'Confirm',
  cancelText = 'Cancel',
  variant = 'default',
  icon,
  showIcon = true,
  destructive = false,
  loading = false,
}) => {
  const [scaleAnimation] = useState(new Animated.Value(0.8));
  const [fadeAnimation] = useState(new Animated.Value(0));

  useEffect(() => {
    if (isVisible) {
      // Vibrate on show for important confirmations
      if (destructive) {
        Vibration.vibrate(100);
      }

      Animated.parallel([
        Animated.timing(fadeAnimation, {
          toValue: 1,
          duration: 300,
          useNativeDriver: true,
        }),
        Animated.spring(scaleAnimation, {
          toValue: 1,
          tension: 50,
          friction: 8,
          useNativeDriver: true,
        }),
      ]).start();
    } else {
      scaleAnimation.setValue(0.8);
      fadeAnimation.setValue(0);
    }
  }, [isVisible]);

  const getVariantConfig = () => {
    switch (variant) {
      case 'danger':
        return {
          iconColor: '#dc2626',
          iconBg: '#fee2e2',
          confirmBg: '#dc2626',
          confirmTextColor: 'white',
          defaultIcon: 'warning' as keyof typeof Ionicons.glyphMap,
        };
      case 'warning':
        return {
          iconColor: '#f59e0b',
          iconBg: '#fef3c7',
          confirmBg: '#f59e0b',
          confirmTextColor: 'white',
          defaultIcon: 'alert-circle' as keyof typeof Ionicons.glyphMap,
        };
      case 'success':
        return {
          iconColor: '#10b981',
          iconBg: '#d1fae5',
          confirmBg: '#10b981',
          confirmTextColor: 'white',
          defaultIcon: 'checkmark-circle' as keyof typeof Ionicons.glyphMap,
        };
      default:
        return {
          iconColor: '#3b82f6',
          iconBg: '#dbeafe',
          confirmBg: '#3b82f6',
          confirmTextColor: 'white',
          defaultIcon: 'information-circle' as keyof typeof Ionicons.glyphMap,
        };
    }
  };

  const config = getVariantConfig();
  const displayIcon = icon || config.defaultIcon;

  const handleConfirm = () => {
    if (!loading) {
      onConfirm();
    }
  };

  return (
    <Modal
      isVisible={isVisible}
      onClose={onClose}
      variant="center"
      size="sm"
      showCloseButton={false}
      closeOnBackdrop={!loading}
    >
      <Animated.View style={{
        opacity: fadeAnimation,
        transform: [{ scale: scaleAnimation }],
        padding: 32,
        alignItems: 'center',
      }}>
        {/* Icon */}
        {showIcon && (
          <View style={{
            width: 80,
            height: 80,
            borderRadius: 40,
            backgroundColor: config.iconBg,
            alignItems: 'center',
            justifyContent: 'center',
            marginBottom: 24,
            borderWidth: 3,
            borderColor: config.iconColor,
            shadowColor: config.iconColor,
            shadowOffset: { width: 0, height: 4 },
            shadowOpacity: 0.2,
            shadowRadius: 8,
            elevation: 6,
          }}>
            <Ionicons 
              name={displayIcon} 
              size={40} 
              color={config.iconColor} 
            />
          </View>
        )}

        {/* Title */}
        <Text style={{
          fontSize: 22,
          fontWeight: 'bold',
          color: '#111827',
          textAlign: 'center',
          marginBottom: 12,
        }}>
          {title}
        </Text>

        {/* Message */}
        <Text style={{
          fontSize: 16,
          color: '#6b7280',
          textAlign: 'center',
          lineHeight: 24,
          marginBottom: 32,
        }}>
          {message}
        </Text>

        {/* Buttons */}
        <View style={{
          flexDirection: 'row',
          width: '100%',
          gap: 12,
        }}>
          {/* Cancel Button */}
          <TouchableOpacity
            onPress={onClose}
            disabled={loading}
            style={{
              flex: 1,
              backgroundColor: 'transparent',
              borderWidth: 2,
              borderColor: '#e5e7eb',
              borderRadius: 16,
              paddingVertical: 16,
              paddingHorizontal: 24,
              alignItems: 'center',
              opacity: loading ? 0.5 : 1,
            }}
          >
            <Text style={{
              fontSize: 16,
              fontWeight: 'bold',
              color: '#6b7280',
            }}>
              {cancelText}
            </Text>
          </TouchableOpacity>

          {/* Confirm Button */}
          <TouchableOpacity
            onPress={handleConfirm}
            disabled={loading}
            style={{
              flex: 1,
              backgroundColor: config.confirmBg,
              borderRadius: 16,
              paddingVertical: 16,
              paddingHorizontal: 24,
              alignItems: 'center',
              flexDirection: 'row',
              justifyContent: 'center',
              opacity: loading ? 0.7 : 1,
              shadowColor: config.confirmBg,
              shadowOffset: { width: 0, height: 4 },
              shadowOpacity: 0.3,
              shadowRadius: 8,
              elevation: 6,
            }}
          >
            {loading && (
              <View style={{
                width: 20,
                height: 20,
                borderRadius: 10,
                borderWidth: 2,
                borderColor: 'rgba(255,255,255,0.3)',
                borderTopColor: 'white',
                marginRight: 8,
              }} />
            )}
            <Text style={{
              fontSize: 16,
              fontWeight: 'bold',
              color: config.confirmTextColor,
            }}>
              {loading ? 'Processing...' : confirmText}
            </Text>
          </TouchableOpacity>
        </View>
      </Animated.View>
    </Modal>
  );
};

export default ConfirmationModal;

// Export specialized confirmation modals
export const DeleteConfirmationModal: React.FC<Omit<ConfirmationModalProps, 'variant' | 'icon' | 'destructive'>> = (props) => (
  <ConfirmationModal
    {...props}
    variant="danger"
    icon="trash"
    destructive={true}
    confirmText={props.confirmText || 'Delete'}
  />
);

export const LogoutConfirmationModal: React.FC<Omit<ConfirmationModalProps, 'variant' | 'icon'>> = (props) => (
  <ConfirmationModal
    {...props}
    variant="warning"
    icon="log-out"
    confirmText={props.confirmText || 'Logout'}
  />
);

export const CancelOrderModal: React.FC<Omit<ConfirmationModalProps, 'variant' | 'icon' | 'destructive'>> = (props) => (
  <ConfirmationModal
    {...props}
    variant="danger"
    icon="close-circle"
    destructive={true}
    confirmText={props.confirmText || 'Cancel Order'}
  />
);

export const CompleteOrderModal: React.FC<Omit<ConfirmationModalProps, 'variant' | 'icon'>> = (props) => (
  <ConfirmationModal
    {...props}
    variant="success"
    icon="checkmark-circle"
    confirmText={props.confirmText || 'Complete Order'}
  />
);
