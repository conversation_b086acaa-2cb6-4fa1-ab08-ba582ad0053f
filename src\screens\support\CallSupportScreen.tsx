import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  ScrollView,
  Alert,
  Linking,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import { useNavigation } from '@react-navigation/native';
import { SupportContact } from '../../types/support';

const CallSupportScreen: React.FC = () => {
  const navigation = useNavigation();
  
  // State
  const [supportContacts, setSupportContacts] = useState<SupportContact[]>([]);
  const [currentTime, setCurrentTime] = useState(new Date());

  // Mock support contacts
  useEffect(() => {
    const mockContacts: SupportContact[] = [
      {
        id: '1',
        type: 'phone',
        label: 'General Support',
        value: '+92-21-111-FOOD (3663)',
        isEmergency: false,
        isActive: true,
        availableHours: {
          start: '09:00',
          end: '21:00',
          timezone: 'PKT',
        },
        description: 'For general inquiries, account issues, and non-urgent matters',
      },
      {
        id: '2',
        type: 'phone',
        label: 'Emergency Support',
        value: '+92-21-111-HELP (4357)',
        isEmergency: true,
        isActive: true,
        availableHours: {
          start: '00:00',
          end: '23:59',
          timezone: 'PKT',
        },
        description: 'For urgent safety concerns, accidents, or emergency situations',
      },
      {
        id: '3',
        type: 'whatsapp',
        label: 'WhatsApp Support',
        value: '+92-300-1234567',
        isEmergency: false,
        isActive: true,
        availableHours: {
          start: '09:00',
          end: '21:00',
          timezone: 'PKT',
        },
        description: 'Quick support via WhatsApp for common issues',
      },
      {
        id: '4',
        type: 'phone',
        label: 'Payment Support',
        value: '+92-21-111-PAY (729)',
        isEmergency: false,
        isActive: true,
        availableHours: {
          start: '09:00',
          end: '18:00',
          timezone: 'PKT',
        },
        description: 'For payment-related issues, earnings, and wallet problems',
      },
      {
        id: '5',
        type: 'email',
        label: 'Email Support',
        value: '<EMAIL>',
        isEmergency: false,
        isActive: true,
        availableHours: {
          start: '09:00',
          end: '18:00',
          timezone: 'PKT',
        },
        description: 'For detailed inquiries and document-related issues',
      },
    ];

    setSupportContacts(mockContacts);
  }, []);

  // Update current time every minute
  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentTime(new Date());
    }, 60000);

    return () => clearInterval(timer);
  }, []);

  const isWithinBusinessHours = (contact: SupportContact): boolean => {
    const now = new Date();
    const currentHour = now.getHours();
    const currentMinute = now.getMinutes();
    const currentTimeInMinutes = currentHour * 60 + currentMinute;

    const [startHour, startMinute] = contact.availableHours.start.split(':').map(Number);
    const [endHour, endMinute] = contact.availableHours.end.split(':').map(Number);
    
    const startTimeInMinutes = startHour * 60 + startMinute;
    const endTimeInMinutes = endHour * 60 + endMinute;

    if (contact.isEmergency) return true; // Emergency lines are always available
    
    return currentTimeInMinutes >= startTimeInMinutes && currentTimeInMinutes <= endTimeInMinutes;
  };

  const makeCall = async (contact: SupportContact) => {
    try {
      let url = '';
      
      if (contact.type === 'phone') {
        url = `tel:${contact.value}`;
      } else if (contact.type === 'whatsapp') {
        url = `whatsapp://send?phone=${contact.value.replace(/[^0-9]/g, '')}`;
      } else if (contact.type === 'email') {
        url = `mailto:${contact.value}?subject=Rider Support Request`;
      }

      const canOpen = await Linking.canOpenURL(url);
      
      if (canOpen) {
        await Linking.openURL(url);
      } else {
        if (contact.type === 'whatsapp') {
          Alert.alert(
            'WhatsApp Not Available',
            'WhatsApp is not installed on your device. Please install WhatsApp or use phone support.',
            [
              { text: 'OK' },
              {
                text: 'Call Instead',
                onPress: () => makeCall({
                  ...contact,
                  type: 'phone',
                  value: contact.value,
                }),
              },
            ]
          );
        } else {
          Alert.alert('Error', 'Unable to open the contact method.');
        }
      }
    } catch (error) {
      Alert.alert('Error', 'Unable to make the call. Please try again.');
    }
  };

  const requestCallback = () => {
    Alert.alert(
      'Request Callback',
      'A support agent will call you back within 30 minutes during business hours.',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Request',
          onPress: () => {
            Alert.alert('Callback Requested', 'We will call you back soon!');
          },
        },
      ]
    );
  };

  const renderContactCard = (contact: SupportContact) => {
    const isAvailable = isWithinBusinessHours(contact);
    
    return (
      <View
        key={contact.id}
        style={{
          backgroundColor: 'white',
          marginHorizontal: 16,
          marginVertical: 8,
          borderRadius: 12,
          borderWidth: 1,
          borderColor: contact.isEmergency ? '#ef4444' : '#e5e7eb',
          overflow: 'hidden',
        }}
      >
        {contact.isEmergency && (
          <View style={{
            backgroundColor: '#ef4444',
            paddingVertical: 4,
            paddingHorizontal: 12,
          }}>
            <Text style={{
              color: 'white',
              fontSize: 12,
              fontWeight: '600',
              textAlign: 'center',
            }}>
              EMERGENCY SUPPORT - 24/7 AVAILABLE
            </Text>
          </View>
        )}
        
        <View style={{ padding: 16 }}>
          <View style={{
            flexDirection: 'row',
            alignItems: 'center',
            marginBottom: 8,
          }}>
            <View style={{
              width: 40,
              height: 40,
              backgroundColor: contact.isEmergency ? '#fef2f2' : '#f3f4f6',
              borderRadius: 20,
              alignItems: 'center',
              justifyContent: 'center',
              marginRight: 12,
            }}>
              <Ionicons
                name={
                  contact.type === 'phone' ? 'call' :
                  contact.type === 'whatsapp' ? 'logo-whatsapp' :
                  'mail'
                }
                size={20}
                color={contact.isEmergency ? '#ef4444' : '#3b82f6'}
              />
            </View>
            
            <View style={{ flex: 1 }}>
              <Text style={{
                fontSize: 16,
                fontWeight: '600',
                color: '#1f2937',
              }}>
                {contact.label}
              </Text>
              <Text style={{
                fontSize: 14,
                color: '#3b82f6',
                fontWeight: '500',
              }}>
                {contact.value}
              </Text>
            </View>
            
            <View style={{
              flexDirection: 'row',
              alignItems: 'center',
            }}>
              <View style={{
                width: 8,
                height: 8,
                backgroundColor: isAvailable ? '#10b981' : '#ef4444',
                borderRadius: 4,
                marginRight: 6,
              }} />
              <Text style={{
                fontSize: 12,
                color: isAvailable ? '#10b981' : '#ef4444',
                fontWeight: '500',
              }}>
                {isAvailable ? 'Available' : 'Offline'}
              </Text>
            </View>
          </View>
          
          <Text style={{
            fontSize: 14,
            color: '#6b7280',
            marginBottom: 12,
            lineHeight: 20,
          }}>
            {contact.description}
          </Text>
          
          <View style={{
            flexDirection: 'row',
            alignItems: 'center',
            marginBottom: 16,
          }}>
            <Ionicons name="time-outline" size={16} color="#6b7280" />
            <Text style={{
              fontSize: 12,
              color: '#6b7280',
              marginLeft: 6,
            }}>
              Available: {contact.availableHours.start} - {contact.availableHours.end} {contact.availableHours.timezone}
            </Text>
          </View>
          
          <TouchableOpacity
            onPress={() => makeCall(contact)}
            style={{
              backgroundColor: contact.isEmergency ? '#ef4444' : '#3b82f6',
              borderRadius: 8,
              paddingVertical: 12,
              alignItems: 'center',
              opacity: isAvailable ? 1 : 0.6,
            }}
          >
            <Text style={{
              color: 'white',
              fontSize: 16,
              fontWeight: '600',
            }}>
              {contact.type === 'phone' ? 'Call Now' :
               contact.type === 'whatsapp' ? 'Open WhatsApp' :
               'Send Email'}
            </Text>
          </TouchableOpacity>
          
          {!isAvailable && !contact.isEmergency && (
            <Text style={{
              fontSize: 12,
              color: '#ef4444',
              textAlign: 'center',
              marginTop: 8,
            }}>
              Currently outside business hours
            </Text>
          )}
        </View>
      </View>
    );
  };

  return (
    <SafeAreaView style={{ flex: 1, backgroundColor: '#f9fafb' }}>
      {/* Header */}
      <View style={{
        flexDirection: 'row',
        alignItems: 'center',
        paddingHorizontal: 16,
        paddingVertical: 12,
        backgroundColor: 'white',
        borderBottomWidth: 1,
        borderBottomColor: '#e5e7eb',
      }}>
        <TouchableOpacity
          onPress={() => navigation.goBack()}
          style={{
            width: 40,
            height: 40,
            alignItems: 'center',
            justifyContent: 'center',
            marginRight: 8,
          }}
        >
          <Ionicons name="arrow-back" size={24} color="#374151" />
        </TouchableOpacity>
        
        <Text style={{
          fontSize: 18,
          fontWeight: '600',
          color: '#1f2937',
          flex: 1,
        }}>
          Call Support
        </Text>
        
        <Text style={{
          fontSize: 12,
          color: '#6b7280',
        }}>
          {currentTime.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
        </Text>
      </View>

      <ScrollView style={{ flex: 1 }}>
        {/* Current Time Info */}
        <View style={{
          backgroundColor: '#f0f9ff',
          marginHorizontal: 16,
          marginTop: 16,
          borderRadius: 12,
          padding: 16,
          borderWidth: 1,
          borderColor: '#bae6fd',
        }}>
          <View style={{
            flexDirection: 'row',
            alignItems: 'center',
            marginBottom: 8,
          }}>
            <Ionicons name="information-circle" size={20} color="#0284c7" />
            <Text style={{
              fontSize: 14,
              fontWeight: '600',
              color: '#0284c7',
              marginLeft: 8,
            }}>
              Current Time: {currentTime.toLocaleString()}
            </Text>
          </View>
          <Text style={{
            fontSize: 12,
            color: '#0369a1',
            lineHeight: 16,
          }}>
            Support availability is shown for each contact method below. Emergency support is available 24/7.
          </Text>
        </View>

        {/* Support Contacts */}
        <View style={{ paddingVertical: 16 }}>
          {supportContacts.map(contact => renderContactCard(contact))}
        </View>

        {/* Callback Request */}
        <View style={{
          backgroundColor: 'white',
          marginHorizontal: 16,
          marginBottom: 16,
          borderRadius: 12,
          padding: 16,
          borderWidth: 1,
          borderColor: '#e5e7eb',
        }}>
          <Text style={{
            fontSize: 16,
            fontWeight: '600',
            color: '#1f2937',
            marginBottom: 8,
          }}>
            Request a Callback
          </Text>
          <Text style={{
            fontSize: 14,
            color: '#6b7280',
            marginBottom: 16,
            lineHeight: 20,
          }}>
            Can't call right now? Request a callback and our support team will call you back within 30 minutes during business hours.
          </Text>
          
          <TouchableOpacity
            onPress={requestCallback}
            style={{
              backgroundColor: '#f3f4f6',
              borderRadius: 8,
              paddingVertical: 12,
              alignItems: 'center',
              borderWidth: 1,
              borderColor: '#d1d5db',
            }}
          >
            <Text style={{
              color: '#374151',
              fontSize: 16,
              fontWeight: '600',
            }}>
              Request Callback
            </Text>
          </TouchableOpacity>
        </View>

        {/* Additional Info */}
        <View style={{
          backgroundColor: '#fffbeb',
          marginHorizontal: 16,
          marginBottom: 32,
          borderRadius: 12,
          padding: 16,
          borderWidth: 1,
          borderColor: '#fed7aa',
        }}>
          <View style={{
            flexDirection: 'row',
            alignItems: 'center',
            marginBottom: 8,
          }}>
            <Ionicons name="warning" size={20} color="#d97706" />
            <Text style={{
              fontSize: 14,
              fontWeight: '600',
              color: '#d97706',
              marginLeft: 8,
            }}>
              Important Information
            </Text>
          </View>
          <Text style={{
            fontSize: 12,
            color: '#92400e',
            lineHeight: 16,
          }}>
            • For emergencies or safety concerns, use Emergency Support (24/7){'\n'}
            • Have your rider ID ready when calling{'\n'}
            • For order-specific issues, have the order ID available{'\n'}
            • Standard call rates may apply
          </Text>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

export default CallSupportScreen;
