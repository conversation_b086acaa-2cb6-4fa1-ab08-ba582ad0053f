import React, { useState } from 'react';
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  Alert,
  Switch,
  Modal,
  StatusBar,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import { useNavigation } from '@react-navigation/native';
import { PrivacySetting } from '../../types/profile';

interface PrivacySettings {
  shareLocation: PrivacySetting;
  shareProfile: PrivacySetting;
  shareEarnings: PrivacySetting;
  shareRating: PrivacySetting;
}

interface SecuritySettings {
  twoFactorAuth: boolean;
  biometricLogin: boolean;
  loginNotifications: boolean;
  dataEncryption: boolean;
}

const PrivacySecurityScreen: React.FC = () => {
  const navigation = useNavigation();
  const [privacySettings, setPrivacySettings] = useState<PrivacySettings>({
    shareLocation: PrivacySetting.WHILE_WORKING,
    shareProfile: PrivacySetting.PRIVATE,
    shareEarnings: PrivacySetting.PRIVATE,
    shareRating: PrivacySetting.PUBLIC,
  });

  const [securitySettings, setSecuritySettings] = useState<SecuritySettings>({
    twoFactorAuth: false,
    biometricLogin: true,
    loginNotifications: true,
    dataEncryption: true,
  });

  const [showPrivacyModal, setShowPrivacyModal] = useState(false);
  const [selectedPrivacySetting, setSelectedPrivacySetting] = useState<keyof PrivacySettings>('shareLocation');

  const privacyOptions = [
    { value: PrivacySetting.PUBLIC, label: 'Public', description: 'Visible to everyone', icon: 'globe' },
    { value: PrivacySetting.PRIVATE, label: 'Private', description: 'Only visible to you', icon: 'lock-closed' },
    { value: PrivacySetting.WHILE_WORKING, label: 'While Working', description: 'Visible only when online', icon: 'time' },
  ];

  const handlePrivacyChange = (setting: keyof PrivacySettings, value: PrivacySetting) => {
    setPrivacySettings(prev => ({ ...prev, [setting]: value }));
    setShowPrivacyModal(false);
    Alert.alert('Privacy Updated', 'Privacy setting has been updated successfully!');
  };

  const handleSecurityToggle = (setting: keyof SecuritySettings) => {
    setSecuritySettings(prev => ({ ...prev, [setting]: !prev[setting] }));
  };

  const getPrivacyLabel = (value: PrivacySetting) => {
    return privacyOptions.find(option => option.value === value)?.label || 'Unknown';
  };

  const renderPrivacyItem = (
    key: keyof PrivacySettings,
    title: string,
    description: string,
    icon: string
  ) => (
    <TouchableOpacity
      key={key}
      onPress={() => {
        setSelectedPrivacySetting(key);
        setShowPrivacyModal(true);
      }}
      style={{
        flexDirection: 'row',
        alignItems: 'center',
        paddingVertical: 16,
        paddingHorizontal: 20,
        borderBottomWidth: 1,
        borderBottomColor: '#f3f4f6',
      }}
    >
      <View style={{
        width: 48,
        height: 48,
        backgroundColor: '#dc262620',
        borderRadius: 24,
        alignItems: 'center',
        justifyContent: 'center',
        marginRight: 16,
      }}>
        <Ionicons name={icon as any} size={24} color="#dc2626" />
      </View>
      
      <View style={{ flex: 1 }}>
        <Text style={{
          fontSize: 16,
          fontWeight: 'bold',
          color: '#111827',
          marginBottom: 4,
        }}>
          {title}
        </Text>
        <Text style={{
          fontSize: 14,
          color: '#6b7280',
        }}>
          {description}
        </Text>
      </View>
      
      <View style={{ alignItems: 'flex-end' }}>
        <Text style={{
          fontSize: 14,
          fontWeight: '600',
          color: '#dc2626',
          marginBottom: 2,
        }}>
          {getPrivacyLabel(privacySettings[key])}
        </Text>
        <Ionicons name="chevron-forward" size={16} color="#9ca3af" />
      </View>
    </TouchableOpacity>
  );

  const renderSecurityItem = (
    key: keyof SecuritySettings,
    title: string,
    description: string,
    icon: string
  ) => (
    <View
      key={key}
      style={{
        flexDirection: 'row',
        alignItems: 'center',
        paddingVertical: 16,
        paddingHorizontal: 20,
        borderBottomWidth: 1,
        borderBottomColor: '#f3f4f6',
      }}
    >
      <View style={{
        width: 48,
        height: 48,
        backgroundColor: '#10b98120',
        borderRadius: 24,
        alignItems: 'center',
        justifyContent: 'center',
        marginRight: 16,
      }}>
        <Ionicons name={icon as any} size={24} color="#10b981" />
      </View>
      
      <View style={{ flex: 1 }}>
        <Text style={{
          fontSize: 16,
          fontWeight: 'bold',
          color: '#111827',
          marginBottom: 4,
        }}>
          {title}
        </Text>
        <Text style={{
          fontSize: 14,
          color: '#6b7280',
        }}>
          {description}
        </Text>
      </View>
      
      <Switch
        value={securitySettings[key]}
        onValueChange={() => handleSecurityToggle(key)}
        trackColor={{ false: '#d1d5db', true: '#86efac' }}
        thumbColor={securitySettings[key] ? '#10b981' : '#f4f3f4'}
      />
    </View>
  );

  return (
    <View style={{ flex: 1, backgroundColor: '#f1f5f9' }}>
      <SafeAreaView style={{ flex: 1 }}>
        <StatusBar barStyle="light-content" backgroundColor="#dc2626" />
        
        {/* Enhanced Header */}
        <View style={{
          backgroundColor: '#dc2626',
          paddingHorizontal: 20,
          paddingTop: 20,
          paddingBottom: 28,
          borderBottomLeftRadius: 28,
          borderBottomRightRadius: 28,
          shadowColor: '#dc2626',
          shadowOffset: { width: 0, height: 12 },
          shadowOpacity: 0.3,
          shadowRadius: 20,
          elevation: 16,
        }}>
          <View style={{
            flexDirection: 'row',
            alignItems: 'center',
            marginBottom: 20,
          }}>
            <TouchableOpacity
              onPress={() => navigation.goBack()}
              style={{
                width: 48,
                height: 48,
                borderRadius: 24,
                backgroundColor: 'rgba(255,255,255,0.2)',
                justifyContent: 'center',
                alignItems: 'center',
                marginRight: 16,
                borderWidth: 2,
                borderColor: 'rgba(255,255,255,0.3)',
              }}
            >
              <Ionicons name="arrow-back" size={24} color="white" />
            </TouchableOpacity>
            
            <View style={{ flex: 1 }}>
              <Text style={{
                fontSize: 24,
                fontWeight: 'bold',
                color: 'white',
                marginBottom: 4,
              }}>
                🔒 Privacy & Security
              </Text>
              <Text style={{
                fontSize: 14,
                color: 'rgba(255,255,255,0.8)',
                fontWeight: '500',
              }}>
                Protect your account and data
              </Text>
            </View>
          </View>
        </View>

        <ScrollView style={{ flex: 1 }} showsVerticalScrollIndicator={false}>
          {/* Privacy Settings Section */}
          <View style={{
            backgroundColor: 'white',
            marginHorizontal: 20,
            marginTop: -12,
            borderRadius: 20,
            shadowColor: '#dc2626',
            shadowOffset: { width: 0, height: 8 },
            shadowOpacity: 0.1,
            shadowRadius: 16,
            elevation: 8,
            borderWidth: 1,
            borderColor: 'rgba(220, 38, 38, 0.1)',
          }}>
            <View style={{
              paddingHorizontal: 20,
              paddingVertical: 20,
              borderBottomWidth: 1,
              borderBottomColor: '#f3f4f6',
            }}>
              <Text style={{
                fontSize: 20,
                fontWeight: 'bold',
                color: '#111827',
              }}>
                🔐 Privacy Settings
              </Text>
              <Text style={{
                fontSize: 14,
                color: '#6b7280',
                marginTop: 4,
              }}>
                Control what information you share
              </Text>
            </View>

            {renderPrivacyItem('shareLocation', 'Share Location', 'Control when your location is visible', 'location')}
            {renderPrivacyItem('shareProfile', 'Share Profile', 'Control who can see your profile', 'person')}
            {renderPrivacyItem('shareEarnings', 'Share Earnings', 'Control who can see your earnings', 'cash')}
            {renderPrivacyItem('shareRating', 'Share Rating', 'Control who can see your rating', 'star')}
          </View>

          {/* Security Settings Section */}
          <View style={{
            backgroundColor: 'white',
            marginHorizontal: 20,
            marginTop: 16,
            borderRadius: 20,
            shadowColor: '#10b981',
            shadowOffset: { width: 0, height: 8 },
            shadowOpacity: 0.1,
            shadowRadius: 16,
            elevation: 8,
            borderWidth: 1,
            borderColor: 'rgba(16, 185, 129, 0.1)',
          }}>
            <View style={{
              paddingHorizontal: 20,
              paddingVertical: 20,
              borderBottomWidth: 1,
              borderBottomColor: '#f3f4f6',
            }}>
              <Text style={{
                fontSize: 20,
                fontWeight: 'bold',
                color: '#111827',
              }}>
                🛡️ Security Settings
              </Text>
              <Text style={{
                fontSize: 14,
                color: '#6b7280',
                marginTop: 4,
              }}>
                Keep your account secure
              </Text>
            </View>

            {renderSecurityItem('twoFactorAuth', 'Two-Factor Authentication', 'Add extra security to your account', 'shield-checkmark')}
            {renderSecurityItem('biometricLogin', 'Biometric Login', 'Use fingerprint or face ID to login', 'finger-print')}
            {renderSecurityItem('loginNotifications', 'Login Notifications', 'Get notified of new logins', 'notifications')}
            {renderSecurityItem('dataEncryption', 'Data Encryption', 'Encrypt sensitive data', 'lock-closed')}
          </View>

          <View style={{ height: 32 }} />
        </ScrollView>

        {/* Privacy Modal */}
        <Modal
          visible={showPrivacyModal}
          animationType="slide"
          presentationStyle="pageSheet"
          onRequestClose={() => setShowPrivacyModal(false)}
        >
          <View style={{ flex: 1, backgroundColor: '#f1f5f9' }}>
            <SafeAreaView style={{ flex: 1 }}>
              {/* Modal Header */}
              <View style={{
                backgroundColor: '#dc2626',
                paddingHorizontal: 20,
                paddingTop: 20,
                paddingBottom: 28,
                borderBottomLeftRadius: 28,
                borderBottomRightRadius: 28,
              }}>
                <View style={{
                  flexDirection: 'row',
                  alignItems: 'center',
                  justifyContent: 'space-between',
                }}>
                  <TouchableOpacity
                    onPress={() => setShowPrivacyModal(false)}
                    style={{
                      width: 48,
                      height: 48,
                      borderRadius: 24,
                      backgroundColor: 'rgba(255,255,255,0.2)',
                      justifyContent: 'center',
                      alignItems: 'center',
                    }}
                  >
                    <Ionicons name="close" size={24} color="white" />
                  </TouchableOpacity>

                  <Text style={{
                    fontSize: 20,
                    fontWeight: 'bold',
                    color: 'white',
                  }}>
                    Privacy Setting
                  </Text>

                  <View style={{ width: 48 }} />
                </View>
              </View>

              {/* Privacy Options */}
              <View style={{
                backgroundColor: 'white',
                marginHorizontal: 20,
                marginTop: -12,
                borderRadius: 20,
                shadowColor: '#dc2626',
                shadowOffset: { width: 0, height: 8 },
                shadowOpacity: 0.1,
                shadowRadius: 16,
                elevation: 8,
              }}>
                {privacyOptions.map((option, index) => (
                  <TouchableOpacity
                    key={option.value}
                    onPress={() => handlePrivacyChange(selectedPrivacySetting, option.value)}
                    style={{
                      flexDirection: 'row',
                      alignItems: 'center',
                      paddingVertical: 20,
                      paddingHorizontal: 20,
                      borderBottomWidth: index < privacyOptions.length - 1 ? 1 : 0,
                      borderBottomColor: '#f3f4f6',
                    }}
                  >
                    <View style={{
                      width: 48,
                      height: 48,
                      backgroundColor: privacySettings[selectedPrivacySetting] === option.value ? '#dc2626' : '#f3f4f6',
                      borderRadius: 24,
                      alignItems: 'center',
                      justifyContent: 'center',
                      marginRight: 16,
                    }}>
                      <Ionicons
                        name={option.icon as any}
                        size={24}
                        color={privacySettings[selectedPrivacySetting] === option.value ? 'white' : '#6b7280'}
                      />
                    </View>

                    <View style={{ flex: 1 }}>
                      <Text style={{
                        fontSize: 18,
                        fontWeight: 'bold',
                        color: '#111827',
                        marginBottom: 4,
                      }}>
                        {option.label}
                      </Text>
                      <Text style={{
                        fontSize: 14,
                        color: '#6b7280',
                      }}>
                        {option.description}
                      </Text>
                    </View>

                    {privacySettings[selectedPrivacySetting] === option.value && (
                      <Ionicons name="checkmark-circle" size={24} color="#dc2626" />
                    )}
                  </TouchableOpacity>
                ))}
              </View>
            </SafeAreaView>
          </View>
        </Modal>
      </SafeAreaView>
    </View>
  );
};

export default PrivacySecurityScreen;
