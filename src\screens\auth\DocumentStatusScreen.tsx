import React, { useState, useRef, useEffect } from 'react';
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  Alert,
  RefreshControl,
  Animated,
  Dimensions,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';

import { <PERSON><PERSON>, Card, LoadingSpinner, Badge } from '../../components/ui';
import { DocumentStatus, DocumentVerificationStatus } from '../../types/auth';
import { formatDate } from '../../utils/helpers';

interface DocumentStatusScreenProps {
  documentStatus: DocumentStatus;
  onUploadDocument: (documentType: string) => void;
  onRefresh: () => void;
  onContinue: () => void;
  isLoading?: boolean;
}

const { width } = Dimensions.get('window');

// Progress steps for verification flow
const VERIFICATION_STEPS = [
  { id: 1, title: 'Signup', icon: 'person-add-outline', description: 'Account created' },
  { id: 2, title: 'Review', icon: 'document-text-outline', description: 'Documents under review' },
  { id: 3, title: 'Approved', icon: 'checkmark-circle-outline', description: 'Ready to deliver' },
];

const DocumentStatusScreen: React.FC<DocumentStatusScreenProps> = ({
  documentStatus,
  onUploadDocument,
  onRefresh,
  onContinue,
  isLoading = false,
}) => {
  const [refreshing, setRefreshing] = useState(false);

  // Animation refs for status badges
  const badgeAnimRefs = useRef<Record<string, Animated.Value>>({});
  const progressAnim = useRef(new Animated.Value(0)).current;
  const pulseAnim = useRef(new Animated.Value(1)).current;

  // Initialize badge animations
  useEffect(() => {
    const documents = ['cnic', 'drivingLicense', 'vehicleRegistration', 'profilePhoto', 'passport'];
    documents.forEach(doc => {
      if (!badgeAnimRefs.current[doc]) {
        badgeAnimRefs.current[doc] = new Animated.Value(0);
      }
    });

    // Animate badges on mount
    documents.forEach((doc, index) => {
      Animated.timing(badgeAnimRefs.current[doc], {
        toValue: 1,
        duration: 300,
        delay: index * 100,
        useNativeDriver: true,
      }).start();
    });

    // Progress animation based on verification status
    const currentStep = getCurrentStep();
    Animated.timing(progressAnim, {
      toValue: (currentStep - 1) / (VERIFICATION_STEPS.length - 1),
      duration: 500,
      useNativeDriver: false,
    }).start();

    // Pulse animation for pending items
    const pulseAnimation = Animated.loop(
      Animated.sequence([
        Animated.timing(pulseAnim, {
          toValue: 1.1,
          duration: 1000,
          useNativeDriver: true,
        }),
        Animated.timing(pulseAnim, {
          toValue: 1,
          duration: 1000,
          useNativeDriver: true,
        }),
      ])
    );
    pulseAnimation.start();

    return () => pulseAnimation.stop();
  }, [documentStatus]);

  const getCurrentStep = () => {
    if (documentStatus.overall === DocumentVerificationStatus.APPROVED) return 3;
    if (documentStatus.overall === DocumentVerificationStatus.PENDING) return 2;
    return 1;
  };

  const handleRefresh = async () => {
    setRefreshing(true);
    await onRefresh();
    setRefreshing(false);
  };

  const getStatusConfig = (status: DocumentVerificationStatus) => {
    switch (status) {
      case DocumentVerificationStatus.APPROVED:
        return {
          color: '#10B981',
          bgColor: '#D1FAE5',
          borderColor: '#6EE7B7',
          icon: 'checkmark-circle',
          text: 'Approved',
          variant: 'success' as const,
        };
      case DocumentVerificationStatus.REJECTED:
        return {
          color: '#EF4444',
          bgColor: '#FEE2E2',
          borderColor: '#FCA5A5',
          icon: 'close-circle',
          text: 'Rejected',
          variant: 'error' as const,
        };
      case DocumentVerificationStatus.PENDING:
        return {
          color: '#F59E0B',
          bgColor: '#FEF3C7',
          borderColor: '#FCD34D',
          icon: 'time',
          text: 'Under Review',
          variant: 'warning' as const,
        };
      default:
        return {
          color: '#6B7280',
          bgColor: '#F3F4F6',
          borderColor: '#D1D5DB',
          icon: 'document-outline',
          text: 'Not Submitted',
          variant: 'default' as const,
        };
    }
  };

  const renderAnimatedBadge = (status: DocumentVerificationStatus, documentKey: string) => {
    const config = getStatusConfig(status);
    const animValue = badgeAnimRefs.current[documentKey] || new Animated.Value(1);

    return (
      <Animated.View
        style={{
          transform: [
            { scale: animValue },
            { scale: status === DocumentVerificationStatus.PENDING ? pulseAnim : 1 }
          ],
          opacity: animValue,
        }}
      >
        <View
          className="px-3 py-2 rounded-full flex-row items-center border"
          style={{
            backgroundColor: config.bgColor,
            borderColor: config.borderColor,
          }}
        >
          <Ionicons
            name={config.icon as keyof typeof Ionicons.glyphMap}
            size={16}
            color={config.color}
          />
          <Text
            className="ml-2 text-sm font-semibold"
            style={{ color: config.color }}
          >
            {config.text}
          </Text>
        </View>
      </Animated.View>
    );
  };

  const getDocumentRejectionReasons = (documentKey: string) => {
    // In a real app, this would come from the API with document-specific reasons
    const rejectionReasons: Record<string, string[]> = {
      cnic: ['Image is blurry or unclear', 'CNIC appears to be expired'],
      drivingLicense: ['License has expired', 'Image quality is poor'],
      vehicleRegistration: ['Document is not clear', 'Registration appears to be fake'],
      profilePhoto: ['Photo does not match CNIC', 'Background is not clear'],
      passport: ['Passport has expired', 'Image is not readable'],
    };

    return rejectionReasons[documentKey] || documentStatus.rejectionReasons || [];
  };

  const getDocumentIcon = (documentKey: string) => {
    const icons: Record<string, keyof typeof Ionicons.glyphMap> = {
      cnic: 'id-card-outline',
      drivingLicense: 'car-outline',
      vehicleRegistration: 'document-text-outline',
      profilePhoto: 'camera-outline',
      passport: 'airplane-outline',
    };
    return icons[documentKey] || 'document-outline';
  };

  const renderProgressTracker = () => {
    const currentStep = getCurrentStep();

    return (
      <View style={{
        backgroundColor: 'white',
        marginHorizontal: 20,
        marginTop: -12,
        marginBottom: 24,
        borderRadius: 24,
        padding: 24,
        shadowColor: '#dc2626',
        shadowOffset: { width: 0, height: 8 },
        shadowOpacity: 0.1,
        shadowRadius: 16,
        elevation: 8,
        borderWidth: 1,
        borderColor: 'rgba(220, 38, 38, 0.05)',
      }}>
        <Text style={{
          fontSize: 20,
          fontWeight: 'bold',
          color: '#111827',
          marginBottom: 24,
          textAlign: 'center',
        }}>
          Verification Progress
        </Text>

        <View style={{
          flexDirection: 'row',
          justifyContent: 'space-between',
          alignItems: 'center',
          marginBottom: 24,
        }}>
          {VERIFICATION_STEPS.map((step, index) => (
            <React.Fragment key={step.id}>
              <View style={{ alignItems: 'center', flex: 1 }}>
                <View style={{
                  width: 56,
                  height: 56,
                  borderRadius: 28,
                  alignItems: 'center',
                  justifyContent: 'center',
                  borderWidth: 3,
                  backgroundColor: step.id <= currentStep ? '#dc2626' : '#f1f5f9',
                  borderColor: step.id <= currentStep ? '#dc2626' : '#e2e8f0',
                  shadowColor: step.id <= currentStep ? '#dc2626' : 'transparent',
                  shadowOffset: { width: 0, height: 4 },
                  shadowOpacity: step.id <= currentStep ? 0.3 : 0,
                  shadowRadius: 8,
                  elevation: step.id <= currentStep ? 6 : 0,
                }}>
                  <Ionicons
                    name={step.icon as keyof typeof Ionicons.glyphMap}
                    size={28}
                    color={step.id <= currentStep ? 'white' : '#9ca3af'}
                  />
                </View>
                <Text style={{
                  fontSize: 14,
                  fontWeight: 'bold',
                  marginTop: 12,
                  textAlign: 'center',
                  color: step.id <= currentStep ? '#dc2626' : '#6b7280',
                }}>
                  {step.title}
                </Text>
                <Text style={{
                  fontSize: 12,
                  color: '#9ca3af',
                  textAlign: 'center',
                  marginTop: 4,
                }}>
                  {step.description}
                </Text>
              </View>
              {index < VERIFICATION_STEPS.length - 1 && (
                <View style={{
                  flex: 1,
                  height: 4,
                  backgroundColor: '#e2e8f0',
                  marginHorizontal: 8,
                  marginTop: 28,
                  borderRadius: 2,
                  overflow: 'hidden',
                }}>
                  <Animated.View style={{
                    height: '100%',
                    backgroundColor: '#dc2626',
                    borderRadius: 2,
                    width: progressAnim.interpolate({
                      inputRange: [index / (VERIFICATION_STEPS.length - 1), (index + 1) / (VERIFICATION_STEPS.length - 1)],
                      outputRange: ['0%', '100%'],
                      extrapolate: 'clamp',
                    }),
                  }} />
                </View>
              )}
            </React.Fragment>
          ))}
        </View>

        <View style={{
          backgroundColor: '#fef2f2',
          borderWidth: 2,
          borderColor: '#fecaca',
          borderRadius: 16,
          padding: 16,
        }}>
          <Text style={{
            color: '#991b1b',
            fontWeight: 'bold',
            textAlign: 'center',
            fontSize: 16,
            lineHeight: 24,
          }}>
            {currentStep === 1 && 'Upload your documents to start verification'}
            {currentStep === 2 && 'Your documents are being reviewed'}
            {currentStep === 3 && 'Verification complete! You can start delivering'}
          </Text>
        </View>
      </View>
    );
  };

  const documents = [
    {
      key: 'cnic',
      title: 'CNIC (Front & Back)',
      description: 'Clear photos of both sides of your CNIC',
      status: documentStatus.cnic,
      required: true,
    },
    {
      key: 'drivingLicense',
      title: 'Driving License',
      description: 'Valid driving license for your vehicle type',
      status: documentStatus.drivingLicense,
      required: true,
    },
    {
      key: 'vehicleRegistration',
      title: 'Vehicle Registration',
      description: 'Vehicle registration certificate',
      status: documentStatus.vehicleRegistration,
      required: true,
    },
    {
      key: 'profilePhoto',
      title: 'Profile Photo',
      description: 'Clear photo of yourself',
      status: documentStatus.profilePhoto,
      required: true,
    },
    {
      key: 'passport',
      title: 'Passport',
      description: 'Passport (if CNIC not available)',
      status: documentStatus.passport,
      required: false,
    },
  ];

  const approvedCount = documents.filter(doc => doc.status === DocumentVerificationStatus.APPROVED).length;
  const totalRequired = documents.filter(doc => doc.required).length;
  const canStartDelivery = documentStatus.overall === DocumentVerificationStatus.APPROVED;

  const renderDocument = (document: any) => {
    const config = getStatusConfig(document.status);
    const rejectionReasons = getDocumentRejectionReasons(document.key);
    const isApproved = document.status === DocumentVerificationStatus.APPROVED;
    const isRejected = document.status === DocumentVerificationStatus.REJECTED;
    const isPending = document.status === DocumentVerificationStatus.PENDING;
    const isNotSubmitted = document.status === DocumentVerificationStatus.NOT_SUBMITTED;

    return (
      <Card key={document.key} variant="elevated" className="mx-4 mb-6">
        <View className="overflow-hidden rounded-xl">
          {/* Header with gradient background */}
          <LinearGradient
            colors={
              isApproved
                ? ['#10b981', '#059669']
                : isRejected
                ? ['#ef4444', '#dc2626']
                : isPending
                ? ['#f59e0b', '#d97706']
                : ['#ef4444', '#dc2626']
            }
            start={{ x: 0, y: 0 }}
            end={{ x: 1, y: 0 }}
            className="px-5 py-4"
          >
            <View className="flex-row items-center justify-between">
              <View className="flex-row items-center flex-1">
                <View className="w-12 h-12 bg-white/20 rounded-full items-center justify-center mr-3">
                  <Ionicons
                    name={getDocumentIcon(document.key)}
                    size={24}
                    color="white"
                  />
                </View>
                <View className="flex-1">
                  <Text className="text-white font-bold text-lg">{document.title}</Text>
                  <Text className="text-white/80 text-sm">{document.description}</Text>
                </View>
              </View>

              {document.required && (
                <View className="bg-white/20 px-2 py-1 rounded-full">
                  <Text className="text-white text-xs font-semibold">Required</Text>
                </View>
              )}
            </View>
          </LinearGradient>

          {/* Content */}
          <View className="p-5">
            {/* Status Badge */}
            <View className="mb-4">
              {renderAnimatedBadge(document.status, document.key)}
            </View>

            {/* Action Buttons */}
            <View className="flex-row gap-3">
              {isNotSubmitted || isRejected ? (
                <TouchableOpacity
                  onPress={() => onUploadDocument(document.key)}
                  className="flex-1 bg-red-500 px-4 py-3 rounded-xl flex-row items-center justify-center"
                  style={{
                    shadowColor: '#ef4444',
                    shadowOffset: { width: 0, height: 4 },
                    shadowOpacity: 0.3,
                    shadowRadius: 8,
                    elevation: 6,
                  }}
                >
                  <Ionicons
                    name={isRejected ? 'refresh' : 'cloud-upload-outline'}
                    size={18}
                    color="white"
                  />
                  <Text className="text-white font-bold ml-2 text-base">
                    {isRejected ? 'Re-upload' : 'Upload'}
                  </Text>
                </TouchableOpacity>
              ) : (
                <>
                  <TouchableOpacity
                    onPress={() => onUploadDocument(document.key)}
                    className="flex-1 border border-gray-300 px-4 py-3 rounded-xl flex-row items-center justify-center bg-white"
                    style={{
                      shadowColor: '#000',
                      shadowOffset: { width: 0, height: 2 },
                      shadowOpacity: 0.1,
                      shadowRadius: 4,
                      elevation: 3,
                    }}
                  >
                    <Ionicons name="eye-outline" size={18} color="#6B7280" />
                    <Text className="text-gray-600 font-semibold ml-2 text-base">View</Text>
                  </TouchableOpacity>

                  <TouchableOpacity
                    onPress={() => onUploadDocument(document.key)}
                    className="flex-1 bg-blue-500 px-4 py-3 rounded-xl flex-row items-center justify-center"
                    style={{
                      shadowColor: '#3b82f6',
                      shadowOffset: { width: 0, height: 4 },
                      shadowOpacity: 0.3,
                      shadowRadius: 8,
                      elevation: 6,
                    }}
                  >
                    <Ionicons name="refresh" size={18} color="white" />
                    <Text className="text-white font-bold ml-2 text-base">Update</Text>
                  </TouchableOpacity>
                </>
              )}
            </View>

            {/* Upload Tips for not submitted documents */}
            {isNotSubmitted && (
              <View className="bg-blue-50 border border-blue-200 rounded-xl p-4 mt-4">
                <View className="flex-row items-center mb-2">
                  <Ionicons name="information-circle" size={18} color="#3B82F6" />
                  <Text className="text-blue-600 font-bold ml-2 text-base">Upload Tips</Text>
                </View>
                <Text className="text-blue-700 text-sm leading-5">
                  • Ensure document is clear and readable{'\n'}
                  • All corners should be visible{'\n'}
                  • Use good lighting when taking photos{'\n'}
                  • File size should be less than 5MB
                </Text>
              </View>
            )}
          </View>

          {/* Rejection Reasons */}
          {document.status === DocumentVerificationStatus.REJECTED && rejectionReasons.length > 0 && (
            <Animated.View
              style={{
                transform: [{ scale: badgeAnimRefs.current[document.key] || new Animated.Value(1) }],
              }}
            >
              <View className="mt-4 p-4 bg-red-50 border border-red-200 rounded-xl">
                <View className="flex-row items-center mb-2">
                  <Ionicons name="warning" size={20} color="#EF4444" />
                  <Text className="text-red-800 font-bold ml-2">Rejection Reasons:</Text>
                </View>
                {rejectionReasons.map((reason, index) => (
                  <View key={index} className="flex-row items-start mt-1">
                    <Text className="text-red-600 mr-2">•</Text>
                    <Text className="text-red-700 text-sm flex-1">{reason}</Text>
                  </View>
                ))}
                <View className="mt-3 p-2 bg-red-100 rounded-lg">
                  <Text className="text-red-800 text-xs font-medium">
                    💡 Please ensure the document is clear, valid, and matches your profile information.
                  </Text>
                </View>
              </View>
            </Animated.View>
          )}
        </View>
      </Card>
    );
  };

  return (
    <View style={{ flex: 1, backgroundColor: '#f1f5f9' }}>
      <SafeAreaView style={{ flex: 1 }}>
        {/* Enhanced Header */}
        <View style={{
          backgroundColor: '#dc2626',
          paddingHorizontal: 20,
          paddingTop: 20,
          paddingBottom: 28,
          borderBottomLeftRadius: 28,
          borderBottomRightRadius: 28,
          shadowColor: '#dc2626',
          shadowOffset: { width: 0, height: 12 },
          shadowOpacity: 0.3,
          shadowRadius: 20,
          elevation: 16,
        }}>
          <View style={{
            flexDirection: 'row',
            alignItems: 'center',
            marginBottom: 20,
          }}>
            <View style={{
              width: 56,
              height: 56,
              backgroundColor: 'rgba(255,255,255,0.2)',
              borderRadius: 28,
              justifyContent: 'center',
              alignItems: 'center',
              marginRight: 16,
              borderWidth: 2,
              borderColor: 'rgba(255,255,255,0.3)',
            }}>
              <Ionicons name="shield-checkmark" size={28} color="white" />
            </View>
            <View style={{ flex: 1 }}>
              <Text style={{
                fontSize: 24,
                fontWeight: 'bold',
                color: 'white',
                marginBottom: 4,
              }}>
                Document Verification
              </Text>
              <Text style={{
                fontSize: 14,
                color: 'rgba(255,255,255,0.8)',
              }}>
                {approvedCount} of {totalRequired} documents approved
              </Text>
            </View>
          </View>
        </View>

        {/* Progress Tracker */}
        {renderProgressTracker()}

        <ScrollView
          style={{ flex: 1 }}
          refreshControl={
            <RefreshControl
              refreshing={refreshing}
              onRefresh={handleRefresh}
              tintColor="#dc2626"
              colors={['#dc2626']}
            />
          }
          showsVerticalScrollIndicator={false}
        >
          {/* Enhanced Overall Status */}
          <View style={{
            backgroundColor: 'white',
            marginHorizontal: 20,
            marginBottom: 24,
            borderRadius: 24,
            padding: 28,
            shadowColor: canStartDelivery ? '#10b981' : '#f59e0b',
            shadowOffset: { width: 0, height: 12 },
            shadowOpacity: 0.15,
            shadowRadius: 20,
            elevation: 12,
            borderWidth: 2,
            borderColor: canStartDelivery ? '#d1fae5' : '#fef3c7',
          }}>
            <View style={{ alignItems: 'center' }}>
              <Animated.View
                style={{
                  transform: [{ scale: pulseAnim }],
                  width: 100,
                  height: 100,
                  borderRadius: 50,
                  alignItems: 'center',
                  justifyContent: 'center',
                  marginBottom: 20,
                  backgroundColor: canStartDelivery ? '#d1fae5' : '#fef3c7',
                  borderWidth: 4,
                  borderColor: canStartDelivery ? '#10b981' : '#f59e0b',
                  shadowColor: canStartDelivery ? '#10b981' : '#f59e0b',
                  shadowOffset: { width: 0, height: 8 },
                  shadowOpacity: 0.3,
                  shadowRadius: 16,
                  elevation: 12,
                }}
              >
                <Ionicons
                  name={canStartDelivery ? 'checkmark-circle' : 'time'}
                  size={48}
                  color={canStartDelivery ? '#10b981' : '#f59e0b'}
                />
              </Animated.View>
              <Text style={{
                fontSize: 28,
                fontWeight: 'bold',
                color: '#111827',
                marginBottom: 12,
                textAlign: 'center',
              }}>
                {canStartDelivery ? 'Verification Complete! 🎉' : 'Verification in Progress ⏳'}
              </Text>
              <Text style={{
                fontSize: 16,
                color: '#6b7280',
                textAlign: 'center',
                lineHeight: 24,
                marginBottom: 24,
                maxWidth: 280,
              }}>
                {canStartDelivery
                  ? 'All documents approved. You can start delivering and earning!'
                  : 'Please upload all required documents for verification. Our team will review them within 24 hours.'
                }
              </Text>

              {/* Enhanced Progress Stats */}
              <View style={{
                flexDirection: 'row',
                justifyContent: 'center',
                gap: 32,
              }}>
                <View style={{ alignItems: 'center' }}>
                  <Text style={{
                    fontSize: 32,
                    fontWeight: 'bold',
                    color: '#10b981',
                    marginBottom: 4,
                  }}>
                    {approvedCount}
                  </Text>
                  <Text style={{
                    fontSize: 14,
                    color: '#6b7280',
                    fontWeight: '600',
                  }}>
                    Approved
                  </Text>
                </View>
                <View style={{ alignItems: 'center' }}>
                  <Text style={{
                    fontSize: 32,
                    fontWeight: 'bold',
                    color: '#f59e0b',
                    marginBottom: 4,
                  }}>
                    {documents.filter(doc => doc.status === DocumentVerificationStatus.PENDING).length}
                  </Text>
                  <Text style={{
                    fontSize: 14,
                    color: '#6b7280',
                    fontWeight: '600',
                  }}>
                    Pending
                  </Text>
                </View>
                  <Text style={{
                    fontSize: 32,
                    fontWeight: 'bold',
                    color: '#dc2626',
                    marginBottom: 4,
                  }}>
                    {documents.filter(doc => doc.status === DocumentVerificationStatus.REJECTED).length}
                  </Text>
                  <Text style={{
                    fontSize: 14,
                    color: '#6b7280',
                    fontWeight: '600',
                  }}>
                    Rejected
                  </Text>
                </View>
              </View>

              {documentStatus.lastUpdated && (
                <Text style={{
                  color: '#9ca3af',
                  fontSize: 14,
                  marginTop: 20,
                  textAlign: 'center',
                }}>
                  Last updated: {formatDate(documentStatus.lastUpdated)}
                </Text>
              )}
            </View>
          </View>

          {/* Documents List */}
          <View className="mb-6">
            <View className="px-4 mb-4">
              <Text className="text-xl font-bold text-gray-800 mb-2">Document Status</Text>
              <Text className="text-gray-600">Review and manage your uploaded documents</Text>
            </View>

            {/* Required Documents */}
            <View className="mb-6">
              <Text className="text-lg font-semibold text-gray-800 mb-3 px-4">Required Documents</Text>
              {documents.filter(doc => doc.required).map(renderDocument)}
            </View>

            {/* Optional Documents */}
            {documents.filter(doc => !doc.required).length > 0 && (
              <View>
                <Text className="text-lg font-semibold text-gray-800 mb-3 px-4">Optional Documents</Text>
                {documents.filter(doc => !doc.required).map(renderDocument)}
              </View>
            )}
          </View>

          {/* Help Section */}
          <Card className="mx-4 mt-6" variant="elevated">
            <View className="p-5">
              <View className="flex-row items-start">
                <View className="w-10 h-10 bg-blue-100 rounded-full items-center justify-center mr-3">
                  <Ionicons name="help-circle" size={20} color="#3B82F6" />
                </View>
                <View className="flex-1">
                  <Text className="font-bold text-gray-800 mb-3 text-lg">📋 Document Guidelines</Text>
                  <View className="space-y-2">
                    <View className="flex-row items-start">
                      <Text className="text-green-600 mr-2">✓</Text>
                      <Text className="text-gray-600 text-sm flex-1">Ensure photos are clear and well-lit</Text>
                    </View>
                    <View className="flex-row items-start">
                      <Text className="text-green-600 mr-2">✓</Text>
                      <Text className="text-gray-600 text-sm flex-1">All text should be readable</Text>
                    </View>
                    <View className="flex-row items-start">
                      <Text className="text-green-600 mr-2">✓</Text>
                      <Text className="text-gray-600 text-sm flex-1">Documents should be valid and not expired</Text>
                    </View>
                    <View className="flex-row items-start">
                      <Text className="text-green-600 mr-2">✓</Text>
                      <Text className="text-gray-600 text-sm flex-1">Upload original documents (no photocopies)</Text>
                    </View>
                  </View>
                </View>
              </View>
            </View>
          </Card>

          {/* Loading State */}
          {isLoading && (
            <View className="p-8">
              <LoadingSpinner size="large" />
            </View>
          )}

          {/* Bottom Spacing */}
          <View className="h-20" />
        </ScrollView>

        {/* Bottom Actions */}
        <View className="p-4 bg-white/95 backdrop-blur-sm border-t border-red-200">
          {canStartDelivery ? (
            <Button
              title="🚀 Start Delivering"
              onPress={onContinue}
              className="mb-3"
              style={{
                shadowColor: '#EF4444',
                shadowOffset: { width: 0, height: 4 },
                shadowOpacity: 0.3,
                shadowRadius: 8,
                elevation: 6,
              }}
            />
          ) : (
            <Button
              title="🔄 Refresh Status"
              onPress={handleRefresh}
              loading={refreshing}
              variant="outline"
              className="mb-3"
            />
          )}
          <TouchableOpacity
            onPress={() => Alert.alert(
              'Support',
              'Need help with document verification?\n\n📧 Email: <EMAIL>\n📞 Phone: +92-300-1234567\n💬 Live Chat: Available 24/7'
            )}
            className="flex-row items-center justify-center"
          >
            <Ionicons name="help-circle-outline" size={20} color="#F97316" />
            <Text className="text-center text-orange-500 font-semibold ml-2">Contact Support</Text>
          </TouchableOpacity>
        </ScrollView>
      </SafeAreaView>
    </View>
  );
};

export default DocumentStatusScreen;
