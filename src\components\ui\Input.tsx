import React, { useState } from 'react';
import {
  View,
  TextInput,
  Text,
  TouchableOpacity,
  TextInputProps,
  ViewStyle,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';

interface InputProps extends TextInputProps {
  label?: string;
  error?: string;
  hint?: string;
  leftIcon?: keyof typeof Ionicons.glyphMap;
  rightIcon?: keyof typeof Ionicons.glyphMap;
  onRightIconPress?: () => void;
  required?: boolean;
  disabled?: boolean;
  variant?: 'default' | 'filled' | 'outlined' | 'minimal';
  size?: 'sm' | 'md' | 'lg';
  containerStyle?: ViewStyle;
}

const Input: React.FC<InputProps> = ({
  label,
  error,
  hint,
  leftIcon,
  rightIcon,
  onRightIconPress,
  required = false,
  disabled = false,
  variant = 'default',
  size = 'md',
  containerStyle,
  ...props
}) => {
  const [isFocused, setIsFocused] = useState(false);

  const getSizeStyles = () => {
    const sizes = {
      sm: { paddingHorizontal: 12, paddingVertical: 10, fontSize: 14, iconSize: 18 },
      md: { paddingHorizontal: 16, paddingVertical: 14, fontSize: 16, iconSize: 20 },
      lg: { paddingHorizontal: 20, paddingVertical: 18, fontSize: 18, iconSize: 22 },
    };
    return sizes[size];
  };

  const getInputContainerStyle = () => {
    const sizeStyles = getSizeStyles();
    const baseStyle = {
      flexDirection: 'row' as const,
      alignItems: 'center' as const,
      borderRadius: 16,
      paddingHorizontal: sizeStyles.paddingHorizontal,
      paddingVertical: sizeStyles.paddingVertical,
      opacity: disabled ? 0.6 : 1,
    };

    const variantStyles = {
      default: {
        backgroundColor: 'white',
        borderWidth: 2,
        borderColor: error ? '#dc2626' : (isFocused ? '#dc2626' : '#e2e8f0'),
        shadowColor: isFocused ? '#dc2626' : '#000',
        shadowOffset: { width: 0, height: isFocused ? 4 : 2 },
        shadowOpacity: isFocused ? 0.1 : 0.05,
        shadowRadius: isFocused ? 8 : 4,
        elevation: isFocused ? 4 : 2,
      },
      filled: {
        backgroundColor: error ? '#fef2f2' : (isFocused ? '#fef2f2' : '#f8fafc'),
        borderWidth: 0,
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.05,
        shadowRadius: 4,
        elevation: 2,
      },
      outlined: {
        backgroundColor: 'transparent',
        borderWidth: 2,
        borderColor: error ? '#dc2626' : (isFocused ? '#dc2626' : '#d1d5db'),
      },
      minimal: {
        backgroundColor: 'transparent',
        borderWidth: 0,
        borderBottomWidth: 2,
        borderBottomColor: error ? '#dc2626' : (isFocused ? '#dc2626' : '#d1d5db'),
        borderRadius: 0,
        paddingHorizontal: 0,
      },
    };

    return { ...baseStyle, ...variantStyles[variant] };
  };

  return (
    <View style={[{ marginBottom: 24 }, containerStyle]}>
      {/* Enhanced Label */}
      {label && (
        <Text style={{
          fontSize: 16,
          fontWeight: '600',
          color: '#111827',
          marginBottom: 12,
        }}>
          {label} {required && <Text style={{ color: '#dc2626' }}>*</Text>}
        </Text>
      )}

      {/* Enhanced Input Container */}
      <View style={getInputContainerStyle()}>
        {/* Enhanced Left Icon */}
        {leftIcon && (
          <Ionicons
            name={leftIcon}
            size={getSizeStyles().iconSize}
            color={error ? '#dc2626' : (isFocused ? '#dc2626' : '#6b7280')}
            style={{ marginRight: 12 }}
          />
        )}

        {/* Enhanced Text Input */}
        <TextInput
          style={{
            flex: 1,
            fontSize: getSizeStyles().fontSize,
            color: '#111827',
            fontWeight: '500',
          }}
          placeholderTextColor="#9ca3af"
          editable={!disabled}
          onFocus={() => setIsFocused(true)}
          onBlur={() => setIsFocused(false)}
          {...props}
        />

        {/* Enhanced Right Icon */}
        {rightIcon && (
          <TouchableOpacity
            onPress={onRightIconPress}
            style={{
              padding: 8,
              borderRadius: 8,
              backgroundColor: isFocused ? 'rgba(220, 38, 38, 0.1)' : 'transparent',
            }}
            disabled={disabled}
          >
            <Ionicons
              name={rightIcon}
              size={getSizeStyles().iconSize}
              color={error ? '#dc2626' : (isFocused ? '#dc2626' : '#6b7280')}
            />
          </TouchableOpacity>
        )}
      </View>

      {/* Enhanced Error Message */}
      {error && (
        <View style={{ flexDirection: 'row', alignItems: 'center', marginTop: 8 }}>
          <Ionicons name="alert-circle" size={16} color="#dc2626" />
          <Text style={{
            fontSize: 14,
            color: '#dc2626',
            marginLeft: 6,
            fontWeight: '500',
          }}>
            {error}
          </Text>
        </View>
      )}

      {/* Enhanced Hint Message */}
      {hint && !error && (
        <View style={{ flexDirection: 'row', alignItems: 'center', marginTop: 8 }}>
          <Ionicons name="information-circle" size={16} color="#6b7280" />
          <Text style={{
            fontSize: 14,
            color: '#6b7280',
            marginLeft: 6,
          }}>
            {hint}
          </Text>
        </View>
      )}
    </View>
  );
};

export default Input;
