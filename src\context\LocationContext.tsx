import React, { createContext, useContext, useReducer, useEffect } from 'react';
import * as Location from 'expo-location';
import {
  LocationState,
  LocationContextType,
  LocationData,
  LocationPermissionStatus,
} from '../types/location';
import { locationService } from '../services/location/locationService';

// Initial state
const initialState: LocationState = {
  currentLocation: null,
  isTracking: false,
  hasPermission: false,
  permissionStatus: 'undetermined',
  isLoading: false,
  error: null,
};

// Action types
type LocationAction =
  | { type: 'SET_LOADING'; payload: boolean }
  | { type: 'SET_ERROR'; payload: string | null }
  | { type: 'SET_PERMISSION_STATUS'; payload: LocationPermissionStatus }
  | { type: 'SET_CURRENT_LOCATION'; payload: LocationData }
  | { type: 'SET_TRACKING'; payload: boolean }
  | { type: 'SET_HAS_PERMISSION'; payload: boolean }
  | { type: 'CLEAR_ERROR' };

// Reducer
const locationReducer = (state: LocationState, action: LocationAction): LocationState => {
  switch (action.type) {
    case 'SET_LOADING':
      return {
        ...state,
        isLoading: action.payload,
      };
    case 'SET_ERROR':
      return {
        ...state,
        error: action.payload,
        isLoading: false,
      };
    case 'SET_PERMISSION_STATUS':
      return {
        ...state,
        permissionStatus: action.payload,
        error: null,
      };
    case 'SET_CURRENT_LOCATION':
      return {
        ...state,
        currentLocation: action.payload,
        error: null,
      };
    case 'SET_TRACKING':
      return {
        ...state,
        isTracking: action.payload,
      };
    case 'SET_HAS_PERMISSION':
      return {
        ...state,
        hasPermission: action.payload,
      };
    case 'CLEAR_ERROR':
      return {
        ...state,
        error: null,
      };
    default:
      return state;
  }
};

// Create context
const LocationContext = createContext<LocationContextType | undefined>(undefined);

// Provider component
interface LocationProviderProps {
  children: React.ReactNode;
}

export const LocationProvider: React.FC<LocationProviderProps> = ({ children }) => {
  const [state, dispatch] = useReducer(locationReducer, initialState);

  // Check permissions on mount
  useEffect(() => {
    checkPermissions();
    
    // Cleanup on unmount
    return () => {
      locationService.cleanup();
    };
  }, []);

  const checkPermissions = async () => {
    try {
      const status = await locationService.getPermissionStatus();
      dispatch({ type: 'SET_PERMISSION_STATUS', payload: status });
    } catch (error: any) {
      dispatch({ type: 'SET_ERROR', payload: error.message });
    }
  };

  const requestPermissions = async (): Promise<boolean> => {
    try {
      dispatch({ type: 'SET_LOADING', payload: true });
      dispatch({ type: 'CLEAR_ERROR' });

      const status = await locationService.requestPermissions();
      dispatch({ type: 'SET_PERMISSION_STATUS', payload: status });

      const hasPermission = status === Location.PermissionStatus.GRANTED;
      dispatch({ type: 'SET_HAS_PERMISSION', payload: hasPermission });

      if (status === Location.PermissionStatus.DENIED) {
        throw new Error('Location permission is required for delivery tracking');
      }

      return hasPermission;
    } catch (error: any) {
      dispatch({ type: 'SET_ERROR', payload: error.message });
      return false;
    } finally {
      dispatch({ type: 'SET_LOADING', payload: false });
    }
  };

  const getCurrentLocation = async () => {
    try {
      dispatch({ type: 'SET_LOADING', payload: true });
      dispatch({ type: 'CLEAR_ERROR' });
      
      const location = await locationService.getCurrentLocation();
      dispatch({ type: 'SET_CURRENT_LOCATION', payload: location });
      
      return location;
    } catch (error: any) {
      dispatch({ type: 'SET_ERROR', payload: error.message });
      throw error;
    } finally {
      dispatch({ type: 'SET_LOADING', payload: false });
    }
  };

  const startLocationTracking = async () => {
    try {
      dispatch({ type: 'SET_LOADING', payload: true });
      dispatch({ type: 'CLEAR_ERROR' });
      
      await locationService.startLocationTracking(
        (location) => {
          dispatch({ type: 'SET_CURRENT_LOCATION', payload: location });
        },
        (error) => {
          dispatch({ type: 'SET_ERROR', payload: error.message });
        }
      );
      
      dispatch({ type: 'SET_TRACKING', payload: true });
    } catch (error: any) {
      dispatch({ type: 'SET_ERROR', payload: error.message });
      throw error;
    } finally {
      dispatch({ type: 'SET_LOADING', payload: false });
    }
  };

  const stopLocationTracking = async () => {
    try {
      await locationService.stopLocationTracking();
      dispatch({ type: 'SET_TRACKING', payload: false });
    } catch (error: any) {
      dispatch({ type: 'SET_ERROR', payload: error.message });
    }
  };

  const calculateDistance = (
    lat1: number,
    lon1: number,
    lat2: number,
    lon2: number
  ): number => {
    return locationService.calculateDistance(lat1, lon1, lat2, lon2);
  };

  const calculateBearing = (
    lat1: number,
    lon1: number,
    lat2: number,
    lon2: number
  ): number => {
    return locationService.calculateBearing(lat1, lon1, lat2, lon2);
  };

  const isWithinGeofence = (
    currentLat: number,
    currentLon: number,
    targetLat: number,
    targetLon: number,
    radiusInMeters: number
  ): boolean => {
    return locationService.isWithinGeofence(
      currentLat,
      currentLon,
      targetLat,
      targetLon,
      radiusInMeters
    );
  };

  const getAddressFromCoordinates = async (
    latitude: number,
    longitude: number
  ): Promise<string> => {
    return locationService.getAddressFromCoordinates(latitude, longitude);
  };

  const getCoordinatesFromAddress = async (address: string): Promise<LocationData | null> => {
    return locationService.getCoordinatesFromAddress(address);
  };

  const clearError = () => {
    dispatch({ type: 'CLEAR_ERROR' });
  };

  const contextValue: LocationContextType = {
    state,
    startTracking: startLocationTracking,
    stopTracking: stopLocationTracking,
    getCurrentLocation,
    requestPermission: requestPermissions,
    requestPermissions,
    startLocationTracking,
    stopLocationTracking,
    clearError,
  };

  return (
    <LocationContext.Provider value={contextValue}>
      {children}
    </LocationContext.Provider>
  );
};

// Hook to use location context
export const useLocation = (): LocationContextType => {
  const context = useContext(LocationContext);
  if (context === undefined) {
    throw new Error('useLocation must be used within a LocationProvider');
  }
  return context;
};
