{"name": "rider_app", "version": "1.0.0", "main": "index.ts", "scripts": {"start": "expo start", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web", "build:android": "expo build:android --type app-bundle", "build:android:apk": "expo build:android --type apk", "build:ios": "expo build:ios", "analyze:bundle": "expo export --dump-assetmap", "performance:profile": "expo start --dev-client", "performance:monitor": "node scripts/performance-monitor.js", "optimize:images": "node scripts/optimize-images.js", "clean:cache": "expo r -c", "clean:node": "rm -rf node_modules && npm install", "lint": "eslint src --ext .ts,.tsx", "lint:fix": "eslint src --ext .ts,.tsx --fix", "type-check": "tsc --noEmit"}, "dependencies": {"@expo/vector-icons": "^14.1.0", "@react-native-async-storage/async-storage": "^2.2.0", "@react-native-community/datetimepicker": "^8.4.2", "@react-native-community/netinfo": "^11.4.1", "@react-navigation/bottom-tabs": "^7.4.2", "@react-navigation/native": "^7.1.14", "@react-navigation/stack": "^7.4.2", "axios": "^1.10.0", "expo": "~53.0.17", "expo-blur": "~14.1.5", "expo-build-properties": "^0.14.8", "expo-clipboard": "^7.1.5", "expo-image-picker": "~16.1.4", "expo-linear-gradient": "~14.1.5", "expo-location": "^18.1.6", "expo-notifications": "^0.31.4", "expo-secure-store": "^14.2.3", "expo-status-bar": "~2.2.3", "nativewind": "^4.1.23", "react": "19.0.0", "react-native": "0.79.5", "react-native-chart-kit": "^6.12.0", "react-native-maps": "^1.24.3", "react-native-qrcode-svg": "^6.3.15", "react-native-safe-area-context": "^5.5.1", "react-native-screens": "^4.11.1", "react-native-svg": "^15.12.0"}, "devDependencies": {"@babel/core": "^7.25.2", "@babel/plugin-transform-runtime": "^7.28.0", "@types/react": "~19.0.10", "@typescript-eslint/eslint-plugin": "^8.35.1", "@typescript-eslint/parser": "^8.35.1", "babel-plugin-transform-imports": "^2.0.0", "babel-plugin-transform-react-remove-prop-types": "^0.4.24", "babel-plugin-transform-remove-console": "^6.9.4", "eslint": "^9.30.1", "eslint-config-prettier": "^10.1.5", "eslint-plugin-prettier": "^5.5.1", "prettier": "^3.6.2", "tailwindcss": "^3.4.17", "typescript": "~5.8.3"}, "private": true}