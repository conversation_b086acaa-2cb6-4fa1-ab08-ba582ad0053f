import React from 'react';
import {
  Modal as RNModal,
  View,
  Text,
  TouchableOpacity,
  TouchableWithoutFeedback,
  ViewStyle,
  ModalProps as RNModalProps,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';

interface ModalProps extends Omit<RNModalProps, 'children'> {
  isVisible: boolean;
  onClose: () => void;
  title?: string;
  children: React.ReactNode;
  variant?: 'default' | 'fullscreen' | 'bottom-sheet' | 'center';
  showCloseButton?: boolean;
  closeOnBackdrop?: boolean;
  size?: 'sm' | 'md' | 'lg' | 'xl';
}

const Modal: React.FC<ModalProps> = ({
  isVisible,
  onClose,
  title,
  children,
  variant = 'default',
  showCloseButton = true,
  closeOnBackdrop = true,
  size = 'md',
  ...props
}) => {
  const getBackdropStyle = (): ViewStyle => {
    return {
      flex: 1,
      backgroundColor: 'rgba(0, 0, 0, 0.5)',
      justifyContent: variant === 'bottom-sheet' ? 'flex-end' : 'center',
      alignItems: 'center',
      padding: variant === 'fullscreen' ? 0 : 20,
    };
  };

  const getModalStyle = (): ViewStyle => {
    const baseStyle: ViewStyle = {
      backgroundColor: 'white',
      shadowColor: '#dc2626',
      shadowOffset: { width: 0, height: 12 },
      shadowOpacity: 0.15,
      shadowRadius: 20,
      elevation: 16,
      borderWidth: 1,
      borderColor: 'rgba(220, 38, 38, 0.1)',
    };

    switch (variant) {
      case 'fullscreen':
        return {
          ...baseStyle,
          flex: 1,
          width: '100%',
          borderRadius: 0,
        };

      case 'bottom-sheet':
        return {
          ...baseStyle,
          width: '100%',
          borderTopLeftRadius: 24,
          borderTopRightRadius: 24,
          maxHeight: '80%',
        };

      case 'center':
        return {
          ...baseStyle,
          borderRadius: 24,
          width: getSizeWidth(),
          maxHeight: '80%',
        };

      default:
        return {
          ...baseStyle,
          borderRadius: 20,
          width: getSizeWidth(),
          maxHeight: '80%',
        };
    }
  };

  const getSizeWidth = (): string => {
    switch (size) {
      case 'sm': return '80%';
      case 'md': return '90%';
      case 'lg': return '95%';
      case 'xl': return '100%';
      default: return '90%';
    }
  };

  const handleBackdropPress = () => {
    if (closeOnBackdrop) {
      onClose();
    }
  };

  return (
    <RNModal
      visible={isVisible}
      transparent
      animationType="fade"
      onRequestClose={onClose}
      {...props}
    >
      <TouchableWithoutFeedback onPress={handleBackdropPress}>
        <View style={getBackdropStyle()}>
          <TouchableWithoutFeedback>
            <View style={getModalStyle()}>
              {/* Header */}
              {(title || showCloseButton) && (
                <View style={{
                  flexDirection: 'row',
                  alignItems: 'center',
                  justifyContent: 'space-between',
                  padding: 20,
                  borderBottomWidth: 1,
                  borderBottomColor: '#f1f5f9',
                }}>
                  {title && (
                    <Text style={{
                      fontSize: 20,
                      fontWeight: 'bold',
                      color: '#111827',
                      flex: 1,
                    }}>
                      {title}
                    </Text>
                  )}
                  
                  {showCloseButton && (
                    <TouchableOpacity
                      onPress={onClose}
                      style={{
                        width: 48,
                        height: 48,
                        borderRadius: 24,
                        backgroundColor: '#dc262620',
                        alignItems: 'center',
                        justifyContent: 'center',
                        borderWidth: 2,
                        borderColor: 'rgba(220, 38, 38, 0.3)',
                      }}
                    >
                      <Ionicons name="close" size={24} color="#dc2626" />
                    </TouchableOpacity>
                  )}
                </View>
              )}

              {/* Content */}
              <View style={{ flex: 1 }}>
                {children}
              </View>
            </View>
          </TouchableWithoutFeedback>
        </View>
      </TouchableWithoutFeedback>
    </RNModal>
  );
};

export default Modal;
