import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  ScrollView,
  Switch,
  FlatList,
  Alert,
  Modal,
  Platform,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import DateTimePicker from '@react-native-community/datetimepicker';
import { useNavigation } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import { AdvancedToolsStackParamList } from '../../types';
import {
  PushNotification,
  NotificationType,
  NotificationPriority,
  NotificationPreferences,
} from '../../types/advancedTools';

type NotificationsScreenNavigationProp = StackNavigationProp<AdvancedToolsStackParamList, 'Notifications'>;

const NotificationsScreen: React.FC = () => {
  const navigation = useNavigation<NotificationsScreenNavigationProp>();
  const [notifications, setNotifications] = useState<PushNotification[]>([]);
  const [preferences, setPreferences] = useState<NotificationPreferences | null>(null);
  const [activeTab, setActiveTab] = useState<'notifications' | 'settings'>('notifications');
  const [loading, setLoading] = useState(true);
  const [showTimePicker, setShowTimePicker] = useState(false);
  const [timePickerMode, setTimePickerMode] = useState<'start' | 'end'>('start');
  const [tempTime, setTempTime] = useState(new Date());

  // Mock notification data
  const mockNotifications: PushNotification[] = [
    {
      id: 'notif-1',
      riderId: 'rider-1',
      type: NotificationType.ORDER_REQUEST,
      title: 'New Order Request',
      message: 'You have a new delivery request from McDonald\'s Gulshan. Tap to view details.',
      data: {
        orderId: 'order-123',
        restaurantName: 'McDonald\'s Gulshan',
        estimatedEarnings: 250,
        distance: 2.5,
      },
      priority: NotificationPriority.HIGH,
      isRead: false,
      createdAt: new Date(Date.now() - 5 * 60 * 1000).toISOString(), // 5 minutes ago
      scheduledAt: new Date(Date.now() - 5 * 60 * 1000).toISOString(),
      sentAt: new Date(Date.now() - 5 * 60 * 1000).toISOString(),
    },
    {
      id: 'notif-2',
      riderId: 'rider-1',
      type: NotificationType.PAYMENT_RECEIVED,
      title: 'Payment Received',
      message: 'Rs.450 has been added to your wallet for order #ORD-456.',
      data: {
        amount: 450,
        orderId: 'ORD-456',
        paymentMethod: 'Cash',
      },
      priority: NotificationPriority.MEDIUM,
      isRead: true,
      createdAt: new Date(Date.now() - 30 * 60 * 1000).toISOString(), // 30 minutes ago
      scheduledAt: new Date(Date.now() - 30 * 60 * 1000).toISOString(),
      sentAt: new Date(Date.now() - 30 * 60 * 1000).toISOString(),
    },
    {
      id: 'notif-3',
      riderId: 'rider-1',
      type: NotificationType.BONUS_EARNED,
      title: 'Bonus Earned! 🎉',
      message: 'Congratulations! You\'ve earned a Rs.100 bonus for completing 10 deliveries today.',
      data: {
        bonusAmount: 100,
        reason: '10 deliveries completed',
        bonusType: 'daily_milestone',
      },
      priority: NotificationPriority.HIGH,
      isRead: false,
      createdAt: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(), // 2 hours ago
      scheduledAt: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),
      sentAt: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),
    },
    {
      id: 'notif-4',
      riderId: 'rider-1',
      type: NotificationType.TRAINING_REMINDER,
      title: 'Training Reminder',
      message: 'Don\'t forget to complete your "Customer Service Excellence" training to earn 150 XP.',
      data: {
        trainingId: 'video-3',
        trainingTitle: 'Customer Service Excellence',
        xpReward: 150,
      },
      priority: NotificationPriority.LOW,
      isRead: true,
      createdAt: new Date(Date.now() - 4 * 60 * 60 * 1000).toISOString(), // 4 hours ago
      scheduledAt: new Date(Date.now() - 4 * 60 * 60 * 1000).toISOString(),
      sentAt: new Date(Date.now() - 4 * 60 * 60 * 1000).toISOString(),
    },
    {
      id: 'notif-5',
      riderId: 'rider-1',
      type: NotificationType.SAFETY_ALERT,
      title: 'Safety Alert',
      message: 'Heavy rain expected in your area. Please drive carefully and consider taking a break if conditions worsen.',
      data: {
        alertType: 'weather',
        severity: 'moderate',
        area: 'Gulshan, Karachi',
      },
      priority: NotificationPriority.HIGH,
      isRead: false,
      createdAt: new Date(Date.now() - 6 * 60 * 60 * 1000).toISOString(), // 6 hours ago
      scheduledAt: new Date(Date.now() - 6 * 60 * 60 * 1000).toISOString(),
      sentAt: new Date(Date.now() - 6 * 60 * 60 * 1000).toISOString(),
    },
    {
      id: 'notif-6',
      riderId: 'rider-1',
      type: NotificationType.SYSTEM_UPDATE,
      title: 'App Update Available',
      message: 'A new version of FoodWay Rider app is available with improved features and bug fixes.',
      data: {
        version: '2.1.0',
        updateType: 'optional',
        features: ['Improved navigation', 'Bug fixes', 'Performance improvements'],
      },
      priority: NotificationPriority.LOW,
      isRead: true,
      createdAt: new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString(), // 1 day ago
      scheduledAt: new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString(),
      sentAt: new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString(),
    },
    {
      id: 'notif-7',
      riderId: 'rider-1',
      type: NotificationType.PERFORMANCE_UPDATE,
      title: 'Weekly Performance Report',
      message: 'Your weekly performance: 45 deliveries, 4.8★ rating, Rs.12,500 earned. Great job!',
      data: {
        period: 'weekly',
        deliveries: 45,
        rating: 4.8,
        earnings: 12500,
        rank: 3,
      },
      priority: NotificationPriority.MEDIUM,
      isRead: false,
      createdAt: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000).toISOString(), // 2 days ago
      scheduledAt: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000).toISOString(),
      sentAt: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000).toISOString(),
    },
  ];

  const mockPreferences: NotificationPreferences = {
    id: 'pref-1',
    riderId: 'rider-1',
    orderRequests: {
      enabled: true,
      sound: true,
      vibration: true,
      priority: NotificationPriority.HIGH,
    },
    paymentUpdates: {
      enabled: true,
      sound: false,
      vibration: true,
      priority: NotificationPriority.MEDIUM,
    },
    bonusAlerts: {
      enabled: true,
      sound: true,
      vibration: true,
      priority: NotificationPriority.HIGH,
    },
    trainingReminders: {
      enabled: true,
      sound: false,
      vibration: false,
      priority: NotificationPriority.LOW,
    },
    safetyAlerts: {
      enabled: true,
      sound: true,
      vibration: true,
      priority: NotificationPriority.HIGH,
    },
    systemUpdates: {
      enabled: false,
      sound: false,
      vibration: false,
      priority: NotificationPriority.LOW,
    },
    performanceUpdates: {
      enabled: true,
      sound: false,
      vibration: true,
      priority: NotificationPriority.MEDIUM,
    },
    quietHours: {
      enabled: true,
      startTime: '22:00',
      endTime: '07:00',
    },
    globalSettings: {
      masterEnabled: true,
      soundEnabled: true,
      vibrationEnabled: true,
      badgeEnabled: true,
    },
    updatedAt: new Date().toISOString(),
  };

  useEffect(() => {
    loadNotificationData();
  }, []);

  const loadNotificationData = async () => {
    setLoading(true);
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      setNotifications(mockNotifications);
      setPreferences(mockPreferences);
    } catch (error) {
      console.error('Error loading notification data:', error);
    } finally {
      setLoading(false);
    }
  };

  const markAsRead = async (notificationId: string) => {
    try {
      const updatedNotifications = notifications.map(notif =>
        notif.id === notificationId ? { ...notif, isRead: true } : notif
      );
      setNotifications(updatedNotifications);
    } catch (error) {
      console.error('Error marking notification as read:', error);
    }
  };

  const markAllAsRead = async () => {
    try {
      const updatedNotifications = notifications.map(notif => ({ ...notif, isRead: true }));
      setNotifications(updatedNotifications);
    } catch (error) {
      console.error('Error marking all notifications as read:', error);
    }
  };

  const deleteNotification = async (notificationId: string) => {
    try {
      const updatedNotifications = notifications.filter(notif => notif.id !== notificationId);
      setNotifications(updatedNotifications);
    } catch (error) {
      console.error('Error deleting notification:', error);
    }
  };

  const clearAllNotifications = async () => {
    Alert.alert(
      'Clear All Notifications',
      'Are you sure you want to clear all notifications? This action cannot be undone.',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Clear All',
          style: 'destructive',
          onPress: () => setNotifications([]),
        },
      ]
    );
  };

  const updatePreferences = async (newPreferences: NotificationPreferences) => {
    try {
      setPreferences(newPreferences);
      // Here you would typically save to API
    } catch (error) {
      console.error('Error updating preferences:', error);
    }
  };

  const openTimePicker = (mode: 'start' | 'end') => {
    if (!preferences) return;

    const currentTime = mode === 'start' ? preferences.quietHours.startTime : preferences.quietHours.endTime;
    const [hours, minutes] = currentTime.split(':').map(Number);
    const date = new Date();
    date.setHours(hours, minutes, 0, 0);

    setTempTime(date);
    setTimePickerMode(mode);
    setShowTimePicker(true);
  };

  const handleTimeChange = (event: any, selectedTime?: Date) => {
    if (Platform.OS === 'android') {
      setShowTimePicker(false);
    }

    if (selectedTime && preferences) {
      const timeString = selectedTime.toTimeString().slice(0, 5); // HH:MM format

      const updatedQuietHours = {
        ...preferences.quietHours,
        [timePickerMode === 'start' ? 'startTime' : 'endTime']: timeString
      };

      updatePreferences({
        ...preferences,
        quietHours: updatedQuietHours
      });
    }
  };

  const formatTime = (timeString: string): string => {
    const [hours, minutes] = timeString.split(':').map(Number);
    const date = new Date();
    date.setHours(hours, minutes);
    return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit', hour12: true });
  };

  const getNotificationIcon = (type: NotificationType): string => {
    switch (type) {
      case NotificationType.ORDER_REQUEST:
        return 'restaurant';
      case NotificationType.PAYMENT_RECEIVED:
        return 'wallet';
      case NotificationType.BONUS_EARNED:
        return 'trophy';
      case NotificationType.TRAINING_REMINDER:
        return 'school';
      case NotificationType.SAFETY_ALERT:
        return 'warning';
      case NotificationType.SYSTEM_UPDATE:
        return 'download';
      case NotificationType.PERFORMANCE_UPDATE:
        return 'analytics';
      default:
        return 'notifications';
    }
  };

  const getNotificationColor = (type: NotificationType): string => {
    switch (type) {
      case NotificationType.ORDER_REQUEST:
        return '#ef4444';
      case NotificationType.PAYMENT_RECEIVED:
        return '#10b981';
      case NotificationType.BONUS_EARNED:
        return '#fbbf24';
      case NotificationType.TRAINING_REMINDER:
        return '#3b82f6';
      case NotificationType.SAFETY_ALERT:
        return '#dc2626';
      case NotificationType.SYSTEM_UPDATE:
        return '#6b7280';
      case NotificationType.PERFORMANCE_UPDATE:
        return '#8b5cf6';
      default:
        return '#6b7280';
    }
  };

  const getPriorityColor = (priority: NotificationPriority): string => {
    switch (priority) {
      case NotificationPriority.HIGH:
        return '#ef4444';
      case NotificationPriority.MEDIUM:
        return '#f59e0b';
      case NotificationPriority.LOW:
        return '#10b981';
      default:
        return '#6b7280';
    }
  };

  const formatTimeAgo = (dateString: string): string => {
    if (!dateString) return 'N/A';

    const now = new Date();
    const date = new Date(dateString);

    // Check if date is valid
    if (isNaN(date.getTime())) return 'Invalid Date';

    const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60));

    if (diffInMinutes < 1) return 'Just now';
    if (diffInMinutes < 60) return `${diffInMinutes}m ago`;

    const diffInHours = Math.floor(diffInMinutes / 60);
    if (diffInHours < 24) return `${diffInHours}h ago`;

    const diffInDays = Math.floor(diffInHours / 24);
    if (diffInDays < 7) return `${diffInDays}d ago`;

    return date.toLocaleDateString();
  };

  const unreadCount = notifications.filter(n => !n.isRead).length;
  const sortedNotifications = [...notifications].sort((a, b) =>
    new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
  );

  const renderNotificationCard = ({ item: notification }: { item: PushNotification }) => (
    <TouchableOpacity
      onPress={() => markAsRead(notification.id)}
      onLongPress={() => {
        Alert.alert(
          'Delete Notification',
          'Are you sure you want to delete this notification?',
          [
            { text: 'Cancel', style: 'cancel' },
            {
              text: 'Delete',
              style: 'destructive',
              onPress: () => deleteNotification(notification.id),
            },
          ]
        );
      }}
      style={{
        backgroundColor: notification.isRead ? 'white' : '#fef2f2',
        borderRadius: 20,
        marginHorizontal: 20,
        marginBottom: 16,
        padding: 20,
        shadowColor: getNotificationColor(notification.type),
        shadowOffset: { width: 0, height: 8 },
        shadowOpacity: 0.15,
        shadowRadius: 16,
        elevation: 8,
        borderWidth: 2,
        borderColor: notification.isRead ? '#f1f5f9' : getNotificationColor(notification.type) + '20',
      }}
    >
      <View style={{
        flexDirection: 'row',
        alignItems: 'flex-start',
      }}>
        {/* Enhanced Notification Icon */}
        <View style={{
          width: 56,
          height: 56,
          borderRadius: 28,
          backgroundColor: getNotificationColor(notification.type) + '20',
          alignItems: 'center',
          justifyContent: 'center',
          marginRight: 16,
          borderWidth: 3,
          borderColor: getNotificationColor(notification.type),
          shadowColor: getNotificationColor(notification.type),
          shadowOffset: { width: 0, height: 4 },
          shadowOpacity: 0.3,
          shadowRadius: 8,
          elevation: 6,
        }}>
          <Ionicons
            name={getNotificationIcon(notification.type) as any}
            size={24}
            color={getNotificationColor(notification.type)}
          />
        </View>

        {/* Enhanced Notification Content */}
        <View style={{ flex: 1 }}>
          <View style={{
            flexDirection: 'row',
            alignItems: 'center',
            justifyContent: 'space-between',
            marginBottom: 8,
          }}>
            <Text style={{
              fontSize: 18,
              fontWeight: 'bold',
              color: '#111827',
              flex: 1,
            }}>
              {notification.title}
            </Text>

            {/* Enhanced Priority Indicator */}
            <View style={{
              width: 16,
              height: 16,
              borderRadius: 8,
              backgroundColor: getPriorityColor(notification.priority),
              marginLeft: 12,
              borderWidth: 2,
              borderColor: 'white',
              shadowColor: getPriorityColor(notification.priority),
              shadowOffset: { width: 0, height: 2 },
              shadowOpacity: 0.3,
              shadowRadius: 4,
              elevation: 4,
            }} />
          </View>

          <Text style={{
            fontSize: 16,
            color: '#374151',
            lineHeight: 24,
            marginBottom: 12,
          }}>
            {notification.message}
          </Text>

          <View style={{
            flexDirection: 'row',
            alignItems: 'center',
            justifyContent: 'space-between',
          }}>
            <Text style={{
              fontSize: 14,
              color: '#6b7280',
              fontWeight: '500',
            }}>
              {formatTimeAgo(notification.createdAt)}
            </Text>

            {!notification.isRead && (
              <View style={{
                width: 12,
                height: 12,
                borderRadius: 6,
                backgroundColor: '#dc2626',
                borderWidth: 2,
                borderColor: 'white',
                shadowColor: '#dc2626',
                shadowOffset: { width: 0, height: 2 },
                shadowOpacity: 0.3,
                shadowRadius: 4,
                elevation: 4,
              }} />
            )}
          </View>
        </View>
      </View>
    </TouchableOpacity>
  );

  const renderNotificationsTab = () => (
    <View style={{ flex: 1 }}>
      {/* Notifications Header */}
      <View style={{
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between',
        paddingHorizontal: 16,
        paddingVertical: 12,
        backgroundColor: 'white',
        borderBottomWidth: 1,
        borderBottomColor: '#e5e7eb',
      }}>
        <View>
          <Text style={{
            fontSize: 16,
            fontWeight: '600',
            color: '#1f2937',
          }}>
            Notifications
          </Text>
          {unreadCount > 0 && (
            <Text style={{
              fontSize: 12,
              color: '#f97316',
            }}>
              {unreadCount} unread
            </Text>
          )}
        </View>

        <View style={{ flexDirection: 'row' }}>
          {unreadCount > 0 && (
            <TouchableOpacity
              onPress={markAllAsRead}
              style={{
                paddingHorizontal: 12,
                paddingVertical: 6,
                borderRadius: 6,
                backgroundColor: '#f97316',
                marginRight: 8,
              }}
            >
              <Text style={{
                fontSize: 12,
                color: 'white',
                fontWeight: '600',
              }}>
                Mark All Read
              </Text>
            </TouchableOpacity>
          )}

          <TouchableOpacity
            onPress={clearAllNotifications}
            style={{
              paddingHorizontal: 12,
              paddingVertical: 6,
              borderRadius: 6,
              backgroundColor: '#ef4444',
            }}
          >
            <Text style={{
              fontSize: 12,
              color: 'white',
              fontWeight: '600',
            }}>
              Clear All
            </Text>
          </TouchableOpacity>
        </View>
      </View>

      {/* Enhanced Notifications List */}
      {sortedNotifications.length === 0 ? (
        <View style={{
          flex: 1,
          alignItems: 'center',
          justifyContent: 'center',
          paddingHorizontal: 40,
        }}>
          <View style={{
            width: 120,
            height: 120,
            backgroundColor: '#fef2f2',
            borderRadius: 60,
            justifyContent: 'center',
            alignItems: 'center',
            marginBottom: 24,
            borderWidth: 4,
            borderColor: '#dc2626',
            shadowColor: '#dc2626',
            shadowOffset: { width: 0, height: 8 },
            shadowOpacity: 0.2,
            shadowRadius: 16,
            elevation: 12,
          }}>
            <Ionicons name="notifications" size={48} color="#dc2626" />
          </View>
          <Text style={{
            fontSize: 24,
            fontWeight: 'bold',
            color: '#111827',
            marginBottom: 12,
            textAlign: 'center',
          }}>
            No Notifications
          </Text>
          <Text style={{
            fontSize: 16,
            color: '#6b7280',
            textAlign: 'center',
            lineHeight: 24,
            maxWidth: 280,
          }}>
            You're all caught up! New notifications will appear here.
          </Text>
        </View>
      ) : (
        <FlatList
          data={sortedNotifications}
          renderItem={renderNotificationCard}
          keyExtractor={(item) => item.id}
          contentContainerStyle={{
            paddingVertical: 16,
          }}
          showsVerticalScrollIndicator={false}
        />
      )}
    </View>
  );

  const renderPreferenceSection = (
    title: string,
    preference: {
      enabled: boolean;
      sound: boolean;
      vibration: boolean;
      priority: NotificationPriority;
    },
    onUpdate: (newPreference: any) => void
  ) => (
    <View style={{
      backgroundColor: 'white',
      borderRadius: 12,
      marginHorizontal: 16,
      marginBottom: 16,
      padding: 16,
    }}>
      <View style={{
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between',
        marginBottom: 16,
      }}>
        <Text style={{
          fontSize: 16,
          fontWeight: '600',
          color: '#1f2937',
        }}>
          {title}
        </Text>
        <Switch
          value={preference.enabled}
          onValueChange={(enabled) => onUpdate({ ...preference, enabled })}
          trackColor={{ false: '#f3f4f6', true: '#f97316' }}
          thumbColor={preference.enabled ? 'white' : '#f4f3f4'}
        />
      </View>

      {preference.enabled && (
        <View style={{ paddingLeft: 16 }}>
          <View style={{
            flexDirection: 'row',
            alignItems: 'center',
            justifyContent: 'space-between',
            marginBottom: 12,
          }}>
            <Text style={{
              fontSize: 14,
              color: '#6b7280',
            }}>
              Sound
            </Text>
            <Switch
              value={preference.sound}
              onValueChange={(sound) => onUpdate({ ...preference, sound })}
              trackColor={{ false: '#f3f4f6', true: '#f97316' }}
              thumbColor={preference.sound ? 'white' : '#f4f3f4'}
            />
          </View>

          <View style={{
            flexDirection: 'row',
            alignItems: 'center',
            justifyContent: 'space-between',
            marginBottom: 12,
          }}>
            <Text style={{
              fontSize: 14,
              color: '#6b7280',
            }}>
              Vibration
            </Text>
            <Switch
              value={preference.vibration}
              onValueChange={(vibration) => onUpdate({ ...preference, vibration })}
              trackColor={{ false: '#f3f4f6', true: '#f97316' }}
              thumbColor={preference.vibration ? 'white' : '#f4f3f4'}
            />
          </View>

          <View style={{
            flexDirection: 'row',
            alignItems: 'center',
            justifyContent: 'space-between',
          }}>
            <Text style={{
              fontSize: 14,
              color: '#6b7280',
            }}>
              Priority
            </Text>
            <View style={{
              flexDirection: 'row',
              backgroundColor: '#f3f4f6',
              borderRadius: 8,
              padding: 2,
            }}>
              {(['LOW', 'MEDIUM', 'HIGH'] as const).map((priority) => (
                <TouchableOpacity
                  key={priority}
                  onPress={() => onUpdate({ ...preference, priority: NotificationPriority[priority] })}
                  style={{
                    paddingHorizontal: 12,
                    paddingVertical: 4,
                    borderRadius: 6,
                    backgroundColor: preference.priority === NotificationPriority[priority] ? '#f97316' : 'transparent',
                  }}
                >
                  <Text style={{
                    fontSize: 12,
                    fontWeight: '600',
                    color: preference.priority === NotificationPriority[priority] ? 'white' : '#6b7280',
                  }}>
                    {priority}
                  </Text>
                </TouchableOpacity>
              ))}
            </View>
          </View>
        </View>
      )}
    </View>
  );

  const renderSettingsTab = () => {
    if (!preferences) return null;

    return (
      <ScrollView style={{ flex: 1 }}>
        {/* Global Settings */}
        <View style={{
          backgroundColor: 'white',
          borderRadius: 12,
          marginHorizontal: 16,
          marginTop: 16,
          marginBottom: 16,
          padding: 16,
        }}>
          <Text style={{
            fontSize: 18,
            fontWeight: '600',
            color: '#1f2937',
            marginBottom: 16,
          }}>
            Global Settings
          </Text>

          <View style={{
            flexDirection: 'row',
            alignItems: 'center',
            justifyContent: 'space-between',
            marginBottom: 12,
          }}>
            <Text style={{
              fontSize: 16,
              color: '#374151',
            }}>
              Master Notifications
            </Text>
            <Switch
              value={preferences.globalSettings.masterEnabled}
              onValueChange={(masterEnabled) =>
                updatePreferences({
                  ...preferences,
                  globalSettings: { ...preferences.globalSettings, masterEnabled }
                })
              }
              trackColor={{ false: '#f3f4f6', true: '#f97316' }}
              thumbColor={preferences.globalSettings.masterEnabled ? 'white' : '#f4f3f4'}
            />
          </View>

          {preferences.globalSettings.masterEnabled && (
            <>
              <View style={{
                flexDirection: 'row',
                alignItems: 'center',
                justifyContent: 'space-between',
                marginBottom: 12,
              }}>
                <Text style={{
                  fontSize: 14,
                  color: '#6b7280',
                }}>
                  Sound
                </Text>
                <Switch
                  value={preferences.globalSettings.soundEnabled}
                  onValueChange={(soundEnabled) =>
                    updatePreferences({
                      ...preferences,
                      globalSettings: { ...preferences.globalSettings, soundEnabled }
                    })
                  }
                  trackColor={{ false: '#f3f4f6', true: '#f97316' }}
                  thumbColor={preferences.globalSettings.soundEnabled ? 'white' : '#f4f3f4'}
                />
              </View>

              <View style={{
                flexDirection: 'row',
                alignItems: 'center',
                justifyContent: 'space-between',
                marginBottom: 12,
              }}>
                <Text style={{
                  fontSize: 14,
                  color: '#6b7280',
                }}>
                  Vibration
                </Text>
                <Switch
                  value={preferences.globalSettings.vibrationEnabled}
                  onValueChange={(vibrationEnabled) =>
                    updatePreferences({
                      ...preferences,
                      globalSettings: { ...preferences.globalSettings, vibrationEnabled }
                    })
                  }
                  trackColor={{ false: '#f3f4f6', true: '#f97316' }}
                  thumbColor={preferences.globalSettings.vibrationEnabled ? 'white' : '#f4f3f4'}
                />
              </View>

              <View style={{
                flexDirection: 'row',
                alignItems: 'center',
                justifyContent: 'space-between',
              }}>
                <Text style={{
                  fontSize: 14,
                  color: '#6b7280',
                }}>
                  Badge Count
                </Text>
                <Switch
                  value={preferences.globalSettings.badgeEnabled}
                  onValueChange={(badgeEnabled) =>
                    updatePreferences({
                      ...preferences,
                      globalSettings: { ...preferences.globalSettings, badgeEnabled }
                    })
                  }
                  trackColor={{ false: '#f3f4f6', true: '#f97316' }}
                  thumbColor={preferences.globalSettings.badgeEnabled ? 'white' : '#f4f3f4'}
                />
              </View>
            </>
          )}
        </View>

        {/* Quiet Hours */}
        <View style={{
          backgroundColor: 'white',
          borderRadius: 12,
          marginHorizontal: 16,
          marginBottom: 16,
          padding: 16,
        }}>
          <View style={{
            flexDirection: 'row',
            alignItems: 'center',
            justifyContent: 'space-between',
            marginBottom: 16,
          }}>
            <Text style={{
              fontSize: 16,
              fontWeight: '600',
              color: '#1f2937',
            }}>
              Quiet Hours
            </Text>
            <Switch
              value={preferences.quietHours.enabled}
              onValueChange={(enabled) =>
                updatePreferences({
                  ...preferences,
                  quietHours: { ...preferences.quietHours, enabled }
                })
              }
              trackColor={{ false: '#f3f4f6', true: '#f97316' }}
              thumbColor={preferences.quietHours.enabled ? 'white' : '#f4f3f4'}
            />
          </View>

          {preferences.quietHours.enabled && (
            <>
              <View style={{
                flexDirection: 'row',
                alignItems: 'center',
                justifyContent: 'space-between',
                marginBottom: 12,
              }}>
                <Text style={{
                  fontSize: 14,
                  color: '#6b7280',
                }}>
                  {formatTime(preferences.quietHours.startTime)} - {formatTime(preferences.quietHours.endTime)}
                </Text>
                <Text style={{
                  fontSize: 12,
                  color: '#9ca3af',
                }}>
                  Notifications will be silenced during these hours
                </Text>
              </View>

              <View style={{
                flexDirection: 'row',
                gap: 12,
              }}>
                <TouchableOpacity
                  onPress={() => openTimePicker('start')}
                  style={{
                    flex: 1,
                    paddingHorizontal: 12,
                    paddingVertical: 8,
                    borderRadius: 8,
                    backgroundColor: '#f3f4f6',
                    alignItems: 'center',
                  }}
                >
                  <Text style={{
                    fontSize: 12,
                    color: '#6b7280',
                    fontWeight: '600',
                    marginBottom: 2,
                  }}>
                    Start Time
                  </Text>
                  <Text style={{
                    fontSize: 14,
                    color: '#1f2937',
                    fontWeight: '600',
                  }}>
                    {formatTime(preferences.quietHours.startTime)}
                  </Text>
                </TouchableOpacity>

                <TouchableOpacity
                  onPress={() => openTimePicker('end')}
                  style={{
                    flex: 1,
                    paddingHorizontal: 12,
                    paddingVertical: 8,
                    borderRadius: 8,
                    backgroundColor: '#f3f4f6',
                    alignItems: 'center',
                  }}
                >
                  <Text style={{
                    fontSize: 12,
                    color: '#6b7280',
                    fontWeight: '600',
                    marginBottom: 2,
                  }}>
                    End Time
                  </Text>
                  <Text style={{
                    fontSize: 14,
                    color: '#1f2937',
                    fontWeight: '600',
                  }}>
                    {formatTime(preferences.quietHours.endTime)}
                  </Text>
                </TouchableOpacity>
              </View>
            </>
          )}
        </View>

        {/* Notification Categories */}
        <Text style={{
          fontSize: 18,
          fontWeight: '600',
          color: '#1f2937',
          marginHorizontal: 16,
          marginBottom: 16,
        }}>
          Notification Categories
        </Text>

        {renderPreferenceSection(
          'Order Requests',
          preferences.orderRequests,
          (newPreference) => updatePreferences({
            ...preferences,
            orderRequests: newPreference
          })
        )}

        {renderPreferenceSection(
          'Payment Updates',
          preferences.paymentUpdates,
          (newPreference) => updatePreferences({
            ...preferences,
            paymentUpdates: newPreference
          })
        )}

        {renderPreferenceSection(
          'Bonus Alerts',
          preferences.bonusAlerts,
          (newPreference) => updatePreferences({
            ...preferences,
            bonusAlerts: newPreference
          })
        )}

        {renderPreferenceSection(
          'Training Reminders',
          preferences.trainingReminders,
          (newPreference) => updatePreferences({
            ...preferences,
            trainingReminders: newPreference
          })
        )}

        {renderPreferenceSection(
          'Safety Alerts',
          preferences.safetyAlerts,
          (newPreference) => updatePreferences({
            ...preferences,
            safetyAlerts: newPreference
          })
        )}



        <View style={{ height: 32 }} />
      </ScrollView>
    );
  };

  return (
    <View style={{ flex: 1, backgroundColor: '#f1f5f9' }}>
      <SafeAreaView style={{ flex: 1 }}>
        {/* Enhanced Header */}
        <View style={{
          backgroundColor: '#dc2626',
          paddingHorizontal: 20,
          paddingTop: 20,
          paddingBottom: 28,
          borderBottomLeftRadius: 28,
          borderBottomRightRadius: 28,
          shadowColor: '#dc2626',
          shadowOffset: { width: 0, height: 12 },
          shadowOpacity: 0.3,
          shadowRadius: 20,
          elevation: 16,
        }}>
          <View style={{
            flexDirection: 'row',
            alignItems: 'center',
            marginBottom: 20,
          }}>
            <TouchableOpacity
              onPress={() => navigation.goBack()}
              style={{
                width: 48,
                height: 48,
                borderRadius: 24,
                backgroundColor: 'rgba(255,255,255,0.2)',
                justifyContent: 'center',
                alignItems: 'center',
                marginRight: 16,
                borderWidth: 2,
                borderColor: 'rgba(255,255,255,0.3)',
              }}
            >
              <Ionicons name="arrow-back" size={24} color="white" />
            </TouchableOpacity>
            <View style={{ flex: 1 }}>
              <Text style={{
                fontSize: 24,
                fontWeight: 'bold',
                color: 'white',
                marginBottom: 4,
              }}>
                Notifications
              </Text>
              <Text style={{
                fontSize: 14,
                color: 'rgba(255,255,255,0.8)',
              }}>
                {unreadCount > 0 ? `${unreadCount} unread messages` : 'All caught up!'}
              </Text>
            </View>

            {unreadCount > 0 && (
              <View style={{
                backgroundColor: 'rgba(255,255,255,0.2)',
                borderRadius: 16,
                minWidth: 32,
                height: 32,
                alignItems: 'center',
                justifyContent: 'center',
            marginRight: 8,
          }}>
            <Text style={{
              fontSize: 12,
              color: 'white',
              fontWeight: '600',
            }}>
              {unreadCount > 99 ? '99+' : unreadCount}
            </Text>
          </View>
        )}

        <TouchableOpacity
          onPress={loadNotificationData}
          style={{
            width: 40,
            height: 40,
            alignItems: 'center',
            justifyContent: 'center',
          }}
        >
          <Ionicons name="refresh" size={24} color="#374151" />
        </TouchableOpacity>
          </View>
        </View>

      {/* Tab Navigation */}
      <View style={{
        flexDirection: 'row',
        backgroundColor: 'white',
        borderBottomWidth: 1,
        borderBottomColor: '#e5e7eb',
      }}>
        {(['notifications', 'settings'] as const).map((tab) => (
          <TouchableOpacity
            key={tab}
            onPress={() => setActiveTab(tab)}
            style={{
              flex: 1,
              paddingVertical: 12,
              borderBottomWidth: 2,
              borderBottomColor: activeTab === tab ? '#f97316' : 'transparent',
            }}
          >
            <Text style={{
              textAlign: 'center',
              fontSize: 14,
              fontWeight: '600',
              color: activeTab === tab ? '#f97316' : '#6b7280',
              textTransform: 'capitalize',
            }}>
              {tab}
            </Text>
          </TouchableOpacity>
        ))}
      </View>

      {/* Tab Content */}
      {loading ? (
        <View style={{
          flex: 1,
          alignItems: 'center',
          justifyContent: 'center',
        }}>
          <Text style={{
            fontSize: 16,
            color: '#6b7280',
          }}>
            Loading notifications...
          </Text>
        </View>
      ) : (
        <>
          {activeTab === 'notifications' && renderNotificationsTab()}
          {activeTab === 'settings' && renderSettingsTab()}
        </>
      )}

      {/* Time Picker Modal */}
      {showTimePicker && (
        <>
          {Platform.OS === 'ios' ? (
            <Modal
              visible={showTimePicker}
              transparent={true}
              animationType="slide"
            >
              <View style={{
                flex: 1,
                justifyContent: 'flex-end',
                backgroundColor: 'rgba(0, 0, 0, 0.5)',
              }}>
                <View style={{
                  backgroundColor: 'white',
                  borderTopLeftRadius: 20,
                  borderTopRightRadius: 20,
                  paddingTop: 20,
                }}>
                  <View style={{
                    flexDirection: 'row',
                    justifyContent: 'space-between',
                    alignItems: 'center',
                    paddingHorizontal: 20,
                    paddingBottom: 10,
                  }}>
                    <TouchableOpacity onPress={() => setShowTimePicker(false)}>
                      <Text style={{ fontSize: 16, color: '#f97316' }}>Cancel</Text>
                    </TouchableOpacity>
                    <Text style={{ fontSize: 16, fontWeight: '600', color: '#1f2937' }}>
                      Select {timePickerMode === 'start' ? 'Start' : 'End'} Time
                    </Text>
                    <TouchableOpacity onPress={() => setShowTimePicker(false)}>
                      <Text style={{ fontSize: 16, color: '#f97316', fontWeight: '600' }}>Done</Text>
                    </TouchableOpacity>
                  </View>
                  <DateTimePicker
                    value={tempTime}
                    mode="time"
                    display="spinner"
                    onChange={handleTimeChange}
                    style={{ backgroundColor: 'white' }}
                  />
                  <View style={{ height: 20 }} />
                </View>
              </View>
            </Modal>
          ) : (
            <DateTimePicker
              value={tempTime}
              mode="time"
              display="default"
              onChange={handleTimeChange}
            />
          )}
        </>
      )}
      </SafeAreaView>
    </View>
  );
};

export default NotificationsScreen;
