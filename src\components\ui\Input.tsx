import React, { useState } from 'react';
import {
  View,
  TextInput,
  Text,
  TouchableOpacity,
  TextInputProps,
  ViewStyle,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';

interface InputProps extends TextInputProps {
  label?: string;
  error?: string;
  hint?: string;
  leftIcon?: keyof typeof Ionicons.glyphMap;
  rightIcon?: keyof typeof Ionicons.glyphMap;
  onRightIconPress?: () => void;
  required?: boolean;
  disabled?: boolean;
  containerStyle?: ViewStyle;
}

const Input: React.FC<InputProps> = ({
  label,
  error,
  hint,
  leftIcon,
  rightIcon,
  onRightIconPress,
  required = false,
  disabled = false,
  containerStyle,
  ...props
}) => {
  const [isFocused, setIsFocused] = useState(false);

  return (
    <View style={[{ marginBottom: 20 }, containerStyle]}>
      {/* Label */}
      {label && (
        <Text style={{
          fontSize: 16,
          fontWeight: '600',
          color: '#111827',
          marginBottom: 8,
        }}>
          {label} {required && <Text style={{ color: '#dc2626' }}>*</Text>}
        </Text>
      )}

      {/* Input Container */}
      <View style={{
        flexDirection: 'row',
        alignItems: 'center',
        backgroundColor: 'white',
        borderRadius: 12,
        borderWidth: 2,
        borderColor: error ? '#dc2626' : (isFocused ? '#dc2626' : '#d1d5db'),
        paddingHorizontal: 16,
        paddingVertical: 12,
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 1 },
        shadowOpacity: 0.05,
        shadowRadius: 2,
        elevation: 1,
        opacity: disabled ? 0.6 : 1,
      }}>
        {/* Left Icon */}
        {leftIcon && (
          <Ionicons 
            name={leftIcon} 
            size={20} 
            color={error ? '#dc2626' : '#dc2626'} 
            style={{ marginRight: 12 }}
          />
        )}

        {/* Text Input */}
        <TextInput
          style={{
            flex: 1,
            fontSize: 16,
            color: '#111827',
          }}
          placeholderTextColor="#9ca3af"
          editable={!disabled}
          onFocus={() => setIsFocused(true)}
          onBlur={() => setIsFocused(false)}
          {...props}
        />

        {/* Right Icon */}
        {rightIcon && (
          <TouchableOpacity
            onPress={onRightIconPress}
            style={{ padding: 4 }}
            disabled={disabled}
          >
            <Ionicons 
              name={rightIcon} 
              size={20} 
              color="#dc2626" 
            />
          </TouchableOpacity>
        )}
      </View>

      {/* Error Message */}
      {error && (
        <Text style={{
          fontSize: 14,
          color: '#dc2626',
          marginTop: 4,
        }}>
          {error}
        </Text>
      )}

      {/* Hint Message */}
      {hint && !error && (
        <Text style={{
          fontSize: 14,
          color: '#6b7280',
          marginTop: 4,
        }}>
          {hint}
        </Text>
      )}
    </View>
  );
};

export default Input;
