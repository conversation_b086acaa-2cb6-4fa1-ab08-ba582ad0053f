import { VehicleType } from './user';

export { VehicleType };

export interface LoginCredentials {
  email: string;
  password: string;
  rememberMe?: boolean;
}

export interface PhoneLoginCredentials {
  phoneNumber: string;
  verificationCode?: string;
}

export interface RegistrationData {
  firstName: string;
  lastName?: string;
  email: string;
  phone: string;
  password: string;
  dateOfBirth?: string;
  cnic?: string;
  passport?: string;
  address?: string;
  city?: string;
  vehicleType: string;
  vehicleMake?: string;
  vehicleModel?: string;
  vehicleYear?: string;
  vehiclePlateNumber?: string;
  vehicleColor?: string;
  bankAccountType: string;
  bankName?: string;
  accountNumber?: string;
  iban?: string;
  mobileWalletProvider?: string;
  mobileWalletNumber?: string;
  documents?: { [key: string]: any };
  agreeToTerms: boolean;
}

export interface RegistrationData {
  // Personal Information
  firstName: string;
  lastName?: string;
  email: string;
  phone: string;
  password: string;
  dateOfBirth?: string;
  cnic?: string;
  passport?: string;
  address?: string;
  city?: string;
  emergencyContact?: {
    name: string;
    phone: string;
    relationship: string;
  };

  // Vehicle Information
  vehicleType: string;
  vehicleMake?: string;
  vehicleModel?: string;
  vehicleYear?: string;
  vehiclePlateNumber?: string;
  vehicleColor?: string;

  // Bank Information
  bankAccountType: string;
  bankName?: string;
  accountNumber?: string;
  iban?: string;
  mobileWalletProvider?: string;
  mobileWalletNumber?: string;

  // Documents (file uploads)
  documents?: {
    [key: string]: {
      uri: string;
      type: string;
      name: string;
    };
  };

  // Terms and conditions
  agreeToTerms: boolean;
}

export interface AuthTokens {
  accessToken: string;
  refreshToken: string;
  expiresAt?: number;
  expiresIn?: number;
}

export interface AuthUser {
  id: string;
  email: string;
  phoneNumber?: string;
  phone?: string;
  firstName: string;
  lastName: string;
  profileImage?: string;
  isVerified: boolean;
  isEmailVerified?: boolean;
  isPhoneVerified?: boolean;
  verificationStatus: 'pending' | 'verified' | 'rejected';
  cnic?: string;
  passport?: string;
  vehicleType?: VehicleType;
  vehicleDetails?: VehicleDetails;
  vehicle?: any;
  bankDetails?: BankDetails;
  bankInfo?: any;
  documentStatus?: any;
  profile?: any;
  isProfileComplete?: boolean;
  isDocumentsVerified?: boolean;
  canStartDelivery?: boolean;
  canGoOnline?: boolean;
  isDemoAccount: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface AuthState {
  user: AuthUser | null;
  tokens: AuthTokens | null;
  isLoading: boolean;
  isAuthenticated: boolean;
  error: string | null;
}

export interface AuthContextType {
  state: AuthState;
  login: (credentials: LoginCredentials) => Promise<void>;
  loginWithPhone: (credentials: PhoneLoginCredentials) => Promise<void>;
  logout: () => Promise<void>;
  refreshToken: () => Promise<void>;
  clearError: () => void;
  register: (userData: RegistrationData) => Promise<any>;
  checkVerificationStatus: () => Promise<void>;
  canGoOnline: () => boolean;
  uploadDocument: (documentType: string, file: any, metadata?: any) => Promise<any>;
}

export interface LoginResponse {
  user: AuthUser;
  tokens: AuthTokens;
  message: string;
}

export interface ApiError {
  message: string;
  code: string;
  statusCode: number;
  details?: Record<string, any>;
}

// Registration Types
export interface RegistrationData {
  firstName: string;
  lastName: string;
  email: string;
  phoneNumber: string;
  password: string;
  confirmPassword: string;
  cnic?: string;
  passport?: string;
  vehicleType: VehicleType;
  vehicleDetails: VehicleDetails;
  bankDetails: BankDetails;
  documents: DocumentUploads;
  agreeToTerms: boolean;
}

// VehicleType is imported from user.ts

export interface VehicleDetails {
  make: string;
  model: string;
  year: number;
  color: string;
  plateNumber: string;
  registrationNumber?: string;
}

export interface BankDetails {
  accountType: BankAccountType;
  accountNumber: string;
  accountTitle: string;
  bankName?: string;
  iban?: string;
  branchCode?: string;
}

export enum BankAccountType {
  BANK_ACCOUNT = 'bank_account',
  EASYPAISA = 'easypaisa',
  JAZZCASH = 'jazzcash',
}

export interface DocumentUploads {
  // Base documents
  cnicFront?: string;
  cnicBack?: string;
  passport?: string;

  // Vehicle-specific documents
  drivingLicense?: string;
  motorcycleLicense?: string;
  scooterLicense?: string;
  vehicleRegistration?: string;

  // Vehicle photos
  bicyclePhoto?: string;
  motorcyclePhoto?: string;
  carPhoto?: string;
  scooterPhoto?: string;
}

export interface DocumentStatus {
  cnic: DocumentVerificationStatus;
  passport: DocumentVerificationStatus;
  drivingLicense: DocumentVerificationStatus;
  vehicleRegistration: DocumentVerificationStatus;
  profilePhoto: DocumentVerificationStatus;
  overall: DocumentVerificationStatus;
  rejectionReasons?: string[];
  lastUpdated: string;
}

export enum DocumentVerificationStatus {
  PENDING = 'pending',
  APPROVED = 'approved',
  REJECTED = 'rejected',
  NOT_SUBMITTED = 'not_submitted',
}

// Onboarding Types
export interface OnboardingSlide {
  id: string;
  title: string;
  description: string;
  image: string;
  icon?: string;
}
