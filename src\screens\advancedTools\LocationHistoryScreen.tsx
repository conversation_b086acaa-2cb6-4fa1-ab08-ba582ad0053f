import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  RefreshControl,
  TextInput,
  Modal,
  Alert,
  StatusBar,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import { useNavigation } from '@react-navigation/native';
import { Card, Button, Badge, LoadingSpinner } from '../../components/ui';
import { LocationData } from '../../types/advancedTools';
import { formatDate, formatTime } from '../../utils/helpers';

interface LocationFilter {
  dateRange: 'today' | 'week' | 'month' | 'all';
  accuracy: 'all' | 'high' | 'medium' | 'low';
  speed: 'all' | 'stationary' | 'moving' | 'fast';
}

const LocationHistoryScreen: React.FC = () => {
  const navigation = useNavigation();
  const [locationHistory, setLocationHistory] = useState<LocationData[]>([]);
  const [filteredHistory, setFilteredHistory] = useState<LocationData[]>([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [showFilters, setShowFilters] = useState(false);
  const [selectedLocation, setSelectedLocation] = useState<LocationData | null>(null);
  const [showDetailModal, setShowDetailModal] = useState(false);
  const [filter, setFilter] = useState<LocationFilter>({
    dateRange: 'week',
    accuracy: 'all',
    speed: 'all'
  });

  // Mock comprehensive location history
  const mockLocationHistory: LocationData[] = Array.from({ length: 50 }, (_, index) => ({
    id: `loc-${index + 1}`,
    riderId: 'rider-1',
    latitude: 24.8607 + (Math.random() - 0.5) * 0.1,
    longitude: 67.0011 + (Math.random() - 0.5) * 0.1,
    accuracy: Math.random() * 10 + 2,
    altitude: Math.random() * 20 + 5,
    speed: Math.random() * 60,
    heading: Math.random() * 360,
    timestamp: new Date(Date.now() - index * 15 * 60 * 1000).toISOString(),
    address: [
      'Gulshan-e-Iqbal, Block 13-D, Karachi',
      'University Road, Gulshan-e-Iqbal, Karachi',
      'Shahrah-e-Faisal, Karachi',
      'Clifton Block 2, Karachi',
      'DHA Phase 5, Karachi',
      'Saddar Town, Karachi',
      'North Nazimabad, Karachi',
      'Korangi Industrial Area, Karachi'
    ][index % 8],
    isOnline: Math.random() > 0.2,
    batteryLevel: Math.max(10, 100 - index * 2),
    networkType: ['4G', '3G', 'WiFi'][Math.floor(Math.random() * 3)] as any,
  }));

  useEffect(() => {
    loadLocationHistory();
  }, []);

  useEffect(() => {
    applyFilters();
  }, [locationHistory, searchQuery, filter]);

  const loadLocationHistory = async () => {
    try {
      setLoading(true);
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      setLocationHistory(mockLocationHistory);
    } catch (error) {
      console.error('Error loading location history:', error);
      Alert.alert('Error', 'Failed to load location history');
    } finally {
      setLoading(false);
    }
  };

  const onRefresh = async () => {
    setRefreshing(true);
    await loadLocationHistory();
    setRefreshing(false);
  };

  const applyFilters = () => {
    let filtered = [...locationHistory];

    // Apply search filter
    if (searchQuery.trim()) {
      filtered = filtered.filter(location =>
        location.address.toLowerCase().includes(searchQuery.toLowerCase()) ||
        location.id.toLowerCase().includes(searchQuery.toLowerCase())
      );
    }

    // Apply date range filter
    const now = new Date();
    switch (filter.dateRange) {
      case 'today':
        filtered = filtered.filter(location => {
          const locationDate = new Date(location.timestamp);
          return locationDate.toDateString() === now.toDateString();
        });
        break;
      case 'week':
        const weekAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
        filtered = filtered.filter(location => new Date(location.timestamp) >= weekAgo);
        break;
      case 'month':
        const monthAgo = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
        filtered = filtered.filter(location => new Date(location.timestamp) >= monthAgo);
        break;
    }

    // Apply accuracy filter
    if (filter.accuracy !== 'all') {
      filtered = filtered.filter(location => {
        switch (filter.accuracy) {
          case 'high': return location.accuracy <= 5;
          case 'medium': return location.accuracy > 5 && location.accuracy <= 15;
          case 'low': return location.accuracy > 15;
          default: return true;
        }
      });
    }

    // Apply speed filter
    if (filter.speed !== 'all') {
      filtered = filtered.filter(location => {
        switch (filter.speed) {
          case 'stationary': return location.speed <= 5;
          case 'moving': return location.speed > 5 && location.speed <= 40;
          case 'fast': return location.speed > 40;
          default: return true;
        }
      });
    }

    setFilteredHistory(filtered);
  };

  const formatSpeed = (speed: number): string => {
    return `${speed.toFixed(1)} km/h`;
  };

  const formatCoordinate = (coord: number): string => {
    return coord.toFixed(6);
  };

  const formatTimeAgo = (dateString: string): string => {
    const now = new Date();
    const date = new Date(dateString);
    const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60));

    if (diffInMinutes < 1) return 'Just now';
    if (diffInMinutes < 60) return `${diffInMinutes}m ago`;

    const diffInHours = Math.floor(diffInMinutes / 60);
    if (diffInHours < 24) return `${diffInHours}h ago`;

    const diffInDays = Math.floor(diffInHours / 24);
    return `${diffInDays}d ago`;
  };

  const getAccuracyColor = (accuracy: number): string => {
    if (accuracy <= 5) return '#10b981'; // High accuracy - green
    if (accuracy <= 15) return '#f59e0b'; // Medium accuracy - yellow
    return '#ef4444'; // Low accuracy - red
  };

  const getSpeedColor = (speed: number): string => {
    if (speed <= 5) return '#6b7280'; // Stationary - gray
    if (speed <= 40) return '#3b82f6'; // Normal speed - blue
    return '#ef4444'; // Fast speed - red
  };

  const handleLocationDetail = (location: LocationData) => {
    setSelectedLocation(location);
    setShowDetailModal(true);
  };

  const exportLocationData = () => {
    Alert.alert(
      'Export Location Data',
      'Choose export format:',
      [
        { text: 'Cancel', style: 'cancel' },
        { text: 'CSV', onPress: () => Alert.alert('Success', 'Location data exported as CSV') },
        { text: 'JSON', onPress: () => Alert.alert('Success', 'Location data exported as JSON') },
        { text: 'GPX', onPress: () => Alert.alert('Success', 'Location data exported as GPX') }
      ]
    );
  };

  const clearLocationHistory = () => {
    Alert.alert(
      'Clear Location History',
      'Are you sure you want to delete all location history? This action cannot be undone.',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Clear All',
          style: 'destructive',
          onPress: () => {
            setLocationHistory([]);
            Alert.alert('Success', 'Location history cleared');
          }
        }
      ]
    );
  };

  const renderHeader = () => (
    <View style={{
      paddingHorizontal: 20,
      paddingVertical: 16,
      backgroundColor: '#ffffff',
      borderBottomWidth: 1,
      borderBottomColor: '#e5e7eb',
      flexDirection: 'row',
      alignItems: 'center',
    }}>
      <TouchableOpacity
        onPress={() => navigation.goBack()}
        style={{ marginRight: 16 }}
      >
        <Ionicons name="arrow-back" size={24} color="#111827" />
      </TouchableOpacity>
      <Text style={{ fontSize: 20, fontWeight: 'bold', color: '#111827', flex: 1 }}>
        Location History
      </Text>
      <TouchableOpacity
        onPress={() => setShowFilters(true)}
        style={{
          padding: 8,
          borderRadius: 8,
          backgroundColor: '#f3f4f6',
          marginRight: 8,
        }}
      >
        <Ionicons name="filter" size={20} color="#6b7280" />
      </TouchableOpacity>
      <TouchableOpacity
        onPress={exportLocationData}
        style={{
          padding: 8,
          borderRadius: 8,
          backgroundColor: '#f3f4f6',
        }}
      >
        <Ionicons name="download" size={20} color="#6b7280" />
      </TouchableOpacity>
    </View>
  );

  const renderSummaryCards = () => (
    <View style={{ padding: 20, gap: 12 }}>
      <View style={{ flexDirection: 'row', gap: 12 }}>
        <Card variant="elevated" style={{ flex: 1 }} padding="md">
          <Text style={{ fontSize: 24, fontWeight: 'bold', color: '#3b82f6' }}>
            {filteredHistory.length}
          </Text>
          <Text style={{ fontSize: 12, color: '#6b7280', marginTop: 4 }}>
            Total Locations
          </Text>
        </Card>
        
        <Card variant="elevated" style={{ flex: 1 }} padding="md">
          <Text style={{ fontSize: 24, fontWeight: 'bold', color: '#10b981' }}>
            {filteredHistory.filter(l => l.accuracy <= 5).length}
          </Text>
          <Text style={{ fontSize: 12, color: '#6b7280', marginTop: 4 }}>
            High Accuracy
          </Text>
        </Card>
      </View>
      
      <View style={{ flexDirection: 'row', gap: 12 }}>
        <Card variant="elevated" style={{ flex: 1 }} padding="md">
          <Text style={{ fontSize: 24, fontWeight: 'bold', color: '#f59e0b' }}>
            {filteredHistory.length > 0 ? (filteredHistory.reduce((sum, l) => sum + l.speed, 0) / filteredHistory.length).toFixed(1) : '0'}
          </Text>
          <Text style={{ fontSize: 12, color: '#6b7280', marginTop: 4 }}>
            Avg Speed (km/h)
          </Text>
        </Card>
        
        <Card variant="elevated" style={{ flex: 1 }} padding="md">
          <Text style={{ fontSize: 24, fontWeight: 'bold', color: '#8b5cf6' }}>
            {Math.round((filteredHistory.filter(l => l.isOnline).length / Math.max(filteredHistory.length, 1)) * 100)}%
          </Text>
          <Text style={{ fontSize: 12, color: '#6b7280', marginTop: 4 }}>
            Online Time
          </Text>
        </Card>
      </View>
    </View>
  );

  if (loading) {
    return (
      <SafeAreaView style={{ flex: 1, backgroundColor: '#f8fafc' }}>
        <LoadingSpinner message="Loading location history..." />
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={{ flex: 1, backgroundColor: '#f8fafc' }}>
      <StatusBar barStyle="dark-content" backgroundColor="#ffffff" />
      {renderHeader()}
      {renderSummaryCards()}
      
      {/* Search Bar */}
      <View style={{ paddingHorizontal: 20, marginBottom: 16 }}>
        <View style={{
          flexDirection: 'row',
          alignItems: 'center',
          backgroundColor: '#ffffff',
          borderRadius: 12,
          paddingHorizontal: 16,
          paddingVertical: 12,
          borderWidth: 1,
          borderColor: '#e5e7eb',
        }}>
          <Ionicons name="search" size={20} color="#6b7280" style={{ marginRight: 12 }} />
          <TextInput
            placeholder="Search by address or location ID..."
            value={searchQuery}
            onChangeText={setSearchQuery}
            style={{
              flex: 1,
              fontSize: 16,
              color: '#111827',
            }}
            placeholderTextColor="#9ca3af"
          />
        </View>
      </View>

      <ScrollView
        style={{ flex: 1 }}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
        showsVerticalScrollIndicator={false}
        contentContainerStyle={{ paddingBottom: 100 }}
      >
        {/* Location List */}
        <Card variant="elevated" margin="md" padding="lg">
          <View style={{ flexDirection: 'row', justifyContent: 'space-between', alignItems: 'center', marginBottom: 16 }}>
            <Text style={{ fontSize: 18, fontWeight: 'bold', color: '#111827' }}>
              Location History ({filteredHistory.length})
            </Text>
            {filteredHistory.length > 0 && (
              <TouchableOpacity onPress={clearLocationHistory}>
                <Text style={{ fontSize: 14, color: '#ef4444', fontWeight: '600' }}>
                  Clear All
                </Text>
              </TouchableOpacity>
            )}
          </View>

          {filteredHistory.length === 0 ? (
            <View style={{ alignItems: 'center', paddingVertical: 40 }}>
              <Ionicons name="location-outline" size={48} color="#d1d5db" />
              <Text style={{ fontSize: 16, color: '#6b7280', marginTop: 8 }}>
                No location history found
              </Text>
              <Text style={{ fontSize: 14, color: '#9ca3af', textAlign: 'center', marginTop: 4 }}>
                Try adjusting your search or filter criteria
              </Text>
            </View>
          ) : (
            <View style={{ gap: 0 }}>
              {filteredHistory.map((location, index) => (
                <TouchableOpacity
                  key={location.id}
                  onPress={() => handleLocationDetail(location)}
                  style={{
                    flexDirection: 'row',
                    alignItems: 'center',
                    paddingVertical: 16,
                    borderBottomWidth: index < filteredHistory.length - 1 ? 1 : 0,
                    borderBottomColor: '#f3f4f6',
                  }}
                >
                  {/* Location Icon */}
                  <View style={{
                    width: 48,
                    height: 48,
                    borderRadius: 24,
                    backgroundColor: location.isOnline ? '#10b981' + '20' : '#6b7280' + '20',
                    alignItems: 'center',
                    justifyContent: 'center',
                    marginRight: 12,
                  }}>
                    <Ionicons 
                      name={location.isOnline ? "location" : "location-outline"} 
                      size={24} 
                      color={location.isOnline ? '#10b981' : '#6b7280'} 
                    />
                  </View>

                  {/* Location Details */}
                  <View style={{ flex: 1 }}>
                    <Text style={{
                      fontSize: 16,
                      fontWeight: '600',
                      color: '#111827',
                      marginBottom: 4,
                    }}>
                      {location.address}
                    </Text>

                    <View style={{
                      flexDirection: 'row',
                      alignItems: 'center',
                      gap: 12,
                      marginBottom: 4,
                    }}>
                      <Text style={{
                        fontSize: 12,
                        color: getSpeedColor(location.speed),
                        fontWeight: '600',
                      }}>
                        {formatSpeed(location.speed)}
                      </Text>
                      <Text style={{
                        fontSize: 12,
                        color: getAccuracyColor(location.accuracy),
                        fontWeight: '600',
                      }}>
                        ±{location.accuracy.toFixed(1)}m
                      </Text>
                      <Text style={{
                        fontSize: 12,
                        color: '#6b7280',
                      }}>
                        {location.batteryLevel}%
                      </Text>
                    </View>

                    <Text style={{
                      fontSize: 12,
                      color: '#9ca3af',
                    }}>
                      {formatDate(location.timestamp)} • {formatTime(location.timestamp)} • {formatTimeAgo(location.timestamp)}
                    </Text>
                  </View>

                  {/* Status Badge */}
                  <View style={{ alignItems: 'flex-end' }}>
                    <Badge
                      text={location.isOnline ? 'Online' : 'Offline'}
                      style={{
                        backgroundColor: location.isOnline ? '#dcfce7' : '#f3f4f6',
                        color: location.isOnline ? '#16a34a' : '#6b7280',
                      }}
                    />
                    <Text style={{
                      fontSize: 10,
                      color: '#9ca3af',
                      marginTop: 4,
                    }}>
                      {location.networkType}
                    </Text>
                  </View>
                </TouchableOpacity>
              ))}
            </View>
          )}
        </Card>
      </ScrollView>
    </SafeAreaView>
  );
};

export default LocationHistoryScreen;
