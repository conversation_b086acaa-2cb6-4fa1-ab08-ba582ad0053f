import { OrderStatus, OrderType } from '../types/deliveryHistory';

export const getStatusColor = (status: OrderStatus): string => {
  switch (status) {
    case OrderStatus.DELIVERED:
      return '#10b981';
    case OrderStatus.CANCELLED:
    case OrderStatus.REJECTED:
      return '#ef4444';
    case OrderStatus.PENDING:
      return '#f59e0b';
    case OrderStatus.ACCEPTED:
    case OrderStatus.PICKED_UP:
    case OrderStatus.IN_TRANSIT:
      return '#3b82f6';
    default:
      return '#6b7280';
  }
};

export const getStatusText = (status: OrderStatus): string => {
  return status.charAt(0).toUpperCase() + status.slice(1).replace('_', ' ');
};

export const getStatusColorWithIcon = (status: OrderStatus) => {
  const color = getStatusColor(status);
  switch (status) {
    case OrderStatus.DELIVERED:
      return { color, bg: '#d1fae5', icon: 'checkmark-circle' };
    case OrderStatus.CANCELLED:
    case OrderStatus.REJECTED:
      return { color, bg: '#fee2e2', icon: 'close-circle' };
    case OrderStatus.PENDING:
    case OrderStatus.ACCEPTED:
    case OrderStatus.PICKED_UP:
    case OrderStatus.IN_TRANSIT:
      return { color, bg: '#fef3c7', icon: 'time' };
    default:
      return { color, bg: '#f3f4f6', icon: 'help-circle' };
  }
};

export const getOrderTypeIcon = (type: OrderType): string => {
  switch (type) {
    case OrderType.FOOD:
      return 'restaurant';
    case OrderType.GROCERY:
      return 'basket';
    case OrderType.PHARMACY:
      return 'medical';
    case OrderType.ELECTRONICS:
      return 'phone-portrait';
    case OrderType.CLOTHING:
      return 'shirt';
    case OrderType.OTHER:
    default:
      return 'cube';
  }
};


