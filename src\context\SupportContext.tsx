import React, { createContext, useContext, useReducer, ReactNode } from 'react';
import {
  SupportContextType,
  SupportState,
  SupportAction,
  ChatSession,
  ChatMessage,
  FAQSection,
  SupportTicket,
  IssueReport,
  AppFeedback,
  SupportContact,
  MessageType,
  MessageStatus,
  IssueType,
  IssueSeverity,
  FeedbackType,
  FeedbackCategory,
} from '../types/support';

// Initial state
const initialState: SupportState = {
  activeChat: null,
  chatHistory: [],
  faqs: [],
  tickets: [],
  issueReports: [],
  feedback: [],
  supportContacts: [],
  loading: false,
  error: null,
};

// Reducer
const supportReducer = (state: SupportState, action: SupportAction): SupportState => {
  switch (action.type) {
    case 'SET_LOADING':
      return { ...state, loading: action.payload };

    case 'SET_ERROR':
      return { ...state, error: action.payload, loading: false };

    case 'SET_ACTIVE_CHAT':
      return { ...state, activeChat: action.payload };

    case 'ADD_CHAT_MESSAGE':
      if (!state.activeChat) return state;
      
      const updatedActiveChat = {
        ...state.activeChat,
        messages: [...state.activeChat.messages, action.payload],
        lastMessage: action.payload,
        updatedAt: new Date().toISOString(),
      };

      return {
        ...state,
        activeChat: updatedActiveChat,
        chatHistory: state.chatHistory.map(chat =>
          chat.id === updatedActiveChat.id ? updatedActiveChat : chat
        ),
      };

    case 'UPDATE_MESSAGE_STATUS':
      if (!state.activeChat) return state;
      
      const updatedMessages = state.activeChat.messages.map(message =>
        message.id === action.payload.messageId
          ? { ...message, status: action.payload.status }
          : message
      );

      const updatedChat = {
        ...state.activeChat,
        messages: updatedMessages,
      };

      return {
        ...state,
        activeChat: updatedChat,
        chatHistory: state.chatHistory.map(chat =>
          chat.id === updatedChat.id ? updatedChat : chat
        ),
      };

    case 'SET_CHAT_HISTORY':
      return { ...state, chatHistory: action.payload };

    case 'SET_FAQS':
      return { ...state, faqs: action.payload };

    case 'SET_TICKETS':
      return { ...state, tickets: action.payload };

    case 'ADD_TICKET':
      return {
        ...state,
        tickets: [action.payload, ...state.tickets],
      };

    case 'UPDATE_TICKET':
      return {
        ...state,
        tickets: state.tickets.map(ticket =>
          ticket.id === action.payload.id ? action.payload : ticket
        ),
      };

    case 'SET_ISSUE_REPORTS':
      return { ...state, issueReports: action.payload };

    case 'ADD_ISSUE_REPORT':
      return {
        ...state,
        issueReports: [action.payload, ...state.issueReports],
      };

    case 'UPDATE_ISSUE_REPORT':
      return {
        ...state,
        issueReports: state.issueReports.map(report =>
          report.id === action.payload.id ? action.payload : report
        ),
      };

    case 'SET_FEEDBACK':
      return { ...state, feedback: action.payload };

    case 'ADD_FEEDBACK':
      return {
        ...state,
        feedback: [action.payload, ...state.feedback],
      };

    case 'UPDATE_FEEDBACK':
      return {
        ...state,
        feedback: state.feedback.map(fb =>
          fb.id === action.payload.id ? action.payload : fb
        ),
      };

    case 'SET_SUPPORT_CONTACTS':
      return { ...state, supportContacts: action.payload };

    default:
      return state;
  }
};

// Context
const SupportContext = createContext<SupportContextType | undefined>(undefined);

// Provider component
interface SupportProviderProps {
  children: ReactNode;
}

export const SupportProvider: React.FC<SupportProviderProps> = ({ children }) => {
  const [state, dispatch] = useReducer(supportReducer, initialState);

  // Chat functions
  const startChat = async (): Promise<ChatSession> => {
    dispatch({ type: 'SET_LOADING', payload: true });
    
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      const newChat: ChatSession = {
        id: `chat-${Date.now()}`,
        riderId: 'rider-1',
        status: 'active',
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        messages: [],
        lastMessage: null,
        adminId: null,
        rating: null,
        tags: [],
      };

      dispatch({ type: 'SET_ACTIVE_CHAT', payload: newChat });
      dispatch({ type: 'SET_LOADING', payload: false });
      
      return newChat;
    } catch (error) {
      dispatch({ type: 'SET_ERROR', payload: 'Failed to start chat' });
      throw error;
    }
  };

  const sendMessage = async (content: string, type: MessageType = MessageType.TEXT): Promise<void> => {
    if (!state.activeChat) {
      throw new Error('No active chat session');
    }

    const message: ChatMessage = {
      id: `msg-${Date.now()}`,
      chatId: state.activeChat.id,
      senderId: 'rider-1',
      senderName: 'Rider',
      senderRole: 'rider',
      type,
      content,
      status: MessageStatus.SENT,
      timestamp: new Date().toISOString(),
    };

    dispatch({ type: 'ADD_CHAT_MESSAGE', payload: message });

    // Simulate message delivery
    setTimeout(() => {
      dispatch({
        type: 'UPDATE_MESSAGE_STATUS',
        payload: { messageId: message.id, status: MessageStatus.DELIVERED },
      });
    }, 1000);

    // Simulate admin response (for demo)
    setTimeout(() => {
      const adminResponse: ChatMessage = {
        id: `msg-${Date.now()}-admin`,
        chatId: state.activeChat!.id,
        senderId: 'admin-1',
        senderName: 'Support Agent',
        senderRole: 'admin',
        type: MessageType.TEXT,
        content: 'Thank you for contacting us. How can I help you today?',
        status: MessageStatus.DELIVERED,
        timestamp: new Date().toISOString(),
      };

      dispatch({ type: 'ADD_CHAT_MESSAGE', payload: adminResponse });
    }, 3000);
  };

  const endChat = async (rating?: number): Promise<void> => {
    if (!state.activeChat) return;

    const updatedChat: ChatSession = {
      ...state.activeChat,
      status: 'closed',
      rating,
      updatedAt: new Date().toISOString(),
    };

    dispatch({ type: 'SET_ACTIVE_CHAT', payload: null });
    
    // Update chat history
    const updatedHistory = state.chatHistory.some(chat => chat.id === updatedChat.id)
      ? state.chatHistory.map(chat => chat.id === updatedChat.id ? updatedChat : chat)
      : [updatedChat, ...state.chatHistory];
    
    dispatch({ type: 'SET_CHAT_HISTORY', payload: updatedHistory });
  };

  // FAQ functions
  const loadFAQs = async (): Promise<void> => {
    dispatch({ type: 'SET_LOADING', payload: true });
    
    try {
      // Simulate API call with mock data
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      const mockFAQs: FAQSection[] = [
        {
          id: 'general',
          title: 'General Questions',
          items: [
            {
              id: 'faq-1',
              question: 'How do I start accepting orders?',
              answer: 'Toggle the "Online" switch on your dashboard to start receiving order requests.',
              category: 'general',
              helpful: 45,
              notHelpful: 3,
              tags: ['orders', 'online', 'dashboard'],
            },
            {
              id: 'faq-2',
              question: 'What should I do if I can\'t find the customer?',
              answer: 'Try calling the customer first. If they don\'t answer, use the "Cannot Deliver" option and follow the instructions.',
              category: 'delivery',
              helpful: 32,
              notHelpful: 1,
              tags: ['delivery', 'customer', 'contact'],
            },
          ],
        },
      ];

      dispatch({ type: 'SET_FAQS', payload: mockFAQs });
      dispatch({ type: 'SET_LOADING', payload: false });
    } catch (error) {
      dispatch({ type: 'SET_ERROR', payload: 'Failed to load FAQs' });
    }
  };

  // Issue reporting functions
  const submitIssueReport = async (report: Omit<IssueReport, 'id' | 'createdAt' | 'updatedAt'>): Promise<IssueReport> => {
    dispatch({ type: 'SET_LOADING', payload: true });
    
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      const newReport: IssueReport = {
        ...report,
        id: `issue-${Date.now()}`,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      };

      dispatch({ type: 'ADD_ISSUE_REPORT', payload: newReport });
      dispatch({ type: 'SET_LOADING', payload: false });
      
      return newReport;
    } catch (error) {
      dispatch({ type: 'SET_ERROR', payload: 'Failed to submit issue report' });
      throw error;
    }
  };

  // Feedback functions
  const submitFeedback = async (feedback: Omit<AppFeedback, 'id' | 'createdAt' | 'updatedAt' | 'deviceInfo'>): Promise<AppFeedback> => {
    dispatch({ type: 'SET_LOADING', payload: true });
    
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      const newFeedback: AppFeedback = {
        ...feedback,
        id: `feedback-${Date.now()}`,
        deviceInfo: {
          platform: 'ios',
          version: '1.0.0',
          model: 'iPhone 12',
        },
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      };

      dispatch({ type: 'ADD_FEEDBACK', payload: newFeedback });
      dispatch({ type: 'SET_LOADING', payload: false });
      
      return newFeedback;
    } catch (error) {
      dispatch({ type: 'SET_ERROR', payload: 'Failed to submit feedback' });
      throw error;
    }
  };

  const contextValue: SupportContextType = {
    // State
    activeChat: state.activeChat,
    chatHistory: state.chatHistory,
    faqs: state.faqs,
    tickets: state.tickets,
    issueReports: state.issueReports,
    feedback: state.feedback,
    supportContacts: state.supportContacts,
    loading: state.loading,
    error: state.error,

    // Chat functions
    startChat,
    sendMessage,
    endChat,

    // FAQ functions
    loadFAQs,

    // Issue reporting functions
    submitIssueReport,

    // Feedback functions
    submitFeedback,
  };

  return (
    <SupportContext.Provider value={contextValue}>
      {children}
    </SupportContext.Provider>
  );
};

// Hook to use the context
export const useSupport = (): SupportContextType => {
  const context = useContext(SupportContext);
  if (context === undefined) {
    throw new Error('useSupport must be used within a SupportProvider');
  }
  return context;
};
