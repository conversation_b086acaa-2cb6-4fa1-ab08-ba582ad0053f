import AsyncStorage from '@react-native-async-storage/async-storage';
import * as SecureStore from 'expo-secure-store';
import { AuthUser, AuthTokens } from '../../types/auth';
import { STORAGE_KEYS } from '../api/apiConfig';

export interface SessionData {
  user: AuthUser;
  tokens: AuthTokens;
  lastActivity: number;
  deviceId: string;
}

class SessionManager {
  private static instance: SessionManager;
  private sessionCheckInterval: NodeJS.Timeout | null = null;
  private readonly SESSION_TIMEOUT = 30 * 24 * 60 * 60 * 1000; // 30 days
  private readonly ACTIVITY_CHECK_INTERVAL = 5 * 60 * 1000; // 5 minutes

  static getInstance(): SessionManager {
    if (!SessionManager.instance) {
      SessionManager.instance = new SessionManager();
    }
    return SessionManager.instance;
  }

  /**
   * Save session data securely
   */
  async saveSession(user: AuthUser, tokens: AuthTokens): Promise<void> {
    try {
      console.log('💾 Saving session data...');

      const deviceId = await this.getOrCreateDeviceId();
      const sessionData: SessionData = {
        user,
        tokens,
        lastActivity: Date.now(),
        deviceId,
      };

      // Store tokens securely
      await SecureStore.setItemAsync(STORAGE_KEYS.ACCESS_TOKEN, tokens.accessToken);
      await SecureStore.setItemAsync(STORAGE_KEYS.REFRESH_TOKEN, tokens.refreshToken);

      // Store user data and session info in AsyncStorage
      await AsyncStorage.setItem(STORAGE_KEYS.USER_DATA, JSON.stringify(user));
      await AsyncStorage.setItem('session_data', JSON.stringify(sessionData));
      await AsyncStorage.setItem('last_activity', Date.now().toString());

      console.log('✅ Session saved successfully');
    } catch (error) {
      console.error('❌ Failed to save session:', error);
      throw new Error('Failed to save session data');
    }
  }

  /**
   * Load session data
   */
  async loadSession(): Promise<SessionData | null> {
    try {
      console.log('📂 Loading session data...');

      // Get tokens from secure storage
      const accessToken = await SecureStore.getItemAsync(STORAGE_KEYS.ACCESS_TOKEN);
      const refreshToken = await SecureStore.getItemAsync(STORAGE_KEYS.REFRESH_TOKEN);

      // Get user data from AsyncStorage
      const userDataStr = await AsyncStorage.getItem(STORAGE_KEYS.USER_DATA);
      const lastActivityStr = await AsyncStorage.getItem('last_activity');

      if (!accessToken || !refreshToken || !userDataStr || !lastActivityStr) {
        console.log('🚫 Incomplete session data found');
        return null;
      }

      const user = JSON.parse(userDataStr);
      const lastActivity = parseInt(lastActivityStr, 10);

      // Check if session is expired
      if (this.isSessionExpired(lastActivity)) {
        console.log('⏰ Session expired');
        await this.clearSession();
        return null;
      }

      const tokens: AuthTokens = {
        accessToken,
        refreshToken,
        expiresIn: 3600, // Default expiry
      };

      const sessionData: SessionData = {
        user,
        tokens,
        lastActivity,
        deviceId: await this.getOrCreateDeviceId(),
      };

      console.log('✅ Session loaded successfully');
      return sessionData;
    } catch (error) {
      console.error('❌ Failed to load session:', error);
      return null;
    }
  }

  /**
   * Update last activity timestamp
   */
  async updateActivity(): Promise<void> {
    try {
      await AsyncStorage.setItem('last_activity', Date.now().toString());
    } catch (error) {
      console.error('Failed to update activity:', error);
    }
  }

  /**
   * Clear all session data
   */
  async clearSession(): Promise<void> {
    try {
      console.log('🧹 Clearing session data...');

      // Clear secure storage
      await SecureStore.deleteItemAsync(STORAGE_KEYS.ACCESS_TOKEN);
      await SecureStore.deleteItemAsync(STORAGE_KEYS.REFRESH_TOKEN);

      // Clear AsyncStorage
      await AsyncStorage.multiRemove([
        STORAGE_KEYS.USER_DATA,
        'session_data',
        'last_activity',
      ]);

      console.log('✅ Session cleared');
    } catch (error) {
      console.error('❌ Failed to clear session:', error);
    }
  }

  /**
   * Check if session is expired
   */
  private isSessionExpired(lastActivity: number): boolean {
    return Date.now() - lastActivity > this.SESSION_TIMEOUT;
  }

  /**
   * Get or create device ID
   */
  private async getOrCreateDeviceId(): Promise<string> {
    try {
      let deviceId = await AsyncStorage.getItem(STORAGE_KEYS.DEVICE_ID);
      if (!deviceId) {
        deviceId = `device_${Date.now()}_${Math.random().toString(36).substring(7)}`;
        await AsyncStorage.setItem(STORAGE_KEYS.DEVICE_ID, deviceId);
      }
      return deviceId;
    } catch (error) {
      console.error('Failed to get device ID:', error);
      return `device_${Date.now()}_${Math.random().toString(36).substring(7)}`;
    }
  }

  /**
   * Start session monitoring
   */
  startSessionMonitoring(): void {
    if (this.sessionCheckInterval) {
      clearInterval(this.sessionCheckInterval);
    }

    this.sessionCheckInterval = setInterval(async () => {
      const session = await this.loadSession();
      if (!session) {
        console.log('🔄 Session expired during monitoring');
        this.stopSessionMonitoring();
        // Trigger logout in auth context
        // This would need to be handled by the auth context
      }
    }, this.ACTIVITY_CHECK_INTERVAL);

    console.log('👁️ Session monitoring started');
  }

  /**
   * Stop session monitoring
   */
  stopSessionMonitoring(): void {
    if (this.sessionCheckInterval) {
      clearInterval(this.sessionCheckInterval);
      this.sessionCheckInterval = null;
      console.log('🛑 Session monitoring stopped');
    }
  }

  /**
   * Refresh session tokens
   */
  async refreshSession(newTokens: AuthTokens): Promise<void> {
    try {
      console.log('🔄 Refreshing session tokens...');

      // Update tokens in secure storage
      await SecureStore.setItemAsync(STORAGE_KEYS.ACCESS_TOKEN, newTokens.accessToken);
      await SecureStore.setItemAsync(STORAGE_KEYS.REFRESH_TOKEN, newTokens.refreshToken);

      // Update activity timestamp
      await this.updateActivity();

      console.log('✅ Session tokens refreshed');
    } catch (error) {
      console.error('❌ Failed to refresh session:', error);
      throw new Error('Failed to refresh session tokens');
    }
  }

  /**
   * Check if user has an active session
   */
  async hasActiveSession(): Promise<boolean> {
    const session = await this.loadSession();
    return session !== null;
  }

  /**
   * Get session info for debugging
   */
  async getSessionInfo(): Promise<{
    hasSession: boolean;
    lastActivity?: Date;
    timeUntilExpiry?: number;
    deviceId?: string;
  }> {
    try {
      const session = await this.loadSession();
      if (!session) {
        return { hasSession: false };
      }

      const timeUntilExpiry = this.SESSION_TIMEOUT - (Date.now() - session.lastActivity);

      return {
        hasSession: true,
        lastActivity: new Date(session.lastActivity),
        timeUntilExpiry: Math.max(0, timeUntilExpiry),
        deviceId: session.deviceId,
      };
    } catch (error) {
      console.error('Failed to get session info:', error);
      return { hasSession: false };
    }
  }
}

export const sessionManager = SessionManager.getInstance();
export default sessionManager;
