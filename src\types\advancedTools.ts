// Advanced Tools & Features Types

// Heatmap & Demand Map Types
export enum DemandLevel {
  VERY_LOW = 'very_low',
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high',
  VERY_HIGH = 'very_high',
  SURGE = 'surge',
}

export enum AreaType {
  RESIDENTIAL = 'residential',
  COMMERCIAL = 'commercial',
  MIXED = 'mixed',
  RESTAURANT_CLUSTER = 'restaurant_cluster',
  SHOPPING_MALL = 'shopping_mall',
  OFFICE_COMPLEX = 'office_complex',
  UNIVERSITY = 'university',
  HOSPITAL = 'hospital',
  AIRPORT = 'airport',
}

export interface HeatmapArea {
  id: string;
  name: string;
  coordinates: {
    latitude: number;
    longitude: number;
  };
  radius: number; // in meters
  demandLevel: DemandLevel;
  areaType: AreaType;
  surgeMultiplier: number;
  bonusAmount: number;
  estimatedOrders: number;
  averageDeliveryTime: number;
  averageEarnings: number;
  activeRiders: number;
  lastUpdated: string;
  peakHours: {
    start: string;
    end: string;
  }[];
  weatherImpact: number; // -1 to 1
  eventImpact?: {
    eventName: string;
    impactLevel: number;
    duration: {
      start: string;
      end: string;
    };
  };
}

export interface DemandForecast {
  areaId: string;
  timeSlot: string;
  predictedDemand: DemandLevel;
  confidence: number; // 0 to 1
  factors: string[];
}

// Performance & Gamification Types
export enum BadgeType {
  DELIVERY_COUNT = 'delivery_count',
  DELIVERY_SPEED = 'delivery_speed',
  CUSTOMER_RATING = 'customer_rating',
  TIME_BASED = 'time_based',
  RATING = 'rating',
  SPEED = 'speed',
  CONSISTENCY = 'consistency',
  SAFETY = 'safety',
  CUSTOMER_SERVICE = 'customer_service',
  EARNINGS = 'earnings',
  SPECIAL_EVENT = 'special_event',
  MILESTONE = 'milestone',
}

export enum BadgeRarity {
  COMMON = 'common',
  UNCOMMON = 'uncommon',
  RARE = 'rare',
  EPIC = 'epic',
  LEGENDARY = 'legendary',
}

export interface Badge {
  id: string;
  name: string;
  description: string;
  type: BadgeType;
  rarity: BadgeRarity;
  icon: string;
  color: string;
  criteria: {
    metric: string;
    threshold: number;
    timeframe?: string;
  };
  requirements: {
    metric: string;
    threshold: number;
    timeframe?: string;
  };
  reward: {
    points: number;
    bonus?: number;
    title?: string;
  };
  isActive: boolean;
  createdAt: string;
}

export interface RiderBadge {
  id: string;
  riderId: string;
  badgeId: string;
  badge: Badge;
  earnedAt: string;
  progress: number; // 0 to 1
  isCompleted: boolean;
  currentValue: number;
  targetValue: number;
}

export interface PerformanceStats {
  riderId: string;
  period: 'daily' | 'weekly' | 'monthly' | 'all_time';
  totalDeliveries: number;
  averageRating: number;
  averageDeliveryTime: number;
  onTimeDeliveryRate: number;
  customerSatisfactionScore: number;
  totalEarnings: number;
  totalDistance: number;
  fuelEfficiency: number;
  safetyScore: number;
  rank: number;
  totalRiders: number;
  points: number;
  level: number;
  nextLevelPoints: number;
  badges: RiderBadge[];
  achievements: Achievement[];
}

export interface Achievement {
  id: string;
  name: string;
  description: string;
  icon: string;
  unlockedAt: string;
  category: string;
  points: number;
}

export interface Leaderboard {
  period: 'daily' | 'weekly' | 'monthly';
  category: 'deliveries' | 'earnings' | 'rating' | 'points';
  riders: LeaderboardEntry[];
  lastUpdated: string;
}

export interface LeaderboardEntry {
  rank: number;
  riderId: string;
  riderName: string;
  riderPhoto?: string;
  value: number;
  change: number; // position change from previous period
  badge?: string;
  isCurrentUser: boolean;
}

// Training Videos Types
export enum TrainingCategory {
  SAFE_DELIVERY = 'safe_delivery',
  APP_USAGE = 'app_usage',
  CUSTOMER_SERVICE = 'customer_service',
  VEHICLE_MAINTENANCE = 'vehicle_maintenance',
  EMERGENCY_PROCEDURES = 'emergency_procedures',
  FOOD_SAFETY = 'food_safety',
  TRAFFIC_RULES = 'traffic_rules',
  EARNINGS_OPTIMIZATION = 'earnings_optimization',
  Safety = 'Safety',
  'Customer Service' = 'Customer Service',
}

export enum VideoStatus {
  NOT_STARTED = 'not_started',
  IN_PROGRESS = 'in_progress',
  COMPLETED = 'completed',
  EXPIRED = 'expired',
}

export interface TrainingVideo {
  id: string;
  title: string;
  description: string;
  category: TrainingCategory;
  duration: number; // in seconds
  thumbnailUrl: string;
  videoUrl: string;
  transcript?: string;
  subtitles?: {
    language: string;
    url: string;
  }[];
  difficulty: 'beginner' | 'intermediate' | 'advanced';
  prerequisites: string[];
  tags: string[];
  isRequired: boolean;
  expiresAt?: string;
  createdAt: string;
  updatedAt: string;
  viewCount: number;
  averageRating: number;
  totalRatings: number;
}

export interface VideoProgress {
  id: string;
  riderId: string;
  videoId: string;
  status: VideoStatus;
  progress: number;
  watchedDuration: number; // in seconds
  totalDuration: number;
  progressPercentage: number;
  startedAt: string;
  lastWatchedAt: string;
  completedAt?: string;
  rating?: number;
  feedback?: string;
  certificateUrl?: string;
}

export interface TrainingCertificate {
  id: string;
  riderId: string;
  videoId: string;
  video: TrainingVideo;
  certificateName: string;
  issuedAt: string;
  expiresAt?: string;
  certificateUrl: string;
  verificationCode: string;
  isValid: boolean;
}

// Push Notifications Types
export enum NotificationType {
  ORDER_REQUEST = 'order_request',
  ORDER_UPDATE = 'order_update',
  PAYMENT_RECEIVED = 'payment_received',
  PAYMENT_REMINDER = 'payment_reminder',
  BONUS_AVAILABLE = 'bonus_available',
  BONUS_EARNED = 'bonus_earned',
  SURGE_ALERT = 'surge_alert',
  SAFETY_ALERT = 'safety_alert',
  TRAINING_REMINDER = 'training_reminder',
  BADGE_EARNED = 'badge_earned',
  LEADERBOARD_UPDATE = 'leaderboard_update',
  SYSTEM_MAINTENANCE = 'system_maintenance',
  SYSTEM_UPDATE = 'system_update',
  PERFORMANCE_UPDATE = 'performance_update',
  PROMOTIONAL = 'promotional',
  EMERGENCY = 'emergency',
}

export enum NotificationPriority {
  LOW = 'low',
  NORMAL = 'normal',
  MEDIUM = 'medium',
  HIGH = 'high',
  URGENT = 'urgent',
}

export interface PushNotification {
  id: string;
  riderId: string;
  type: NotificationType;
  priority: NotificationPriority;
  title: string;
  body: string;
  message: string;
  data?: Record<string, any>;
  imageUrl?: string;
  actionButtons?: {
    id: string;
    title: string;
    action: string;
  }[];
  scheduledAt?: string;
  sentAt: string;
  createdAt: string;
  readAt?: string;
  clickedAt?: string;
  expiresAt?: string;
  isRead: boolean;
  isClicked: boolean;
  category?: string;
  sound?: string;
  badge?: number;
}

export interface NotificationPreferences {
  riderId: string;
  orderRequests: {
    enabled: boolean;
    sound: boolean;
    vibration: boolean;
    priority: NotificationPriority;
  };
  orderUpdates: boolean;
  paymentNotifications: {
    enabled: boolean;
    sound: boolean;
    vibration: boolean;
    priority: NotificationPriority;
  };
  paymentUpdates: {
    enabled: boolean;
    sound: boolean;
    vibration: boolean;
    priority: NotificationPriority;
  };
  bonusAlerts: {
    enabled: boolean;
    sound: boolean;
    vibration: boolean;
    priority: NotificationPriority;
  };
  surgeAlerts: boolean;
  safetyAlerts: {
    enabled: boolean;
    sound: boolean;
    vibration: boolean;
    priority: NotificationPriority;
  };
  trainingReminders: {
    enabled: boolean;
    sound: boolean;
    vibration: boolean;
    priority: NotificationPriority;
  };
  gamificationUpdates: boolean;
  promotional: boolean;
  quietHours: {
    enabled: boolean;
    start: string;
    end: string;
    startTime: string;
    endTime: string;
  };
  globalSettings: {
    masterEnabled: boolean;
    soundEnabled: boolean;
    vibrationEnabled: boolean;
    badgeEnabled: boolean;
  };
  soundEnabled: boolean;
  vibrationEnabled: boolean;
  lastUpdated: string;
}

// Background Location Tracking Types
export enum LocationTrackingStatus {
  DISABLED = 'disabled',
  ENABLED = 'enabled',
  PAUSED = 'paused',
  ERROR = 'error',
}

export enum TrackingStatus {
  STOPPED = 'stopped',
  ACTIVE = 'active',
  PAUSED = 'paused',
  ERROR = 'error',
}

export interface LocationData {
  id: string;
  riderId: string;
  latitude: number;
  longitude: number;
  accuracy: number;
  altitude?: number;
  speed?: number;
  heading?: number;
  timestamp: string;
  address?: string;
  isOnline: boolean;
  batteryLevel?: number;
  networkType?: string;
}

export enum LocationAccuracy {
  LOW = 'low',
  MEDIUM = 'medium',
  BALANCED = 'balanced',
  HIGH = 'high',
  HIGHEST = 'highest',
}

export enum BatteryOptimization {
  DISABLED = 'disabled',
  BALANCED = 'balanced',
  AGGRESSIVE = 'aggressive',
}

export interface LocationTrackingConfig {
  id: string;
  riderId: string;
  isEnabled: boolean;
  accuracy: LocationAccuracy;
  updateInterval: number; // in milliseconds
  distanceFilter: number; // in meters
  batteryOptimization: boolean;
  backgroundTracking: boolean;
  trackingDuringDelivery: boolean;
  trackingForSafety: boolean;
  shareWithCustomers: boolean;
  shareWithSupport: boolean;
  emergencySharing: boolean;
  dataRetentionDays: number;
  lastUpdated: string;
}

export interface LocationPoint {
  id: string;
  riderId: string;
  latitude: number;
  longitude: number;
  altitude?: number;
  accuracy: number;
  speed?: number;
  heading?: number;
  timestamp: string;
  batteryLevel?: number;
  isDelivering: boolean;
  orderId?: string;
  address?: string;
  source: 'foreground' | 'background' | 'manual';
}

export interface LocationSession {
  id: string;
  riderId: string;
  startTime: string;
  endTime?: string;
  totalDistance: number;
  averageSpeed: number;
  maxSpeed: number;
  points: LocationPoint[];
  deliveries: string[];
  batteryUsage: number;
  dataUsage: number; // in bytes
  status: LocationTrackingStatus;
}

// Advanced Tools State & Context Types
export interface AdvancedToolsState {
  // Heatmap & Demand
  heatmapAreas: HeatmapArea[];
  demandForecasts: DemandForecast[];
  selectedArea: HeatmapArea | null;
  
  // Performance & Gamification
  performanceStats: PerformanceStats | null;
  availableBadges: Badge[];
  riderBadges: RiderBadge[];
  leaderboards: Leaderboard[];
  achievements: Achievement[];
  
  // Training Videos
  trainingVideos: TrainingVideo[];
  videoProgress: VideoProgress[];
  certificates: TrainingCertificate[];
  currentVideo: TrainingVideo | null;
  
  // Push Notifications
  notifications: PushNotification[];
  notificationPreferences: NotificationPreferences | null;
  unreadCount: number;
  
  // Background Location
  locationTrackingConfig: LocationTrackingConfig | null;
  locationSessions: LocationSession[];
  currentLocation: LocationPoint | null;
  trackingStatus: LocationTrackingStatus;
  
  // General state
  loading: boolean;
  error: string | null;
}

export type AdvancedToolsAction =
  | { type: 'SET_LOADING'; payload: boolean }
  | { type: 'SET_ERROR'; payload: string | null }
  
  // Heatmap actions
  | { type: 'SET_HEATMAP_AREAS'; payload: HeatmapArea[] }
  | { type: 'UPDATE_HEATMAP_AREA'; payload: { id: string; updates: Partial<HeatmapArea> } }
  | { type: 'SET_DEMAND_FORECASTS'; payload: DemandForecast[] }
  | { type: 'SET_SELECTED_AREA'; payload: HeatmapArea | null }
  
  // Performance actions
  | { type: 'SET_PERFORMANCE_STATS'; payload: PerformanceStats }
  | { type: 'SET_AVAILABLE_BADGES'; payload: Badge[] }
  | { type: 'SET_RIDER_BADGES'; payload: RiderBadge[] }
  | { type: 'UPDATE_BADGE_PROGRESS'; payload: { badgeId: string; progress: number; currentValue: number } }
  | { type: 'COMPLETE_BADGE'; payload: RiderBadge }
  | { type: 'SET_LEADERBOARDS'; payload: Leaderboard[] }
  | { type: 'ADD_ACHIEVEMENT'; payload: Achievement }
  
  // Training actions
  | { type: 'SET_TRAINING_VIDEOS'; payload: TrainingVideo[] }
  | { type: 'SET_VIDEO_PROGRESS'; payload: VideoProgress[] }
  | { type: 'UPDATE_VIDEO_PROGRESS'; payload: { videoId: string; progress: Partial<VideoProgress> } }
  | { type: 'COMPLETE_VIDEO'; payload: { videoId: string; certificate?: TrainingCertificate } }
  | { type: 'SET_CURRENT_VIDEO'; payload: TrainingVideo | null }
  | { type: 'SET_CERTIFICATES'; payload: TrainingCertificate[] }
  
  // Notification actions
  | { type: 'SET_NOTIFICATIONS'; payload: PushNotification[] }
  | { type: 'ADD_NOTIFICATION'; payload: PushNotification }
  | { type: 'MARK_NOTIFICATION_READ'; payload: string }
  | { type: 'MARK_NOTIFICATION_CLICKED'; payload: string }
  | { type: 'SET_NOTIFICATION_PREFERENCES'; payload: NotificationPreferences }
  | { type: 'UPDATE_UNREAD_COUNT'; payload: number }
  
  // Location tracking actions
  | { type: 'SET_LOCATION_TRACKING_CONFIG'; payload: LocationTrackingConfig }
  | { type: 'UPDATE_LOCATION_TRACKING_CONFIG'; payload: Partial<LocationTrackingConfig> }
  | { type: 'SET_LOCATION_SESSIONS'; payload: LocationSession[] }
  | { type: 'ADD_LOCATION_POINT'; payload: LocationPoint }
  | { type: 'SET_CURRENT_LOCATION'; payload: LocationPoint }
  | { type: 'SET_TRACKING_STATUS'; payload: LocationTrackingStatus }
  | { type: 'START_LOCATION_SESSION'; payload: LocationSession }
  | { type: 'END_LOCATION_SESSION'; payload: { sessionId: string; endData: Partial<LocationSession> } };

export interface AdvancedToolsContextType {
  // State
  heatmapAreas: HeatmapArea[];
  selectedArea: HeatmapArea | null;
  badges: Badge[];
  performanceStats: PerformanceStats | null;
  leaderboard: any[];
  trainingVideos: TrainingVideo[];
  videoProgress: VideoProgress[];
  certificates: TrainingCertificate[];
  notifications: PushNotification[];
  notificationPreferences: NotificationPreferences | null;
  unreadCount: number;
  locationConfig: LocationTrackingConfig | null;
  currentLocation: LocationData | null;
  locationHistory: LocationData[];
  trackingStatus: TrackingStatus;
  loading: boolean;
  error: string | null;

  // Heatmap Methods
  setHeatmapAreas: (areas: HeatmapArea[]) => void;
  setSelectedArea: (area: HeatmapArea | null) => void;

  // Performance Methods
  setBadges: (badges: Badge[]) => void;
  setPerformanceStats: (stats: PerformanceStats) => void;
  setLeaderboard: (leaderboard: any[]) => void;
  addBadge: (badge: Badge) => void;

  // Training Methods
  setTrainingVideos: (videos: TrainingVideo[]) => void;
  setVideoProgress: (progress: VideoProgress[]) => void;
  setCertificates: (certificates: TrainingCertificate[]) => void;
  updateVideoProgress: (progress: VideoProgress) => void;
  addCertificate: (certificate: TrainingCertificate) => void;

  // Notification Methods
  setNotifications: (notifications: PushNotification[]) => void;
  setNotificationPreferences: (preferences: NotificationPreferences) => void;
  addNotification: (notification: PushNotification) => void;
  markNotificationRead: (notificationId: string) => void;
  deleteNotification: (notificationId: string) => void;
  clearAllNotifications: () => void;

  // Location Methods
  setLocationConfig: (config: LocationTrackingConfig) => void;
  setCurrentLocation: (location: LocationData) => void;
  setLocationHistory: (history: LocationData[]) => void;
  addLocationHistory: (location: LocationData) => void;
  setTrackingStatus: (status: TrackingStatus) => void;

  // General Methods
  setLoading: (loading: boolean) => void;
  setError: (error: string | null) => void;
  refreshData: () => Promise<void>;
}
